package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巡检插件查询请求DTO
 */
@Data
@ApiModel(description = "巡检插件查询请求DTO")
public class InspectionPluginQueryReqDTO {

    @ApiModelProperty(value = "插件ID", example = "1001")
    private Long pluginId;

    @ApiModelProperty(value = "插件名称", example = "CPU使用率检查")
    private String name;

    @ApiModelProperty(value = "插件类型(SHELL、PYTHON等)", example = "SHELL")
    private PluginType type;

    @ApiModelProperty(value = "插件状态(0禁用，1启用)", example = "1")
    private PluginStatus status;

    private PageDTO<?> page;
}
