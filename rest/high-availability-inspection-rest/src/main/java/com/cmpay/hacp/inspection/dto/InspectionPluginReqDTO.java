package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 巡检插件请求DTO
 */
@Data
@Schema(description = "巡检插件请求DTO")
public class InspectionPluginReqDTO {

    @Schema(description = "插件ID，新增时不需要传入", example = "1001")
    private String pluginId;

    @Schema(description = "插件名称", required = true, example = "CPU使用率检查")
    @NotBlank(message = "插件名称不能为空")
    @Size(max = 128, message = "插件名称长度不能超过128个字符")
    private String name;

    @Schema(description = "插件类型：1-SHELL脚本, 2-PYTHON脚本, 3-页面检查, 4-自动化测试", required = true, example = "1")
    @NotNull(message = "插件类型不能为空")
    private PluginType type;

    @Schema(description = "插件状态：0-禁用，1-启用", example = "1")
    private PluginStatus status;

    @Schema(description = "插件描述", example = "检查服务器CPU使用率是否超过阈值")
    @Size(max = 500, message = "插件描述长度不能超过500个字符")
    private String description;

    @Schema(description = "脚本内容", required = true, example = "#!/bin/bash\necho \"CPU使用率检查\"")
    @NotBlank(message = "脚本内容不能为空")
    private String scriptContent;

    @Schema(description = "脚本输出类型：1-结构化，2-纯文本", example = "1")
    private ScriptResultType scriptResultType;

    @Schema(description = "标签ID列表", example = "[1, 2, 3]")
    private List<Long> tagIds;

    /**
     * 输出字段定义
     */
    @Schema(description = "输出字段定义列表")
    private List<PluginScriptResultDTO> results;

    /**
     * 参数设置列表
     */
    @Schema(description = "参数设置列表")
    private List<PluginScriptParameterDTO> parameters;

}
