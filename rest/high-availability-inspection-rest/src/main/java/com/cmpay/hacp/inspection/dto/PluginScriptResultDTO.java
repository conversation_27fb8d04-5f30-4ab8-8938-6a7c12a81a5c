package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultFieldType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 插件脚本输出字段定义DTO
 */
@Data
@ApiModel(description = "插件脚本输出字段定义DTO")
public class PluginScriptResultDTO {
    /**
     * 字段名称(如cpu.usage)
     */
    @ApiModelProperty(value = "字段名称", required = true, example = "disk.usage")
    @NotBlank(message = "字段名称不能为空")
    private String fieldName;

    /**
     * 示例值
     */
    @ApiModelProperty(value = "示例值", required = true, example = "85.5")
    @NotBlank(message = "示例值不能为空")
    private String exampleValue;

    /**
     * 单位(如%)
     */
    @ApiModelProperty(value = "单位", example = "%")
    private String fieldUnit;

    @ApiModelProperty(value = "字段类型：0-数值型, 1-字符串型, 2-布尔型", required = true, example = "0")
    @NotNull(message = "字段类型不能为空")
    private ScriptResultFieldType fieldType;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", example = "磁盘使用率")
    private String description;
}
