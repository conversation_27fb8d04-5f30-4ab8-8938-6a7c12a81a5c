package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionTaskService;
import com.cmpay.hacp.inspection.assembler.InspectionTaskDTOMapper;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.dto.InspectionTaskQueryReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionTaskReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionTaskRspDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 巡检任务控制器
 */
@Slf4j
@RestController
@RequestMapping("/v1/inspection/task")
@Tag(name = "巡检任务管理")
@RequiredArgsConstructor
public class InspectionTaskController {

    private final InspectionTaskService inspectionTaskService;
    private final InspectionTaskDTOMapper inspectionTaskDTOMapper;

    /**
     * 创建巡检任务
     *
     * @param reqDTO 请求DTO
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建巡检任务", description = "创建新的巡检任务")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionTaskController','inspection:task:create')")
    public DefaultRspDTO<String> createTask(@Validated @RequestBody InspectionTaskReqDTO reqDTO) {
        log.info("创建巡检任务: {}", reqDTO);

        // 转换请求DTO为领域对象
        InspectionTask inspectionTask = inspectionTaskDTOMapper.toInspectionTask(reqDTO);

        try {
            // 调用服务层创建任务
            String taskId = inspectionTaskService.createTask(inspectionTask);
            return DefaultRspDTO.newSuccessInstance(taskId);
        } catch (Exception e) {
            log.error("Failed to create inspection task", e);
            return DefaultRspDTO.newInstance(ErrorCodeEnum.TASK_CREATE_ERROR);
        }
    }

    /**
     * 更新巡检任务
     *
     * @param reqDTO 请求DTO
     * @return 创建结果
     */
    @PostMapping("/update")
    @Operation(summary = "更新巡检任务", description = "更新巡检任务")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionTaskController','inspection:task:update')")
    public DefaultRspDTO<NoBody> updateTask(@Validated @RequestBody InspectionTaskReqDTO reqDTO) {
        // 验证任务ID
        if (reqDTO.getTaskId() == null) {
            return DefaultRspDTO.newInstance(ErrorCodeEnum.TASK_ID_REQUIRED);
        }

        InspectionTask inspectionTask = inspectionTaskDTOMapper.toInspectionTask(reqDTO);

        // 调用服务更新任务
        boolean success = inspectionTaskService.updateTask(inspectionTask);

        if (success) {
            return DefaultRspDTO.newSuccessInstance();
        } else {
            return DefaultRspDTO.newInstance(ErrorCodeEnum.TASK_UPDATE_FAILED);
        }
    }

    /**
     * 获取巡检任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "获取巡检任务详情", description = "根据任务ID获取巡检任务详情")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionTaskController','inspection:task:query')")
    public DefaultRspDTO<InspectionTaskRspDTO> getTaskDetail(
            @Parameter(name = "taskId", description = "任务ID", example = "TASK-000001", required = true)
            @PathVariable("taskId") String taskId) {

        // 调用服务层获取任务详情
        InspectionTask task = inspectionTaskService.getTaskDetail(taskId);

        if (task == null) {
            return DefaultRspDTO.newInstance(ErrorCodeEnum.TASK_NOT_FOUND);
        }

        // 转换为响应DTO
        InspectionTaskRspDTO rspDTO = inspectionTaskDTOMapper.toInspectionTaskRspDTO(task);
        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

    /**
     * 立即执行巡检任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    @PostMapping("/{taskId}/execute")
    @ApiOperation(value = "立即执行巡检任务", notes = "立即执行指定的巡检任务")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionTaskController','inspection:task:execute')")
    public DefaultRspDTO<NoBody> executeTask(
            @ApiParam(name = "taskId", value = "任务ID", example = "TASK-000001", required = true)
            @PathVariable("taskId") String taskId) {
        try {
            // 调用服务层立即执行任务
            inspectionTaskService.executeTaskNow(taskId);
            return DefaultRspDTO.newSuccessInstance();
        } catch (Exception e) {
            log.error("Failed to execute inspection task: {}", taskId, e);
            return DefaultRspDTO.newInstance(ErrorCodeEnum.TASK_EXECUTION_ERROR);
        }
    }


    /**
     * 删除巡检任务
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{taskId}")
    @ApiOperation(value = "删除巡检任务", notes = "删除指定的巡检任务")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionTaskController','inspection:task:delete')")
    public DefaultRspDTO<NoBody> deleteTask(
            @ApiParam(name = "taskId", value = "任务ID", required = true)
            @PathVariable("taskId") String taskId) {
        inspectionTaskService.deleteTask(taskId);

        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 分页查询巡检任务列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询巡检任务列表", notes = "根据条件分页查询巡检任务列表")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionTaskController','inspection:task:page')")
    public DefaultRspDTO<PageDTO<InspectionTaskRspDTO>> getTaskPage(@Validated @RequestBody InspectionTaskQueryReqDTO reqDTO) {
        InspectionTask inspectionTask = inspectionTaskDTOMapper.toInspectionTask(reqDTO);

        IPage<InspectionTask> page = inspectionTaskService.getTaskPage(reqDTO.getPage(), inspectionTask);
        List<InspectionTaskRspDTO> rspDTOList = inspectionTaskDTOMapper.toInspectionTaskRspDTOList(page.getRecords());
        PageDTO<InspectionTaskRspDTO> result = new PageDTO<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(rspDTOList);

        return DefaultRspDTO.newSuccessInstance(result);
    }

}
