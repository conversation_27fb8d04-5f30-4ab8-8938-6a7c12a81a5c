package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.TaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 巡检任务响应DTO
 */
@Data
@Schema(description = "巡检任务响应DTO")
public class InspectionTaskRspDTO {

    @Schema(description = "任务ID", example = "TASK-000001")
    private String taskId;

    @Schema(description = "任务名称", example = "每日系统巡检")
    private String name;

    @Schema(description = "任务描述", example = "每日对系统进行全面巡检，检查系统健康状态")
    private String description;

    @Schema(description = "任务状态：0-禁用，1-启用", example = "1")
    private TaskStatus status;

    @Schema(description = "任务关联的规则执行配置列表")
    private List<TaskRuleExecutionDTO> taskRuleExecutions;

    @Schema(description = "调度配置")
    private ScheduleConfigDTO scheduleConfig;

    @Schema(description = "审计信息")
    private AuditInfoDTO auditInfo;
}
