package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.dto.DailyInspectionReportDTO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 按日巡检报告DTO转换器
 */
@Mapper(componentModel = "spring")
public interface DailyInspectionReportDTOMapper {

    /**
     * 将汇总数据转换为按日报告DTO
     * 
     * @param summaryData 汇总数据
     * @return 按日报告DTO
     */
    @Mapping(target = "reportDate", source = "reportDate")
    @Mapping(target = "reportId", source = "reportId")
    @Mapping(target = "totalChecks", source = "totalChecks")
    @Mapping(target = "successCount", source = "successCount")
    @Mapping(target = "warningCount", source = "warningCount")
    @Mapping(target = "failedCount", source = "failedCount")
    @Mapping(target = "passRate", source = "passRate")
    @Mapping(target = "generateTime", source = "generateTime")
    @Mapping(target = "executionSummary", ignore = true)
    @Mapping(target = "executionDistribution", ignore = true)
    @Mapping(target = "passRateTrend", ignore = true)
    @Mapping(target = "exceptionSummary", ignore = true)
    @Mapping(target = "exceptionDetails", ignore = true)
    DailyInspectionReportDTO toDTO(Map<String, Object> summaryData);

    /**
     * 将按次报告列表转换为按日汇总DTO
     * 
     * @param reports 按次报告列表
     * @param reportDate 报告日期
     * @return 按日报告DTO
     */
    @Mapping(target = "reportDate", source = "reportDate")
    @Mapping(target = "reportId", source = "reports", qualifiedByName = "generateDailyReportId")
    @Mapping(target = "totalChecks", source = "reports", qualifiedByName = "calculateTotalChecks")
    @Mapping(target = "successCount", source = "reports", qualifiedByName = "calculateSuccessCount")
    @Mapping(target = "warningCount", source = "reports", qualifiedByName = "calculateWarningCount")
    @Mapping(target = "failedCount", source = "reports", qualifiedByName = "calculateFailedCount")
    @Mapping(target = "passRate", source = "reports", qualifiedByName = "calculatePassRate")
    @Mapping(target = "generateTime", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "executionSummary", source = "reports", qualifiedByName = "buildExecutionSummary")
    @Mapping(target = "executionDistribution", source = "reports", qualifiedByName = "buildExecutionDistribution")
    @Mapping(target = "passRateTrend", ignore = true)
    @Mapping(target = "exceptionSummary", source = "reports", qualifiedByName = "buildExceptionSummary")
    @Mapping(target = "exceptionDetails", source = "reports", qualifiedByName = "buildExceptionDetails")
    DailyInspectionReportDTO fromReports(List<ReportDO> reports, LocalDate reportDate);

    /**
     * 生成按日报告ID
     */
    @Named("generateDailyReportId")
    default String generateDailyReportId(List<ReportDO> reports) {
        if (reports == null || reports.isEmpty()) {
            return "DAILY-" + java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        return "DAILY-" + reports.get(0).getStartTime().toLocalDate().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 计算总检查数
     */
    @Named("calculateTotalChecks")
    default Integer calculateTotalChecks(List<ReportDO> reports) {
        if (reports == null || reports.isEmpty()) {
            return 0;
        }
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getTotal();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 计算成功数
     */
    @Named("calculateSuccessCount")
    default Integer calculateSuccessCount(List<ReportDO> reports) {
        if (reports == null || reports.isEmpty()) {
            return 0;
        }
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getStatusCounts().stream()
                                .filter(status -> "passed".equals(status.getStatusCode()))
                                .mapToInt(status -> status.getCount())
                                .sum();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 计算告警数
     */
    @Named("calculateWarningCount")
    default Integer calculateWarningCount(List<ReportDO> reports) {
        if (reports == null || reports.isEmpty()) {
            return 0;
        }
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getStatusCounts().stream()
                                .filter(status -> "warning".equals(status.getStatusCode()))
                                .mapToInt(status -> status.getCount())
                                .sum();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 计算失败数
     */
    @Named("calculateFailedCount")
    default Integer calculateFailedCount(List<ReportDO> reports) {
        if (reports == null || reports.isEmpty()) {
            return 0;
        }
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getStatusCounts().stream()
                                .filter(status -> "failed".equals(status.getStatusCode()))
                                .mapToInt(status -> status.getCount())
                                .sum();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 计算通过率
     */
    @Named("calculatePassRate")
    default Integer calculatePassRate(List<ReportDO> reports) {
        Integer total = calculateTotalChecks(reports);
        Integer success = calculateSuccessCount(reports);
        if (total == null || total == 0) {
            return 0;
        }
        return (success * 100) / total;
    }

    /**
     * 构建执行概况
     */
    @Named("buildExecutionSummary")
    default DailyInspectionReportDTO.ExecutionSummaryDTO buildExecutionSummary(List<ReportDO> reports) {
        if (reports == null || reports.isEmpty()) {
            return DailyInspectionReportDTO.ExecutionSummaryDTO.builder()
                    .totalTasks(0)
                    .successTasks(0)
                    .failedTasks(0)
                    .totalRules(0)
                    .successRules(0)
                    .failedRules(0)
                    .build();
        }

        int totalTasks = reports.size();
        int successTasks = (int) reports.stream()
                .filter(report -> "SUCCESS".equals(report.getExecutionStatus().name()))
                .count();
        int failedTasks = totalTasks - successTasks;

        int totalRules = reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionSummary() != null) {
                        return report.getContent().getExecutionSummary().getTotalRules();
                    }
                    return 0;
                })
                .sum();

        int successRules = reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionSummary() != null) {
                        return report.getContent().getExecutionSummary().getSuccessRules();
                    }
                    return 0;
                })
                .sum();

        int failedRules = totalRules - successRules;

        return DailyInspectionReportDTO.ExecutionSummaryDTO.builder()
                .totalTasks(totalTasks)
                .successTasks(successTasks)
                .failedTasks(failedTasks)
                .totalRules(totalRules)
                .successRules(successRules)
                .failedRules(failedRules)
                .build();
    }

    /**
     * 构建执行结果分布
     */
    @Named("buildExecutionDistribution")
    default DailyInspectionReportDTO.ExecutionDistributionDTO buildExecutionDistribution(List<ReportDO> reports) {
        Integer total = calculateTotalChecks(reports);
        Integer success = calculateSuccessCount(reports);
        Integer warning = calculateWarningCount(reports);
        Integer failed = calculateFailedCount(reports);
        Integer passRate = calculatePassRate(reports);

        List<DailyInspectionReportDTO.ExecutionDistributionDTO.StatusCountDTO> statusCounts = List.of(
                DailyInspectionReportDTO.ExecutionDistributionDTO.StatusCountDTO.builder()
                        .status("通过")
                        .statusCode("passed")
                        .count(success)
                        .percentage(total > 0 ? (success * 100.0) / total : 0.0)
                        .build(),
                DailyInspectionReportDTO.ExecutionDistributionDTO.StatusCountDTO.builder()
                        .status("告警")
                        .statusCode("warning")
                        .count(warning)
                        .percentage(total > 0 ? (warning * 100.0) / total : 0.0)
                        .build(),
                DailyInspectionReportDTO.ExecutionDistributionDTO.StatusCountDTO.builder()
                        .status("失败")
                        .statusCode("failed")
                        .count(failed)
                        .percentage(total > 0 ? (failed * 100.0) / total : 0.0)
                        .build()
        );

        return DailyInspectionReportDTO.ExecutionDistributionDTO.builder()
                .total(total)
                .successRate(passRate)
                .statusCounts(statusCounts)
                .build();
    }

    /**
     * 构建异常汇总
     */
    @Named("buildExceptionSummary")
    default List<DailyInspectionReportDTO.ExceptionSummaryDTO> buildExceptionSummary(List<ReportDO> reports) {
        // 这里可以根据实际需求实现异常汇总逻辑
        return List.of();
    }

    /**
     * 构建异常详情
     */
    @Named("buildExceptionDetails")
    default List<DailyInspectionReportDTO.ExceptionDetailDTO> buildExceptionDetails(List<ReportDO> reports) {
        // 这里可以根据实际需求实现异常详情逻辑
        return List.of();
    }
}
