package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 按次巡检报告查询请求DTO
 */
@Data
@ApiModel(description = "按次巡检报告查询请求")
public class PerInspectionReportQueryReqDTO {

    @ApiModelProperty(value = "任务ID", example = "TASK-001")
    private String taskId;

    @ApiModelProperty(value = "任务名称", example = "网络连通性检测")
    private String taskName;

    @ApiModelProperty(value = "报告ID", example = "RPT-20250124001")
    private String reportId;

    @ApiModelProperty(value = "开始日期", example = "2025-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期", example = "2025-01-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty(value = "开始时间", example = "2025-01-01T00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-01-31T23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "执行状态", example = "SUCCESS", allowableValues = "SUCCESS,FAILED,WARNING,RUNNING")
    private String executionStatus;

    @ApiModelProperty(value = "触发方式", example = "SCHEDULED", allowableValues = "SCHEDULED,MANUAL,API")
    private String triggerMode;

    @ApiModelProperty(value = "任务ID列表")
    private List<String> taskIds;

    @ApiModelProperty(value = "分页参数")
    private PageDTO<?> page;
}
