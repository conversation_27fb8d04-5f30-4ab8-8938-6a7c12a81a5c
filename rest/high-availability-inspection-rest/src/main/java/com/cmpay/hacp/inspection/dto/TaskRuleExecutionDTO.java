package com.cmpay.hacp.inspection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 规则执行配置DTO
 */
@Data
@Schema(description = "规则执行配置DTO")
public class TaskRuleExecutionDTO {

    @Schema(description = "任务ID", required = true, example = "TASK-000001")
    @NotBlank(message = "HAI30001")
    private String taskId;

    @Schema(description = "规则ID", required = true, example = "RULE-000001")
    @NotBlank(message = "HAI20001")
    private String ruleId;

    @Schema(description = "规则名称", example = "CPU 使用率阈值检查")
    private String ruleName;

    @Schema(description = "目标环境ID列表", required = true, example = "[\"ENV-000001\", \"ENV-000002\"]")
    @NotEmpty(message = "HAI50511")
    private List<String> targetEnvironmentId;
}
