package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.common.Tag;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.dto.InspectionRuleQueryReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionRuleReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionRuleRspDTO;
import com.cmpay.hacp.inspection.dto.TagDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 巡检规则对象转换器
 */
@Mapper(componentModel = "spring")
public interface InspectionRuleDTOMapper {

    /**
     * 请求DTO转领域对象
     *
     * @param reqDTO 请求DTO
     * @return 领域对象
     */
    @Mapping(target = "auditInfo", ignore = true)
    InspectionRule toInspectionRule(InspectionRuleReqDTO reqDTO);

    List<InspectionRuleRspDTO> toInspectionRuleRspDTOList(List<InspectionRule> records);

    @Mapping(target = "level", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "pluginId", ignore = true)
    @Mapping(target = "angleView", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "pluginParams", ignore = true)
    @Mapping(target = "pluginResult", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    InspectionRule toInspectionRule(InspectionRuleQueryReqDTO reqDTO);

    @Mapping(target = "tags", ignore = true)
    InspectionRuleRspDTO toInspectionRuleRspDTO(InspectionRule inspectionRule);

    List<TagDTO> toTagDtoList(List<Tag> tagByTagIds);
}
