package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.TaskStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 巡检任务请求DTO
 */
@Data
@ApiModel(description = "巡检任务请求DTO")
public class InspectionTaskReqDTO {

    @ApiModelProperty(value = "任务ID，新增时不需要传入", example = "TASK-000001")
    private String taskId;

    @ApiModelProperty(value = "任务名称", required = true, example = "每日系统巡检")
    @NotBlank(message = "任务名称不能为空")
    @Size(max = 128, message = "任务名称长度不能超过128个字符")
    private String name;

    @ApiModelProperty(value = "任务描述", example = "每日对系统进行全面巡检，检查系统健康状态")
    @Size(max = 500, message = "任务描述长度不能超过500个字符")
    private String description;

    @ApiModelProperty(value = "任务状态：0-禁用，1-启用", example = "1")
    private TaskStatus status;

    @ApiModelProperty(value = "任务关联的规则执行配置列表", required = true)
    @NotNull(message = "规则执行配置不能为空")
    @Valid
    private List<TaskRuleExecutionDTO> taskRuleExecutions;

    @ApiModelProperty(value = "调度配置", required = true)
    @NotNull(message = "调度配置不能为空")
    @Valid
    private ScheduleConfigDTO scheduleConfig;
}
