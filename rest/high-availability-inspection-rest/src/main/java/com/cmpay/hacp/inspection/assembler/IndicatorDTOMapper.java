package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.dto.IndicatorParamDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IndicatorParamDTOMapper {
    @Mapping(source = "indicatorDefinition.inputParams", target = ".")
    List<IndicatorParamDTO> toIndicatorParamDTO(IndicatorDefinition indicatorDefinition);
}
