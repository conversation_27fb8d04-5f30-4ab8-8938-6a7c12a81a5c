package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.RuleCheckPeakEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 规则监控字段DTO
 */
@Data
@Schema(description = "规则监控字段DTO")
public class RulePluginResultDTO {

    @Schema(description = "插件ID", required = true, example = "1")
    @NotBlank(message = "HAI10001")
    private String pluginId;

    @Schema(description = "监控字段（插件输出字段ID）", required = true, example = "1")
    @NotNull(message = "HAI50306")
    private Long pluginResultId;

    /**
     * @see RuleComparisonOperator
     */
    @Schema(description = "判断条件(1大于,2小于,3大于等于,4小于等于,5等于)", required = true, example = "1")
    @NotNull(message = "HAI50507")
    private RuleComparisonOperator judge;

    @Schema(description = "数值", required = true, example = "100")
    @NotNull(message = "HAI50508")
    private BigDecimal judgeValue;

    @Schema(description = "持续时间（秒）", required = true, example = "300")
    @NotNull(message = "HAI10006")
    private Integer duration;

    @Schema(description = "检查间隔（秒）", required = true, example = "60")
    @NotNull(message = "HAI50407")
    private Integer checkInterval;

    /**
     * @see RuleCheckPeakEnum
     */
    @Schema(description = "仅检查峰值（0-否，1-是）", required = true, example = "0")
    @NotNull(message = "HAI50509")
    private RuleCheckPeakEnum checkPeak;

    @Schema(description = "检查特定进程（逗号分隔）", example = "java,mysql,nginx")
    private Integer specificProcesses;

    @Schema(description = "规则预览", example = "当 connection_count > 持续 300 秒，每 60 秒检查一次 时触发告警")
    private String rulePreview;

    @Schema(description = "治理建议", required = true, example = "请输入当规则触发时的治理建议")
    @NotBlank(message = "HAI50510")
    private String suggest;

}
