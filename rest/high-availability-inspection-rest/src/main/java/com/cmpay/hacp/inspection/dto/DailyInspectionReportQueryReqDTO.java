package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 按日巡检报告查询请求DTO
 */
@Data
@ApiModel(description = "按日巡检报告查询请求DTO")
public class DailyInspectionReportQueryReqDTO {

    @ApiModelProperty(value = "报告ID", example = "DR20250324")
    private String reportId;

    @ApiModelProperty(value = "报告日期", example = "2025-03-24")
    private LocalDate reportDate;

    @ApiModelProperty(value = "最低通过率（查询通过率大于等于此值的报告）", example = "80.0")
    private Double passRate;

    @ApiModelProperty(value = "报告状态", example = "COMPLETED")
    private String reportStatus;

    @ApiModelProperty(value = "分页参数", required = true)
    private PageDTO<?> page;
}
