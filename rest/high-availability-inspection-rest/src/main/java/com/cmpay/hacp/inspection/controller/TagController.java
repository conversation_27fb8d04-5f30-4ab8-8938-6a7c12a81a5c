package com.cmpay.hacp.inspection.controller;

import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.dto.TagReqDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标签管理
 */
@RestController
@RequestMapping("/v1/inspection/tag")
@Tag(name = "标签管理")
@RequiredArgsConstructor
public class TagController {
    private final TagService tagService;

    /**
     * 创建标签
     * @param tagReqDTO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "创建标签", description = "创建标签")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('TagController','inspection:tag:create')")
    public DefaultRspDTO<Long> createPlugin(@RequestBody TagReqDTO tagReqDTO) {

        Long tagId = tagService.createTag(tagReqDTO.getTagName());

        return DefaultRspDTO.newSuccessInstance(tagId);
    }

    /**
     * 获取所有的插件标签
     * @return
     */
    @GetMapping("/pluginTags")
    @Operation(summary = "获取所有的插件标签", description = "获取所有的插件标签")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('TagController','inspection:tag:pluginTags')")
    public DefaultRspDTO<Map<Long, String>> pluginTags() {
        Map<Long, String> tags = tagService.pluginTags();
        return DefaultRspDTO.newSuccessInstance(tags);
    }

    /**
     * 获取所有的规则标签
     * @return
     */
    @GetMapping("/ruleTags")
    @Operation(summary = "获取所有的规则标签", description = "获取所有的规则标签")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('TagController','inspection:tag:ruleTags')")
    public DefaultRspDTO<Map<Long, String>> ruleTags() {
        Map<Long, String> tags = tagService.ruleTags();
        return DefaultRspDTO.newSuccessInstance(tags);
    }

}
