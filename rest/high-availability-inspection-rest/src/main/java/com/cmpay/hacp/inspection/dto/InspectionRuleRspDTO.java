package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 巡检插件响应DTO
 */
@Data
@Schema(description = "巡检规则响应DTO")
public class InspectionRuleRspDTO {

    @Schema(description = "规则ID", example = "1001")
    private String ruleId;

    @Schema(description = "规则名称（非空）", example = "CPU 使用率阈值检查")
    private String name;

    @Schema(description = "告警等级：1-低, 2-中, 3-高, 4-严重", example = "1")
    private RuleLevelEnum level;

    @Schema(description = "插件状态：0-启用，1-禁用", example = "0")
    private RuleStatusEnum status;

    @Schema(description = "规则类型：1-指标, 2-日志, 3-可用性", example = "1")
    private RuleType type;

    @Schema(description = "标签列表")
    private List<TagDTO> tags;

    /**
     * 规则插件关联
     */
    @Schema(description = "插件ID", example = "1")
    private String pluginId;

    /**
     * 目标类型：规则目标范围
     * @see RuleAngleViewEnum
     */
    @Schema(description = "功能视角【1-中枢产品(基础设施服务)、2-业务应用(业务功能服务)、3-中间件(支撑服务)】" , example = "1")
    private RuleAngleViewEnum angleView;

    /**
     * @see RuleDeployEnvEnum
     */
    @Schema(description = "部署环境【1-虚拟机(部署在虚拟机上的服务)、2-容器(部署在K8s/Docker环境的服务)】", example = "1")
    private RuleDeployEnvEnum deployEnv;

    @Schema(description = "规则插件参数配置")
    private List<RulePluginParamDTO> pluginParams;

    @Schema(description = "规则执行条件")
    private RulePluginResultDTO pluginResult;

    /**
     * 比req多的字段
     */
    private AuditInfoDTO auditInfo;
}
