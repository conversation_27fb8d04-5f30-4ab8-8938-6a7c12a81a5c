ext {
    versions = [
            mapstruct: '1.6.3',
    ]
}

dependencies {
    implementation project(":service:high-availability-inspection-service")

    implementation 'com.cmpay:lemon-framework-starter-context'
    implementation 'com.cmpay:lemon-framework-starter-session-hazelcast'
    implementation 'com.cmpay:lemon-swagger-starter'

    implementation "org.mapstruct:mapstruct:${versions.mapstruct}"

    annotationProcessor "org.mapstruct:mapstruct-processor:${versions.mapstruct}"
}