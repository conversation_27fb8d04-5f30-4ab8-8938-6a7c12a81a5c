package com.cmpay.hacp.extend.contaiiner.controller;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.extend.container.bo.EmergencyContainerBO;
import com.cmpay.hacp.extend.container.service.EmergencyContainerService;
import com.cmpay.hacp.extend.contaiiner.dto.EmergencyContainerAddReqDTO;
import com.cmpay.hacp.extend.contaiiner.dto.EmergencyContainerPageableReqDTO;
import com.cmpay.hacp.extend.contaiiner.dto.EmergencyContainerUpdateReqDTO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(tags = "容器相关管理")
@RequestMapping("/v1/emergency/container")
@RequiredArgsConstructor
public class ContainerConfigController {
    private final EmergencyContainerService emergencyContainerService;


    /**
     * 新增
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增云平台配置详情", action = "新增")
    @PreAuthorize("hasPermission('ContainerConfigController','container:config:add')")
    public DefaultRspDTO<NoBody> addOrUpdate(@Validated @RequestBody EmergencyContainerAddReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        EmergencyContainerBO convert = BeanConvertUtil.convert(reqDTO, EmergencyContainerBO.class);
        if (JudgeUtils.isEmpty(reqDTO.getCloudClusterList())) {
            BusinessException.throwBusinessException(MsgEnum.PARAM_IS_NULL_ERROR);
        }
        convert.setCloudClusters(JsonUtil.objToStr(convert.getCloudClusterList()));
        emergencyContainerService.add(convert);
        return DefaultRspDTO.newSuccessInstance();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新", notes = "更新")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改云平台配置详情", action = "修改")
    @PreAuthorize("hasPermission('ContainerConfigController','container:config:update')")
    public DefaultRspDTO<NoBody> update(@Validated @RequestBody EmergencyContainerUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        EmergencyContainerBO convert = BeanConvertUtil.convert(reqDTO, EmergencyContainerBO.class);
        if (JudgeUtils.isEmpty(reqDTO.getCloudClusterList())) {
            BusinessException.throwBusinessException(MsgEnum.PARAM_IS_NULL_ERROR);
        }
        convert.setCloudClusters(JsonUtil.objToStr(reqDTO.getCloudClusterList()));
        emergencyContainerService.update(convert);
        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 查询详情
     *
     * @param reqDTO req dto
     * @return 详情
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    @ApiResponse(code = 200, message = "删除")
    @LogRecord(title = "删除云平台配置详情", action = "删除")
    @PreAuthorize("hasPermission('ContainerConfigController','container:config:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody EmergencyContainerUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        emergencyContainerService.delete(BeanConvertUtil.convert(reqDTO, EmergencyContainerBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }

    @GetMapping("/page")
    @ApiOperation(value = "查询分页列表", notes = "查询分页列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('ContainerConfigController','container:config:query')")
    public DefaultRspDTO<PageInfo<EmergencyContainerBO>> getPage(EmergencyContainerPageableReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        PageInfo<EmergencyContainerBO> page = emergencyContainerService.getPage(reqDTO.getPageNum(),reqDTO.getPageSize(),BeanConvertUtil.convert(reqDTO, EmergencyContainerBO.class));
        return DefaultRspDTO.newSuccessInstance(page);
    }

    /**
     * 查询详情
     *
     * @param reqDTO req dto
     * @return 详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", notes = "查询详情")
    @ApiResponse(code = 200, message = "详情")
    @LogRecord(title = "查询云平台配置详情", action = "查询")
    @PreAuthorize("hasPermission('ContainerConfigController','container:config:query')")
    public DefaultRspDTO<EmergencyContainerBO> getInfo(EmergencyContainerUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        EmergencyContainerBO emergencyContainerBO = new EmergencyContainerBO();
        emergencyContainerBO.setWorkspaceId(reqDTO.getWorkspaceId());
        emergencyContainerBO.setContainerId(reqDTO.getContainerId());
        EmergencyContainerBO result = emergencyContainerService.getDetailInfo(emergencyContainerBO);
        if(JudgeUtils.isNotNull(result)){
            result.setPassword(JudgeUtils.isNotBlank(result.getPassword())? CommonConstant.ENCRYPTED_DISPLAY:"");
        }
        return DefaultRspDTO.newSuccessInstance(result);
    }
}

