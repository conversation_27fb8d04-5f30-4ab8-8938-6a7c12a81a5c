package com.cmpay.hacp.extend.contaiiner.dto;

import com.cmpay.hacp.capable.TenantCapable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
public class EmergencyContainerAddReqDTO implements TenantCapable {
    private Integer containerId;

    /**
     * @Fields clientId 客户端id
     */
    @ApiModelProperty(value = "客户端id", required = true, example = "客户端id")
    @NotNull(message = "HAC70084")
    private String clientId;
    /**
     * @Fields clientSecret 密钥
     */
    @ApiModelProperty(value = "客户端密钥", required = true, example = "客户端密钥")
    @NotNull(message = "HAC70085")
    private String clientSecret;
    /**
     * @Fields grantType 授权类型
     */
    @ApiModelProperty(value = "授权类型", required = true, example = "授权类型")
    @NotNull(message = "HAC70086")
    private String grantType;
    /**
     * @Fields username 账号
     */
    @NotNull(message = "HAC22000")
    @ApiModelProperty(value = "账号", required = true, example = "账号")
    private String username;
    /**
     * @Fields password 密码
     */
    @ApiModelProperty(value = "密码", example = "密码")
    private String password;
    /**
     * @Fields rootUrl 端点根地址
     */
    @ApiModelProperty(value = "地址", required = true, example = "地址")
    @NotNull(message = "HAC70087")
    private String rootUrl;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;

    /**
     * @Fields cloudWorkspaceId 业务工作空间
     */
    private String cloudWorkspaceId;

    /**
     * @Fields cloudWorkspaceId 集群列表
     */
    private List<String> cloudClusterList;

    /**
     * 密钥缓存key
     */
    @ApiModelProperty(value = "加密uuid", required = true, example = "加密uuid")
    private String uuid;
}
