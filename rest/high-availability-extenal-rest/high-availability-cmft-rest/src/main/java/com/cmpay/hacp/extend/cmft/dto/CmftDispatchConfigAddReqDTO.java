package com.cmpay.hacp.extend.cmft.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CmftDispatchConfigAddReqDTO {

    /**
     * @Fields externalPublicKey 公钥
     */
    @NotNull(message = "HAC00030")
    private String externalPublicKey;
    /**
     * @Fields externalPrivateKey 私钥
     */
    @NotNull(message = "HAC00031")
    private String externalPrivateKey;
    /**
     * @Fields cmftPublicKey 双活公钥
     */
    @NotNull(message = "HAC00032")
    private String cmftPublicKey;
    /**
     * @Fields projectId 在双活的项目id
     */
    @NotNull(message = "HAC00006")
    private String projectId;

    @NotNull(message = "HAC21008")
    private String uuid;
}
