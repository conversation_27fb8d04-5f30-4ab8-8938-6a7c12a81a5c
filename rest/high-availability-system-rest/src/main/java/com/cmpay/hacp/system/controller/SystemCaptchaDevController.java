package com.cmpay.hacp.system.controller;


import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.hacp.utils.crypto.SM4EncryptorUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Api(tags = "测试验证码管理")
@ConditionalOnProperty(value = "spring.profiles.active", havingValue = "dev")
@RestController
public class SystemCaptchaDevController {

    @Autowired
    private SystemCacheService systemCacheService;
    @Autowired
    private SystemCipherService systemCipherService;

    @Value("${spring.application.name: }")
    private String applicationName;

    /**
     * 生成图片验证码
     *
     * @param request
     * @return
     */
    @ApiOperation("获取图片验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "随机数",
                    name = "captchaReqId",
                    dataType = "java.lang.String",
                    paramType = "query",
                    example = "123456",
                    required = true)
    })
    @GetMapping(value = {"/upms/v1/captcha/getCaptcha", "/v1/sys/captcha/getCaptcha"})
    public DefaultRspDTO<String> captcha(HttpServletRequest request, HttpServletResponse resp, String captchaReqId) {
        //3.将生成的图片验证码存入缓存当中
        systemCacheService.setValue(captchaReqId, "12345", 60, TimeUnit.SECONDS);
        return DefaultRspDTO.newSuccessInstance("12345");
    }

    @GetMapping("/v1/sys/encrypt-password")
    public DefaultRspDTO<String> encryptPassword(String username, String password, String captchaReqId) throws Exception {
        String sm2PublicKey = systemCipherService.getSm2PublicKey(applicationName);

        String encrypt = SM2EncryptorUtil.encrypt(sm2PublicKey, password);
        String sm4RandomSalt = systemCipherService.setSm4RandomSalt(username, captchaReqId);
        String s = SM4EncryptorUtil.encryptEcb(sm4RandomSalt, encrypt);
        return DefaultRspDTO.newSuccessInstance(s);
    }

    public static void main(String[] args) throws Exception {
        String encrypt = SM2EncryptorUtil.encrypt("0440FDC670E2590E238DD451D38BE13FB40FC7C19EB413D0DECE6B51BEED0E0BF6CF53AF77616D9BC0B291B7AD69B5416640905497BDEECCF97D78F2822F61BBE5", "Zt#P*vg3g");
        // String sm4RandomSalt = systemCipherService.setSm4RandomSalt(username, captchaReqId);
        String s = SM4EncryptorUtil.encryptEcb("79b89524c10b027df42095e9168ce867", encrypt);
        System.out.println(s);
    }
}
