package com.cmpay.hacp.system.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemCipherApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.dto.system.CipherKeyInfoRspDTO;
import com.cmpay.hacp.dto.system.CipherLoginKeyRspDTO;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.system.service.SystemCipherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "密钥管理")
@RestController
public class SystemCipherController {


    @Autowired
    protected SystemCipherService systemCipherService;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 查询sm2公钥
     *
     * @return
     */
    @ApiOperation("查询sm2公钥")
    @LogRecord(title = "查询sm2公钥", action = "查询")
    @GetMapping(VersionApi.VERSION_V1 + SystemCipherApi.SM2_PUBLICKEY)
    public GenericRspDTO<CipherKeyInfoRspDTO> getSm2PublicKey() {
        String publicKey = systemCipherService.getSm2PublicKey(applicationName);
        CipherKeyInfoRspDTO cipherKeyInfoRspDTO = new CipherKeyInfoRspDTO();
        cipherKeyInfoRspDTO.setPublicKey(publicKey);
        return GenericRspDTO.newSuccessInstance(cipherKeyInfoRspDTO);
    }


    /**
     * 查询rsa公钥
     *
     * @return
     */
    @ApiOperation("查询rsa公钥")
    @LogRecord(title = "查询rsa公钥", action = "查询")
    @GetMapping(VersionApi.VERSION_V1 + SystemCipherApi.RSA_PUBLICKEY)
    public GenericRspDTO<CipherKeyInfoRspDTO> getRsaPublicKey() {
        String publicKey = systemCipherService.getRsaPublicKey(applicationName);
        CipherKeyInfoRspDTO cipherKeyInfoRspDTO = new CipherKeyInfoRspDTO();
        cipherKeyInfoRspDTO.setPublicKey(publicKey);
        return GenericRspDTO.newSuccessInstance(cipherKeyInfoRspDTO);
    }

    @ApiOperation("sm2与sm4的密钥请求")
    @LogRecord(title = "查询sm2与sm4的密钥请求", action = "查询")
    @GetMapping(VersionApi.VERSION_V1 + SystemCipherApi.KEYS)
    public GenericRspDTO<CipherLoginKeyRspDTO> getLoginKeys(String username, String captchaReqId) {
        String publicKey = systemCipherService.getSm2PublicKey(applicationName);
        String random = systemCipherService.setSm4RandomSalt(username, captchaReqId);
        CipherLoginKeyRspDTO cipherLoginKeyRspDTO = new CipherLoginKeyRspDTO();
        cipherLoginKeyRspDTO.setSm2PublicKey(publicKey);
        cipherLoginKeyRspDTO.setRandomSalt(random);
        return GenericRspDTO.newSuccessInstance(cipherLoginKeyRspDTO);
    }
}
