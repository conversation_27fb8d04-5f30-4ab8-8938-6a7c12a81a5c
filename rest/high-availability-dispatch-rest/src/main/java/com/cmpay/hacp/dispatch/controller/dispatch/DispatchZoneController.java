package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.hacp.dispatch.bo.DispatchZoneBO;
import com.cmpay.hacp.dispatch.controller.dto.*;
import com.cmpay.hacp.dispatch.service.DispatchZoneService;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/05/15 11:14
 * @since 1.0.0
 */
@Api(tags = "机房管理")
@Validated
@RestController
@RequestMapping("/v1/dispatch-zone")
public class DispatchZoneController {

    @Autowired
    private DispatchZoneService dispatchZoneService;

    /**
     * 新增租户
     *
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增机房")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增机房", action = "新增")
    @PreAuthorize("hasPermission('none','dispatch:dispatch-zone:add')")
    public DefaultRspDTO<NoBody> add(@Validated @RequestBody DispatchZoneAddDTO reqDTO) {
        DispatchZoneBO entity = BeanUtils.copyPropertiesReturnDest(new DispatchZoneBO(), reqDTO);
        TenantSecurityUtils.copyTenantSecurity(entity);
        dispatchZoneService.add( entity);
        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 修改
     *
     * @param zoneUpdateDTO 信息
     * @return 成功/失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改机房", action = "修改")
    @PreAuthorize("hasPermission('none','dispatch:dispatch-zone:update')")
    public DefaultRspDTO<NoBody> update(@Validated @RequestBody DispatchZoneUpdateDTO zoneUpdateDTO) {
        DispatchZoneBO entity = BeanUtils.copyPropertiesReturnDest(new DispatchZoneBO(), zoneUpdateDTO);
        TenantSecurityUtils.copyTenantSecurity(entity);
        dispatchZoneService.update(entity);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除
     *
     * @param deleteDTO 信息
     * @return 成功/失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    @LogRecord(title = "删除机房", action = "删除")
    @ApiResponse(code = 200, message = "返回结果")
    @PreAuthorize("hasPermission('none','dispatch:dispatch-zone:delete')")
    public DefaultRspDTO<NoBody> delete(@Validated @RequestBody DispatchZoneDeleteDTO deleteDTO) {
        dispatchZoneService.delete(deleteDTO.getId(),TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance();
    }

    @GetMapping("/info")
    @ApiOperation(value = "获取详细信息", notes = "获取详细信息")
    @ApiResponse(code = 200, message = "获取详细信息")
    @LogRecord(title = "查询机房详情", action = "查询")
    @PreAuthorize("hasPermission('none','dispatch:dispatch-zone:query')")
    public DefaultRspDTO<DispatchZoneDTO> getInfo(@RequestParam("id")String id){
        return DefaultRspDTO.newSuccessInstance(BeanUtils.copyPropertiesReturnDest(new DispatchZoneDTO(), dispatchZoneService.getInfo(id,TenantUtils.getWorkspaceId())));
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表", notes = "分页查询列表")
    @ApiResponse(code = 200, message = "分页列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('none','dispatch:dispatch-zone:query')")
    public DefaultRspDTO<PageInfo<DispatchZoneDTO>> getPageInfo(@Validated @RequestBody DispatchZoneQueryDTO queryDTO){
        DispatchZoneBO dispatchZoneBO = BeanUtils.copyPropertiesReturnDest(new DispatchZoneBO(), queryDTO);
        TenantSecurityUtils.copyWorkspace(dispatchZoneBO);
        PageInfo<DispatchZoneBO> pageInfo = dispatchZoneService.getPageInfo(queryDTO.getPageNum(),queryDTO.getPageSize(), dispatchZoneBO);
        PageInfo<DispatchZoneDTO> result = new PageInfo<>(new ArrayList<>());
        BeanUtils.copyProperties(result, pageInfo);
        if (JudgeUtils.isNotEmpty(pageInfo.getList())) {
            result.setList(BeanConvertUtil.convertList(pageInfo.getList(), DispatchZoneDTO.class));
        }
        return DefaultRspDTO.newSuccessInstance(result);
    }

    @PostMapping("/list")
    @LogNoneRecord
    @ApiOperation(value = "查询列表", notes = "查询列表")
    @ApiResponse(code = 200, message = "列表")
    @PreAuthorize("hasPermission('none','dispatch:dispatch-zone:query')")
    public DefaultRspDTO<List<DispatchZoneDTO>> getList(){
        List<DispatchZoneBO> list = dispatchZoneService.getSimpleList(TenantUtils.getWorkspaceId());
        if (JudgeUtils.isNotEmpty(list)) {
            return DefaultRspDTO.newSuccessInstance(BeanConvertUtil.convertList(list, DispatchZoneDTO.class));
        }
        return DefaultRspDTO.newSuccessInstance(new ArrayList<>());
    }

}
