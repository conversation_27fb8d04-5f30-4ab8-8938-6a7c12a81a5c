package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.hacp.dispatch.controller.dto.DispatchTestPageableReqDTO;
import com.cmpay.hacp.dispatch.controller.dto.DispatchTestReqDTO;
import com.cmpay.hacp.dispatch.bo.DispatchTestBO;
import com.cmpay.hacp.dispatch.service.DispatchTestService;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hafr.agent.dto.TraceResult;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/dispatch-test")
@Api(tags = "调度测试")
public class DispatchTestController {

    @Autowired
    private DispatchTestService dispatchTestService;

    @ApiOperation(value = "add", notes = "添加调度测试", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "新增调度测试", action = "新增")
    @PostMapping("/add")
    @PreAuthorize("hasPermission('DispatchTestController','dispatch:dispatch-test:add')")
    public DefaultRspDTO<NoBody> addDispatchNode(@RequestBody DispatchTestReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        dispatchTestService.addDispatchTest(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "list", notes = "获取调度测试列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/list")
    @PreAuthorize("hasPermission('DispatchTestController','dispatch:dispatch-test:query')")
    public DefaultRspDTO<PageInfo<DispatchTestBO>> getDispatchNodeList(DispatchTestPageableReqDTO reqDTO) {
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(workspaceId -> reqDTO.setWorkspaceId(workspaceId));
        PageInfo<DispatchTestBO> pageInfo = dispatchTestService.getDispatchTestList(reqDTO.getPageNum(), reqDTO.getPageSize(), BeanConvertUtil.convert(reqDTO, DispatchTestBO.class));
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "delete", notes = "删除调度测试", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "删除调度测试", action = "删除")
    @PostMapping("/delete")
    @PreAuthorize("hasPermission('DispatchTestController','dispatch:dispatch-test:delete')")
    public DefaultRspDTO<NoBody> deleteDispatchNodeList(@RequestBody DispatchTestReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        dispatchTestService.deleteDispatchTest(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "get", notes = "获取调度测试", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "查询调度测试详情", action = "查询")
    @GetMapping("/get")
    @PreAuthorize("hasPermission('DispatchTestController','dispatch:dispatch-test:query')")
    public DefaultRspDTO<DispatchTestBO> get(DispatchTestReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        DispatchTestBO dispatchTest = dispatchTestService.getDispatchTest(reqDTO);
        return DefaultRspDTO.newSuccessInstance(dispatchTest);
    }

    @ApiOperation(value = "update", notes = "更新调度测试", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改调度测试", action = "修改")
    @PostMapping("/update")
    @PreAuthorize("hasPermission('DispatchTestController','dispatch:dispatch-test:update')")
    public DefaultRspDTO<NoBody> updateDispatchNode(@RequestBody DispatchTestReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        dispatchTestService.updateDispatchTest(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "call", notes = "执行调度测试", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "执行调度测试", action = "添加")
    @GetMapping("/call")
    @PreAuthorize("hasPermission('DispatchTestController','dispatch:dispatch-test:call')")
    public DefaultRspDTO<TraceResult> call(DispatchTestReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        return dispatchTestService.call(reqDTO);
    }

}