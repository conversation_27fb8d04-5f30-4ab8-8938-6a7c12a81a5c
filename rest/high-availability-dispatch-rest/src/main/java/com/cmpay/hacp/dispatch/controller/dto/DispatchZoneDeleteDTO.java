/*
 * @ClassName TenantDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.dispatch.controller.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "删除")
public class DispatchZoneDeleteDTO {

    /**
     * @Fields tenantId ID
     */
    @ApiModelProperty(value = "ID", required = true, example = "0179035dc433428c84ff434379374157")
    @NotNull(message = "HAC00004")
    private Long id;

}
