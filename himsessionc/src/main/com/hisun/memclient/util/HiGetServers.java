package com.hisun.memclient.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.hisun.hilog4j.Logger;

public class HiGetServers {
	static Logger log;
	public HiGetServers(Logger log) {
		this.log = log;
	}

	public String[] getSerArray(String path) {
		File file = new File(path);
		List<String> servers = new ArrayList<String>();
		try {
			BufferedReader bfr = new BufferedReader(new FileReader(file));
			String line = "";
			while ((line = bfr.readLine()) != null) {
				if (!line.startsWith("#")) {
					servers.add(line.trim());
				}
			}
		} catch (FileNotFoundException e) {
			log.error("servers configure file is null = ", e);
		} catch (IOException e) {
			log.error("servers configure file read fail =", e);
		}
		return servers.toArray(new String[servers.size()]);
	}

	 public static void  main(String[] args) {
		 HiGetServers gc = new HiGetServers(log);
		  
	  }
}
