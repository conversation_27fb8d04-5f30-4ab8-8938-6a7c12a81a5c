package com.hisun.memclient.schooner;

import java.util.Date;
import java.util.Map;

import com.schooner.MemCached.MemcachedItem;

/**
 * java_memcached-release interface
 * <AUTHOR>
 *
 */
public interface IMemcacheClient {
	Object get(String key);   
	  
	MemcachedItem gets(String key);   
  
    boolean add(String key, Object value,Date expiryDate);   
  
    boolean set(String key, Object value,Date expiryDate);   
  
    boolean cas(String key, Object value, long unique);   
       
    boolean delete(String key);
    
    boolean replace(String key, Object value, Date expiryDate) ;
    
    boolean storeCounter(String key, long counter);
    
    public long incr(String key);

    long incr(String key, long inc);
    
    public long decr(String key);
    
    long decr(String key, long inc);
    
    public Map<String, Map<String, String>> stats();
    
    public Map<String, Map<String, String>> stats(String[] servers);
  
    boolean flushAll();   

}
