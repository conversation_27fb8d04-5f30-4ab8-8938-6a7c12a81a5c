package com.hisun.memclient.schooner;

import java.util.Date;
import java.util.Map;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

import com.danga.MemCached.MemCachedClient;
import com.danga.MemCached.SockIOPool;
import com.hisun.hilog4j.Logger;
import com.hisun.memclient.util.HiGetServers;
import com.hisun.util.HiICSProperty;
import com.schooner.MemCached.MemcachedItem;

/**
 * <AUTHOR>
 * 
 */
public class HiSmemcachedClient implements IMemcacheClient {
	
	private MemCachedClient client;
	private SockIOPool pool;
	private String poolName = "default";
	private String path = "/app/conf/memsession.properties";
	private Logger log;
	private String[] svr;
	private String svrStr;
	private Integer[] weights;
	
	private int initConn = 10;
	private int minConn = 10;
	private int maxConn = 100;
	private long maxIdle = 1000 * 60 * 30;
	private long maintSleep = 5000;
	private int socketTO = 3000;
	private int socketConnectTO = 1000*3;
	private boolean aliveCheck = false;
	
	private String encoding="UTF-8";
	private boolean binaryProtocal = true;
	//private long compressThresh = 30720;
	
	public HiSmemcachedClient(Logger log, String path){
		this.log = log;
		this.path = path;
		try {
			loadservers(path);
		} catch (Exception e1) {
			log.error("Load Server Exception:", e1);
			return;
		}
	}
	
	public void builder(){
		log.info("HiSmemcachedClient builder... poolName=["+poolName+"],path=["+svrStr+"],weights=["+weights+"],initConn=["+initConn+"],maxConn=["+maxConn+"],maxIdle=["+maxIdle+
				"],maintSleep=["+maintSleep+"],socketTO=["+socketTO+"],socketConnectTO=["+socketConnectTO+"],aliveCheck=["+aliveCheck+
				"],encoding=["+encoding+"],binaryProtocal=["+binaryProtocal+"]");
		
		if(StringUtils.isBlank(poolName)){
			client = new MemCachedClient(binaryProtocal);
			pool = SockIOPool.getInstance();   
		}else{
			client = new MemCachedClient(poolName,binaryProtocal);
			pool = SockIOPool.getInstance(poolName);   
		}
		client.setDefaultEncoding(encoding);
		//client.setCompressThreshold(compressThresh);
		
        pool.setServers(svr);   
        if(!ArrayUtils.isEmpty(weights)&&ArrayUtils.isSameLength(weights, svr)){
        	pool.setWeights(weights);   
        }
        //设置开始时每个cache服务器的可用连接数
        pool.setInitConn(initConn);   
        //设置每个服务器最少可用连接数 
        pool.setMinConn(minConn);   
        //设置每个服务器最大可用连接数
        pool.setMaxConn(maxConn);
        //设置可用连接池的最长等待时间
        pool.setMaxIdle(maxIdle);
        //设置连接池维护线程的睡眠时间 设置为0，维护线程不启动 
        pool.setMaintSleep(maintSleep);
        //设置是否使用Nagle算法，因为我们的通讯数据量通常都比较大（相对TCP控制数据）而且要求响应及时，因此该值需要设置为false（默认是true）
        pool.setNagle(false);
        //设置socket的读取等待超时值
        pool.setSocketTO(socketTO);
        //设置socket的连接等待超时值
        pool.setSocketConnectTO(socketConnectTO);
        //设置连接心跳监测开关
        pool.setAliveCheck(aliveCheck);
        /**
         * alg=0 使用String.hashCode()获得hash code,该方法依赖JDK，可能和其他客户端不兼容，建议不使用 
		 * alg=1 使用original 兼容hash算法，兼容其他客户端 
		 * alg=2 使用CRC32兼容hash算法，兼容其他客户端，性能优于original算法 
		 * alg=3 使用MD5 hash算法 
         */
        pool.setHashingAlg(SockIOPool.CONSISTENT_HASH);   
        pool.initialize();   

	}
	
	public void loadservers(String pathIn) throws Exception {
		String workdir = HiICSProperty.getWorkDir();
		HiGetServers servers = new HiGetServers(log);
		log.info("HiSmemcachedClient constructor with param path=[" + path
				+ "]");
		if (!StringUtils.isBlank(pathIn)) {
			path = pathIn;
		}
		svr = servers.getSerArray(workdir + path);
		if (null == svr || svr.length==0) {
			throw new Exception("the config servers is empty");
		}
		StringBuilder svrSb = new StringBuilder();
		for(String server:svr){
			svrSb.append(server).append(" ");
		}
		this.svrStr = svrSb.toString();
		log.info("load servers ="+svrSb.toString());
	}

	public boolean add(String key, Object value, Date expiryDate) {
		return client.add(key, value, expiryDate);
		
	}

	public boolean cas(String key, Object value, long unique) {
		return client.cas(key, value, unique);
	}

	public boolean delete(String key) {
		return client.delete(key);
	}
	
	public boolean replace(String key, Object value, Date expiryDate) {
		return client.replace(key, value, expiryDate);
	}

	public boolean flushAll() {
		return false;
	}

	public Object get(String key) {
		return client.get(key);
	}

	public MemcachedItem gets(String key) {
		return client.gets(key);
	}
	
	public boolean storeCounter(String key, long counter) {
		return client.storeCounter(key, counter);
	}

	public boolean set(String key, Object value, Date expiryDate) {
		return client.set(key, value, expiryDate);
	}
	
	public long decr(String key, long inc) {
		return client.decr(key, inc);
	}
	
	public long decr(String key) {
		return client.decr(key);
	}

	public long incr(String key) {
		return client.incr(key);
	}

	public long incr(String key, long inc) {
		return client.incr(key, inc);
	}

	public Map<String, Map<String, String>> stats() {
		return client.stats();
	}

	public Map<String, Map<String, String>> stats(String[] servers) {
		return client.stats(servers);
	}

	/**
	 * @return the client
	 */
	public MemCachedClient getClient() {
		return client;
	}

	public String[] getServers(){
		return pool.getServers();
	}
	
	public String getServersForString(){
		return this.svrStr;
	}
	
	public String getServer(String key){
		return pool.getHost(key);
	}
	/**
	 * @param client the client to set
	 */
	public void setClient(MemCachedClient client) {
		this.client = client;
	}

	/**
	 * @return the poolName
	 */
	public String getPoolName() {
		return poolName;
	}

	/**
	 * @param poolName the poolName to set
	 */
	public void setPoolName(String poolName) {
		this.poolName = poolName;
	}

	/**
	 * @return the path
	 */
	public String getPath() {
		return path;
	}

	/**
	 * @param path the path to set
	 */
	public void setPath(String path) {
		this.path = path;
	}

	/**
	 * @return the log
	 */
	public Logger getLog() {
		return log;
	}

	/**
	 * @param log the log to set
	 */
	public void setLog(Logger log) {
		this.log = log;
	}

	/**
	 * @return the svr
	 */
	public String[] getSvr() {
		return svr;
	}

	/**
	 * @param svr the svr to set
	 */
	public void setSvr(String[] svr) {
		this.svr = svr;
	}

	/**
	 * @return the weights
	 */
	public Integer[] getWeights() {
		return weights;
	}

	/**
	 * @param weights the weights to set
	 */
	public void setWeights(Integer[] weights) {
		this.weights = weights;
	}

	/**
	 * @return the initConn
	 */
	public int getInitConn() {
		return initConn;
	}

	/**
	 * @param initConn the initConn to set
	 */
	public void setInitConn(int initConn) {
		this.initConn = initConn;
	}

	/**
	 * @return the minConn
	 */
	public int getMinConn() {
		return minConn;
	}

	/**
	 * @param minConn the minConn to set
	 */
	public void setMinConn(int minConn) {
		this.minConn = minConn;
	}

	/**
	 * @return the maxConn
	 */
	public int getMaxConn() {
		return maxConn;
	}

	/**
	 * @param maxConn the maxConn to set
	 */
	public void setMaxConn(int maxConn) {
		this.maxConn = maxConn;
	}

	/**
	 * @return the maxIdle
	 */
	public long getMaxIdle() {
		return maxIdle;
	}

	/**
	 * @param maxIdle the maxIdle to set
	 */
	public void setMaxIdle(long maxIdle) {
		this.maxIdle = maxIdle;
	}

	/**
	 * @return the maintSleep
	 */
	public long getMaintSleep() {
		return maintSleep;
	}

	/**
	 * @param maintSleep the maintSleep to set
	 */
	public void setMaintSleep(long maintSleep) {
		this.maintSleep = maintSleep;
	}

	/**
	 * @return the socketTO
	 */
	public int getSocketTO() {
		return socketTO;
	}

	/**
	 * @param socketTO the socketTO to set
	 */
	public void setSocketTO(int socketTO) {
		this.socketTO = socketTO;
	}

	/**
	 * @return the socketConnectTO
	 */
	public int getSocketConnectTO() {
		return socketConnectTO;
	}

	/**
	 * @param socketConnectTO the socketConnectTO to set
	 */
	public void setSocketConnectTO(int socketConnectTO) {
		this.socketConnectTO = socketConnectTO;
	}

	/**
	 * @return the aliveCheck
	 */
	public boolean isAliveCheck() {
		return aliveCheck;
	}

	/**
	 * @param aliveCheck the aliveCheck to set
	 */
	public void setAliveCheck(boolean aliveCheck) {
		this.aliveCheck = aliveCheck;
	}

	/**
	 * @return the encoding
	 */
	public String getEncoding() {
		return encoding;
	}

	/**
	 * @param encoding the encoding to set
	 */
	public void setEncoding(String encoding) {
		this.encoding = encoding;
	}

	/**
	 * @return the binaryProtocal
	 */
	public boolean isBinaryProtocal() {
		return binaryProtocal;
	}

	/**
	 * @param binaryProtocal the binaryProtocal to set
	 */
	public void setBinaryProtocal(boolean binaryProtocal) {
		this.binaryProtocal = binaryProtocal;
	}

}
