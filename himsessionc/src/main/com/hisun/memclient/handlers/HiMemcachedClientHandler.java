package com.hisun.memclient.handlers;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.hisun.exception.HiException;
import com.hisun.framework.event.IServerEventListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.hilog4j.Logger;
import com.hisun.memclient.schooner.HiSmemcachedClient;
import com.hisun.memclient.util.HiSessConstants;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.pubinterface.IHandler;

public class HiMemcachedClientHandler implements IHandler, IServerEventListener {

	private Logger log;

	private HiSmemcachedClient client;

	protected boolean _paused = false;
	protected boolean _running = false;
	
	/**
	 * session 超时时间
	 */
	private int sessTimeOut;
	
	private int initConn = 10;
	private int minConn = 10;
	private int maxConn = 100;
	private long maxIdle = 1000 * 60 * 30;
	private long maintSleep = 5000;
	private int socketTO = 3000;
	private int socketConnectTO = 1000*3;
	private String aliveCheck = "false";
	
	private String encoding="UTF-8";
	private String binaryProtocal = "true";
	
	private String poolName = "default";
	private String serverFile;


	public void serverStart(ServerEvent arg0) throws HiException {
		log.info("serverStart ...");
		_running = true;
		_paused = false;
	}

	public void serverStop(ServerEvent arg0) throws HiException {
		log.debug("serverStop start ... ");
		_running = false;
	}

	public void serverDestroy(ServerEvent arg0) throws HiException {
		serverStop(arg0);
	}

	public void serverPause(ServerEvent serverevent) {
		_paused = true;
	}

	public void serverResume(ServerEvent serverevent) {
		_paused = false;
	}

	public void serverInit(ServerEvent arg0) throws HiException {
		log = arg0.getLog();
		log.info("serverInit... poolName=["+poolName+"],path=["+serverFile+"],initConn=["+initConn+"],maxConn=["+maxConn+"],maxIdle=["+maxIdle+
				"],maintSleep=["+maintSleep+"],socketTO=["+socketTO+"],socketConnectTO=["+socketConnectTO+"],aliveCheck=["+aliveCheck+
				"],encoding=["+encoding+"],binaryProtocal=["+binaryProtocal+"]");
		client = new HiSmemcachedClient(log,serverFile);
		client.setPoolName(poolName);
		client.setPath(serverFile);
		
		client.setInitConn(initConn);
		client.setMaxConn(maxConn);
		client.setMaxIdle(maxIdle);
		client.setMaintSleep(maintSleep);
		client.setSocketTO(socketTO);
		client.setSocketConnectTO(socketConnectTO);
		if(StringUtils.equalsIgnoreCase(aliveCheck, "true")){
			client.setAliveCheck(true);
		}else{
			client.setAliveCheck(false);
		}
		client.setEncoding(encoding);
		if(StringUtils.equalsIgnoreCase(binaryProtocal, "true")){
			client.setBinaryProtocal(true);
		}else{
			client.setBinaryProtocal(false);
		}
		client.builder();
	}

	@SuppressWarnings("unchecked")
	public void process(HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		String key = msg.getHeadItem(HiSessConstants.SESSION_KEY);
		String type = msg.getHeadItem(HiSessConstants.SESSION_TYP);
		Object val = msg.getObjectHeadItem(HiSessConstants.SESSION_OBJ);
		
		String list = client.getServersForString();
		String server = client.getServer(key);;
		log.info("key=[" + key + "]type=[" + type + "]avaliableServers=[" + list  + "] currentServer=[" + server + "]");
		
		Date expiryDate = getExpireDate(sessTimeOut);
		
		if (log.isInfoEnabled()) {
			log.info("PROCESS START KEY:[" + key + "] TYPE:[" + type
					+ "] VAL:[" + val + "] server:[" + server + "]"+ " EXPIRYDATE:["
					+ expiryDate + "]");
		}

		long st = System.currentTimeMillis();
		try {
			if (StringUtils.equalsIgnoreCase(type, "GET")) {
				Object obj = client.get(key);
				msg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);

				if (log.isInfoEnabled()) {
					log.info("KEY:[" + key + "] TYPE:[" + type + "] VALUE:["
							+ obj + "]");
				}

			} else if (StringUtils.equalsIgnoreCase(type, "SET")) {
				client.set(key, val, expiryDate);

			} else if (StringUtils.equalsIgnoreCase(type, "RPL")) {
				client.replace(key, val, expiryDate);
			} else if (StringUtils.equalsIgnoreCase(type, "DEL")) {
				client.delete(key);
			} else if (StringUtils.equalsIgnoreCase(type, "INCR")) {
				long init = ((Long) msg
						.getObjectHeadItem(HiSessConstants.XMEM_INIT))
						.longValue();
				long ret = client.incr(key, ((Long) val).longValue());
				if(ret == -1){
					client.storeCounter(key, init);
					ret = init;
				}
				msg.setHeadItem(HiSessConstants.SESSION_OBJ, ret);

				if (log.isInfoEnabled()) {
					log.info("KEY:[" + key + "] TYPE:[" + type + "] VALUE:["
							+ ret + "]");
				}

			} else if (StringUtils.equalsIgnoreCase(type, "DECR")) {
				long init = ((Long) msg
						.getObjectHeadItem(HiSessConstants.XMEM_INIT))
						.longValue();
				long ret = client.decr(key, ((Long) val).longValue());
				if(ret == -1){
					client.storeCounter(key, init);
					ret = init;
				}

				msg.setHeadItem(HiSessConstants.SESSION_OBJ, ret);

				if (log.isInfoEnabled()) {
					log.info("KEY:[" + key + "] TYPE:[" + type + "] VALUE:["
							+ ret + "]");
				}

			} else if (StringUtils.equalsIgnoreCase(type, "STAT")) {
				Map map = client.stats();
				msg.setHeadItem(HiSessConstants.SESSION_OBJ, map);

			}
			long et = System.currentTimeMillis();
			if (log.isInfoEnabled()) {
				log.info("PROCESS KEY:[" + key + "] TIME:[" + (et - st) + "ms]");
			}

		} catch (Exception e) {
			log.error("", e);
			throw new HiException(e);
		} finally {
			msg.setHeadItem("SCH", "rp");

		}
	}
	
	/**
	 * 获取超时时间间
	 * @param expiryTime 单位：秒
	 * @return
	 */
	public Date getExpireDate(int expiryTime){
		Date curDate = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(curDate);
		calendar.add(Calendar.SECOND, expiryTime);
		return calendar.getTime();
	}

	/**
	 * @return the sessTimeOut
	 */
	public int getSessTimeOut() {
		return sessTimeOut;
	}

	/**
	 * @param sessTimeOut the sessTimeOut to set
	 */
	public void setSessTimeOut(int sessTimeOut) {
		this.sessTimeOut = sessTimeOut;
	}

	/**
	 * @return the initConn
	 */
	public int getInitConn() {
		return initConn;
	}

	/**
	 * @param initConn the initConn to set
	 */
	public void setInitConn(int initConn) {
		this.initConn = initConn;
	}

	/**
	 * @return the minConn
	 */
	public int getMinConn() {
		return minConn;
	}

	/**
	 * @param minConn the minConn to set
	 */
	public void setMinConn(int minConn) {
		this.minConn = minConn;
	}

	/**
	 * @return the maxConn
	 */
	public int getMaxConn() {
		return maxConn;
	}

	/**
	 * @param maxConn the maxConn to set
	 */
	public void setMaxConn(int maxConn) {
		this.maxConn = maxConn;
	}

	/**
	 * @return the maxIdle
	 */
	public long getMaxIdle() {
		return maxIdle;
	}

	/**
	 * @param maxIdle the maxIdle to set
	 */
	public void setMaxIdle(long maxIdle) {
		this.maxIdle = maxIdle;
	}

	/**
	 * @return the maintSleep
	 */
	public long getMaintSleep() {
		return maintSleep;
	}

	/**
	 * @param maintSleep the maintSleep to set
	 */
	public void setMaintSleep(long maintSleep) {
		this.maintSleep = maintSleep;
	}

	/**
	 * @return the socketTO
	 */
	public int getSocketTO() {
		return socketTO;
	}

	/**
	 * @param socketTO the socketTO to set
	 */
	public void setSocketTO(int socketTO) {
		this.socketTO = socketTO;
	}

	/**
	 * @return the socketConnectTO
	 */
	public int getSocketConnectTO() {
		return socketConnectTO;
	}

	/**
	 * @param socketConnectTO the socketConnectTO to set
	 */
	public void setSocketConnectTO(int socketConnectTO) {
		this.socketConnectTO = socketConnectTO;
	}


	/**
	 * @return the encoding
	 */
	public String getEncoding() {
		return encoding;
	}

	/**
	 * @param encoding the encoding to set
	 */
	public void setEncoding(String encoding) {
		this.encoding = encoding;
	}


	/**
	 * @return the poolName
	 */
	public String getPoolName() {
		return poolName;
	}

	/**
	 * @param poolName the poolName to set
	 */
	public void setPoolName(String poolName) {
		this.poolName = poolName;
	}

	/**
	 * @return the serverFile
	 */
	public String getServerFile() {
		return serverFile;
	}

	/**
	 * @param serverFile the serverFile to set
	 */
	public void setServerFile(String serverFile) {
		this.serverFile = serverFile;
	}

	/**
	 * @return the aliveCheck
	 */
	public String getAliveCheck() {
		return aliveCheck;
	}

	/**
	 * @param aliveCheck the aliveCheck to set
	 */
	public void setAliveCheck(String aliveCheck) {
		this.aliveCheck = aliveCheck;
	}

	/**
	 * @return the binaryProtocal
	 */
	public String getBinaryProtocal() {
		return binaryProtocal;
	}

	/**
	 * @param binaryProtocal the binaryProtocal to set
	 */
	public void setBinaryProtocal(String binaryProtocal) {
		this.binaryProtocal = binaryProtocal;
	}


}
