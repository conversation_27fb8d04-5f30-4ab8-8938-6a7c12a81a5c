package com.hisun.sms.cgw.handler;

import java.io.UnsupportedEncodingException;

import com.hisun.common.util.HiByteUtil;
import com.hisun.exception.HiException;
import com.hisun.framework.HiDefaultServer;
import com.hisun.framework.event.IServerInitListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.framework.imp.HiDefaultProcess;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiContext;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.pubinterface.IHandler;
import com.hisun.sms.cgw.HiSMSMenu;
import com.hisun.sms.cgw.HiSMSRuleFileLoader;
import com.hisun.util.HiByteBuffer;

public class HiSMSGetProcHandler implements IHandler, IServerInitListener {

    public final static String PARAM_NAME_SMS_RULE = "__SMS_RULE";

    private HiDefaultServer _server;

    private HiSMSMenu smsMenu = null;
    private String ruleFile;
    private int smsCodLen = 8;

    private String version = "3.0";
    private String strMsgFmt = null;
    private String defaultProcName = "999999";

    final Logger log = (Logger) HiContext.getCurrentContext().getProperty(
            "SVR.log");

    public void process(HiMessageContext ctx) throws HiException {

        HiMessage msg = ctx.getCurrentMsg();
        HiByteBuffer buf = (HiByteBuffer) msg.getBody();
        int cmdId = HiByteUtil.byteArrayToInt(buf.subbyte(0, 4));
        String procName = this.defaultProcName;
        if (cmdId == 5) {
            int registered_Delivery_off = 50
                    + ("3.0".equals(this.version) ? 32 : 21)
                    + ("3.0".equals(this.version) ? 1 : 0);

            int registered_Delivery = buf.charAt(registered_Delivery_off) & 0xFF;
            int Msg_Fmt_off = 49;
            int Msg_Fmt = buf.charAt(Msg_Fmt_off) & 0xFF;

            int TP_udhi = buf.charAt(Msg_Fmt_off - 1) & 0xFF;
            this.log.info("registered_Delivery:[" + registered_Delivery
                    + "],Msg_Fmt:[" + Msg_Fmt + "]");

            if ((registered_Delivery == 0)
                    && ((Msg_Fmt == 0) || (Msg_Fmt == 8) || (Msg_Fmt == 15))) {
                int Dest_Id_off = 16;
                String smsCod = buf.substr(Dest_Id_off, 21);
                int Msg_Length_off = 50
                        + ("3.0".equals(this.version) ? 32 : 21)
                        + ("3.0".equals(this.version) ? 1 : 0) + 1;

                int msg_length = buf.charAt(Msg_Length_off) & 0xFF;
                String smsMsg = null;
                if (Msg_Fmt == 8) {
                    int Tp_head_off = 0;
                    if (TP_udhi == 1) {
                        Tp_head_off = 1 + buf.charAt(Msg_Length_off + 1) & 0xFF;
                    }

                    smsMsg = UCS2DECODE(buf.subbyte(Msg_Length_off + 1
                            + Tp_head_off, msg_length - Tp_head_off));

                    // byte[] bytes = smsMsg.getBytes();
                    //
                    // HiByteBuffer buf2 = new HiByteBuffer(buf.length());
                    // buf2.append(buf.subbyte(0, Msg_Length_off));
                    // buf2.append((byte) bytes.length);
                    // buf2.append(bytes);
                    // buf2.append(buf.subbyte(Msg_Length_off + 1 + msg_length,
                    // buf.length() - (Msg_Length_off + 1 + msg_length)));
                    // msg.setBody(buf2);
                    //
                    // buf = buf2;
                } else {
                    smsMsg = buf.substr(Msg_Length_off + 1, msg_length);
                }

                procName = smsMenu.getProcName(smsCod, smsMsg);
                this.log.info("smsCod:[" + smsCod + "],smsMsg:[" + smsMsg
                        + "],procName:[" + procName + "]");
            }
            if ((this.strMsgFmt != null)
                    && (this.strMsgFmt.indexOf(String.valueOf(Msg_Fmt)) == -1)) {
                procName = this.defaultProcName;
            }
        }

        // 分发到其他处理器
        dispatchProc(ctx, procName);
    }

    private void dispatchProc(HiMessageContext ctx, String procName)
            throws HiException {

        HiMessage msg = ctx.getCurrentMsg();
        HiDefaultProcess process = _server.getProcessByName(procName);
        if (null == process) {
            String errmsg = HiDefaultServer.sm.getString(
                    "HiDefaultServer.process02", msg.getRequestId(), _server
                            .getName(), _server.getType(), procName);
            this.log.error(errmsg);
            throw new HiException("211004", errmsg);
        }
        msg.setHeadItem("SCH", "rq");
        msg.setType(process.getMsgtype());

        process.process(ctx);
    }

    public void serverInit(ServerEvent arg0) throws HiException {

        _server = ((HiDefaultServer) arg0.getServer());

        smsMenu = HiSMSRuleFileLoader.parse(ruleFile);
        smsMenu.setSms_cod_len(this.smsCodLen);
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    private String UCS2DECODE(String srcData) throws HiException {
        if ((srcData == null) || (srcData.length() == 0)
                || (srcData.length() % 2 != 0)) {
            return srcData;
        }

        byte[] bytes = new byte[srcData.length() / 2];
        for (int i = 0; i < srcData.length(); i += 2) {
            bytes[(i / 2)] = (byte) Integer.parseInt(srcData
                    .substring(i, i + 2), 16);
        }

        String reValue = "";
        try {
            reValue = new String(bytes, "UTF-16BE");
        } catch (UnsupportedEncodingException e) {
            throw new HiException(e);
        }
        return reValue;
    }

    private String UCS2DECODE(byte[] srcData) throws HiException {
        if (srcData == null) {
            return null;
        }

        String reValue = "";
        try {
            reValue = new String(srcData, "UTF-16BE");
        } catch (UnsupportedEncodingException e) {
            throw new HiException(e);
        }

        return reValue;
    }

    public String getRuleFile() {
        return ruleFile;
    }

    public void setRuleFile(String ruleFile) {
        this.ruleFile = ruleFile;
    }

    public int getSmsCodLen() {
        return smsCodLen;
    }

    public void setSmsCodLen(int smsCodLen) {
        this.smsCodLen = smsCodLen;
    }

    public String getMsgFmt() {
        return this.strMsgFmt;
    }

    public void setMsgFmt(String strMsgFmt) {
        this.strMsgFmt = strMsgFmt;
    }

    public String getDefaultProcName() {
        return this.defaultProcName;
    }

    public void setDefaultProcName(String defaultProcName) {
        this.defaultProcName = defaultProcName;
    }
}
