<?xml version="1.0" encoding="UTF-8"?>
<project name="hismsrule" basedir=".">
	<description>Builds the Hibank component</description>

    <property name="tomcat.lib" value="D:/apache-tomcat-5.5.27/common/lib">
    </property>
    <property name="libroot" value="G:/workspace/work/motion2">
    </property>
    <property name="pro.lib" value="${libroot}/lib">
    </property>
    <property name="pub.lib" value="${libroot}/applib">
    </property>
	<property name="distdir" value="${basedir}/dist">
	</property>
	<property name="component.dir" value="${distdir}/component">
	</property>
	<property name="classdir" value="${basedir}/dist_class">
	</property>
	<property name="srcbasedir" value="${basedir}/src/main/">
	</property>
	<property name="compile.encode" value="GBK">
	</property>
	<property name="compile.target" value="1.5">
	</property>
    <property name="compile.level" value="line">
    </property>
    <property name="smsrule.lib" value="hismsrule.jar" />

	<target name="init" description="init before distribute all">
		<mkdir dir="${classdir}" />
		<mkdir dir="${distdir}" />
		<delete includeemptydirs="true">
			<fileset dir="${distdir}" includes="**/*" />
            <fileset dir="${classdir}" includes="**/*" />
		</delete>
		<mkdir dir="${component.dir}" />
        <mkdir dir="${component.dir}/lib" />
	</target>

    <target name="distribute">

        <jar destfile="${component.dir}/lib/${smsrule.lib}">
            <fileset dir="${classdir}">
                <include name="com/hisun/sms/cgw/**/*.class" />
                <include name="com/hisun/sms/cgw/**/*.properties" />
                <exclude name="**/*Test.class" />
            </fileset>
            <fileset file="${basedir}/Change.Log"/>
        </jar>

    </target>

	<target name="compile" depends="init">
		<javac srcdir="${srcbasedir}" destdir="${classdir}" excludes="**/test/*.java"  
			fork="true" memoryMaximumSize="256m" encoding="${compile.encode}" 
			target="${compile.target}" debug="on">
			<classpath>
                <fileset dir="${pro.lib}">
                    <include name="*.jar" />
                </fileset>
                <fileset dir="${pub.lib}">
                    <include name="*.jar" />
                </fileset>
                <fileset dir="${basedir}/lib">
                    <include name="*.jar" />
                </fileset>
                <fileset dir="${tomcat.lib}">
                    <include name="*.jar" />
                </fileset>
            </classpath>
		</javac>
		<copy todir="${classdir}">
    		<fileset dir="${srcbasedir}">
    			<include name="**/*.properties" />
    			<exclude name="**/*.java" />
    			<exclude name="**/test/*.*"/>
    		</fileset>
    	</copy>

	</target>

</project>