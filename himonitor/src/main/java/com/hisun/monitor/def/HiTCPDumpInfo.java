package com.hisun.monitor.def;

import com.hisun.message.HiMessage;

public class HiTCPDumpInfo {

	//告警阀值
	private int warnPecent;
	//当前使用线程池名称
	private String threadPoolName;
	//超过阀值的处理
	private String errProccessor;
	//当前线程池队列中消息数
	private String queueCurSize;
	//线程池队列剩余空间
	private String queueAvailable;
	//当前线程池大小
	private String totSize;
	//当前线程池中工作线程数
	private String activeSize;
	//当前交易消息ID
	private String msgId;
	//当前消息
	private HiMessage message;
	
	
}
