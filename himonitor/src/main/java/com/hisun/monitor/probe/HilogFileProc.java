package com.hisun.monitor.probe;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;

import com.hisun.monitor.util.HiMongoDBUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.CommandResult;

public class HilogFileProc {
	private String fileName;

	private String regionId;
	

	private ArrayList list = new ArrayList();
	private HiMongoDBUtil dbu = new HiMongoDBUtil();

	public String getFileName() {
		return fileName;
	}
	
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
	public String getRegionId() {
		return regionId;
	}
	
	public void setRegionId(String regionId) {
		this.regionId = regionId;
	}
	
	public void InsertToDB(String collName) throws Exception {
		for (int i = 0; i < list.size(); i++) {

			HashMap map = (HashMap) list.get(i);
			BasicDBObject dbo = new BasicDBObject(map);
			CommandResult ret = dbu.Insert(collName, dbo);
		}
	}
	
	public void parseLogFile() throws IOException {
		File file = new File(fileName);
		BufferedReader bfr = new BufferedReader(new FileReader(file));
		String tmp = null;
		String msgId = null;
		String ip = null;
		String data = null;
		String type = null;
		String date = null;
		while((tmp = bfr.readLine()) != null) {
			int start = tmp.indexOf("MsgId=[");
			if(start == -1) {
				continue;
			}
			date = tmp.substring(0,start);
			int end = tmp.indexOf("];");
			if(end != -1) {
				msgId = tmp.substring(start + 7, end);
			}
			
			tmp = tmp.substring(end+2);
			type = tmp.substring(0,2);
			end = tmp.indexOf("数据=");
			
			ip = tmp.substring(3,end);
			data = tmp.substring(end+3);
//			String ip = 
			HashMap map = new HashMap();
			map.put("msgId", msgId);
			map.put("ip", ip);
			map.put("date", date);
			map.put("data", data);
			map.put("type", type);
			map.put("regionId", regionId);
			System.out.println(map);
			list.add(map);
		}
		
	}
	
	public static void main(String[] args) {
		HilogFileProc lp = new HilogFileProc();
		try {
			lp.setFileName("E:\\tmp\\CPIGPUE1_REG.trc");
			lp.parseLogFile();
			lp.InsertToDB("TRCINFO");
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	

}
