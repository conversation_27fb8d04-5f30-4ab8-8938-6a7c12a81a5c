package com.hisun.monitor.handler;


import java.util.Date;

import com.hisun.exception.HiException;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.monitor.util.HiMongoDBUtil;
import com.hisun.pubinterface.IHandler;
import com.mongodb.BasicDBObject;

/**
 * @function tcp流量监控,将每一笔从CCA进来的交易记录到mongodb
 * @ 记录字段：请求来源ip，请求业务类型（交易码），请求开始时间，请求报文
 * <AUTHOR>
 *
 */
public class HiTcpQpsHandler implements IHandler{
	private final static String IP ="IP";
	private final static String TXCOD ="TXCOD";
	private final static String STARTTM ="STARTTM";
	private final static String MESS ="MESS";
	private static Long rqTotal = 0L;
	private static Long rqSucc = 0L;
	private static Long rqFail = 0L;
	private String ip;
	private String txCod;
	private String startTm;
	private HiMessage mess;
	private String collection;

	private HiMongoDBUtil dbu;
	public void process(HiMessageContext ctx) throws HiException {
		mess = ctx.getCurrentMsg();
		Logger log = HiLog.getLogger(mess);
		txCod = mess.getHeadItem("STC");
		ip = mess.getHeadItem("SIP");
		String stm = mess.getHeadItem("STM");
		Date srtTm = new Date(stm);
		dbu = new HiMongoDBUtil();
		BasicDBObject dbo = new BasicDBObject();
		dbo.put(IP, ip);
		dbo.put(TXCOD, txCod);
		dbo.put(MESS, mess);
		dbo.put(STARTTM, srtTm);
		try {
			dbu.Insert(collection, dbo);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("excption=[",e,"]");
		} finally {
			if(dbu != null) {
				dbu.close();
			}
		}
		
		
		
		
		
		
	}
	

}
