package com.hisun.rest.ext.parser;

import com.hisun.exception.HiException;
import com.hisun.util.HiICSProperty;
import org.apache.commons.digester.Digester;
import org.dom4j.Document;
import org.dom4j.io.SAXReader;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <?xml version="1.0" encoding="GB18030" ?>
 *
 * <Root application="URMOLN">
 *   <Group algorithm="SM3" algorithmVersion="v1" secureKey="key">
 *      <Item server="OURMPUB1" txCd="010001"/>
 *   </Group>
 * </Root>
 *
 * @author: Dev.Yi.Zeng
 * @date: 2022/5/18
 * @since: 1.0.0
 */
public class HiSignatureConfigParser {

    private static final Map<String, SignatureMetadata> SECURES = new HashMap<String, SignatureMetadata>(8);

    private String filename;

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public boolean isNeedParse() {
        if (SECURES.isEmpty()) {
            return true;
        }
        return false;
    }

    public void parser() throws HiException {
        String file = HiICSProperty.getWorkDir() + File.separator + this.filename;
        InputStream in = null;
        ByteArrayInputStream bais = null;
        try {
            in = new FileInputStream(new File(file));
            Document doc = new SAXReader().read(in);
            String strXML = doc.asXML();
            bais = new ByteArrayInputStream(strXML.getBytes());
            HiSignatureRoot root = (HiSignatureRoot) getParser().parse(bais);
            convertRootToMetadata(root);
        } catch (Exception e) {
            throw new HiException(e);
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            }
            if (null != bais) {
                try {
                    bais.close();
                } catch (IOException e) {
                }
            }
        }
    }

    public static SignatureMetadata getMetadata(String key) {
        return SECURES.get(key);
    }

    private void convertRootToMetadata(HiSignatureRoot root) throws HiException {
        if (null == root) {
            return;
        }

        String application = root.getApplication();
        List<HiSignatureGroup> groups = root.getGroups();
        if (null == groups) {
            return;
        }

        for (int i = 0; i < groups.size(); i++) {
            String algorithm = groups.get(i).getAlgorithm();
            String algorithmVersion = groups.get(i).getAlgorithmVersion();
            String secureKey = groups.get(i).getSecureKey();
            List<HiSignatureItem> items = groups.get(i).getItems();
            if (null == items) {
                continue;
            }

            for (int j = 0; j < items.size(); j++) {
                String server = items.get(j).getServer();
                String txCd = items.get(j).getTxCd();
                if (null == application || null == algorithm
                        || null == algorithmVersion || null == secureKey
                        || null == server || null == txCd) {
                    throw new HiException("application,algorithm,algorithmVersion,secureKey,server,txCd must be not null");
                }

                SignatureMetadata metadata = new SignatureMetadata();
                metadata.setApplication(application);
                metadata.setServer(server);
                metadata.setTxCd(txCd);
                metadata.setAlgorithm(algorithm);
                metadata.setAlgorithmVersion(algorithmVersion);
                metadata.setSecure(secureKey);
                SECURES.put(server + txCd, metadata);
            }
        }
    }


    private static Digester getParser() {
        Digester digester = new Digester();
        digester.setClassLoader(Thread.currentThread().getContextClassLoader());
        digester.setValidating(false);
        digester.addObjectCreate("Root", "com.hisun.rest.ext.parser.HiSignatureRoot");
        digester.addSetProperties("Root");

        digester.addObjectCreate("*/Group", "com.hisun.rest.ext.parser.HiSignatureGroup");
        digester.addSetProperties("*/Group");
        digester.addSetNext("*/Group", "addGroup", "com.hisun.rest.ext.parser.HiSignatureGroup");

        digester.addObjectCreate("*/Item", "com.hisun.rest.ext.parser.HiSignatureItem");
        digester.addSetProperties("*/Item");
        digester.addSetNext("*/Item", "addItem", "com.hisun.rest.ext.parser.HiSignatureItem");

        return digester;
    }



    public static class SignatureMetadata {
        private String application;
        private String secure;
        private String algorithm;
        private String algorithmVersion;
        private String server;
        private String txCd;

        public String getApplication() {
            return application;
        }

        public void setApplication(String application) {
            this.application = application;
        }

        public String getSecure() {
            return secure;
        }

        public void setSecure(String secure) {
            this.secure = secure;
        }

        public String getAlgorithm() {
            return algorithm;
        }

        public void setAlgorithm(String algorithm) {
            this.algorithm = algorithm;
        }

        public String getAlgorithmVersion() {
            return algorithmVersion;
        }

        public void setAlgorithmVersion(String algorithmVersion) {
            this.algorithmVersion = algorithmVersion;
        }

        public String getServer() {
            return server;
        }

        public void setServer(String server) {
            this.server = server;
        }

        public String getTxCd() {
            return txCd;
        }

        public void setTxCd(String txCd) {
            this.txCd = txCd;
        }
    }
}
