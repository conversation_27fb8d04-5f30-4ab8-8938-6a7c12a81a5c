package com.hisun.msession.handlers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import net.rubyeye.xmemcached.CASOperation;
import net.rubyeye.xmemcached.GetsResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.hisun.database.HiDataBaseUtil;
import com.hisun.exception.HiException;
import com.hisun.framework.event.IServerEventListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.msession.HiSessConstants;
import com.hisun.pubinterface.IHandler;
import com.hisun.xmem.HiXmemcachedClient;
import com.hisun.xmem.cas.HiCasOperation;

public class HiMemcachedHandler implements IHandler, IServerEventListener {

	private Logger log;

	private ConcurrentHashMap<String, AtomicInteger> map = new ConcurrentHashMap<String, AtomicInteger>();

	private HiXmemcachedClient client;

	private String serverFile;

	private int poolSize;

	private long connTimeOut = 5 * 1000L;

	private long timeOut = 5 * 1000L;

	private long sessTimeOut = 1800L;

	private int maxFailureNum = 5;

	private int casMaxTries = 0;

	private int autoFlag = 0; // -- 自动断开服务连接标志 0: 关闭自动 1：打开自动

	private String commandFactory;
	private String sessionLocator;
	
	private String _checkData = "stats";
	private int failTms = 5;

	public int getFailTms() {
		return failTms;
	}

	public void setFailTms(int failTms) {
		this.failTms = failTms;
	}

	protected boolean _paused = false;

	public String get_checkData() {
		return _checkData;
	}

	public void set_checkData(String checkData) {
		_checkData = checkData;
	}

	// --
	private String dbName = "";
	private String strSql = "";

	/*
	 * private static AtomicLong setcount = new AtomicLong(); private static
	 * AtomicLong getcount = new AtomicLong();
	 */
	protected boolean _running = false;
	// protected Thread _mngThd = null;
	protected int _checkItv = 30;

	protected int[] _port;
	protected int _checkTim = 1;

	public int get_checkItv() {
		return _checkItv;
	}

	public void set_checkItv(int checkItv) {
		_checkItv = checkItv;
	}

	public int get_checkTim() {
		return _checkTim;
	}

	public void set_checkTim(int checkTim) {
		_checkTim = checkTim;
	}
	
	public void setSessionLocator(String sessionLocator){
		this.sessionLocator = sessionLocator;
	}
	
	public void setCommandFactory(String commandFactory){
		this.commandFactory = commandFactory;
	}

	/*
	 * class HiMemManager implements Runnable { private int itv = 0;
	 * 
	 * public void run() { while (true) { try { if (Thread.interrupted()) break;
	 * if (!_running) break; Thread.sleep(_checkTim * 1000);
	 * log.debug("HiMemManager before durun ..."); doRun(); } catch
	 * (InterruptedException e1) { ; } catch (ConnectException e2) {
	 * 
	 * } catch (Throwable e) { if (_running) log.error(e, e); //
	 * closeConnection(_socket); } }
	 * 
	 * }
	 * 
	 * public void doRun() throws IOException, HiException {
	 * log.debug("HiMemManager durun ..."); itv++; if (itv == _checkItv) { itv =
	 * 0; client.stats(failTms);
	 * 
	 * } } }
	 */

	public void serverStart(ServerEvent arg0) throws HiException {
		log.info("serverStart ...");
		_running = true;
		_paused = false;
		// -- 心跳
		/*
		 * mngThd = new Thread(new HiMemManager(), "Manager:[" + client.getSvr()
		 * + "]"); _mngThd.start();
		 */
	}

	public void serverStop(ServerEvent arg0) throws HiException {
		log.debug("serverStop start ... ");
		client.close();
		_running = false;

		/*
		 * if (_mngThd != null) { _mngThd.interrupt(); try { _mngThd.join(5 *
		 * 1000); } catch (InterruptedException e) { } _mngThd = null; }
		 * log.debug(_mngThd + " stopped");
		 */
	}

	public void serverDestroy(ServerEvent arg0) throws HiException {
		serverStop(arg0);
	}

	public void serverPause(ServerEvent serverevent) {
		_paused = true;
	}

	public void serverResume(ServerEvent serverevent) {
		_paused = false;
	}

	public void serverInit(ServerEvent arg0) throws HiException {
		log = arg0.getLog();
		log.info("serverInit");
		client = new HiXmemcachedClient(log, serverFile);
		client.setSize(poolSize);
		client.setConnTimOut(connTimeOut);
		client.setOpTimOut(timeOut);
		client.setSessTimOut(sessTimeOut);
		client.setPath(serverFile);
		client.setLogger(log);
		
		client.setCommandFactory(this.commandFactory);
		client.setSessionLocator(this.sessionLocator);

		client.builder();

	}

	public void process(HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		String key = msg.getHeadItem(HiSessConstants.SESSION_KEY);
		String type = msg.getHeadItem(HiSessConstants.SESSION_TYP);
		Object val = msg.getObjectHeadItem(HiSessConstants.SESSION_OBJ);

		// --
		ArrayList list = client.getAvaliableServers();
		AtomicInteger failureNum = new AtomicInteger();
		String server = client.getServer(key);;
		log.info("key=[" + key + "]type=[" + type + "]avaliableServers=[" + list  + "] currentServer=[" + server + "]");
//		log.info("type=" + type);
		if (!StringUtils.equalsIgnoreCase(type, "STAT")) {
			server = client.getServer(key);
			if (StringUtils.isEmpty(server)) {
				if (log.isErrorEnabled()) {
					log.error("server is null.key=[" + key + "]");
				}

				// --
				// --TODO

			} else {
				failureNum = (AtomicInteger) map.get(server);
				if (failureNum == null) {
					failureNum = new AtomicInteger();
					map.put(server, failureNum);
				}
			}
		}
		if (log.isInfoEnabled()) {
			log.info("PROCESS START KEY:[" + key + "] TYPE:[" + type
					+ "] VAL:[" + val + "] server:[" + server + "]");
		}

		long st = System.currentTimeMillis();
		try {
			if (StringUtils.equalsIgnoreCase(type, "GET")) {
				Object obj = client.get(key, (int) timeOut);
				msg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);

				// --long count = getcount.addAndGet(1);

				if (log.isInfoEnabled()) {
					log.info("KEY:[" + key + "] TYPE:[" + type + "] VALUE:["
							+ obj + "]");
				}

				/*
				 * if (log.isDebugEnabled()) { log.debug("getcount=[ " + count +
				 * "]"); }
				 */
			} else if (StringUtils.equalsIgnoreCase(type, "SET")) {
				client.set(key, (int) sessTimeOut, val);

				/*
				 * long count = setcount.addAndGet(1); if (log.isDebugEnabled())
				 * { log.debug("setcount=[ " + count + "]"); }
				 */
			} else if (StringUtils.equalsIgnoreCase(type, "RPL")) {
				client.replace(key, (int) sessTimeOut, val);
			} else if (StringUtils.equalsIgnoreCase(type, "DEL")) {
				client.delete(key);
			} else if (StringUtils.equalsIgnoreCase(type, "INCR")) {
				long init = ((Long) msg
						.getObjectHeadItem(HiSessConstants.XMEM_INIT))
						.longValue();
				long ret = client.incr(key, ((Long) val).longValue(), init,
						(int) timeOut, (int) sessTimeOut);

				msg.setHeadItem(HiSessConstants.SESSION_OBJ, ret);

				// --long count = getcount.addAndGet(1);

				if (log.isInfoEnabled()) {
					log.info("KEY:[" + key + "] TYPE:[" + type + "] VALUE:["
							+ ret + "]");
				}

			} else if (StringUtils.equalsIgnoreCase(type, "DECR")) {
				long init = ((Long) msg
						.getObjectHeadItem(HiSessConstants.XMEM_INIT))
						.longValue();
				long ret = client.decr(key, ((Long) val).longValue(), init,
						(int) timeOut, (int) sessTimeOut);

				msg.setHeadItem(HiSessConstants.SESSION_OBJ, ret);

				// --long count = getcount.addAndGet(1);

				if (log.isInfoEnabled()) {
					log.info("KEY:[" + key + "] TYPE:[" + type + "] VALUE:["
							+ ret + "]");
				}

			} else if (StringUtils.equalsIgnoreCase(type, "STAT")) {
				HashMap map = client.stats();
				msg.setHeadItem(HiSessConstants.SESSION_OBJ, map);

			}else if (StringUtils.equalsIgnoreCase(type, "CAS")){
				GetsResponse<Object> getsRsp = client.gets(key, (int) timeOut);
				HiCasOperation casOper = (HiCasOperation) msg
				.getObjectHeadItem(HiSessConstants.XMEM_CASDATA);
				if(casOper == null ){
					throw new HiException("HiCasOperation is null");
				}
				casOper.setMaxTries(this.casMaxTries);
				client.cas(key, (int) sessTimeOut, getsRsp, casOper);
				msg.addHeadItem(HiSessConstants.XMEM_CASDATA, null);
			}
			long et = System.currentTimeMillis();
			if (log.isInfoEnabled()) {
				log.info("PROCESS KEY:[" + key + "] TIME:[" + (et - st) + "ms]");
			}

			if (failureNum.get() > 0) {
				failureNum.decrementAndGet();
			}
		} catch (HiException e) {
			if (autoFlag == 1) {
				// -- FAILOVER PROCESS
				// String server = client.getServer(key);
				int currentNum = failureNum.addAndGet(1);
				log.warn("server:[" + server + "] failure:[" + currentNum + "]");
				if (currentNum > maxFailureNum) {
					client.removeServer(server);
					failureNum.set(0);
				}
				map.put(server, failureNum);
			}
			log.error("", e);
			throw e;
		} finally {
			msg.setHeadItem("SCH", "rp");

		}
	}

	// -- 增加server
	public void addServer(HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		String server = msg.getHeadItem(HiSessConstants.SESSION_SVR);
		if (log.isInfoEnabled()) {
			log.info("add server [" + server + "]");
		}
		if (StringUtils.isEmpty(server)) {
			throw new HiException("SERVER IS EMPTY!");
		}

		String tmp[] = StringUtils.split(server, ":");
		String host = tmp[0];
		int port = NumberUtils.toInt(tmp[1]);
		client.addServer(host, port);
	}

	// -- 删除server
	public void removeServer(HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		Logger log = HiLog.getLogger(msg);
		String server = msg.getHeadItem(HiSessConstants.SESSION_SVR);
		if (log.isInfoEnabled()) {
			log.info("remove server [" + server + "]");
		}
		if (StringUtils.isEmpty(server)) {
			throw new HiException("SERVER IS EMPTY!");
		}

		String tmp[] = StringUtils.split(server, ":");
		String host = tmp[0];
		int port = NumberUtils.toInt(tmp[1]);
		client.removeServer(host, port);
	}

	/**
	 * 支持从数据库获取数据 如果缓存中没有数据，从数据库中获取
	 */
	public void processWithDB(HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		// -- 1. 先查询缓存
		String key = msg.getHeadItem(HiSessConstants.SESSION_KEY);
		Object obj = client.get(key,
				NumberUtils.toInt(String.valueOf(sessTimeOut)));

		// -- 2. 缓存没有，查询数据库
		if (obj == null) {
			HiDataBaseUtil dbUtil = new HiDataBaseUtil();
			if (!StringUtils.isEmpty(dbName)) {
				dbUtil.setDsName(dbName);
			}

			HashMap map = dbUtil.readRecord(strSql, key);
			// -- 3. 将数据库的数据放入缓存
			if (map == null) {
				return;
			}

			StringBuffer buffer = new StringBuffer();
			for (Iterator it = map.keySet().iterator(); it.hasNext();) {
				String key0 = (String) it.next();
				Object val = map.get(key0);
				buffer.append(val);
				buffer.append("|");
			}
			client.set(key, (int) sessTimeOut, buffer.toString());
		}

		msg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);
	}

	public void processGetsAndCas(HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		String key = msg.getHeadItem(HiSessConstants.SESSION_KEY);
		String type = msg.getHeadItem(HiSessConstants.SESSION_TYP);
		Object val = msg.getObjectHeadItem(HiSessConstants.SESSION_OBJ);

		// --
		AtomicInteger failureNum = new AtomicInteger();
		String server = client.getServer(key);
		if (StringUtils.isEmpty(server)) {
			if (log.isErrorEnabled()) {
				log.error("server is null.key=[" + key + "]");
			}

			// -- TODO 是否需要重新连接
		} else {
			failureNum = (AtomicInteger) map.get(server);
			if (failureNum == null) {
				failureNum = new AtomicInteger();
				map.put(server, failureNum);
			}
		}

		long st = System.currentTimeMillis();
		try {
			if (StringUtils.equalsIgnoreCase(type, "GETS")) {
				if (log.isInfoEnabled()) {
					log.info("PROCESS START KEY:[" + key + "] TYPE:[" + type
							+ "] server:[" + server + "]");
				}

				GetsResponse<Object> obj = client.gets(key, (int) timeOut);
				msg.setHeadItem(HiSessConstants.XMEM_GETSRSP, obj);
				if (null != obj) {
					msg.setHeadItem(HiSessConstants.SESSION_OBJ, obj.getValue());
				}
				// long count = getscount.addAndGet(1);

				if (log.isInfoEnabled()) {
					log.info("KEY:[" + key + "] TYPE:[" + type + "] VALUE:["
							+ obj + "]");
				}

				// if (log.isDebugEnabled()) {
				// log.debug("getscount=[ " + count + "]");
				// }
			} else if (StringUtils.equalsIgnoreCase(type, "CAS")) {

				if (log.isInfoEnabled()) {
					log.info("PROCESS START KEY:[" + key + "] TYPE:[" + type
							+ "] server:[" + server + "]");
				}

				GetsResponse getsRsp = (GetsResponse) msg
						.getObjectHeadItem(HiSessConstants.XMEM_GETSRSP);
				HiCasOperation casOper = (HiCasOperation) msg
						.getObjectHeadItem(HiSessConstants.XMEM_CASDATA);
				if (null == casOper) {
					throw new HiException("CAS operation is null!");
				}
				casOper.setMaxTries(this.casMaxTries);

				if (null == getsRsp) {
					client.cas(key, (int) sessTimeOut, casOper);
				} else {
					GetsResponse newGetsRsp = new GetsResponse(
							getsRsp.getCas(), val);
					client.cas(key, (int) sessTimeOut, newGetsRsp, casOper);
				}

				// long count = cascount.addAndGet(1);
				// if (log.isDebugEnabled()) {
				// log.debug("cascount=[ " + count + "]");
				// }

			} else {
				process(ctx);
				return;
			}
			long et = System.currentTimeMillis();
			if (log.isInfoEnabled()) {
				log.info("PROCESS KEY:[" + key + "] TIME:[" + (et - st) + "ms]");
			}

			if (failureNum.get() > 0) {
				failureNum.decrementAndGet();
			}
		} catch (HiException e) {
			if (autoFlag == 1) {
				// -- FAILOVER PROCESS
				// String server = client.getServer(key);
				int currentNum = failureNum.addAndGet(1);
				log.warn("server:[" + server + "] failure:[" + currentNum + "]");
				if (currentNum > maxFailureNum) {
					client.removeServer(server);
					failureNum.set(0);
				}
				map.put(server, failureNum);
			}
			if (e.getMessage() != null && e.getMessage().indexOf("CAS") != 0) {
				log.error(e.getMessage());
			} else {
				log.error("", e);
			}
			throw e;
		} finally {
			msg.setHeadItem(HiMessage.REQUEST_RESPONSE, HiMessage.TYPE_RESPONSE);
		}
	}

	// public void serverDestroy(ServerEvent arg0) throws HiException {
	// // --
	// log.debug("serverDestroy!");
	// client.close();
	// }
	//
	// public void serverStop(ServerEvent arg0) throws HiException {
	// log.debug("server stop");
	// client.close();
	// }

	public String getServerFile() {
		return serverFile;
	}

	public void setServerFile(String serverFile) {
		this.serverFile = serverFile;
	}

	public int getPoolSize() {
		return poolSize;
	}

	public void setPoolSize(int poolSize) {
		this.poolSize = poolSize;
	}

	public long getTimeOut() {
		return timeOut;
	}

	public void setTimeOut(long timeOut) {
		this.timeOut = timeOut;
	}

	public long getSessTimeOut() {
		return sessTimeOut;
	}

	public void setSessTimeOut(long sessTimeOut) {
		this.sessTimeOut = sessTimeOut;
	}

	public long getConnTimeOut() {
		return connTimeOut;
	}

	public void setConnTimeOut(long connTimeOut) {
		this.connTimeOut = connTimeOut;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

	public String getStrSql() {
		return strSql;
	}

	public void setStrSql(String strSql) {
		this.strSql = strSql;
	}

	public int getMaxFailureNum() {
		return maxFailureNum;
	}

	public void setMaxFailureNum(int maxFailureNum) {
		this.maxFailureNum = maxFailureNum;
	}

	public int getAutoFlag() {
		return autoFlag;
	}

	public void setAutoFlag(int autoFlag) {
		this.autoFlag = autoFlag;
	}

	public int getCasMaxTries() {
		return casMaxTries;
	}

	public void setCasMaxTries(int casMaxTries) {
		this.casMaxTries = casMaxTries;
	}
}
