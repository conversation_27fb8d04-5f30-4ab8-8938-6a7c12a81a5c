package com.hisun.msession;

public class HiSessConstants {
     public  final static String ENABLE_FLAG = "1";
     public final static String DISABLE_FLAG = "0";
     public final static String SESS_FLAG = "_SESS_FLAG";
     public final static String MODIFIED_FLAG = "1";
     public final static String MODIFIED_FLAG_NO = "0";

     public final static String SESSION_STAT = "SESS_STAT";


     //--
//     public final static String SESSION_KEY = "SESSION_KEY";
//     
//     public final static String SESSION_TYP = "SESSION_TYP";
//     
//     public final static String SESSION_OBJ = "SESSION_OBJ";
//     
//     public final static String SESSION_SVR = "SESSION_SVR";
     public final static String SESSION_KEY = "SKY";
     
     public final static String SESSION_TYP = "STY";
     
     public final static String SESSION_OBJ = "SOB";
     
     public final static String SESSION_SVR = "SSV";

     public final static String SESSION_MSG_TYP = "BIN";
     
     public final static String SESSION_SVRNM = "S.MEMSVR";

     public final static String XMEM_GETSRSP = "XGR";
     public final static String XMEM_CASDATA = "XCD";
     public final static String XMEM_INIT = "XIN";
     public final static String CAS_MAX_TRIES = "CASMAXTRIES";
     
     public final static String XMEM_CASSETDATA = "XCSD";
     public final static String XMEM_CASSETREMOVEDATA = "XCSRD";
     public final static String XMEM_CASREMOVEOBJ = "XCRDO";
}

