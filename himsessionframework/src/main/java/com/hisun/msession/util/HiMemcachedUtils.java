package com.hisun.msession.util;

import net.rubyeye.xmemcached.CASOperation;

import com.hisun.dispatcher.HiRouterOut;
import com.hisun.exception.HiException;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiContext;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.msession.HiSessConstants;

public class HiMemcachedUtils {

	private static Logger log = HiLog.getLogger("HIMEMCACHED.trc");

	public static void setToMem(String key, Object obj, String server)
			throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "SET");
		newMsg.setHeadItem("SCH", "rq");

		if (log.isDebugEnabled()) {
			log.debug("sid = [" + key + "]Set Process starting... ");
		}
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key + "] hictx is null msgId =["
						+ hictx.getId() + "]");
			}
		} else {

		}
		// HiMessageContext.setCurrentContext(ctx);
		newMsg = HiRouterOut.syncProcess(newMsg);
	}

	public static void casToMem(String key, CASOperation casOprData,
			Object obj, String server) throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);
		newMsg.setHeadItem(HiSessConstants.XMEM_CASDATA, casOprData);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "CAS");
		newMsg.setHeadItem("SCH", "rq");

		if (log.isDebugEnabled()) {
			log.debug("sid = [" + key + "]Set Process starting... ");
		}
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key + "] hictx is null");
			}
		} else {

		}
		// HiMessageContext.setCurrentContext(ctx);
		newMsg = HiRouterOut.syncProcess(newMsg);
	}
	
	
	public static void casToMem(String key, 
			Object obj, String server) throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "CAS");
		newMsg.setHeadItem("SCH", "rq");
		
		if (log.isDebugEnabled()) {
			log.debug("sid = [" + key + "]casToMem Process starting... ");
		}
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key + "] hictx is null");
			}
		} else {
			
		}
		// HiMessageContext.setCurrentContext(ctx);
		newMsg = HiRouterOut.syncProcess(newMsg);
	}
	
	public static void casRemoveToMem(String key, 
			Object obj, String server) throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "CASRMV");
		newMsg.setHeadItem("SCH", "rq");
		
		if (log.isDebugEnabled()) {
			log.debug("sid = [" + key + "]casRemoveToMem Process starting... ");
		}
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key + "] hictx is null");
			}
		} else {
			
		}
		// HiMessageContext.setCurrentContext(ctx);
		newMsg = HiRouterOut.syncProcess(newMsg);
	}

	public static Object getFromMem(String key, String server)
			throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "GET");
		newMsg.setHeadItem("SCH", "rq");

		if (log.isDebugEnabled()) {
			log.debug("get Process starting...");
		}
		// HiMessageContext ctx = new HiMessageContext();

		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key
						+ "] getFromMem hictx is null msgId =["
						+ ctx.getCurrentMsg().getRequestId() + "]");
			}
			HiMessageContext.setCurrentContext(ctx);
		} else {

		}
		newMsg = HiRouterOut.syncProcess(newMsg);

		Object res = newMsg.getObjectHeadItem(HiSessConstants.SESSION_OBJ);
		return res;
	}

	public static Object getsFromMem(String key, String server)
			throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "GETS");
		newMsg.setHeadItem("SCH", "rq");

		if (log.isDebugEnabled()) {
			log.debug("get Process starting...");
		}
		// HiMessageContext ctx = new HiMessageContext();

		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key
						+ "] getFromMem hictx is null msgId =["
						+ ctx.getCurrentMsg().getRequestId() + "]");
			}
			HiMessageContext.setCurrentContext(ctx);
		} else {

		}
		newMsg = HiRouterOut.syncProcess(newMsg);

		Object res = newMsg.getObjectHeadItem(HiSessConstants.SESSION_OBJ);
		return res;
	}

	public static void replaceMem(String key, Object obj, String server)
			throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_OBJ, obj);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "RPL");
		newMsg.setHeadItem("SCH", "rq");

		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
		} else {

		}
		newMsg = HiRouterOut.syncProcess(newMsg);
	}

	public static void deleteMem(String key, String server) throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "DEL");
		newMsg.setHeadItem("SCH", "rq");

		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
		} else {

		}
		newMsg = HiRouterOut.syncProcess(newMsg);
	}

	public static long incrToMem(String key, long increment, long initValue,
			String server) throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_OBJ, increment);
		newMsg.setHeadItem(HiSessConstants.XMEM_INIT, initValue);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "INCR");
		newMsg.setHeadItem("SCH", "rq");

		if (log.isDebugEnabled()) {
			log.debug("sid = [" + key + "]Incr Process starting... ");
		}
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key + "] hictx is null");
			}
		} else {

		}
		// HiMessageContext.setCurrentContext(ctx);
		newMsg = HiRouterOut.syncProcess(newMsg);

		long res = ((Long) newMsg
				.getObjectHeadItem(HiSessConstants.SESSION_OBJ)).longValue();
		return res;
	}

	public static long decrToMem(String key, long increment, long initValue,
			String server) throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_KEY, key);
		newMsg.setHeadItem(HiSessConstants.SESSION_OBJ, increment);
		newMsg.setHeadItem(HiSessConstants.XMEM_INIT, initValue);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "DECR");
		newMsg.setHeadItem("SCH", "rq");

		if (log.isDebugEnabled()) {
			log.debug("sid = [" + key + "]Decr Process starting... ");
		}
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
			if (log.isDebugEnabled()) {
				log.debug("sid = [" + key + "] hictx is null");
			}
		} else {

		}
		// HiMessageContext.setCurrentContext(ctx);
		newMsg = HiRouterOut.syncProcess(newMsg);

		long res = ((Long) newMsg
				.getObjectHeadItem(HiSessConstants.SESSION_OBJ)).longValue();
		return res;
	}

	public static Object statsMem(String server) throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_TYP, "STAT");
		newMsg.setHeadItem("SCH", "rq");
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
		} else {

		}
		newMsg = HiRouterOut.syncProcess(newMsg);
		Object res = newMsg.getObjectHeadItem(HiSessConstants.SESSION_OBJ);
		return res;
	}

	public static void mngServer(String memServer, String server, String msgType)
			throws HiException {
		HiMessage newMsg = new HiMessage(server, "DEFAULT");
		newMsg.setType(msgType);
		newMsg.setHeadItem("SDT", server);
		newMsg.setHeadItem(HiSessConstants.SESSION_SVR, memServer);
		newMsg.setHeadItem("SCH", "rq");

		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
		} else {

		}
		newMsg = HiRouterOut.syncProcess(newMsg);
	}
}
