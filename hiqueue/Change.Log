修改日期      修改人  BUGID     修改文件名                              修改原因
20150306    yuzhou           IHAlgorithm,RoundRobin,HiHaEndpoint   ArrayList改为CopyOnWriteArrayList
20150306    yuzhou           HiQueueEndpoint                       Daemon异常时捕获
20150306    yuzhou           HiServerConsumer                      订阅发布根据订阅者判断是否发布和选择队列，订阅发布由查DB改为参数服务
20150414    yuzhou           HiMongoAtc HiResendMongoMsgHandler    消息重发及打印前一天U P 状态的消息
20150414    yuzhou           HiResendMongoMsgHandler ProducerHandler 
20150414    yuzhou           ConsumerListener HiHAEndpoint HiQueueEndpoint 
20150414    yuzhou           HiServerConsumer ServerRecieveListener rabbitmq 新增username,pwd,虚拟主机 及 代码优化
20151012    yuzhou           HIQueueATC  ProducerHandler 			文件已作废，删除
20160307    yuzhou           重构及新增confirm、return机制,新增出现producer出现异常时存文件，轮询重发
20160601    yuzhou           DBManager，HiRabbitResendMongoMsgHandler,AbstractServerConsumer,NormalServerConsumer,TopicServerConsumer,HiMongoAtc mongodb从2.6升级到3.2，重发机制优化
20160701    yuzhou           HiRabbitResendMongoMsgHandler，BatchResendMsgGetFromMongo，IBatchResendMsg，com/hisun/queue/listener/consumer       代码结构优化
20170721    deng_wj          DBManager 添加密码解密
20171026    deng_wj          TopicServerConsumer 添加lsn
20180108    deng_wj          TopicServerConsumer *Serial*
20180605    fu_yx            HiRabbitUtils                          消费队列通过服务化找到对应消费节点,mq服务端消费捕获异常重新消费
                             HiParaConstants
20180920    fu_yx            HiParaConstants.java                   灰度消息队列消费根据版本转发至灰度队列
                             HiRabbitUtils.java
                             BatchResendMsgGetFromMongo.java
                             AbstractServerConsumer.java
                             ConsumerMessage.java
                             HiRabbitConsumerListener.java
                             HiRabbitServerConsumer.java
                             HiRabbitServerListener.java
20190816    fu_yx            ConsumerMessage.java                  灰度标签修改