apply plugin: 'java'
apply from: '../common.gradle'

//version = '1.0.0-SNAPSHOT'
version = '1.0.0.RELEASE'

sourceCompatibility = 1.7
targetCompatibility = 1.7

dependencies {
    compile 'com.rabbitmq:amqp-client:3.2.1'
    compile 'commons-collections:commons-collections:3.2.1'
    compile 'dom4j:dom4j:1.6.1'
    compile 'commons-lang:commons-lang:2.3'
    compile 'org.mongodb:mongo-java-driver:3.2.2'
    compile 'org.apache.commons:commons-compress:1.14'
    compile 'commons-codec:commons-codec:1.10'

    compile 'com.hisun:hiparam:1.0.0.RELEASE'
    compile 'com.hisun:hicommon:1.0.0.RELEASE'
    compile 'com.hisun:hidbutil:1.0.0.RELEASE'
    compile 'com.hisun:hidispatcher:1.0.0.RELEASE'
    compile 'com.hisun:hiframework:1.0.0.RELEASE'
    compile 'com.hisun:hijson:1.0.0.RELEASE'
    compile 'com.hisun:hiparse:1.0.0.RELEASE'
    compile 'com.hisun:hiconstant:1.0.0.RELEASE'
    compile 'com.hisun:hipubatcext:1.0.0.RELEASE'
    compile 'com.hisun:hiutil:1.0.0.RELEASE'
    compile 'com.hisun:hiservice:1.0.0.RELEASE'
    compile 'com.hisun:hipubatc:1.0.0.RELEASE'
}
