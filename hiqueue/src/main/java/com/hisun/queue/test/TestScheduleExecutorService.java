package com.hisun.queue.test;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import com.hisun.rabbit.support.NamedThreadFactory;

public class TestScheduleExecutorService {
	public static void main(String[] args) {
		 RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();
	        String name = runtime.getName();
	        System.out.println(Integer.parseInt(name.substring(0, name.indexOf("@"))));
		Runnable thread0 = new Runnable(){

			@Override
			public void run() {
				for(int i = 0; i<5; i++){
					System.out.print(".");
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
					}
				}
				System.out.println();
			}
			
		};
		
		ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(1, new NamedThreadFactory("hiusn-queue-readFileResender", true));
		ScheduledFuture<?> scheduledFuture = scheduledExecutor.scheduleWithFixedDelay(
				thread0, 10000, 15000, TimeUnit.MILLISECONDS);
		
		try {
			Thread.sleep(10000000);
		} catch (InterruptedException e) {
		}
	}
}
