package com.hisun.queue.test;

import java.io.IOException;

import com.hisun.rabbit.file.IFileWriter;
import com.hisun.rabbit.file.MsgStore;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;

public class TestFileWriter {
public static void main(String[] args) {
	/*IFileWriter fileWriter = new BufferedFileWriter(HiRabbitUtils.getRabbitFileDir()+File.separator+"rabmsg.dat",HiRabbitUtils.RABBIT_MSG_FILE_SIZE);
	TestFileWriter t = new TestFileWriter();
	Thread t1 = new Thread(t.getChangeFileName(fileWriter));
	t1.start();
	Thread t2 = new Thread(t.getWriter(fileWriter,"logqueue_send"));
	t2.start();
	Thread t3 = new Thread(t.getWriter(fileWriter,"mirror.queue_send_urmoln"));
	t3.start();
	Thread t4 = new Thread(t.getWriter(fileWriter,"mirror.queue_send_ppdoln"));
	t4.start();
	Thread t5 = new Thread(t.getWriter(fileWriter,"mirror.queue_send_ppdbat"));
	t5.start();
	Thread t6 = new Thread(t.getWriter(fileWriter,"mirror.queue_send_urmbat"));
	t6.start();
	for(int i =0 ;i<10;i++){
		Thread t7 = new Thread(t.getWriter(fileWriter,"mirror.queue_send_urm"+i));
		t7.start();
	}*/
	
	Logger log = HiLog.getLogger("Testttt.trc");
	log.info("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa");
	log.info("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaab");
	log.info("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaac");
	log.close();
	
}


public String getMsg(int i){
		StringBuilder sb = new StringBuilder();
		sb.append("SIDS.PRODUCERURMOLN065000000000248	STPPLTIN0	SSZ000000000164	STSN	STCSUBER	RTPSUB	RTDSUBER	ECTtext/etf	RIDURMOLN_REGION_URMOLN_S.PRODUCERURMOLN065000000000248_10000015	ONDURMOLN	STM1457346412708	SDTS.PRODUCER	SRTOURMPUB1|URMOLN	SMFY	RTM20160307182652	RABBITMQ==AC_DT: |MSG_ID:S.PRODUCERURMOLN065000000000248|RTP:SUB|BUS_TYP: |TX_TYP: |REG_ID:URMOLN_REGION|RID:URMOLN_REGION_URMOLN_S.PRODUCERURMOLN065000000000248_10000015|RTD:SUBER|SMF:Y|TX_DT:20470606|RTC:SUBER|TX_CD:0011563|TX_TM:182642|SYS_CNL: |OBJ_NOD_ID:URMOLN|BUS_CNL: |JRN_NO: |NOD_ID:URMOLN==RABBITMQ	STF1	SCHrq	RTCSUBER	TID0011563	SDNURMOLN").append('\n')
				.append("<ROOT><GWA><TX_DT>20470606</TX_DT><GGG>").append(i).append("</GGG><TX_CD>0011563</TX_CD><TX_TM>182642</TX_TM></GWA><CURRENT_IP_CITY>长沙</CURRENT_IP_CITY><MBL_NO>15034181727</MBL_NO><SMS_CD/></ROOT>");
		return sb.toString();
}

public Runnable getWriter(final IFileWriter fileWriter,final String queue){
	return new Runnable(){
		int i = 0;
		int j = 0;
		@Override
		public void run() {
			System.out.println("thread "+queue+" started.");
			while(true){
				if(i>10000000){
					System.out.println("thread "+queue+" finished.");
					break;
				}
				try {
					/*try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}*/
					MsgStore ms = new MsgStore(queue, getMsg(i));
					fileWriter.write(ms);
				} catch (IOException e) {
					e.printStackTrace();
				}
				i++;
				j++;
				if(j %2==0){
					j=0;
				}
			}
		}
		
	};
}

public Runnable getChangeFileName(final IFileWriter fileWriter){
	return new Runnable(){
		int i =0;
		@Override
		public void run() {
			System.out.println("change file thread started.");
			while(true){
				try {
					if(i>10){
						System.out.println("change file thread finished.");
						break;
					}
					Thread.sleep(1000*20);
					fileWriter.changeFile();
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				i++;
			}
		}
		
	};
}
}
