package com.hisun.queue.handler.support;

/**
 * msg resend interface
 * <AUTHOR>
 *
 */
public interface IBatchResendMsg {
	
	/**
	 * 开始当前批次的重发消息
	 */
	public void resend();
	
	/**
	 * 终止当前批次的重发消息
	 */
	public void termination();
	
	/**
	 * 一条消息的最大发送次数
	 * @param maxSendTimes
	 */
	public void setMaxResendTimes(int maxResendTimes) ;
	
	/**
	 * 一条消息下一批次重发时间间隔
	 * @param sendIntv
	 */
	public void setResendIntv(int resendIntv);
	
	/**
	 * 每批次重发消息数量
	 * @param queryLimit
	 */
	public void setBatchLimit(int batchLimit);
}
