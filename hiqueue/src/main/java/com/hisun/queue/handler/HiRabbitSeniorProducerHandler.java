package com.hisun.queue.handler;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import com.hisun.manage.password.HiPasswordUtils;
import org.apache.commons.lang.StringUtils;

import com.hisun.exception.HiException;
import com.hisun.framework.HiDefaultServer;
import com.hisun.framework.event.IServerEventListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.pubinterface.IHandler;
import com.hisun.queue.constants.HiParaConstants;
import com.hisun.queue.constants.HiRabbitUtils;
import com.hisun.rabbit.confirm.CorrelationData;
import com.hisun.rabbit.confirm.CorrelationDataIdGen;
import com.hisun.rabbit.connection.CachingConnectionFactory;
import com.hisun.rabbit.core.Message;
import com.hisun.rabbit.core.RabbitAdmin;
import com.hisun.rabbit.core.RabbitTemplate.ConfirmCallback;
import com.hisun.rabbit.file.BufferedFileReader;
import com.hisun.rabbit.file.BufferedFileWriter;
import com.hisun.rabbit.file.BytesFileReader;
import com.hisun.rabbit.file.BytesFileWriter;
import com.hisun.rabbit.file.IFileReader;
import com.hisun.rabbit.file.IFileWriter;
import com.hisun.rabbit.file.MsgProcessCallBack;
import com.hisun.rabbit.monitor.PerformStats;
import com.hisun.rabbit.sender.BlockingQueueSenderContainer;
import com.hisun.rabbit.support.NamedThreadFactory;

/**
 * rabbit mq 客户端生产者
 * 客户端不支持负载均衡，支持rabbitmq集群
 * 支持confirm 机制
 * 
 * forceChangeFileItv 强制修改文件名的时间间隔，修改文件名后，FileReader才可以处理该文件
 * <AUTHOR>
 *
 */
public class HiRabbitSeniorProducerHandler implements IHandler, IServerEventListener{
	private static final Logger logger = HiLog.getLogger("rabbitMonitor.trc");
	private static final Logger fileResend = HiLog.getLogger("rabbitFileResend.lst");
	private static final Logger confirmMonitor = HiLog.getLogger("rabbitConfirmMonitor.lst");
	private static final Logger nask = HiLog.getLogger("rabbitNask.lst");
	
	private String producerServers; // rabbitmq server列表
	private String userName;
	private String password;
	private String virtualHost;
	private String producerQueue;// 发送队列名
	private int heartBeat;//心跳间隔
	private int channelCacheSize;	//channel cache size
	private int connectionTimeOut;//连接超时时间

	private Logger log = null;	//服务日志
	private HiDefaultServer server = null;
	private CachingConnectionFactory connectionFactory= null;
	private RabbitAdmin rabbitAdmin = null;
	private boolean running = false;
	
	private static final int DEFATUL_HEART_BEAT = 5;
	private static final int DEFATUL_CONNECTION_TIMEOUT = 5*1000;
	
	private String directToRabbitMq = "N";//是否直接发送到rabbitmq，N：否，Y ：是 。默认：N
	
	private String fileName;//当系统出现异常时，append msg 到此文件
	private int limitFileSize = HiRabbitUtils.RABBIT_MSG_FILE_SIZE;
	private int blockingQueueCapacity = HiRabbitUtils.RABBIT_SENDER_QUEUE_CAPACITY;
	private IFileWriter fileWriter = null;	//操作写文件
	private IFileReader fileReader = null;	//操作读文件，发送消息
	private BlockingQueueSenderContainer senderContainer = null;	//消息发送者容器
	
	private long scheduledPeriod = 1000*60*1;//调度消息重发的时间间隔
	private ScheduledFuture<?> scheduledFuture = null;
	private final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(1, new NamedThreadFactory("hiusn-queue-readFileResender", true));
	private int forceChangeFileItv = HiRabbitUtils.RABBIT_MSG_FILE_FORCE_CHANGE_NAME_INTERVAL;//强制修改文件名时间间隔；默认20分钟,单位秒
	//private int maxFileReader = 1;	//fileReader线程最大并发数,scheduledExecutor不会产生并发，所以目前这个参数不起作用
	private int overTimeForTest = 3*1000;//测试发送多久为超时
	private String isTest="Y";//文件处理之前是否需要测试rabbitmq，Y是，N否
	private int fileMode = 1;//1,byte;2,String ； 文件处理模式，字节，字符形式;目前只支持1
	
	private int isLogFileResend = 1;	//是否打印fileResend日志，压力测试可以关闭;1打开；其他值：关闭；
	
	private List<String> producerQueues = null;	//生产者对列缓存
	
	private ConcurrentMap<String, String> pendingConfirms = new ConcurrentHashMap<String, String>();
	
	private int age = 20000;	//unconfirm age
	
	public void serverInit(ServerEvent event) throws HiException {
		log = event.getLog();
		if (log.isInfoEnabled()) {
			log.info("server initializing " + this.toString());
		}
		server = (HiDefaultServer) event.getServer();
		if(StringUtils.isBlank(this.producerServers)){
			throw new HiException("Parameter error:sendServers is null");
		}
		if(this.getHeartBeat()<=0){
			heartBeat = DEFATUL_HEART_BEAT;
		}
		if(channelCacheSize <=0){
			channelCacheSize = 1;
		}
		if(this.connectionTimeOut <= 0){
			this.connectionTimeOut = DEFATUL_CONNECTION_TIMEOUT;
		}
		//rabbitmq 出现问题时，消息保存在fileName文件中
		if(StringUtils.isBlank(fileName)){
			fileName = HiRabbitUtils.RABBIT_MSG_FILE_NAME;
		}

		password = HiPasswordUtils.decrypt(password);
		connectionFactory = new CachingConnectionFactory(producerServers,userName,password,virtualHost);
		connectionFactory.setRequestedHeartBeat(heartBeat);
		connectionFactory.setConnectionTimeout(connectionTimeOut);
		connectionFactory.setChannelCacheSize(channelCacheSize);
		connectionFactory.setServerName(server.getName());
		connectionFactory.setPublisherConfirms(true);//confirm 

        log.info("sendServers["+producerServers+"],userName["+userName+"],password["+ HiPasswordUtils.desensitization(password) +"],virtualHost["+virtualHost+"],heartBeat["+heartBeat+
                "],connectionTimeOut["+connectionTimeOut+"],channelCacheSize["+channelCacheSize+"],isTest["+isTest
                +"],overTimeForTest["+overTimeForTest+"ms],directToRabbitMq["+directToRabbitMq+"],fileMode["+fileMode
                +"]，forceChangeFileItv["+forceChangeFileItv+"s],scheduledPeriod["+scheduledPeriod+"ms]");

		rabbitAdmin = new RabbitAdmin(connectionFactory);
		rabbitAdmin.getRabbitTemplate().setConfirmCallback(this.createConfirmCallback());//confirm callback
		rabbitAdmin.setAutoStartup(true);
		rabbitAdmin.setQueueNames(this.parseAndWarpProducerQueues(this.getProducerQueue()));
		rabbitAdmin.setServerName(server.getName());
		rabbitAdmin.afterPropertiesSet();//添加监听
		rabbitAdmin.validate(log);
		
	}

	public void serverStart(ServerEvent event) throws HiException {
		//创建写文件对象
		createFileWriter();
		//创建并启动异步消息发送容器
		senderContainer = new BlockingQueueSenderContainer(rabbitAdmin.getRabbitTemplate(),fileWriter,this.getBlockingQueueCapacity());
		senderContainer.setConcurrentSenders(1); //最小并发线程数
		senderContainer.setMaxConcurrentSenders(this.channelCacheSize);//最大并发线程数等于channel size
		senderContainer.setPendingConfirms(pendingConfirms);	//confirm
		senderContainer.start();
		//创建读文件重发消息对象
		createFileReader();
		//调度 读文件重发消息
		this.scheduledFuture = scheduledExecutor.scheduleWithFixedDelay(
					fileReader, scheduledPeriod, scheduledPeriod, TimeUnit.MILLISECONDS);
		
		running = true;
		//调度性能监控
		PerformStats.scheduledDump();
		//启动检查unconfirmed msg 线程
		startGetUnconfirmedThread();
	}

	public void serverStop(ServerEvent event) throws HiException {
		running = false;
		//关闭线程池
		try {
			scheduledExecutor.shutdown();
		} catch (Exception e) {
		}
		//停止调度
		if(scheduledFuture != null){
			try {
				scheduledFuture.cancel(false);
			} catch (Throwable t) {
				log.warn(t.getMessage(), t);
			}
			scheduledFuture = null;
		}
		//停止Reader向container 的queue 发送数据
		fileReader.shutdown();//此时FileReader线程会将消息重新登记到新的文件
		//stop container；停止向container发送数据
		if(null != senderContainer){
			try{
				senderContainer.stop();
			}catch(Exception e){	log.error("stoping container occur err.",e);}
		}
		
		//关闭资源(物理关闭)
		if(connectionFactory != null){
			try{
				connectionFactory.destroy();//关闭channel and connection 
				connectionFactory.clearConnectionListeners();//清理监听
				connectionFactory.clearChannelListeners();
				connectionFactory = null;
			}catch(Exception e){	log.error("closing resourse occur err.",e);}
		}
		
		//尽量等待处理文件线程结束
		fileReader.awaitTerminal(90000, TimeUnit.MILLISECONDS);//可能导致重启服务时间较长
		
		//关闭文件句柄
		try {
			fileWriter.close();
		} catch (IOException e) {
		}
		//关闭调度性能统计
		PerformStats.cancelDump();
	}

	public void serverDestroy(ServerEvent event) throws HiException {
		if(running){
			running = false;
			serverStop(event);
		}
	}

	public void serverPause(ServerEvent event) {
	}

	public void serverResume(ServerEvent event) {
	}

	public void process(HiMessageContext ctx) throws HiException {
		HiMessage m = ctx.getCurrentMsg();
		
		final HiMessage newMsg = m.cloneNoBody();
		newMsg.setHeadItem("RABBITMQ==", HiRabbitUtils.getRabHead(m) + "==RABBITMQ");
		newMsg.setBody(m.getBody());

		//原消息改变消息头返回
		m.delHeadItem("SCH");
		m.addHeadItem("SCH", "rp");
		ctx.setCurrentMsg(m);

		// 获取发送队列名称
		String sendQueueName = HiRabbitUtils.getWarpSendQueue(
				newMsg.getHeadItem(HiParaConstants.RAB_OBJSVR),
				newMsg.getHeadItem(HiParaConstants.RAB_OBJTXN));
		
		if (log.isInfoEnabled()) {
			log.info("routingKey ["+sendQueueName+"], producerMsg [ " + newMsg + " ]");
		}
		
		try{
			if(StringUtils.equalsIgnoreCase("N", directToRabbitMq)){
				sendMsg(sendQueueName,newMsg.toString());
			}else{
				this.rabbitAdmin.getRabbitTemplate().send(
						sendQueueName,
						new Message(newMsg.toString().getBytes(), HiRabbitUtils
								.getTextPlainMessageProperties(sendQueueName)));
			}
		}catch(Exception e){
			if(log.isErrorEnabled()){
				log.error("Unexpected err occured at send msg to rabbitmq. directToRabbitMq="+directToRabbitMq,e);
			}
			throw new HiException(e);
		}
	}
	
	/**
	 * sending msg to blockingQueueContainer
	 * write msg to file immediate if no space for queue
	 * @param queueName
	 * @param msg
	 */
	public void sendMsg(String queueName,String msg){
		senderContainer.send(queueName, msg);
	}
	
	/**
	 * sending msg to blockingQueueContainer
	 * writing msg to file if necessary for waiting up to the specified wait time but no space to become available.
	 * @param queueName
	 * @param msg
	 * @param timeout
	 * @param unit
	 */
	public void sendMsg(String queueName,String msg,long timeout, TimeUnit unit){
		senderContainer.send(queueName, msg, timeout, unit);
	}
	
	/**
	 * sending msg to blockingQueueContainer
	 * waiting if necessary for space to become available.
	 * @param queueName
	 * @param msg
	 */
	public void sendMsgBolckedIfNecessary(String queueName,String msg){
		senderContainer.sendBlockedIfNecessary(queueName, msg);
	}
	
	/**
	 * sending test msg to rabbitmq
	 * @return
	 * @throws HiException
	 */
	public boolean testRabbitMq() throws HiException{
		long startTm = System.currentTimeMillis();
		String routingKey = HiRabbitUtils.getWarpSendQueue("Test", "Test");
		String correlationDataId = CorrelationDataIdGen.getCorrelationDataId();
		rabbitAdmin.getRabbitTemplate().send(routingKey, 
				new Message(HiRabbitUtils.createTestMsgForRabbit().getBytes(),
						HiRabbitUtils.getTextPlainMessageProperties(routingKey)),
						new CorrelationData(correlationDataId));
		this.pendingConfirms.put(correlationDataId, "");
		long execTm = System.currentTimeMillis() - startTm;
		if(execTm > overTimeForTest){
			logger.warn("Send testing message overtime. execTm ="+execTm);
			return false;
		}
		return true;
	}
	
	private void createFileWriter() throws HiException{
		if(this.getFileMode() == 1){	//type
			fileWriter = new BytesFileWriter(HiRabbitUtils.getRabbitFileDir()+File.separator+this.getFileName(),this.limitFileSize);
		}else if(this.getFileMode() == 2 ){//String
			fileWriter = new BufferedFileWriter(HiRabbitUtils.getRabbitFileDir()+File.separator+this.getFileName(),this.limitFileSize);
		}else{
			throw new HiException("Not support file process mode ="+this.getFileMode());
		}
		fileWriter.setForceChangeFileItv(forceChangeFileItv);
	}
	
	private void createFileReader() throws HiException{
		MsgProcessCallBack callBack = new MsgProcessCallBack(){
			@Override
			public void doMsgProcess(String routingKey, String msg) {
				if(isLogFileResend == 1 && fileResend.isInfoEnabled()){
					fileResend.info("sendQueue["+routingKey+"],msg["+msg+"]");
				}
				sendMsgBolckedIfNecessary(routingKey,msg);
			}

			@Override
			public boolean doTest() throws HiException{
				if("N".equalsIgnoreCase(isTest)){	//不需要测试，直接返回true
					return true;
				}
				return testRabbitMq();
			}

			@Override
			public boolean check(String routingKey, String msg) {
				if(producerQueues.contains(routingKey)){
					return true;
				}
				return false;
			}
		};
		
		if(this.getFileMode() == 1){//byte
			fileReader = new BytesFileReader(callBack,fileWriter);
		}else if(this.getFileMode() ==2){//String
			fileReader = new BufferedFileReader(callBack,fileWriter);
		}else{
			throw new HiException("Not support file process mode ="+this.getFileMode());
		}
		fileReader.setForceChangeFileItv(forceChangeFileItv);
	}
	
	/**
	 * 将输入的producerQueue解析and warp
	 * @param inputQueueNames
	 * @return
	 */
	private String[] parseAndWarpProducerQueues(String inputQueueNames){
		String [] tempQues = StringUtils.split(inputQueueNames, "|");
		List<String> ques = new ArrayList<String>();
		for(String que : tempQues){
			que = HiRabbitUtils.getWarpQueueName(que);
			ques.add(que); 
		}
		producerQueues = ques;//缓存对列，check用
		return ques.toArray(new String[ques.size()]);
	}
	
	/**
	 * create confirm callback
	 * @return
	 */
	private ConfirmCallback createConfirmCallback(){
		return new ConfirmCallback(){
			@Override
			public void confirm(CorrelationData correlationData, boolean ack,
					String cause) {
				String msg = pendingConfirms.remove(correlationData.getId());
				if(!ack){ //nask
					if(nask.isWarnEnabled()){
						nask.warn("NASK|"+correlationData.getId()+"|"+cause+"|"+msg);
					}
				}else{//ask test
					if(nask.isInfoEnabled()){
						nask.info("AASK|"+correlationData.getId());
					}
				}
			}
		};
	}
	
	private void startGetUnconfirmedThread(){
		
		Thread thread = new Thread(new Runnable(){

			@Override
			public void run() {
				while(true){
					if(! running){
						break;
					}
					
					try{
						doRun();
					}catch(Throwable t){
						if(confirmMonitor.isErrorEnabled()){
							confirmMonitor.error("doRun occur err ,",t);
						}
					}
					
					try {
						Thread.sleep(30000);
					} catch (InterruptedException e) {
					}
				}
			}
			
		});
		thread.start();
	}
	
	private void doRun(){
		Collection<CorrelationData> unconfirms = this.rabbitAdmin.getRabbitTemplate().getUnconfirmed(age);
		if(unconfirms == null){
			if(confirmMonitor.isInfoEnabled()){
				confirmMonitor.info("unconfirm collections is null.");
			}
			return;
		}
		for(CorrelationData cd : unconfirms){
			this.pendingConfirms.remove(cd.getId());
			if(nask.isWarnEnabled()){
				nask.warn("UNCONFIRM|"+cd.getId());
			}
		}
		if(confirmMonitor.isInfoEnabled()){
			confirmMonitor.info("pendingConfirms size "+pendingConfirms.size());
		}
	}
	
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getVirtualHost() {
		return virtualHost;
	}

	public void setVirtualHost(String virtualHost) {
		this.virtualHost = virtualHost;
	}

	public HiDefaultServer getServer() {
		return server;
	}

	public void setServer(HiDefaultServer server) {
		this.server = server;
	}

	public boolean isRunning() {
		return running;
	}

	public void setRunning(boolean running) {
		this.running = running;
	}

	public int getHeartBeat() {
		return heartBeat;
	}

	public void setHeartBeat(int heartBeat) {
		this.heartBeat = heartBeat;
	}

	public int getChannelCacheSize() {
		return channelCacheSize;
	}

	public void setChannelCacheSize(int channelCacheSize) {
		this.channelCacheSize = channelCacheSize;
	}

	public int getConnectionTimeOut() {
		return connectionTimeOut;
	}

	public void setConnectionTimeOut(int connectionTimeOut) {
		this.connectionTimeOut = connectionTimeOut;
	}

	public String getProducerServers() {
		return producerServers;
	}

	public void setProducerServers(String producerServers) {
		this.producerServers = producerServers;
	}

	public String getProducerQueue() {
		return producerQueue;
	}

	public void setProducerQueue(String producerQueue) {
		this.producerQueue = producerQueue;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public int getBlockingQueueCapacity() {
		return blockingQueueCapacity;
	}

	public void setBlockingQueueCapacity(int blockingQueueCapacity) {
		this.blockingQueueCapacity = blockingQueueCapacity;
	}
	
	public int getLimitFileSize() {
		return limitFileSize;
	}

	public void setLimitFileSize(int limitFileSize) {
		this.limitFileSize = limitFileSize;
	}

	public long getScheduledPeriod() {
		return scheduledPeriod;
	}

	public void setScheduledPeriod(long scheduledPeriod) {
		this.scheduledPeriod = scheduledPeriod;
	}

	public int getForceChangeFileItv() {
		return forceChangeFileItv;
	}
	/**
	 * 如果没有可以处理的文件，强制将目前的lst文件改名为可以处理的文件
	 * 此时间间隔为lst文件生成多久后可以改名，单位是s
	 * @param forceChangeFileItv
	 */
	public void setForceChangeFileItv(int forceChangeFileItv) {
		this.forceChangeFileItv = forceChangeFileItv;
	}

	public int getOverTimeForTest() {
		return overTimeForTest;
	}

	/**
	 * 测试超时时间
	 * @param overTimeForTest
	 */
	public void setOverTimeForTest(int overTimeForTest) {
		this.overTimeForTest = overTimeForTest;
	}

	public String getIsTest() {
		return isTest;
	}

	public void setIsTest(String isTest) {
		this.isTest = isTest;
	}

	public String getDirectToRabbitMq() {
		return directToRabbitMq;
	}

	public void setDirectToRabbitMq(String directToRabbitMq) {
		this.directToRabbitMq = directToRabbitMq;
	}

	public int getFileMode() {
		return fileMode;
	}

	public void setFileMode(int fileMode) {
		this.fileMode = fileMode;
	}

	public int getIsLogFileResend() {
		return isLogFileResend;
	}

	public void setIsLogFileResend(int isLogFileResend) {
		this.isLogFileResend = isLogFileResend;
	}
	
}
