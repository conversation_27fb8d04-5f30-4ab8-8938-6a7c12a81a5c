package com.hisun.queue.common;

import java.util.List;

public class RoundRobin implements IHAIgorithm {
	
	private int[] server;
	private int cw = 0;  //当前调度权值
	private int max; //最大权值
	private int gcd; //最大公约数
	private int number = -1; //当前服务NO
	//private ArrayList<Integer> isOpen;
	
	public RoundRobin(int[] server) {
		this.server = server;
		//this.isOpen = isOpen;
		max = getMaxWeight(server);
		gcd = gcd(server);
	}

	/**
	 * 获取请求的SERVER序号
	 * 
	 * @return
	 */
	public Integer next(List<Integer> isOpen) {

		while (true) {
			//检查服务器列表open标志,如果全部关闭，则直接返回-1
			boolean flag = false;
			for (int i = 0;i < isOpen.size(); i++) {  
		        if(isOpen.get(i)==1){
		        	flag = true;
		        	break;
		        }
		    } 
			if(!flag){
				return -1;
			}
			number = (number + 1) % server.length;
			// System.out.println("number=" + number);
			// System.out.println("cw=" + cw);
			if (number == 0) {
				cw = cw - gcd;// cw比较因子，从最大权重开始，以最大公约数为步长递减
				if (cw <= 0) {//
					cw = max;
					if (cw == 0)
						return null;
				}
			}
			if ((server[number] >= cw) && (isOpen.get(number)== 1) )
				return number;
		}

	}
	
	/**
	 * 求最大公约数
	 * 
	 * @param array
	 * @return
	 */
	public int gcd(int[] ary) {

		int min = ary[0];

		for (int i = 0; i < ary.length; i++) {
			if (ary[i] < min) {
				min = ary[i];
			}
		}
		while (min >= 1) {
			boolean isCommon = true;
			for (int i = 0; i < ary.length; i++) {
				if (ary[i] % min != 0) {
					isCommon = false;
					break;
				}
			}
			if (isCommon) {
				break;
			}
			min--;
		}
		// System.out.println("gcd=" + min);
		return min;
	}

	/**
	 * 求最大值，权重
	 * 
	 * @return
	 */

	public int getMaxWeight(int[] ary) {
		int max = 0;
		for (int i = 0; i < ary.length; i++) {
			if (max < ary[i]) {
				max = ary[i];
			}
		}
		return max;
	}

}
