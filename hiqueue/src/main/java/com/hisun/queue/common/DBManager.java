package com.hisun.queue.common;
import com.hisun.common.util.CrypticPasswordUtil;
import com.hisun.util.HiICSProperty;
import com.mongodb.*;
import com.mongodb.client.MongoDatabase;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.io.*;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

//import com.mongodb.WriteConcern;

/**
 * mongo version 3.2.6
 * 
 * <AUTHOR>
 *
 */
public class DBManager {

	public static boolean FLAG = false;
	
	public static DBManager getInstance() {
		return InnerHolder.INSTANCE;
	}
	
	private static class InnerHolder {
		static final DBManager INSTANCE = new DBManager();
	}

	private MongoClient mongoClient;

	/**
	 * Creates a new <code>DBManager</code> instance.
	 * @throws Exception 
	 * 
	 */
	private DBManager() {
		try {
			List<ServerAddress> setList = new ArrayList<ServerAddress>();
			List<MongoCredential> credentials = new ArrayList<MongoCredential>();
			String workdir = HiICSProperty.getWorkDir();
			File file = new File(workdir + "/app/conf/mongoconfig.properties");
			BufferedReader bfr = null;
			try {
				bfr = new BufferedReader(new FileReader(file));
				String line = "";
				while ((line = bfr.readLine()) != null) {
					if (!line.startsWith("#")) {
						String[] mogcfgs = StringUtils.split(line, " ");
						String[] mogsvr = StringUtils.split(mogcfgs[0], ":");
						setList.add(new ServerAddress(mogsvr[0],NumberUtils.toInt(mogsvr[1])));
						if(mogcfgs.length == 3){

							String userName = CrypticPasswordUtil.getDecryptPassword(mogcfgs[1]);
							String password = CrypticPasswordUtil.getDecryptPassword(mogcfgs[2]);
							credentials.add(MongoCredential.createCredential(userName, "rabdb", password.toCharArray()));
						}
					}
				}
			} catch (FileNotFoundException e) {
				throw new RuntimeException("File \"/app/conf/mongoconfig.properties\" not found. ",e);
			} catch (IOException e) {
				throw new RuntimeException("Unexpected error occur at read file \"/app/conf/mongoconfig.properties\". ",e);
			}finally{
				if(bfr != null){
					try {
						bfr.close();
					} catch (IOException e) {
					}
				}
			}
			
			String poolSize = HiICSProperty.getProperty("ics.mongo.connectionsPerHost", "200");
			init(setList, credentials, Integer.parseInt(poolSize));
		} catch (UnknownHostException e) {
			throw new RuntimeException("Unexpected error occur at init Mongo DB.",e);
		}
	}

	/**
	 * 
	 * Gets a database object. 
	 * Users should use DBManager.getDatabase() instead.
	 * @return
	 */
	@Deprecated
	public synchronized DB getDB() {
		DB db = mongoClient.getDB("rabdb");
		return db;
	}
	
	/**
	 * Gets a database object.
	 * @return
	 */
	public synchronized MongoDatabase getDatabase() {
		MongoDatabase db = mongoClient.getDatabase("rabdb");
		return db;
	}


	public void init(final List<ServerAddress> list,final List<MongoCredential> credentials, int poolSize)
			throws java.net.UnknownHostException {
		System.setProperty("MONGO.POOLSIZE", String.valueOf(poolSize));
		if (mongoClient == null) {
			int maxWaitTime = HiICSProperty.getInt("ics.mongo.maxWaitTime", 10000);
			int socketTimeout = HiICSProperty.getInt("ics.mongo.socketTimeout", 30000);
			int connectTimeout = HiICSProperty.getInt("ics.mongo.connectTimeout", 5000);
			
			MongoClientOptions.Builder build = new MongoClientOptions.Builder();
			build.connectionsPerHost(poolSize);
			build.maxWaitTime(maxWaitTime);				//Sets the maximum time that a thread will block waiting for a connection.
			build.socketTimeout(socketTimeout);
			build.connectTimeout(connectTimeout);
			//build.writeConcern(WriteConcern.SAFE);
			MongoClientOptions options = build.build(); 
			
			mongoClient = new MongoClient(list,credentials,options);
		}
	}
	
	/**
	 * 释放mongo连接
	 */
	public void close(){
		mongoClient.close();
	}
}