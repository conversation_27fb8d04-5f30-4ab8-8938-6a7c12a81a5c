package com.hisun.queue.listener;

import java.util.concurrent.RejectedExecutionException;

import com.hisun.exception.HiException;
import com.hisun.framework.HiDefaultServer;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.queue.constants.HiParaConstants;
import com.hisun.rabbit.core.Message;
import com.hisun.rabbit.core.MessageProperties;
import com.hisun.rabbit.listener.MessageListener;
import com.hisun.util.HiICSProperty;
import com.hisun.util.HiThreadPool;

/**
 * rabbitmq client consumer
 * <AUTHOR>
 *
 */
public class HiRabbitClientConsumer implements MessageListener {
	private Logger log;
	private static Logger errorLog = HiLog.getLogger("rabErrorConsumer.dat");
	private static Logger redeliveredLog = HiLog.getLogger("rabRedeliveredConsumer.dat");
	private HiThreadPool threadPool;
	private HiDefaultServer server;

	public HiRabbitClientConsumer(HiThreadPool threadPool,HiDefaultServer server) {
		super();
		this.threadPool = threadPool;
		this.server = server;
	}

	public void onMessage(Message message) {
		
		try {
			MessageProperties  props = message.getMessageProperties();
			if(props.getRedelivered()){ // whether redelivered
				redeliveredLog.warn(message);
				return;
			}
			
			String msg = new String(message.getBody());
			
			HiMessage himsg = new HiMessage(msg);

			HiMessageContext ctx = new HiMessageContext();
			HiMessageContext.setCurrentMessageContext(ctx);
			
			if(himsg.hasHeadItem(HiMessage.SDN))
			{
				himsg.delHeadItem(HiMessage.SDN);
			}
			himsg.setHeadItem(HiMessage.SDN, HiICSProperty.getProperty("ics.sys.nodeid"));
			
			if(himsg.hasHeadItem(HiParaConstants.RAB_OBJSVR))
			{
				himsg.setHeadItem(HiMessage.SDT, himsg.getHeadItem(HiParaConstants.RAB_OBJSVR));
				himsg.delHeadItem(HiParaConstants.RAB_OBJSVR);
			}
			
			if(himsg.hasHeadItem(HiParaConstants.RAB_OBJTXN))
			{
				himsg.setHeadItem(HiMessage.STC, himsg.getHeadItem(HiParaConstants.RAB_OBJTXN));
				himsg.delHeadItem(HiParaConstants.RAB_OBJTXN);
			}
			
			ctx.setCurrentMsg(himsg);
			log.info("process msg = "+himsg);

			HiJobWorker worker = new HiJobWorker();
			worker.setMessageContext(ctx);
			worker.execute();
			
		} catch (Throwable t){
			errorLog.error(message,t);
		}
	}
	
	class HiJobWorker implements Runnable {

		public HiJobWorker() {

		}

		private HiMessageContext ctx;

		public void setMessageContext(HiMessageContext ctx) {
			this.ctx = ctx;
		}

		public void execute() {
			while (true) {
				try {
					if (threadPool.isShutdown()) {
						break;
					}
					threadPool.execute(this);
					break;
				} catch (RejectedExecutionException e) {
					log.warn("Please increase maxThreads!");
					Thread.yield();
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e1) {
						break;
					}
				}
			}
		}

		public void run() {
			try {
				doRun();
			} catch (Throwable t) {
				log.error("Msg:<<<" + ctx.getCurrentMsg() + ">>>", t);
			}
		}

		private void doRun() throws HiException {
			server.process(ctx);
		}
	}

	public Logger getLog() {
		return log;
	}

	public void setLog(Logger log) {
		this.log = log;
	}

}
