package com.hisun.queue.listener.serial;

import com.hisun.rabbit.connection.CachingConnectionFactory;
import com.hisun.rabbit.connection.ConnectionFactory;
import com.hisun.rabbit.core.*;
import com.hisun.rabbit.listener.AbstractMessageListenerContainer;
import com.hisun.rabbit.listener.ActiveObjectCounter;
import com.hisun.rabbit.listener.BlockingQueueConsumer;
import com.hisun.rabbit.listener.exception.*;
import com.hisun.rabbit.support.*;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.ConsumerCancelledException;
import com.rabbitmq.client.ShutdownSignalException;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


public class SerialMessageListenerContainer extends AbstractMessageListenerContainer {

	private static final long DEFAULT_START_CONSUMER_MIN_INTERVAL = 10000;

	private static final long DEFAULT_STOP_CONSUMER_MIN_INTERVAL = 60000;

	private static final int DEFAULT_CONSECUTIVE_ACTIVE_TRIGGER = 10;

	private static final int DEFAULT_CONSECUTIVE_IDLE_TRIGGER = 10;

	public static final long DEFAULT_RECEIVE_TIMEOUT = 1000;

	public static final int DEFAULT_PREFETCH_COUNT = 1;

	public static final long DEFAULT_SHUTDOWN_TIMEOUT = 5000;

	/**
	 * The default recovery interval: 5000 ms = 5 seconds.
	 */
	public static final long DEFAULT_RECOVERY_INTERVAL = 5000;

	private volatile int prefetchCount = DEFAULT_PREFETCH_COUNT;

	private volatile long startConsumerMinInterval = DEFAULT_START_CONSUMER_MIN_INTERVAL;

	private volatile long stopConsumerMinInterval = DEFAULT_STOP_CONSUMER_MIN_INTERVAL;

	private volatile int consecutiveActiveTrigger = DEFAULT_CONSECUTIVE_ACTIVE_TRIGGER;

	private volatile int consecutiveIdleTrigger = DEFAULT_CONSECUTIVE_IDLE_TRIGGER;

	private volatile int txSize = 1;//一个事务内处理消息数，处理txSize个消息后ask

	private volatile SingleSyncTaskExecutor taskExecutor = new SingleSyncTaskExecutor("hisun");

	private volatile int concurrentConsumers = 1;//min consurrent consumers

	private volatile Integer maxConcurrentConsumers;//max consurrent consumers

	private volatile boolean exclusive;

	private volatile long lastConsumerStarted;

	private volatile long lastConsumerStopped;

	private long receiveTimeout = DEFAULT_RECEIVE_TIMEOUT;

	private volatile long shutdownTimeout = DEFAULT_SHUTDOWN_TIMEOUT;

	private BackOff recoveryBackOff = new FixedBackOff(DEFAULT_RECOVERY_INTERVAL, FixedBackOff.UNLIMITED_ATTEMPTS);

	// Map entry value, when false, signals the consumer to terminate
	private Map<BlockingQueueConsumer, Boolean> consumers;

	private final Object consumersMonitor = new Object();

	private final ActiveObjectCounter<BlockingQueueConsumer> cancellationLock = new ActiveObjectCounter<BlockingQueueConsumer>();

	private volatile MessagePropertiesConverter messagePropertiesConverter = new DefaultMessagePropertiesConverter();

	private volatile boolean defaultRequeueRejected = true;

	private final Map<String, Object> consumerArgs = new HashMap<String, Object>();

	private volatile RabbitAdmin rabbitAdmin;

	private volatile boolean missingQueuesFatal = true;

	private volatile boolean missingQueuesFatalSet;

	private volatile boolean autoDeclare = true;//redeclare queue etc..

	private String serverName = "SimpleMessageListener";

	public interface ContainerDelegate {
		void invokeListener(Channel channel, Message message) throws Exception;
	}

	private final ContainerDelegate delegate = new ContainerDelegate() {
		public void invokeListener(Channel channel, Message message) throws Exception {
			SerialMessageListenerContainer.super.invokeListener(channel, message);
		}
	};

	/**
	 * 最终用该对象调用监听的方法，可再initializeProxy() 方法用动态代理对功能增强
	 */
	private ContainerDelegate proxy = delegate;

	private Integer declarationRetries;

	private Long failedDeclarationRetryInterval;

	private Long retryDeclarationInterval;

	/**
	 * Default constructor
	 */
	public SerialMessageListenerContainer() {
	}

	/**
	 * Create a listener container from the connection factory (mandatory).
	 *
	 * @param connectionFactory the {@link ConnectionFactory}
	 */
	public SerialMessageListenerContainer(ConnectionFactory connectionFactory) {
		this.setConnectionFactory(connectionFactory);
	}


	/**
	 * Specify the interval between recovery attempts, in <b>milliseconds</b>.
	 * The default is 5000 ms, that is, 5 seconds.
	 * @param recoveryInterval The recovery interval.
	 */
	public void setRecoveryInterval(long recoveryInterval) {
		this.recoveryBackOff = new FixedBackOff(recoveryInterval, FixedBackOff.UNLIMITED_ATTEMPTS);
	}

	/**
	 * Specify the {@link BackOff} for interval between recovery attempts.
	 * The default is 5000 ms, that is, 5 seconds.
	 * With the {@link BackOff} you can supply the {@code maxAttempts} for recovery before
	 * the {@link #stop()} will be performed.
	 * @param recoveryBackOff The BackOff to recover.
	 */
	public void setRecoveryBackOff(BackOff recoveryBackOff) {
		this.recoveryBackOff = recoveryBackOff;
	}
	/**
	 * Set to true for an exclusive consumer - if true, the concurrency must be 1.
	 * @param exclusive true for an exclusive consumer.
	 */
	public final void setExclusive(boolean exclusive) {
		if(exclusive  && (this.concurrentConsumers != 1 || (this.maxConcurrentConsumers !=null && this.maxConcurrentConsumers != 1))){
			throw new AmqpException("When the consumer is exclusive, the concurrency must be 1");
		}
		
		this.exclusive = exclusive;
	}

	/**
	 * If {@link #maxConcurrentConsumers} is greater then {@link #concurrentConsumers}, and
	 * {@link #maxConcurrentConsumers} has not been reached, specifies
	 * the minimum time (milliseconds) between starting new consumers on demand. Default is 10000
	 * (10 seconds).
	 * @param startConsumerMinInterval The minimum interval between new consumer starts.
	 * @see #setStartConsumerMinInterval(long)
	 */
	public final void setStartConsumerMinInterval(long startConsumerMinInterval) {
		if(startConsumerMinInterval <= 0){
			throw new AmqpException("'startConsumerMinInterval' must be > 0");
		}
		this.startConsumerMinInterval = startConsumerMinInterval;
	}

	/**
	 * If {@link #maxConcurrentConsumers} is greater then {@link #concurrentConsumers}, and
	 * the number of consumers exceeds {@link #concurrentConsumers}, specifies the
	 * minimum time (milliseconds) between stopping idle consumers. Default is 60000
	 * (1 minute).
	 * @param stopConsumerMinInterval The minimum interval between consumer stops.
	 * @see #setStopConsumerMinInterval(long)
	 */
	public final void setStopConsumerMinInterval(long stopConsumerMinInterval) {
		if(startConsumerMinInterval <= 0){
			throw new AmqpException("'stopConsumerMinInterval' must be > 0");
		}
		this.stopConsumerMinInterval = stopConsumerMinInterval;
	}

	/**
	 * If {@link #maxConcurrentConsumers} is greater then {@link #concurrentConsumers}, and
	 * {@link #maxConcurrentConsumers} has not been reached, specifies the number of
	 * consecutive cycles when a single consumer was active, in order to consider
	 * starting a new consumer. If the consumer goes idle for one cycle, the counter is reset.
	 * This is impacted by the {@link #txSize}.
	 * Default is 10 consecutive messages.
	 * @param consecutiveActiveTrigger The number of consecutive receives to trigger a new consumer.
	 * @see #setStartConsumerMinInterval(long)
	 * @see #setTxSize(int)
	 */
	public final void setConsecutiveActiveTrigger(int consecutiveActiveTrigger) {
		if(startConsumerMinInterval <= 0){
			throw new AmqpException("'consecutiveActiveTrigger' must be > 0");
		}
		this.consecutiveActiveTrigger = consecutiveActiveTrigger;
	}

	/**
	 * If {@link #maxConcurrentConsumers} is greater then {@link #concurrentConsumers}, and
	 * the number of consumers exceeds {@link #concurrentConsumers}, specifies the
	 * number of consecutive receive attempts that return no data; after which we consider
	 * stopping a consumer. The idle time is effectively
	 * {@link #receiveTimeout} * {@link #txSize} * this value because the consumer thread waits for
	 * a message for up to {@link #receiveTimeout} up to {@link #txSize} times.
	 * Default is 10 consecutive idles.
	 * @param consecutiveIdleTrigger The number of consecutive timeouts to trigger stopping a consumer.
	 * @see #setStopConsumerMinInterval(long)
	 * @see #setReceiveTimeout(long)
	 * @see #setTxSize(int)
	 */
	public final void setConsecutiveIdleTrigger(int consecutiveIdleTrigger) {
		if(startConsumerMinInterval <= 0){
			throw new AmqpException("'consecutiveIdleTrigger' must be > 0");
		}
		this.consecutiveIdleTrigger = consecutiveIdleTrigger;
	}

	/**
	 * The time (in milliseconds) that a consumer should wait for data. Default
	 * 1000 (1 second).
	 * @param receiveTimeout the timeout.
	 * @see #setConsecutiveIdleTrigger(int)
	 */
	public void setReceiveTimeout(long receiveTimeout) {
		this.receiveTimeout = receiveTimeout;
	}

	/**
	 * The time to wait for workers in milliseconds after the container is stopped, and before the connection is forced
	 * closed. If any workers are active when the shutdown signal comes they will be allowed to finish processing as
	 * long as they can finish within this timeout. Otherwise the connection is closed and messages remain unacked (if
	 * the channel is transactional). Defaults to 5 seconds.
	 * @param shutdownTimeout the shutdown timeout to set
	 */
	public void setShutdownTimeout(long shutdownTimeout) {
		this.shutdownTimeout = shutdownTimeout;
	}

	public void setTaskExecutor(SingleSyncTaskExecutor taskExecutor) {
		this.taskExecutor = taskExecutor;
	}

	/**
	 * Tells the broker how many messages to send to each consumer in a single request. Often this can be set quite high
	 * to improve throughput. It should be greater than or equal to {@link #setTxSize(int) the transaction size}.
	 * @param prefetchCount the prefetch count
	 */
	public void setPrefetchCount(int prefetchCount) {
		this.prefetchCount = prefetchCount;
	}

	/**
	 * Tells the container how many messages to process in a single transaction (if the channel is transactional). For
	 * best results it should be less than or equal to {@link #setPrefetchCount(int) the prefetch count}. Also affects
	 * how often acks are sent when using {@link AcknowledgeMode#AUTO} - one ack per txSize. Default is 1.
	 * @param txSize the transaction size
	 */
	public void setTxSize(int txSize) {
		this.txSize = txSize;
	}

	/**
	 * Set the {@link MessagePropertiesConverter} for this listener container.
	 * @param messagePropertiesConverter The properties converter.
	 */
	public void setMessagePropertiesConverter(MessagePropertiesConverter messagePropertiesConverter) {
		this.messagePropertiesConverter = messagePropertiesConverter;
	}

	/**
	 * Determines the default behavior when a message is rejected, for example because the listener
	 * threw an exception. When true, messages will be requeued, when false, they will not. For
	 * versions of Rabbit that support dead-lettering, the message must not be requeued in order
	 * to be sent to the dead letter exchange. Setting to false causes all rejections to not
	 * be requeued. When true, the default can be overridden by the listener throwing an
	 * {@link AmqpRejectAndDontRequeueException}. Default true.
	 * @param defaultRequeueRejected true to reject by default.
	 */
	public void setDefaultRequeueRejected(boolean defaultRequeueRejected) {
		this.defaultRequeueRejected = defaultRequeueRejected;
	}

	public void setConsumerArguments(Map<String, Object> args) {
		synchronized(consumersMonitor) {
			this.consumerArgs.clear();
			this.consumerArgs.putAll(args);
		}
	}

	protected RabbitAdmin getRabbitAdmin() {
		return rabbitAdmin;
	}

	/**
	 * Set the {@link RabbitAdmin}, used to declare any auto-delete queues, bindings
	 * etc when the container is started. Only needed if those queues use conditional
	 * declaration (have a 'declared-by' attribute). If not specified, an internal
	 * admin will be used which will attempt to declare all elements not having a
	 * 'declared-by' attribute.
	 * @param rabbitAdmin The admin.
	 */
	public void setRabbitAdmin(RabbitAdmin rabbitAdmin) {
		this.rabbitAdmin = rabbitAdmin;
	}

	/**
	 * If all of the configured queue(s) are not available on the broker, this setting
	 * determines whether the condition is fatal (default true). When true, and
	 * the queues are missing during startup, the context refresh() will fail. If
	 * the queues are removed while the container is running, the container is
	 * stopped.
	 * <p> When false, the condition is not considered fatal and the container will
	 * continue to attempt to start the consumers according to the {@link #setRecoveryInterval(long)}.
	 * Note that each consumer will make 3 attempts (at 5 second intervals) on each
	 * recovery attempt.
	 * @param missingQueuesFatal the missingQueuesFatal to set.
	 */
	public void setMissingQueuesFatal(boolean missingQueuesFatal) {
		this.missingQueuesFatal = missingQueuesFatal;
		this.missingQueuesFatalSet = true;
	}

	@Override
	public void setQueueNames(String... queueName) {
		super.setQueueNames(queueName);
		this.queuesChanged();
	}

	@Override
	public void setQueues(Queue... queues) {
		super.setQueues(queues);
		this.queuesChanged();
	}

	/**
	 * @param autoDeclare the boolean flag to indicate an redeclaration operation.
	 * @since 1.4
	 * @see #redeclareElementsIfNecessary
	 */
	public void setAutoDeclare(boolean autoDeclare) {
		this.autoDeclare = autoDeclare;
	}

	/**
	 * Add queue(s) to this container's list of queues. The existing consumers
	 * will be cancelled after they have processed any pre-fetched messages and
	 * new consumers will be created. The queue must exist to avoid problems when
	 * restarting the consumers.
	 * @param queueName The queue to add.
	 */
	@Override
	public void addQueueNames(String... queueName) {
		super.addQueueNames(queueName);
		this.queuesChanged();
	}

	/**
	 * Add queue(s) to this container's list of queues. The existing consumers
	 * will be cancelled after they have processed any pre-fetched messages and
	 * new consumers will be created. The queue must exist to avoid problems when
	 * restarting the consumers.
	 * @param queue The queue to add.
	 */
	@Override
	public void addQueues(Queue... queue) {
		super.addQueues(queue);
		this.queuesChanged();
	}

	/**
	 * Remove queues from this container's list of queues. The existing consumers
	 * will be cancelled after they have processed any pre-fetched messages and
	 * new consumers will be created. At least one queue must remain.
	 * @param queueName The queue to remove.
	 */
	@Override
	public boolean removeQueueNames(String... queueName) {
		if (super.removeQueueNames(queueName)) {
			this.queuesChanged();
			return true;
		}
		else {
			return false;
		}
	}

	/**
	 * Remove queue(s) from this container's list of queues. The existing consumers
	 * will be cancelled after they have processed any pre-fetched messages and
	 * new consumers will be created. At least one queue must remain.
	 * @param queue The queue to remove.
	 */
	@Override
	public boolean removeQueues(Queue... queue) {
		if (super.removeQueues(queue)) {
			this.queuesChanged();
			return true;
		}
		else {
			return false;
		}
	}

	/**
	 * Set the number of retries after passive queue declaration fails.
	 * @param declarationRetries The number of retries, default 3.
	 * @see #setFailedDeclarationRetryInterval(long)
	 * @since 1.3.9
	 */
	public void setDeclarationRetries(int declarationRetries) {
		this.declarationRetries = declarationRetries;
	}

	/**
	 * Set the interval between passive queue declaration attempts in milliseconds.
	 * @param failedDeclarationRetryInterval the interval, default 5000.
	 * @see #setDeclarationRetries(int)
	 * @since 1.3.9
	 */
	public void setFailedDeclarationRetryInterval(long failedDeclarationRetryInterval) {
		this.failedDeclarationRetryInterval = failedDeclarationRetryInterval;
	}

	/**
	 * When consuming multiple queues, set the interval between declaration attempts when only
	 * a subset of the queues were available (milliseconds).
	 * @param retryDeclarationInterval the interval, default 60000.
	 * @since 1.3.9
	 */
	public void setRetryDeclarationInterval(long retryDeclarationInterval) {
		this.retryDeclarationInterval = retryDeclarationInterval;
	}

	
	public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	/**
	 * Avoid the possibility of not configuring the CachingConnectionFactory in sync with the number of concurrent
	 * consumers.
	 */
	@Override
	protected void validateConfiguration() {

		super.validateConfiguration();

		if (this.getConnectionFactory() instanceof CachingConnectionFactory) {
			CachingConnectionFactory cf = (CachingConnectionFactory) getConnectionFactory();
			if (cf.getChannelCacheSize() < this.concurrentConsumers) {
				cf.setChannelCacheSize(this.concurrentConsumers);
				logger.warn("CachingConnectionFactory's channelCacheSize can not be less than the number " +
						"of concurrentConsumers so it was reset to match: "	+ this.concurrentConsumers);
			}
		}

	}

	private void initializeProxy() {
		//TODO 
	}

	// -------------------------------------------------------------------------
	// Implementation of AbstractMessageListenerContainer's template methods
	// -------------------------------------------------------------------------

	/**
	 * Always use a shared Rabbit Connection.
	 * @return true
	 */
	protected final boolean sharedConnectionEnabled() {
		return true;
	}

	/**
	 * Creates the specified number of concurrent consumers, in the form of a Rabbit Channel plus associated
	 * MessageConsumer.
	 * @throws Exception Any Exception.
	 */
	@Override
	protected void doInitialize() throws Exception {
		checkMissingQueuesFatal();
		initializeProxy();
	}

	/**
	 * 获取活动的consuemr
	 * @return
	 */
	public int getActiveConsumerCount() {
		return cancellationLock.getCount();
	}

	/**
	 * Re-initializes this container's Rabbit message consumers, if not initialized already. Then submits each consumer
	 * to this container's task executor.
	 * @throws Exception Any Exception.
	 */
	@Override
	protected void doStart() throws Exception {
		super.doStart();
		if (this.rabbitAdmin == null && this.autoDeclare) {
			RabbitAdmin rabbitAdmin = new RabbitAdmin(this.getConnectionFactory());
			rabbitAdmin.setQueueNames(this.getQueueNames());
			rabbitAdmin.setServerName(serverName);
			rabbitAdmin.afterPropertiesSet();// init connection,channel,queue etc...
			this.rabbitAdmin = rabbitAdmin;
		}
		
		synchronized (this.consumersMonitor) {
			int newConsumers = initializeConsumers();
			if (this.consumers == null) {
				if (logger.isInfoEnabled()) {
					logger.info("Consumers were initialized and then cleared (presumably the container was stopped concurrently)");
				}
				return;
			}
			if (newConsumers <= 0) {
				if (logger.isInfoEnabled()) {
					logger.info("Consumers are already running");
				}
				return;
			}
			Set<SerialMessageProcessingConsumer> processors = new HashSet<SerialMessageProcessingConsumer>();
			for (BlockingQueueConsumer consumer : this.consumers.keySet()) {
				SerialMessageProcessingConsumer processor = new SerialMessageProcessingConsumer(consumer);
				processors.add(processor);
				this.taskExecutor.execute(processor, consumer.getQueues()[0]);
			}
			for (SerialMessageProcessingConsumer processor : processors) {
				FatalListenerStartupException startupException = processor.getStartupException();
				if (startupException != null) {
					throw new AmqpIllegalStateException("Fatal exception on listener startup", startupException);
				}
			}
			logger.info("Consumers initialized,active consumer count="+getActiveConsumerCount());
		}
	}

	@Override
	protected void doStop() {
		shutdown();
		super.doStop();
	}

	@Override
	protected void doShutdown() {

		if (!this.isRunning()) {
			logger.warn("Consumer is already stoped...");
			return;
		}

		try {
			synchronized (consumersMonitor) {
				if (this.consumers != null) {
					for (BlockingQueueConsumer consumer : this.consumers.keySet()) {
						consumer.basicCancel();
					}
				}
			}
			logger.info("Waiting for workers to finish.");
			boolean finished = cancellationLock.await(shutdownTimeout, TimeUnit.MILLISECONDS);
			if (finished) {
				logger.info("Successfully waited for workers to finish.");
			}
			else {
				logger.info("Workers not finished.  Forcing connections to close.");
			}
		}
		catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			logger.warn("Interrupted waiting for workers.  Continuing with shutdown.");
		}

		synchronized (this.consumersMonitor) {
			this.consumers = null;
		}

	}

	private boolean isActive(BlockingQueueConsumer consumer) {
		Boolean consumerActive;
		synchronized(consumersMonitor) {
			consumerActive = this.consumers != null && this.consumers.get(consumer);
		}
		return consumerActive && this.isActive();
	}

	protected int initializeConsumers() {
		int count = 0;
		synchronized (this.consumersMonitor) {
			if (this.consumers == null) {
				cancellationLock.reset();//clear
				String[] queueNames = this.getQueueNames();
				this.consumers = new HashMap<BlockingQueueConsumer, Boolean>(queueNames.length);
				for (String queueName : queueNames) {
					BlockingQueueConsumer consumer = createBlockingQueueConsumer(queueName);
					this.consumers.put(consumer, true);
					count++;
				}
			}
		}
		return count;
	}

	private void checkMissingQueuesFatal() {
		if (!this.missingQueuesFatalSet) {
			//TODO
		}
	}

	protected void addAndStartConsumers() {
		synchronized (this.consumersMonitor) {
			if (this.consumers != null) {
				String[] queueNames = getQueueNames();
				for (String queueName : queueNames) {
					BlockingQueueConsumer consumer = createBlockingQueueConsumer(queueName);
					this.consumers.put(consumer, true);
					SerialMessageProcessingConsumer processor = new SerialMessageProcessingConsumer(consumer);
					if (logger.isDebugEnabled()) {
						logger.debug("Starting a new consumer: " + consumer);
					}
					this.taskExecutor.execute(processor, queueName);
					try {
						FatalListenerStartupException startupException = processor.getStartupException();
						if (startupException != null) {
							this.consumers.remove(consumer);
							throw startupException;
						}
						logger.info("add a new conumer:"+consumer);
						logger.info("active consumer count="+getActiveConsumerCount());
					}catch (InterruptedException ie) {
						Thread.currentThread().interrupt();
					}catch (Exception e) {
						consumer.stop();
						logger.error("Error starting new consumer", e);
						this.cancellationLock.release(consumer);
						this.consumers.remove(consumer);
					}
				}
			}
		}
	}

	private void considerAddingAConsumer() {
		synchronized(consumersMonitor) {
			if (this.consumers != null
					&& this.maxConcurrentConsumers != null && this.consumers.size() < this.maxConcurrentConsumers) {
				long now = System.currentTimeMillis();
				if (this.lastConsumerStarted + startConsumerMinInterval < now) {
					this.addAndStartConsumers();
					this.lastConsumerStarted = now;
				}
			}
		}
	}

	private void considerStoppingAConsumer(BlockingQueueConsumer consumer) {
		synchronized (consumersMonitor) {
			if (this.consumers != null && this.consumers.size() > concurrentConsumers) {
				long now = System.currentTimeMillis();
				if (this.lastConsumerStopped + this.stopConsumerMinInterval < now) {
					consumer.basicCancel();
					this.consumers.put(consumer, false);
					if (logger.isInfoEnabled()) {
						logger.info("Idle consumer terminating: " + consumer);
						logger.info("active consumer count="+getActiveConsumerCount());
					}
					this.lastConsumerStopped = now;
				}
			}
		}
	}

	private void queuesChanged() {
		synchronized (consumersMonitor) {
			if (this.consumers != null) {
				int count = 0;
				for (Entry<BlockingQueueConsumer, Boolean> consumer : this.consumers.entrySet()) {
					if (consumer.getValue()) {
						if (logger.isDebugEnabled()) {
							logger.debug("Queues changed; stopping consumer: " + consumer.getKey());
						}
						consumer.getKey().basicCancel();
						consumer.setValue(false);
						count++;
					}
				}
				this.addAndStartConsumers();
			}
		}
	}

	@Override
	protected boolean isChannelLocallyTransacted(Channel channel) {
		return super.isChannelLocallyTransacted(channel) ;
	}

	protected BlockingQueueConsumer createBlockingQueueConsumer(String queueName) {
		BlockingQueueConsumer consumer;
		String[] queues = new String[]{queueName};
		// There's no point prefetching less than the tx size, otherwise the consumer will stall because the broker
		// didn't get an ack for delivered messages
		int actualPrefetchCount = prefetchCount > txSize ? prefetchCount : txSize;
		consumer = new BlockingQueueConsumer(getConnectionFactory(), this.messagePropertiesConverter, cancellationLock,
				getAcknowledgeMode(), isChannelTransacted(), actualPrefetchCount, this.defaultRequeueRejected,
				this.consumerArgs, this.exclusive, queues);
		if (this.declarationRetries != null) {
			consumer.setDeclarationRetries(this.declarationRetries);
		}
		if (this.failedDeclarationRetryInterval != null) {
			consumer.setFailedDeclarationRetryInterval(this.failedDeclarationRetryInterval);
		}
		if (this.retryDeclarationInterval != null) {
			consumer.setRetryDeclarationInterval(this.retryDeclarationInterval);
		}
		consumer.setBackOffExecution(this.recoveryBackOff.start());
		return consumer;
	}

	private void restart(BlockingQueueConsumer consumer) {
		synchronized (this.consumersMonitor) {
			if (this.consumers != null) {
				try {
					// Need to recycle the channel in this consumer
					consumer.stop();
					// Ensure consumer counts are correct (another is going
					// to start because of the exception, but
					// we haven't counted down yet)
					this.cancellationLock.release(consumer);
					this.consumers.remove(consumer);
					BlockingQueueConsumer newConsumer = createBlockingQueueConsumer(consumer.getQueues()[0]);
					newConsumer.setBackOffExecution(consumer.getBackOffExecution());
					consumer = newConsumer;
					this.consumers.put(consumer, true);
				}catch (RuntimeException e) {
					logger.warn("Consumer failed irretrievably on restart. " + e.getClass() + ": " + e.getMessage());
					// Re-throw and have it logged properly by the caller.
					throw e;
				}
				this.taskExecutor.execute(new SerialMessageProcessingConsumer(consumer), consumer.getQueues()[0]);
			}
		}
	}

	/**
	 * Use {@link RabbitAdmin#initialize()} to redeclare everything if any of our
	 * queues are missing. Also auto deletion of a queue can cause upstream elements
	 * (bindings, exchanges) to be deleted too, so everything needs to be redeclared.
	 * Declaration is idempotent so, aside from some network chatter, there is no issue,
	 * and we only will do it if we detect our queue is gone.
	 * <p>
	 * In general it makes sense only for the 'auto-delete' or 'expired' queues,
	 * but with the server TTL policy we don't have ability to determine 'expiration'
	 * option for the queue.
	 */
	private synchronized void redeclareElementsIfNecessary() {
		try {
			for(String queueName : this.getQueueNamesAsSet()){
				if(this.rabbitAdmin.getQueueProperties(queueName) == null){
					if (logger.isDebugEnabled()) {
						logger.debug("At least one queue is missing: " + queueName
								+ "; redeclaring context exchanges, queues, bindings.");
					}
					this.rabbitAdmin.initialize();
					return;
				}
			}
			
		}catch (Exception e) {
			logger.error("Failed to check/redeclare auto-delete queue(s).", e);
		}
	}

	private boolean receiveAndExecute(final BlockingQueueConsumer consumer) throws Throwable {

		return doReceiveAndExecute(consumer);

	}

	private boolean doReceiveAndExecute(BlockingQueueConsumer consumer) throws Throwable {

		Channel channel = consumer.getChannel();

		for (int i = 0; i < txSize; i++) {

			//logger.debug("Waiting for message from consumer.");
			Message message = consumer.nextMessage(receiveTimeout);
			if (message == null) {
				break;
			}
			try {
				executeListener(channel, message);
			}catch (ImmediateAcknowledgeAmqpException e) {
				break;
			}catch (Throwable ex) {//NOSONAR
				consumer.rollbackOnExceptionIfNecessary(ex);
				throw ex;
			}

		}

		return consumer.commitIfNecessary(isChannelLocallyTransacted(channel));

	}


	/********************************************start SerialMessageProcessingConsumer**********************************/
	
	private class SerialMessageProcessingConsumer implements Runnable {

		private final BlockingQueueConsumer consumer;

		private final CountDownLatch start;

		private volatile FatalListenerStartupException startupException;

		public SerialMessageProcessingConsumer(BlockingQueueConsumer consumer) {
			this.consumer = consumer;
			this.start = new CountDownLatch(1);
		}

		/**
		 * Retrieve the fatal startup exception if this processor completely failed to locate the broker resources it
		 * needed. Blocks up to 60 seconds waiting for an exception to occur
		 * (but should always return promptly in normal circumstances).
		 * No longer fatal if the processor does not start up in 60 seconds.
		 * @return a startup exception if there was one
		 * @throws TimeoutException if the consumer hasn't started
		 * @throws InterruptedException if the consumer startup is interrupted
		 */
		public FatalListenerStartupException getStartupException() throws TimeoutException, InterruptedException {
			start.await(60000L, TimeUnit.MILLISECONDS);//NOSONAR - ignore return value
			return this.startupException;
		}

		public void run() {

			boolean aborted = false;// if true ,Stop this container

			int consecutiveIdles = 0; //consecutive idle

			int consecutiveMessages = 0; // consecutive get message

			try {

				try {
					if (SerialMessageListenerContainer.this.autoDeclare) {
						SerialMessageListenerContainer.this.redeclareElementsIfNecessary(); //redeclare queue etc... if necessary
					}
					this.consumer.start();
					this.start.countDown();
					
					if(logger.isInfoEnabled()){
						logger.info("current active consumer count="+getActiveConsumerCount());
					}
						
				}catch (QueuesNotAvailableException e) {
					if (SerialMessageListenerContainer.this.missingQueuesFatal) {
						throw e;
					}else {
						this.start.countDown();
						handleStartupFailure(this.consumer.getBackOffExecution());
						throw e;
					}
				}catch (FatalListenerStartupException ex) {
					throw ex;
				}catch (Throwable t) {//NOSONAR
					this.start.countDown();
					handleStartupFailure(this.consumer.getBackOffExecution());
					throw t;
				}

				while (isActive(this.consumer) || this.consumer.hasDelivery()) {
					try {
						boolean receivedOk = receiveAndExecute(this.consumer); // At least one message received
						if (SerialMessageListenerContainer.this.maxConcurrentConsumers != null) { //如果设置了maxConcurrentConsumers，动态添加或删除consumer
							if (receivedOk) {
								if (isActive(this.consumer)) {
									consecutiveIdles = 0;
									if (consecutiveMessages++ > SerialMessageListenerContainer.this.consecutiveActiveTrigger) {
										considerAddingAConsumer();
										consecutiveMessages = 0;
									}
								}
							}
							else {
								consecutiveMessages = 0;
								if (consecutiveIdles++ > SerialMessageListenerContainer.this.consecutiveIdleTrigger) {
									considerStoppingAConsumer(this.consumer);
									consecutiveIdles = 0;
								}
							}
						}
					}catch (ListenerExecutionFailedException ex) {
						// Continue to process, otherwise re-throw
						if (ex.getCause() instanceof NoSuchMethodException) {
							throw new FatalListenerExecutionException("Invalid listener", ex);
						}
						
					}catch (AmqpRejectAndDontRequeueException rejectEx) {
						/*
						 *  These will normally be wrapped by an LEFE if thrown by the
						 *  listener, but we will also honor it if thrown by an
						 *  error handler.
						 */
					}
				}

			}catch (InterruptedException e) { 
				logger.warn("Consumer thread interrupted, processing stopped. thread abort.");
				Thread.currentThread().interrupt();
				aborted = true;
			}catch (QueuesNotAvailableException ex) {
				if (SerialMessageListenerContainer.this.missingQueuesFatal) {
					logger.error("Consumer received fatal exception on startup, thread abort.", ex);
					this.startupException = ex;
					// Fatal, w point re-throwing, so just abort.
					aborted = true;
				}
			}catch (FatalListenerStartupException ex) {
				logger.error("Consumer received fatal exception on startup", ex);
				this.startupException = ex;
				// Fatal, but no point re-throwing, so just abort.
				//if AmqpConnectException ,restart
				if(!(ex.getCause() instanceof AmqpConnectException)){
					logger.error("Consumer received fatal exception on startup, thread abort.");
					aborted = true;
				}else{
					logger.warn("Consumer received AmqpConnectException,processing can restart if the connection factory supports it");
					try {
						Thread.sleep(30000);
					} catch (InterruptedException e) {
					}
				}
			}catch (FatalListenerExecutionException ex) {
				logger.error("Consumer received fatal exception during processing, thread abort.", ex);
				// Fatal, but no point re-throwing, so just abort.
				aborted = true;
			}catch (ShutdownSignalException e) {
				if (RabbitUtils.isNormalShutdown(e)) {
					if (logger.isDebugEnabled()) {
						logger.debug("Consumer received Shutdown Signal, processing stopped: " + e.getMessage());
					}
				}else {
					this.logConsumerException(e);
				}
			}catch (AmqpIOException e) {
				if (e.getCause() instanceof IOException && e.getCause().getCause() instanceof ShutdownSignalException
						&& e.getCause().getCause().getMessage().contains("in exclusive use")) {
					logger.warn(e.getCause().getCause().toString());
				}else {
					this.logConsumerException(e);
				}
			}catch (Error e) {//NOSONAR
				// ok to catch Error - we're aborting so will stop
				logger.error("Consumer thread error, thread abort.", e);
				aborted = true;
			}catch (Throwable t) {//NOSONAR
				// by now, it must be an exception
				if (isActive()) {
					this.logConsumerException(t);
				}
			}finally {
			}

			// In all cases count down to allow container to progress beyond startup
			start.countDown();

			if (!isActive(consumer) || aborted) {
				logger.info("Cancelling " + this.consumer);
				try {
					this.consumer.stop();
					SerialMessageListenerContainer.this.cancellationLock.release(this.consumer);
					synchronized (consumersMonitor) {
						if (SerialMessageListenerContainer.this.consumers != null) {
							SerialMessageListenerContainer.this.consumers.remove(this.consumer);
						}
					}
				}catch (AmqpException e) {
					logger.info("Could not cancel message consumer", e);
				}
				logger.info("stop consumer,active consumer count="+getActiveConsumerCount());
				if (aborted) {
					logger.error("Stopping container from aborted consumer");
					stop();
				}
			}else {
				logger.info("Restarting " + this.consumer);
				restart(this.consumer);
				
			}

		}

		private void logConsumerException(Throwable t) {
			if (logger.isDebugEnabled()
					|| !(t instanceof AmqpConnectException  || t instanceof ConsumerCancelledException)) {
				logger.warn(
						"Consumer raised exception, processing can restart if the connection factory supports it",
						t);
			}else {
				logger.warn("Consumer raised exception, processing can restart if the connection factory supports it. "
						+ "Exception summary: " + t);
			}
		}

	}

	/********************************************end AsyncMessageProcessingConsumer**********************************/
	
	
	@Override
	protected void invokeListener(Channel channel, Message message) throws Exception {
		proxy.invokeListener(channel, message);
	}

	/**
	 * Wait for a period determined by the {@link #setRecoveryInterval(long) recoveryInterval}
	 * or {@link #setRecoveryBackOff(BackOff)} to give the container a
	 * chance to recover from consumer startup failure, e.g. if the broker is down.
	 * @param backOffExecution the BackOffExecution to get the {@code recoveryInterval}
	 * @throws Exception if the shared connection still can't be established
	 */
	protected void handleStartupFailure(BackOffExecution backOffExecution) throws Exception {
		long recoveryInterval = backOffExecution.nextBackOff();
		if (BackOffExecution.STOP == recoveryInterval) {
			synchronized (this) {
				if (isActive()) {
					logger.warn("stopping container - restart recovery attempts exhausted");
					stop();
				}
			}
			return;
		}
		try {
			if (logger.isDebugEnabled() && isActive()) {
				logger.debug("Recovering consumer in " + recoveryInterval + " ms.");
			}
			long timeout = System.currentTimeMillis() + recoveryInterval;
			while (isActive() && System.currentTimeMillis() < timeout) {
				Thread.sleep(200);
			}
		}
		catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new IllegalStateException("Unrecoverable interruption on consumer restart");
		}
	}

}

