package com.hisun.queue.listener.consumer;

import com.hisun.translog.util.HiTransLog2;
import com.hisun.translog.util.HiTransLogUtils;
import org.apache.commons.lang.StringUtils;

import com.hisun.atc.HiPubExtConstants;
import com.hisun.exception.HiException;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.ics.datafmt.json.HiJSONArray;
import com.hisun.ics.datafmt.json.HiJSONObject;
import com.hisun.ics.param.ehcache.HiEhCacheUtil;
import com.hisun.message.HiETF;
import com.hisun.message.HiETFFactory;
import com.hisun.message.HiMessage;
import com.hisun.queue.constants.HiParaConstants;
import com.hisun.queue.listener.ConsumerMessage;
import com.hisun.rabbit.core.RabbitTemplate;

/**
 * 订阅发布
 * <AUTHOR>
 *
 */
public class TopicServerConsumer extends AbstractServerConsumer {
	
	private static Logger topicUniqueLog = HiLog.getLogger("rabUniqueForTopic.dat");
	
	public TopicServerConsumer(RabbitTemplate producerRabbitTemplate){
		super(producerRabbitTemplate);
	}

	@Override
	public void consume(ConsumerMessage consumerMessage) throws ConsumerException {
		try{
			topicProcess(consumerMessage,false);
		}catch(ConsumerException e){
			logLost("~~BeforeDispatchMsg="+consumerMessage.getMsg(),e);
			consumerLog.error(consumerMessage.getRid(),e);
			throw e;
		}
	}

	@Override
	public void delayConsume(ConsumerMessage consumerMessage) throws ConsumerException {
		try{
			topicProcess(consumerMessage,true);
		}catch(ConsumerException e){
			logLost("~~BeforeDispatchMsg="+consumerMessage.getMsg(),e);
			consumerLog.error(consumerMessage.getRid(),e);
			throw e;
		}
	}
	
	/**
	 * @param consumerMessage
	 * @param delayResend true:登记db等待重发；false:直接发送rabbitmq
	 * @throws HiException
	 * @throws ConsumerException 
	 */
	private void topicProcess(ConsumerMessage consumerMessage,boolean delayResend) throws ConsumerException{
		String rid = consumerMessage.getRid();
		String msg = consumerMessage.getMsg();
		//堵重检查
		try{
			if(checkRepeat(rid+"_0")){	//rid如果已经分发，rid＋"_0" 一定存在
				topicUniqueLog.warn(msg);
				return;
			}
		}catch(Exception e){
			throw new ConsumerException(e,true);
		}
		
		HiMessage himsg = null;
		String topId = null;
		HiJSONArray subInfs = null;
		try{
			himsg = new HiMessage(msg);
			topId = himsg.getHeadItem(HiPubExtConstants.TID);
			subInfs = HiEhCacheUtil.getJSONArrayFromEhCache("SUBINF_FLDS", topId);
		}catch(Exception e){
			throw new ConsumerException(e,true);
		}
		
		if(subInfs  == null || subInfs.isEmpty()){
			throw new ConsumerException("Subscribe informations is empty. please check the configure file for the service S.PARSVR. paramName='SUBINF_FLDS',topId="+topId,true);
		}

		try{


            String oriObjNod = consumerMessage.getObjNod();			//默认的发布者节点,consumerMessage该属性值后面会发生变化
            himsg.setHeadItem(HiMessage.SPT, System.currentTimeMillis());
            // 遍历订阅信息
            int i = 0;												//订阅者消息唯一ID＝rid＋i
            for(Object subInfObj : subInfs){
                //rid，msg，routeInfo 每循环一次都会变化，直接清理
                consumerMessage.clearRid();
                consumerMessage.clearMsg();
                consumerMessage.clearRouteInfo();
                consumerMessage.setObjNod(oriObjNod);  				//ObjNod设置为默认的发布者节点

                String subId = null;									//订阅者ID
                try{
                    HiJSONObject subinf = (HiJSONObject)subInfObj;
                    subId = subinf.getString("SUB_ID");
                    HiJSONObject suber = null;
                    try{
                        suber = HiEhCacheUtil.getJSONObjectFromEhCache("PUBSUBER",subId);
                    }catch(Exception e){
                        throw new WarpperBeforeSaveMsgException("Unexpected exception occur at get suber from ehcache",e);
                    }
                    if(suber == null || suber.isEmpty()){
                        throw new WarpperBeforeSaveMsgException("Suber is null, subId="+subId);
                    }

                    if(StringUtils.equals("Y", suber.getString("TO_RABBIT"))){
                        String ridForSub = rid + "_" + i;//防止多个订阅者id相同，插入mongo唯一
                        consumerMessage.setRid(ridForSub);
                        if(delayResend){
                            delaySubProcess(consumerMessage, himsg, subinf,suber, topId);
                        }else{
                            subProcess(consumerMessage, himsg, subinf,suber, topId);
                        }
                        i++;		//必须放在此处，防止因异常缺失rid+"_0" 的订阅消息而造成堵重失败
                    }
                }catch(DuplicateKeyException mdke){						    //唯一性约束
                    topicUniqueLog.warn(rid,mdke.getType(),mdke.getCollectionName(),msg);
                    break;//遇到一次唯一性约束，结束循环，所有订阅者都不再分发
                }catch(WarpperBeforeSaveMsgException wbsme){			        //消息保存到DB之前抛出异常
                    logLost("TopicMsgLostForSubId="+subId+"~~BeforeDispatchMsg="+msg,wbsme);
                    consumerLog.error(rid,wbsme);
                }catch(SaveMessageFailException smfe){						//保存消息到DB时抛出异常
                    logLost("TopicMsgLostForSubId="+subId+"~~BeforeDispatchMsg="+msg,smfe);
                    consumerLog.error(rid,smfe);
                }catch(ConsumerException e){									//其他消费者异常，抛出此异常，消息已经保存到DB
                    logLost("TopicMsgLostForSubId="+subId+"~~BeforeDispatchMsg="+msg,e);
                    consumerLog.error(rid,e);
                    i++;
                }catch(Throwable t){											//非捕获的异常，特殊处理；该异常理论上不应该抛出
                    consumerLog.error(rid,"i="+i,"TopicMsgLostForSubId="+subId+" ,The msg may be lost. "+"~~BeforeDispatchMsg="+msg,t);
                    if(i==0){	//当第一条抛出该异常，不能判断DB是否已保存该条消息，如果i继续等于0，有可能抛出唯一性异常，如果i＋1，有可能导致堵重失败
                        break;
                    }else{		//有可能i 条消息保存DB失败
                        i++;
                    }
                }
            }//end for

        }finally {
            //打印PLET日志需要
            himsg.setRequestId(himsg.getHeadItem(HiMessage.LSN));
            himsg.setHeadItem(HiMessage.EPT, System.currentTimeMillis());
            himsg.setHeadItem("SDT","S.RECEIVER");
            himsg.setHeadItem("STC","RECEIVER_" + topId);
            new HiTransLog2(himsg).end();
        }


	}
	
	/**
	 * 订阅发布处理
	 * @param consumerMessage
	 * @param himsg
	 * @param subinf
	 * @param suber
	 * @param topId
	 * @throws HiException
	 * @throws ConsumerException 
	 * @throws DuplicateKeyException 违反唯一性约束
	 */
	private void subProcess(ConsumerMessage consumerMessage,HiMessage himsg,HiJSONObject subinf,HiJSONObject suber,String topId) throws  ConsumerException, DuplicateKeyException{
		String subMsg = null;
		try {
			subMsg = createSubMsg(himsg, subinf, suber, topId);

		} catch (Exception e1) {
			throw new WarpperBeforeSaveMsgException("create suber message occur error.",e1);
		}
		consumerMessage.setMsg(subMsg);                      //设置订阅消息
		
		String objSvr = suber.getString("OBJ_SVR");			//订阅者服务名
		String objTxn = suber.getString("TTXN_CD");			//订阅者交易名
		String objNod = suber.getString("SUB_NODE");			//订阅者节点
		if(StringUtils.isBlank(objNod)){
			objNod = consumerMessage.getObjNod();			//发布者节点
		}
		//add by deng_wj 20180102 串行队列分区数和分区字段
		int routeParts = suber.getInt("ROUTE_PARTS");
		String routeFiled = suber.getString("ROUTE_FILED");
		consumerMessage.setRouteParts(routeParts);
		consumerMessage.setRouteFiled(routeFiled);
		consumerMessage.setRouteValue(getRouteValue(himsg, consumerMessage));
		//end add deng_wj 20180102
		consumerMessage.setRouteInfo(objSvr, objTxn, objNod);//路由信息
		
		try{
			sendMsgAndCheckRepeat(consumerMessage,true);		//因已经对昨日数据做堵重检查，所以只对当日数据做堵重检查
		}catch(ConsumerException e){
			logLost(subMsg,e);								//此处登记丢失日志，可以记录订阅消息
			throw e;
		}
	}
	
	/**
	 * 订阅发布延迟处理
	 * @param consumerMessage
	 * @param himsg
	 * @param subinf
	 * @param suber
	 * @param topId
	 * @throws HiException
	 * @throws ConsumerException 
	 * @throws DuplicateKeyException 违反唯一性索引异常
	 */
	private void delaySubProcess(ConsumerMessage consumerMessage,HiMessage himsg,HiJSONObject subinf,HiJSONObject suber,String topId) throws ConsumerException, DuplicateKeyException{
		String subMsg = null;
		try {
			subMsg = createSubMsg(himsg, subinf,suber, topId);
		} catch (Exception e1) {
			throw new WarpperBeforeSaveMsgException("create suber message occur error.",e1);
		}
		consumerMessage.setMsg(subMsg);                      			//设置订阅消息
		
		String objSvr = suber.getString("OBJ_SVR");						//目标服务
		String objTxn = suber.getString("TTXN_CD");						//目标交易
		String objNod = suber.getString("SUB_NODE");						//目标实例名，订阅者没有指定，则使用发布者的实例名
		if(StringUtils.isBlank(objNod)){
			objNod = consumerMessage.getObjNod();
		}
		consumerMessage.setRouteInfo(objSvr, objTxn, objNod);            //设置路由信息
		try{
			saveMsgResendAndCheckRepeat(consumerMessage, true);
		}catch(ConsumerException e){
			logLost(subMsg,e);
			throw e;
		}
	}
	
	/**
	 * 生成订阅消息 并生成订阅发布详细日志
	 * @return
	 * @throws HiException 
	 */
	public String createSubMsg(HiMessage mess,HiJSONObject subinf,HiJSONObject suber,String topId) throws HiException{
		String tpFlds = subinf.getString("TP_FLDS");
		String subFlds = subinf.getString("SUB_FLDS");
		String subId = suber.getString("SUB_ID");
		String objSvr = suber.getString("OBJ_SVR");
		String txnCd = suber.getString("TTXN_CD");
		
		if (StringUtils.isBlank(objSvr) || StringUtils.isBlank(txnCd)
				|| StringUtils.isBlank(tpFlds)) {
			throw new HiException("Value of the fields 'ObjSvr' or 'TxnCd' or 'tpFlds' is null in the table ‘PUBSUBER’, please check it on database.");
		}
		
		Logger txlog = HiLog.getLogger(mess);	//订阅发布详细日志
		if (txlog.isInfoEnabled()) {
			txlog.info("TOP_ID:" + topId + ",SUB_ID:" + subId + ",OBJ_SVR:"
					+ objSvr + ",TTXN_CD:" + txnCd);
		}
		
		// 根据订阅者订阅的字段生成完整的订阅消息msgDat
		String[] tflds = tpFlds.split("\\|");
		String[] sflds = subFlds.split("\\|");
		if (tflds.length != sflds.length) {
			throw new HiException("Value of the fields 'TP_FLDS' and 'SUB_FLDS' is not match,please check it on database.");
		}
		//创建订阅发布消息的ETF树
		HiETF subEtf = HiETFFactory.createETF();
		//解析订阅字段
		HiETF root = mess.getETFBody();
		for (int i = 0; i < tflds.length; i++) {
			String topFldNm = tflds[i];
			String nodeValue = "";
			if (topFldNm.charAt(0) == '\''
					&& topFldNm.charAt(topFldNm.length() - 1) == '\'') {
				nodeValue = topFldNm.substring(1, topFldNm.length() - 1);
			} else {
				nodeValue = root.getGrandChildValue(topFldNm);
			}
			subEtf.setGrandChildNode(sflds[i], nodeValue);
		}
		//订阅发布发起交易
		subEtf.setChildValue("FROM_TX_CD", topId);
		// GWA直接传指针
		if (root.getChildNode("GWA") != null)
			subEtf.appendNode(root.getChildNode("GWA"));

		if (subEtf.getChildNodes().size() < 1){
			throw new HiException("Message to send is null:[" + subEtf + "]");
		}

		HiMessage subMessage = new HiMessage(objSvr, "PLTIN0");
		String messStr = mess.toString();
		subMessage.setHeadItem("RABBITMQ==", messStr.substring(messStr.indexOf("RABBITMQ==") + 10,
				messStr.indexOf("==RABBITMQ")) + "==RABBITMQ");
		subMessage.setHeadItem(HiParaConstants.RABMSG_ID, mess.getHeadItem(HiParaConstants.RABMSG_ID));
		subMessage.setHeadItem(HiPubExtConstants.TID, mess.getHeadItem(HiPubExtConstants.TID));
		subMessage.setLogId(mess.getHeadItem(HiMessage.LSN));
		HiTransLogUtils.appendLogInfo(mess, subMessage,true);
		subMessage.setHeadItem(HiMessage.TEXT_TYPE, HiMessage.TEXT_TYPE_ETF);
		subMessage.setHeadItem(HiMessage.STM, mess.getObjectHeadItem(HiMessage.STM));
		subMessage.setHeadItem(HiParaConstants.RABMSG_TIME, mess.getHeadItem(HiParaConstants.RABMSG_TIME));
		subMessage.setHeadItem(HiMessage.STF, mess.getHeadItem(HiMessage.STF));
		subMessage.setHeadItem(HiParaConstants.RABMSG_TYP, mess.getHeadItem(HiParaConstants.RABMSG_TYP));
		subMessage.setHeadItem(HiMessage.SRT, mess.getHeadItem(HiMessage.SRT));
		subMessage.setHeadItem(HiMessage.REQUEST_RESPONSE,HiMessage.TYPE_REQUEST);
		subMessage.setHeadItem(HiParaConstants.RABMSG_NODE, mess.getHeadItem(HiParaConstants.RABMSG_NODE));//发布者node
		subMessage.setHeadItem(HiParaConstants.RABMSG_REGION, mess.getHeadItem(HiParaConstants.RABMSG_REGION));//发布者region
		subMessage.setHeadItem(HiParaConstants.RAB_OBJSVR, objSvr);
		subMessage.setHeadItem(HiParaConstants.RAB_OBJTXN, txnCd);
		subMessage.setBody(subEtf);

		String submsg =  subMessage.toString();
		txlog.info(submsg);
		return submsg;
	}

}
