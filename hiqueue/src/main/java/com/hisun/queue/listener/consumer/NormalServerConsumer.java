package com.hisun.queue.listener.consumer;

import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.queue.listener.ConsumerMessage;
import com.hisun.rabbit.core.RabbitTemplate;

/**
 * normal 类型消息处理
 * 目前已有的消息类型有：LOG
 * <AUTHOR>
 *
 */
public class NormalServerConsumer extends AbstractServerConsumer {
	
	private static Logger normalUniqueLog = HiLog.getLogger("rabUniqueForNormal.dat");
	
	public NormalServerConsumer(RabbitTemplate producerRabbitTemplate){
		super(producerRabbitTemplate);
	}

	@Override
	public void consume(ConsumerMessage consumerMessage) throws ConsumerException {
		if(consumerMessage.isRedelivered() && !consumerMessage.isSaveDB()){	//如果不保存DB且redelivered，丢弃
			logDiscard(consumerMessage, "redelivered");
			return;
		}
		
		try{
			normalProcess(consumerMessage);
		}catch (DuplicateKeyException e) {
			normalUniqueLog.warn(consumerMessage.getRid(),e.getType(),e.getCollectionName(),consumerMessage.getMsg());
			return;
		}catch(ConsumerException e ){
			logLost(consumerMessage.getMsg(),e);
			consumerLog.error(consumerMessage.getRid(),e);
			throw e;
		}
	}

	@Override
	public void delayConsume(ConsumerMessage consumerMessage) throws ConsumerException {
		if(!consumerMessage.isSaveDB()){						//不登记DB
			logDiscard(consumerMessage, "dealyAndNotSaveDB");
			return;
		}
		String msg = consumerMessage.getMsg();
		try{
			saveMsgResendAndCheckRepeat(consumerMessage, false);
		}catch (DuplicateKeyException e) {
			normalUniqueLog.warn(consumerMessage.getRid(),e.getType(),e.getCollectionName(),msg);
			return;
		}catch(ConsumerException e ){
			logLost(msg,e);
			consumerLog.error(consumerMessage.getRid(),e);
			throw e;
		} 
	}
	
	public void normalProcess(ConsumerMessage consumerMessage) throws ConsumerException, DuplicateKeyException{
		String objSvr = consumerMessage.getObjSvr();				//目标服务名
		String objTxn = consumerMessage.getObjTxn();				//目标交易名
		String objNode = consumerMessage.getObjNod();			//目标节点名
		if (consumerMessage.isSaveDB()) {
			sendMsgAndCheckRepeat(consumerMessage, false);
		}else{
			String sendQueueName = sendMsg(objSvr,objTxn,objNode, consumerMessage.getMsg());
			logSended(consumerMessage , sendQueueName);
		}
	}
}
