package com.hisun.queue.listener;

import java.io.IOException;

import org.apache.commons.lang.StringUtils;

import com.hisun.exception.HiException;
import com.hisun.framework.HiDefaultServer;
import com.hisun.hilog4j.Logger;
import com.hisun.queue.constants.HiParaConstants;
import com.hisun.util.HiThreadPool;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

/**
 * 负载队列的初始化的动作，消费者和生产者都可以继承这个类
 * 
 * <AUTHOR>
 * 
 */
public class HiQueueEndpoint implements HiEndpoint {
	// -- Private Zone
	protected ConnectionFactory factory;
	protected Connection connection;
	protected Channel channel;
	protected String queueName;

	protected Logger log = null;
	protected boolean running = false;

	// private String config;
	private String host;
	private int port;
	private int weight; // 权重
	private String exchangeName;
	private int interval = 1; // 间隔时间,sencond
	private Thread daemonThread = null;
	private HiThreadPool threadPool;
	private HiHAEndpoint prohaEndpoint;
	private HiDefaultServer server;
	private String pointTyp;
	
	private String userName;
	private String password;
	private String virtualHost;

	public HiQueueEndpoint(String queueName, HiThreadPool threadPool,
			HiHAEndpoint prohaEndpoint, String pointTyp, Logger log) {
		this.queueName = queueName;
		this.threadPool = threadPool;
		this.prohaEndpoint = prohaEndpoint;
		this.log = log;
		this.pointTyp = pointTyp;
	}

	public HiQueueEndpoint(String queueName, HiThreadPool threadPool,
			HiDefaultServer server, String pointTyp, Logger log) {
		this.queueName = queueName;
		this.threadPool = threadPool;
		this.server = server;
		this.log = log;
		this.pointTyp = pointTyp;
	}

	public HiQueueEndpoint(String queueName, String pointTyp, Logger log) {
		this.queueName = queueName;
		this.log = log;
		this.pointTyp = pointTyp;
	}

	public void init() {
		// 还原对象
		factory = null;
		connection = null;
		channel = null;

		factory = new ConnectionFactory();
		factory.setHost(this.host);
		factory.setPort(this.port);
		if(StringUtils.isNotBlank(this.userName) && StringUtils.isNotBlank(this.password)){
			factory.setUsername(userName);
			factory.setPassword(password);
		}
		if(StringUtils.isNotBlank(this.virtualHost)){
			factory.setVirtualHost(virtualHost);
		}
		factory.setConnectionTimeout(5 * 1000);
	}

	public void close() throws HiException {
		running = false;
		// try {
		if (null != channel && channel.isOpen()) {
			try {
				channel.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		if (null != connection && connection.isOpen()) {
			try {
				connection.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		if (daemonThread != null) {
			daemonThread.interrupt();
		}
	}

	public void build() {

		try {
			connection = factory.newConnection();
			channel = connection.createChannel();

			// 判断节点类型
			if (pointTyp.equals(HiParaConstants.CLIENT_PRODUCER_TYP)) {
				String[] arrqueue = StringUtils.split(this.queueName, "|");
				for (int i = 0; i < arrqueue.length; i++) {
					String que = arrqueue[i];
					channel.queueDeclare(que, true, false, false, null);
				}
				running = true;
			} else if (pointTyp.equals(HiParaConstants.SERVER_PRODUCER_TYP)) {
				String[] arrqueue = StringUtils.split(this.queueName, "|");
				for (int i = 0; i < arrqueue.length; i++) {
					String que = arrqueue[i];
					channel.queueDeclare(que, true, false, false, null);
				}
				running = true;
			} else if (pointTyp.equals(HiParaConstants.CLIENT_CONSUMER_TYP)) {
				configureConsumer(channel);
			} else if (pointTyp.equals(HiParaConstants.SERVER_CONSUMER_TYP)) {
				configureServerConsumer(channel);

			}
		} catch (IOException e) {
			// -- 是否重连
			log.error("build connection failed", e);
			throw new RuntimeException(e);//连接不上，启动服务直接抛出异常
			// rebuild();
		}

	}

	private void rebuild(HiHAEndpoint endPoints, int Index) {
		try {
			if (connection != null) {
				if (!connection.isOpen()) {
					connection.abort();
					if (connection != null){
						connection.close();
					}
					connection = factory.newConnection();
				}

			} else {
				connection = factory.newConnection();

			}

			if (channel != null) {
				if (!channel.isOpen()) {
					channel.abort();
					if (connection != null){
						channel.close();
					}
					channel = connection.createChannel();
				}

			} else {
				channel = connection.createChannel();
			}

			// 判断节点类型
			if (pointTyp.equals(HiParaConstants.CLIENT_PRODUCER_TYP)) {
				String[] arrqueue = StringUtils.split(this.queueName, "|");
				for (int i = 0; i < arrqueue.length; i++) {
					String que = arrqueue[i];
					channel.queueDeclare(que, true, false, false, null);
				}
				running = true;
			} else if (pointTyp.equals(HiParaConstants.SERVER_PRODUCER_TYP)) {
				String[] arrqueue = StringUtils.split(this.queueName, "|");
				for (int i = 0; i < arrqueue.length; i++) {
					String que = arrqueue[i];
					channel.queueDeclare(que, true, false, false, null);
				}
				running = true;
			} else if (pointTyp.equals(HiParaConstants.CLIENT_CONSUMER_TYP)) {
				configureConsumer(channel);
			} else if (pointTyp.equals(HiParaConstants.SERVER_CONSUMER_TYP)) {
				configureServerConsumer(channel);

			}
			
			endPoints.isOpen.set(Index, 1);

		} catch (IOException e) {
			// -- 是否重连
			log.error("build connection failed", e);
			// rebuild();
		}
	}

	private synchronized void reset(HiHAEndpoint endPoints, int Index) {
		rebuild(endPoints, Index);

	}
	
	public void startDamen(HiHAEndpoint endPoints, int Index) {

		daemonThread = new Thread(new Daemon(endPoints, Index));
		daemonThread.start();

	}

	private void configureConsumer(Channel channel) throws IOException {

		try {
			// start consuming messages. Auto acknowledge messages.

			HiClientConsumer consumer = new HiClientConsumer(this.threadPool,
					this.server);
			consumer.setLog(log);
			consumer.setChannel(channel);
			// 多发送队列(遍历)
			String[] arrqueue = StringUtils.split(this.queueName, "|");
			for (int i = 0; i < arrqueue.length; i++) {
				String que = arrqueue[i];
				// channel.queueDeclare(que, true, false, false, null);
				channel.queueDeclare(que, true, false, false, null);
				channel.basicConsume(que, true, consumer);

			}

			running = true;
		} catch (IOException e) {
			// --
			e.printStackTrace();
		}
	}

	private void configureServerConsumer(Channel channel) throws IOException {

		try {
			// start consuming messages. Auto acknowledge messages.

			HiServerConsumer consumer = new HiServerConsumer(this.threadPool,
					this.prohaEndpoint);
			consumer.setLog(log);
			consumer.setChannel(channel);
			// 多发送队列(遍历)
			String[] arrqueue = StringUtils.split(this.queueName, "|");
			for (int i = 0; i < arrqueue.length; i++) {
				String que = arrqueue[i];
				// channel.queueDeclare(que, true, false, false, null);
				channel.queueDeclare(que, true, false, false, null);
				channel.basicConsume(que, true, consumer);

			}

			running = true;
		} catch (IOException e) {
			// --
			e.printStackTrace();
		}
	}

	public Channel getChannel() {
		return this.channel;
	}

	public String getExchangeName() {
		return exchangeName;
	}

	public void setExchangeName(String exchangeName) {
		this.exchangeName = exchangeName;
	}

	public String getQueueName() {
		return queueName;
	}

	public void setQueueName(String queueName) {
		this.queueName = queueName;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public int getInterval() {
		return interval;
	}

	public void setInterval(int interval) {
		this.interval = interval;
	}

	public void setLog(Logger log) {
		this.log = log;
	}

	public int getWeight() {
		return weight;
	}

	public void setWeight(int weight) {
		this.weight = weight;
	}

	public String toString() {
		return "host:{" + this.host + "} port:{" + this.port + "}";
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getVirtualHost() {
		return virtualHost;
	}

	public void setVirtualHost(String virtualHost) {
		this.virtualHost = virtualHost;
	}



	/**
	 * 负责定时扫描链接是否有效，失效重连
	 * 
	 * <AUTHOR>
	 * 
	 */
	class Daemon implements Runnable {
		private HiHAEndpoint endPoints;
		private int Index;

		public Daemon(HiHAEndpoint endPoints, int Index) {
			this.endPoints = endPoints;
			this.Index = Index;
		}

		public void run() {
			dorun();
		}

		private void dorun() {
			while (true) {
				if (!running) {
					break;
				}
				try{
					
					if (connection != null && connection.isOpen()) {
						try {
							// log.info("xxxx");
							Thread.currentThread().sleep(interval * 1000);
						} catch (InterruptedException e) {
							//break;
						}
					} else {
							endPoints.isOpen.set(Index, 0);
							reset(endPoints, Index);
					}
				
				}catch(Throwable t){
				}
			}
		}
	}

}
