package com.hisun.queue.listener.consumer;

import com.hisun.exception.HiException;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.queue.common.DBManager;
import com.hisun.queue.constants.HiRabbitUtils;
import com.hisun.queue.listener.ConsumerMessage;
import com.hisun.rabbit.core.Message;
import com.hisun.rabbit.core.RabbitTemplate;
import com.hisun.rabbit.monitor.PerformStats;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import com.mongodb.ErrorCategory;
import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.IndexOptions;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 服务端消费者抽象类
 * 登记mongo
 * 发送消息到mq
 * <AUTHOR>
 *
 */
public abstract class AbstractServerConsumer implements ServerConsumer {
	public static final Logger consumerLog = HiLog.getLogger("rabServerConsumer.trc");
	protected static final Logger sendedLog = HiLog.getLogger("rabSended.lst");	//消息发送日志
	public static final Logger lostLog = HiLog.getLogger("rabLost.dat");	//消息丢失
	public static final Logger saveForResendLog = HiLog.getLogger("rabSaveForResend.lst");	//  保存mongo，等待重发
	protected static Logger discardLog = HiLog.getLogger("rabDiscard.dat");//丢弃的消息;
	
	public static final Logger mongoMonitorLog = HiLog.getLogger("rabMongoMonitor.trc");
	
	private static final int mongoOpTmThreshold = 100; // mongo operation time threshold，ms
	private static final int secondaryPreferredAfterHour = 1;		//超过几点上一天的数据直接查从库
	
	private RabbitTemplate producerRabbitTemplate;
	
	private PerformStats performStats = PerformStats.getInstance("MongoMonitorForServerConsumer");
	
	public AbstractServerConsumer(RabbitTemplate producerRabbitTemplate){
		this.producerRabbitTemplate = producerRabbitTemplate;
	}
	
	/**
	 * 将消息存入mongo，状态为设置为R，由 S.RESEND发送
	 * 消费者队列由”服务名.交易名“ 或者 “目标实例名” 确认，否则重发时会抛出异常（找不到目标队列）
	 * @param consumerMessage	消费者消息封装
	 * @param checkRepeatOnlyToday true：只对当日数据进行堵重检查；false：对当日和昨日数据进行堵重检查
	 *        当日堵重必须检查，依靠唯一性约束抛出DuplicateKeyException
	 * @throws DuplicateKeyException  唯一性约束异常
	 * @throws ConsumerException  消费者异常，包含其子异常
	 */
	protected void saveMsgResendAndCheckRepeat(ConsumerMessage consumerMessage, boolean checkRepeatOnlyToday) throws ConsumerException, DuplicateKeyException {
		String rid = consumerMessage.getRid();
		MongoCollection<Document> todayColl = null;
		try {
			MongoDatabase database = DBManager.getInstance().getDatabase();
			// 此处获取当前日期集合
			SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
			Calendar cal = Calendar.getInstance();
			String todayCollNm = fmt.format(cal.getTime());
			cal.add(Calendar.DAY_OF_MONTH, -1);
			String yesterdayCollNm = fmt.format(cal.getTime());
			
			if(!checkRepeatOnlyToday){
				MongoCollection<Document> yesterdayColl = database.getCollection(yesterdayCollNm);
				//上一天的堵重检查，当天的依靠唯一性约束
				if(checkRepeatBySecondaryIfNecessary(rid, yesterdayColl)){	//上一天的堵重检查
					throw new DuplicateKeyException(rid,yesterdayCollNm,DuplicateKeyException.DuplicateKeyType.REPEAT_CHECK);
				}
			}
			
			todayColl = database.getCollection(todayCollNm);
			//为当前集合创建索引,  
			createUniqueIndex(todayColl,"RAB_MSG_ID");				//唯一性约束堵重，必须放在此处
			///createIndex(todayColl,"MSG_STS");//FIXME 创建索引放到定时任务中
			
		}catch(DuplicateKeyException dke){
			throw dke;
		}catch(Exception e){
			throw new WarpperBeforeSaveMsgException("Unexpected error occur when exec method 'saveMsgWaitingForResend'",e);
		}
		
		saveMsg(consumerMessage,"R", todayColl);
		//登记重发日志
		logResend(consumerMessage,"STSR");
	}
	
	
	/**
	 * 实时发送消息且消息save mongoDB进行堵重检查
	 * 消费者队列由”服务名.交易名“ 或者 “目标实例名” 确认
	 * mondo中数据状态变化 U-->S(F),状态为F会进行重发
	 * 做了去重检查
	 * @param consumerMessage	消费者消息封装
	 * @param checkRepeatOnlyToday true：只对当日数据进行堵重检查；false：对当日和昨日数据进行堵重检查
	 *        当日堵重必须检查，依靠唯一性约束抛出DuplicateKeyException
	 * @throws DuplicateKeyException  唯一性约束异常
	 * @throws ConsumerException  消费者异常，包含其子异常
	 */
	protected void sendMsgAndCheckRepeat(ConsumerMessage consumerMessage,boolean checkRepeatOnlyToday) throws ConsumerException, DuplicateKeyException{
		String rid    = consumerMessage.getRid();
		String msg    = consumerMessage.getMsg();
		String objSvr = consumerMessage.getObjSvr();
		String objTxn = consumerMessage.getObjTxn();
		String objNod = consumerMessage.getObjNod();
		MongoDatabase database = null;
		MongoCollection<Document> todayColl = null;
		MongoCollection<Document> yesterdayColl = null;
		try{
			// 此处获取当前日期集合
			SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
			Calendar cal = Calendar.getInstance();
			String todayCollNm = fmt.format(cal.getTime());
			//此处获取上一日期集合
			cal.add(Calendar.DAY_OF_MONTH, -1);
			String yesterdayCollNm = fmt.format(cal.getTime());
			
			database = DBManager.getInstance().getDatabase();
			
			if(!checkRepeatOnlyToday){
				//检查上一天的堵重，当天的依靠唯一性约束
				yesterdayColl = database.getCollection(yesterdayCollNm);
				if(checkRepeatBySecondaryIfNecessary(rid, yesterdayColl)){	//上一天的堵重检查
					throw new DuplicateKeyException(rid,yesterdayCollNm,DuplicateKeyException.DuplicateKeyType.REPEAT_CHECK);
				}
			}
			
			todayColl = database.getCollection(todayCollNm);
			createUniqueIndex(todayColl,"RAB_MSG_ID");		//唯一性约束堵重，必须放在此处
			///createIndex(todayColl,"MSG_STS");				//FIXME 为当前集合创建索引,将创建索引操作放到定时扫描任务中

		}catch(DuplicateKeyException dke){
			throw dke;
		}catch(Exception e){//可能抛出异常，该消息会丢失
			throw new WarpperBeforeSaveMsgException("Unexpected error occur at unique check or creating unique index, rid="+rid,e);
		}
		
		//Save message to Db, status = U
		saveMsg(consumerMessage,"U", todayColl);
			
		//send message to mq
		String newSts = "S";
		String sendQueueName = null;
		try{

			//add by deng_wj 20180102  得到路由队列名字
			sendQueueName = HiRabbitUtils.getWarpQueueName(HiRabbitUtils.getConsumeQueueForServerConsumer(consumerMessage, objSvr, objTxn, objNod));
			//end by deng_wj 20180102
			sendMsg(sendQueueName, msg);
		}catch(Exception e){
			newSts = "F";
			consumerLog.error("Occured error at sending message to rabbitmq.rid="+rid,e);
		}
		if(StringUtils.equals("S", newSts)){
			logSended(consumerMessage , sendQueueName);
		}
		
		//更改状态 U => F/S
		try{
			updMsgSts(rid, newSts, todayColl);
		}catch(Exception e){
			if(StringUtils.equals("F", newSts)){
				throw new ConsumerException("Unexpected error occur at updating msg status from 'U' to '"+newSts+"',rid="+rid,e,true);
			}else{
				consumerLog.error("Occured error at updating msg status from 'U' to '"+newSts+"',rid="+rid,e);
			}
		}
		//重发日志登记
		if(StringUtils.equals("F", newSts)){
			logResend(consumerMessage,"STSF");
		}

	}
	
	/**
	 * 堵重检查,由secondaryPreferredAfterHour 确认查主库还是从库
	 * @param rid
	 * @param mongoCollection
	 * @return true 重复，false 不重复
	 */
	protected boolean checkRepeatBySecondaryIfNecessary(String rid,MongoCollection<Document> mongoCollection){
		int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
		if(hour >= secondaryPreferredAfterHour){
			return checkRepeat(rid, mongoCollection,true);
		}else{
			return checkRepeat(rid, mongoCollection,false);
		}
	}
	
	/**
	 * 堵重检查，读主库
	 * @param rid
	 * @param mongoCollection
	 * @return
	 */
	protected boolean checkRepeatByPrimary(String rid,MongoCollection<Document> mongoCollection){
		return checkRepeat(rid, mongoCollection,false);
	}
	
	/**
	 * 堵重检查
	 * @param rid
	 * @param mongoCollection
	 * @param isSecondaryPreferred if true,secondary preferred
	 * @return true 重复，false 不重复
	 */
	protected boolean checkRepeat(String rid,MongoCollection<Document> mongoCollection,boolean isSecondaryPreferred){
		if(mongoCollection == null){
			return false;
		}
		
		long startTm = System.currentTimeMillis();
		
		if(isSecondaryPreferred){								//从库优先
			mongoCollection.withReadPreference(ReadPreference.secondaryPreferred());
		}else{													//主库
			mongoCollection.withReadPreference(ReadPreference.primary());
		}
		Document queryCriteria = new Document("RAB_MSG_ID", rid);	//查询条件
		Document projection = new Document();					//查询指定字段
		projection.append("RAB_MSG_ID", 1).append("MSG_STS", 1);
		FindIterable<Document> finds = mongoCollection.find(queryCriteria);
		finds.projection(projection);
		MongoCursor<Document> cursor= finds.iterator();
		boolean isRepeat = false;
		if(cursor != null && cursor.hasNext()){
			isRepeat =  true;
		}
		
		long execTm = System.currentTimeMillis() - startTm;
		if(isSecondaryPreferred){
			performStats.statsSuccess("checkRepeatBySecondary", execTm);
		}else{
			performStats.statsSuccess("checkRepeatByPrimary", execTm);
		}
		
		if(execTm >mongoOpTmThreshold){
			if(isSecondaryPreferred){
				mongoMonitorLog.warn("checkRepeat by secondary exec "+execTm+" ms");
			}else{
				mongoMonitorLog.warn("checkRepeat by primary exec "+execTm+" ms");
			}
		}
		return isRepeat;
	}
	
	/**
	 * 堵重检查
	 * 检查当天的和上一天的Collection
	 * @param rid
	 * @return true 重复，false 不重复
	 */
	protected boolean checkRepeat(String rid){
		SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
		Calendar cal = Calendar.getInstance();
		String todayCollNm = fmt.format(cal.getTime());			//current Collection name
		cal.add(Calendar.DAY_OF_MONTH, -1);
		String yesterdayCollNm = fmt.format(cal.getTime());		//last Collection name
		
		MongoDatabase database = DBManager.getInstance().getDatabase();
		MongoCollection<Document> coll = database.getCollection(todayCollNm);
		if(checkRepeatByPrimary(rid,coll)){
			return true;
		}
		
		MongoCollection<Document> lastColl = database.getCollection(yesterdayCollNm);
		return checkRepeatBySecondaryIfNecessary(rid,lastColl);
	}
	
	/**
	 * 堵重检查,查主库
	 * @param rid
	 * @param dbCollection
	 * @return true 重复，false 不重复
	 */
	@Deprecated
	protected boolean checkRepeat(String rid,DBCollection dbCollection){
		return checkRepeat(rid,dbCollection,false);
	}
	
	/**
	 * 堵重检查
	 * @param rid
	 * @param dbCollection
	 * @param isSecondaryPreferred if true,secondary preferred
	 * @return true 重复，false 不重复
	 */
	@Deprecated
	protected boolean checkRepeat(String rid,DBCollection dbCollection,boolean isSecondaryPreferred){
		if(dbCollection == null){
			return false;
		}
		
		long startTm = System.currentTimeMillis();
		
		DBObject query = new BasicDBObject("RAB_MSG_ID", rid);
		
		DBObject fields = new BasicDBObject();	//查询指定字段
		fields.put("RAB_MSG_ID", 1);
		fields.put("MSG_STS", 1);
		
		DBObject result = null;
		if(isSecondaryPreferred){
			ReadPreference preference = ReadPreference.secondaryPreferred();//从库优先
			result = dbCollection.findOne(query,fields,preference);
		}else{
			result = dbCollection.findOne(query,fields);
		}
		long execTm = System.currentTimeMillis() - startTm;
		if(execTm >mongoOpTmThreshold){
			mongoMonitorLog.warn("checkRepeat exec "+execTm+" ms");
		}
		
		if (null != result) {
			return true;
		}
		return false;
	}

	protected String getRouteValue(HiMessage himsg, ConsumerMessage cmsg){

		String routeFiled = cmsg.getRouteFiled();
		if(StringUtils.isNotBlank(routeFiled)){
			return himsg.getETFBody().getGrandChildValue(routeFiled);
		}

		return null;
	}
	
	/**
	 * 创建索引
	 * @param coll
	 * @param indexFields
	 */
	private void createIndex(MongoCollection<Document> coll,String... indexFields) {
		for(String indexField : indexFields){
			long startTm = System.currentTimeMillis();
			IndexOptions options = new IndexOptions();
			options.name("index_"+indexField);
			coll.createIndex(new Document(indexField,1),options);//索引的字段和排序(升序1，降序-1)
			long execTm = System.currentTimeMillis() - startTm;
			performStats.statsSuccess("createIndex", execTm);
			if(execTm >mongoOpTmThreshold){
				mongoMonitorLog.warn("create index exec "+execTm+" ms");
			}
		}
	}
	
	/**
	 * 创建唯一索引
	 * @param coll
	 * @param indexFields
	 */
	private void createUniqueIndex(MongoCollection<Document> coll,String... indexFields){
		for(String indexField : indexFields){
			long startTm = System.currentTimeMillis();
			IndexOptions options = new IndexOptions();
			options.name("unique_index_"+indexField);
			options.unique(true);
			coll.createIndex(new Document(indexField,1),options);//索引的字段和排序(升序1，降序-1)
			long execTm = System.currentTimeMillis() - startTm;
			performStats.statsSuccess("createIndex", execTm);
			if(execTm >mongoOpTmThreshold){
				mongoMonitorLog.warn("create unique index exec "+execTm+" ms");
			}
		}
	}
	
	/**
	 * 保存消息到mongo，可以指定目标实例名
	 * @param consumerMessage 消费者消息封装
	 * @param status  保存的状态：R重发，F失败，U，正常保存 S 发送成功
	 * @param coll
	 * @throws DuplicateKeyException  唯一性约束异常
	 * @throws SaveMessageFailException 	  saveMsg抛出的异常，业务异常或DB抛出异常的封装
	 */
	private void saveMsg(ConsumerMessage consumerMessage, String status, 
			MongoCollection<Document> coll) throws DuplicateKeyException,SaveMessageFailException {
		if(consumerMessage == null){
			throw new SaveMessageFailException("Consumer message object must not be null.");
		}
		String msg    = consumerMessage.getMsg();
		String objSvr = consumerMessage.getObjSvr();			//目标服务
		String objTxn = consumerMessage.getObjTxn();			//目标交易
		String objNod = consumerMessage.getObjNod();			//目标实例名
		if(StringUtils.isBlank(msg)){
			throw new SaveMessageFailException("The msg for saveing to mongo must not be null.");
		}
		if(StringUtils.isBlank(status) || (!StringUtils.equals(status, "R") && !StringUtils.equals(status, "U"))){
			throw new SaveMessageFailException("Save mongo status must equals \"R\" or \"U\"");
		}
		if(StringUtils.isBlank(objSvr) || StringUtils.isBlank(objTxn)){
			throw new SaveMessageFailException("OjbSvr and ObjTxn must not be null.");
		}
		if(null == coll){
			throw new SaveMessageFailException("Mongo collection must not be null.");
		}
		
		long startTm = System.currentTimeMillis();
		
		try{
			Document doc = new Document();
			doc.put("RAB_MSG_ID", consumerMessage.getRid());
			doc.put("RAB_MSG_TYP", consumerMessage.getType());
			doc.put("MSG_STS", status);			//保存状态
			doc.put("MSG_DAT", msg);				//发送消息
			doc.put("OBJ_SVR",objSvr);			//目标服务名
			doc.put("OBJ_TXN",objTxn);			//目标交易名
			doc.put("OBJ_NOD", objNod);			//目标节点
			doc.put("SND_NUM", 0);				//重发次数
	
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
			Date now = new Date();
			String curTm = sdf.format(now);
			doc.put("SAV_TM", curTm);			//保存时间戳
			doc.put("NXT_TM", now);				//下次重发时间
			//登记消息时间戳
			doc.put("TM_SMP", curTm);			//时间戳
	
			coll.withWriteConcern(WriteConcern.ACKNOWLEDGED);	//写关注
			coll.insertOne(doc);
			
		}catch(com.mongodb.MongoWriteException e){
			if(e.getError().getCategory().equals(ErrorCategory.DUPLICATE_KEY)){
				throw new DuplicateKeyException(consumerMessage.getRid(),DuplicateKeyException.DuplicateKeyType.DUPLICATE_KEY,e);	//唯一性约束
			}else{
				throw new SaveMessageFailException(e);
			}
		}catch(Exception e){
			throw new SaveMessageFailException(e);
		}
		
		long execTm = System.currentTimeMillis() - startTm;
		performStats.statsSuccess("insertOne", execTm);
		if(execTm >mongoOpTmThreshold){
			mongoMonitorLog.warn("insert exec "+execTm+" ms");
		}
	}

	/**
	 * 更新消息在mongo中的状态
	 * @param rid
	 * @param newSts
	 * @param coll
	 * @throws HiException
	 */
	private void updMsgSts(String rid, String newSts,MongoCollection<Document> coll) throws HiException{
		Document updateCriteria = new Document("RAB_MSG_ID", rid);			//条件
		
		Document updatedValue = new Document();
		updatedValue.put("MSG_STS", newSts);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		updatedValue.put("TM_SMP", sdf.format(new Date()));
		Document updateSetValue = new Document("$set", updatedValue);

		long startTm = System.currentTimeMillis();
		coll.withWriteConcern(WriteConcern.ACKNOWLEDGED);
		coll.updateOne(updateCriteria, updateSetValue);
		
		long execTm = System.currentTimeMillis() - startTm;
		performStats.statsSuccess("updateOne", execTm);
		if(execTm >mongoOpTmThreshold){
			mongoMonitorLog.warn("update exec "+execTm+" ms");
		}
	}

	/**
	 * 将消息发送至rabbitmq
	 * 发送队列查找方式为1、服务名.交易名	2、目标实例名	3、默认的目标实例名
	 * @param forwardQueueName 目标队列名字
	 * @param msg 消息
	 * @return 发送队列名称
	 * @throws ConsumerException
	 */
	protected String sendMsg(String forwardQueueName, String msg) throws ConsumerException {
		long startTime  = System.currentTimeMillis();
		try{
			producerRabbitTemplate.send(forwardQueueName, new Message(msg.getBytes(),
					HiRabbitUtils.getTextPlainMessageProperties(forwardQueueName)));
		}catch(Exception e){
			throw new ConsumerException("Send msg to rabbit mq occur err.",e,true);
		}
		long execTm = System.currentTimeMillis() - startTime;
		if(execTm > 3000){
			consumerLog.warn("Send message execution time greater than 3 seconds,sendQueueName["+forwardQueueName+"]");
		}
		return forwardQueueName;
	}
	
	/**
	 * 将消息发送至rabbitmq
	 * 发送队列查找方式为1、服务名.交易名	2、目标实例名	3、默认的目标实例名
	 * @param objSvr 目标服务名
	 * @param objTxn 目标交易名
	 * @param objNod 目标实例名
	 * @param msg 消息
	 * @return 发送队列名称
	 * @throws ConsumerException
	 */
	@Deprecated
	protected String sendMsg(String objSvr,String objTxn,String objNod, String msg) throws ConsumerException {
		String sendQueueName = null;
		long startTime  = System.currentTimeMillis();
		try{
            ConsumerMessage consumerMessage = HiRabbitUtils.createConsumerMessage(msg);
            sendQueueName = HiRabbitUtils.getWarpQueueName(HiRabbitUtils.getConsumeQueueForServerConsumer(consumerMessage, objSvr, objTxn, objNod));
            producerRabbitTemplate.send(sendQueueName, new Message(msg.getBytes(),
					HiRabbitUtils.getTextPlainMessageProperties(sendQueueName)));
		}catch(Exception e){
			throw new ConsumerException("Send msg to rabbit mq occur err.",e,true);
		}
		long execTm = System.currentTimeMillis() - startTime;
		if(execTm > 3000){
			consumerLog.warn("Send message execution time greater than 3 seconds,sendQueueName["+sendQueueName+"]");
		}
		return sendQueueName;
	}

	
	/**
	 * 登记发送成功的日志
	 * @param conMsg
	 * @param sendQueueName
	 */
	protected void logSended(ConsumerMessage conMsg,String sendQueueName){
		if(sendedLog.isInfoEnabled()){
			StringBuilder logsb = new StringBuilder();
			logsb.append(conMsg.getType()).append("|")
			.append(conMsg.getRid()).append("|")
			.append(conMsg.getOriMsgId()).append("|")
			.append(conMsg.getObjSvr()).append("|")
			.append(conMsg.getObjTxn()).append("|")
			.append(conMsg.getObjNod()).append("|")
			.append(sendQueueName);
			sendedLog.info(logsb.toString());
		}
	}
	
	/**
	 * 重发日志
	 * @param cm
	 * @param reason
	 */
	protected void logResend(ConsumerMessage cm,String reason){
		if(saveForResendLog.isInfoEnabled()){
			StringBuilder logSb = new StringBuilder();
			logSb.append(cm.getType()).append("|")
			.append(cm.getRid()).append("|")
			.append(cm.getOriMsgId()).append("|")
			.append(reason);
			saveForResendLog.info(logSb.toString());
		}
	}
	
	/**
	 * 登记丢失的消息
	 * @param msg
	 */
	protected void logLost(String msg){
		logLost(msg,null);
	}
	
	/**
	 * 登记丢失的消息
	 * @param msg
	 * @param e
	 */
	protected void logLost(String msg,ConsumerException e){
		if(e != null && e.needLogLost()){
			lostLog.info(msg);
			e.afterLogLost();
		}else if(e == null){
			lostLog.info(msg);
		}
	}
	
	protected void logDiscard(ConsumerMessage consumerMessage,String reason){
		discardLog.info(consumerMessage.getRid()+"|"+consumerMessage.getType()+"|"+reason);
	}
	
	/**
	 * Mongo 唯一性约束异常（RID）
	 * <AUTHOR>
	 *
	 */
	protected static class DuplicateKeyException extends Exception{
		private static final long serialVersionUID = 7042731962130574235L;
		
		private DuplicateKeyType type;
		private String collectionName;
		
		public enum DuplicateKeyType{
			DUPLICATE_KEY,REPEAT_CHECK
		}

		public DuplicateKeyException(String rid,DuplicateKeyType type,Throwable t){
			super("rid="+rid+",type="+type,t);
			this.type = type;
		}
		
		public DuplicateKeyException(String rid,String collNm,DuplicateKeyType type){
			super("rid="+rid+",type="+type+",collectionName="+collNm);
			this.type = type;
			this.collectionName = collNm;
		}
		
		public DuplicateKeyType getType(){
			return this.type;
		}
		
		public String getCollectionName(){
			return this.collectionName;
		}
	}
	
	/**
	 * 抛出该异常表示消息没有成功插入DB
	 * <AUTHOR>
	 *
	 */
	protected static class SaveMessageFailException extends ConsumerException{
		private static final long serialVersionUID = -2359264969721993535L;
		
		public SaveMessageFailException(String msg){
			super(msg,true);
		}
		
		public SaveMessageFailException(Throwable t){
			super(t,true);
		}
		
		public SaveMessageFailException(String rid,Throwable t){
			super(rid,t,true);
		}
		
	}
	
	/**
	 * 封装saveMsg之前抛出的所有异常,抛出该异常时，消息丢失
	 * <AUTHOR>
	 *
	 */
	protected static class WarpperBeforeSaveMsgException extends ConsumerException{
		private static final long serialVersionUID = 8614646769894818250L;
		
		public WarpperBeforeSaveMsgException(String msg,Throwable t){
			super(msg,t,true);
		}
		
		public WarpperBeforeSaveMsgException(String msg){
			super(msg,true);
		}
	}
	
}
