package com.hisun.queue.listener;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.hisun.constants.HiConstants;
import com.hisun.exception.HiException;
import com.hisun.framework.HiDefaultServer;
import com.hisun.framework.event.IServerEventListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.hilog4j.Logger;
import com.hisun.queue.constants.HiParaConstants;
import com.hisun.util.HiThreadPool;

public class ServerRecieveListener extends ConsumerListener implements
		IServerEventListener {

	protected HiDefaultServer server = null;
	protected HiThreadPool threadPool;
	// -- 输入参数
	private int minThreads = 5, maxThreads = 50, queueSize = -1;

	private HiHAEndpoint prohaEndpoint;
	private HiHAEndpoint conhaEndpoint;
	private Logger log;
	protected boolean running = false;
	
	private String sendUserName;
	private String sendPassword;
	private String sendVirtualHost;
	
	private String consumeUserName;
	private String consumePassword;
	private String consumeVirtualHost;

	public void serverInit(ServerEvent event) throws HiException {
		log = event.getLog();

		if (log.isInfoEnabled()) {
			log.info("server initializing " + this.toString());
		}

		server = (HiDefaultServer) event.getServer();

		if (this.getQueueSize() != -1) {
			threadPool = HiThreadPool.createThreadPool(event.getServerContext()
					.getStrProp(HiConstants.SERVERNAME), this.getMinThreads(),
					this.getMaxThreads(), this.getQueueSize());
		} else {
			threadPool = HiThreadPool.createThreadPool(event.getServerContext()
					.getStrProp(HiConstants.SERVERNAME), this.getMinThreads(),
					this.getMaxThreads());
		}
		
		String[] arrsvrConsume = StringUtils.split(this.consumeServers, " ");
		if(arrsvrConsume == null || arrsvrConsume.length <1) {
			throw new HiException("ServerRecieveListener serverInit Failed. Can't parse Server:["+consumeServers+"]");
		}
		
		List<RabHostConfig> consumeConfigs = new ArrayList<RabHostConfig>();
		for( int i = 0; i < arrsvrConsume.length; i++) {
			String svr = arrsvrConsume[i];
			String[] tmp = StringUtils.split(svr, ":");
			consumeConfigs.add(new RabHostConfig( tmp[0],  NumberUtils.toInt(tmp[1]),NumberUtils.toInt(tmp[2]),  this.getConsumeUserName(),this.getConsumePassword(),  this.getConsumeVirtualHost()));
		}
		prohaEndpoint = new HiHAEndpoint(consumeConfigs, this.consumeQueue,
				HiParaConstants.SERVER_PRODUCER_TYP, log);
		prohaEndpoint.startDamens();
		
		String[] arrsvrSend = StringUtils.split(this.sendServers, " ");
		if(arrsvrSend == null || arrsvrSend.length <1) {
			throw new HiException("ServerRecieveListener serverInit Failed. Can't parse Server:["+sendServers+"]");
		}
		List<RabHostConfig> sendConfigs = new ArrayList<RabHostConfig>();
		for( int i = 0; i < arrsvrSend.length; i++) {
			String svr = arrsvrSend[i];
			String[] tmp = StringUtils.split(svr, ":");
			sendConfigs.add(new RabHostConfig( tmp[0],  NumberUtils.toInt(tmp[1]),NumberUtils.toInt(tmp[2]),  this.getSendUserName(),this.getSendPassword(),  this.getSendVirtualHost()));
		}
		conhaEndpoint = new HiHAEndpoint(sendConfigs, this.sendQueue,
				this.threadPool, prohaEndpoint,
				HiParaConstants.SERVER_CONSUMER_TYP,log);
		conhaEndpoint.startDamens();
	}

	public void serverStart(ServerEvent event) throws HiException {
		running = true;

	}

	public void serverStop(ServerEvent arg0) throws HiException {
		running = false;
		conhaEndpoint.close();
		prohaEndpoint.close();

		threadPool.shutdown();
		try {
			threadPool.awaitTermination(5, TimeUnit.SECONDS);
		} catch (InterruptedException e1) {
			;
		}
	}

	public String getSendUserName() {
		return sendUserName;
	}

	public void setSendUserName(String sendUserName) {
		this.sendUserName = sendUserName;
	}

	public String getSendPassword() {
		return sendPassword;
	}

	public void setSendPassword(String sendPassword) {
		this.sendPassword = sendPassword;
	}

	public String getSendVirtualHost() {
		return sendVirtualHost;
	}

	public void setSendVirtualHost(String sendVirtualHost) {
		this.sendVirtualHost = sendVirtualHost;
	}

	public String getConsumeUserName() {
		return consumeUserName;
	}

	public void setConsumeUserName(String consumeUserName) {
		this.consumeUserName = consumeUserName;
	}

	public String getConsumePassword() {
		return consumePassword;
	}

	public void setConsumePassword(String consumePassword) {
		this.consumePassword = consumePassword;
	}

	public String getConsumeVirtualHost() {
		return consumeVirtualHost;
	}

	public void setConsumeVirtualHost(String consumeVirtualHost) {
		this.consumeVirtualHost = consumeVirtualHost;
	}

	
	
}
