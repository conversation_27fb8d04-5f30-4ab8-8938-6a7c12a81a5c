package com.hisun.rabbit.support;


import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.rabbit.listener.exception.AmqpRejectAndDontRequeueException;
import com.hisun.rabbit.listener.exception.ListenerExecutionFailedException;

/**
 * {@link ErrorHandler} that conditionally wraps the Exception in an
 * {@link AmqpRejectAndDontRequeueException} if the configured rejection
 * strategy determines that the message is fatal and should not be requeued.
 * Such messages will be discarded or sent to a Dead Letter Exchange, depending
 * on broker configuration.
 * <p>
 * The default strategy will do this if the exception is a
 * {@link ListenerExecutionFailedException} with a cause of {@link MessageConversionException}.
 * <p>
 * The exception will not be wrapped if the {@code cause} chain already contains an
 * {@link AmqpRejectAndDontRequeueException}.
 *
 */
public class ConditionalRejectingErrorHandler implements ErrorHandler {

	protected final Logger logger = HiLog.getLogger("rabbitMonitor.trc");

	private final FatalExceptionStrategy exceptionStrategy;

	/**
	 * Create a handler with the {@link ConditionalRejectingErrorHandler.DefaultExceptionStrategy}.
	 */
	public ConditionalRejectingErrorHandler() {
		this.exceptionStrategy = new DefaultExceptionStrategy();
	}

	/**
	 * Create a handler with the supplied {@link FatalExceptionStrategy} implementation.
	 * @param exceptionStrategy The strategy implementation.
	 */
	public ConditionalRejectingErrorHandler(FatalExceptionStrategy exceptionStrategy) {
		this.exceptionStrategy = exceptionStrategy;
	}

	public void handleError(Throwable t) {
		if (logger.isWarnEnabled()) {
			logger.warn("Execution of Rabbit message listener failed.", t);
		}
		if (!this.causeChainContainsARADRE(t) && this.exceptionStrategy.isFatal(t)) {
			throw new AmqpRejectAndDontRequeueException("Error Handler converted exception to fatal", t);
		}
	}

	/**
	 * @return true if the cause chain already contains an
	 * {@link AmqpRejectAndDontRequeueException}.
	 */
	private boolean causeChainContainsARADRE(Throwable t) {
		Throwable cause = t.getCause();
		while (cause != null) {
			if (cause instanceof AmqpRejectAndDontRequeueException) {
				return true;
			}
			cause = cause.getCause();
		}
		return false;
	}

	private class DefaultExceptionStrategy implements FatalExceptionStrategy {

		public boolean isFatal(Throwable t) {
			if (t instanceof ListenerExecutionFailedException) {
				if (logger.isWarnEnabled()) {
					logger.warn("Fatal message conversion error; message rejected; "
							+ "it will be dropped or routed to a dead letter exchange, if so configured: "
							+ ((ListenerExecutionFailedException) t).getFailedMessage(), t);
				}
				return true;
			}
			return false;
		}

	}

}

interface FatalExceptionStrategy {

	boolean isFatal(Throwable t);

}
