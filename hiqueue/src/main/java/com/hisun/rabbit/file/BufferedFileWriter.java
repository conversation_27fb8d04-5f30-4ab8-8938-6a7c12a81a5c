package com.hisun.rabbit.file;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

import com.hisun.rabbit.file.IFileWriter;
import com.hisun.rabbit.file.MsgStore;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.queue.constants.HiRabbitUtils;

/**
 * <AUTHOR>
 *
 */
public class  BufferedFileWriter implements IFileWriter{
	
	private static final Logger logger = HiLog.getLogger("rabbitFileResendMonitor.trc");
	
	private BufferedWriter bw;
	private String fileName;
	private int limitSize;
	private long lastModified = -1;
	private int checkItv = 20;//s
	
	private ReentrantLock renameLock;
	private ReentrantLock lock;
	private AtomicBoolean changeFile = new AtomicBoolean(false);
	private long fileCreateTime = System.currentTimeMillis();
	//强制修改文件名间隔，单位s
	private int forceChangeFileItv = HiRabbitUtils.RABBIT_MSG_FILE_FORCE_CHANGE_NAME_INTERVAL;
	
	/**
	 * @param fileName 文件名称
	 * @param limitSize 文件大小
	 */
	public BufferedFileWriter(String fileName , int limitSize) {
		this.fileName = fileName;
		this.limitSize = limitSize;
		this.renameLock = new ReentrantLock();
		this.lock = new ReentrantLock();
	}
	
	public BufferedFileWriter(String fileName , int limitSize,ReentrantLock renameLock) {
		this.fileName = fileName;
		this.limitSize = limitSize;
		this.renameLock = renameLock;
		this.lock = new ReentrantLock();
	}

	@Override
	public void write(MsgStore msgStore) throws IOException {
		try{
			lock.lock();
			open();
			
			StringBuilder sb = new StringBuilder(msgStore.getRoutingKey());
			sb.append(HiRabbitUtils.RABBIT_MSG_FILE_SEPRATE_TYPE).append(msgStore.getMessage()).append(HiRabbitUtils.RABBIT_MSG_FILE_SEPRATE_NEWLINE);
			
			bw.write(sb.toString());
			
			sb.delete(0, sb.length());
			
			bw.flush();
			
		}finally{
			lock.unlock();
		}
	}

	@Override
	public void clear() throws IOException {
		flush();
	}

	@Override
	public void flush() throws IOException {
		if(null != bw)
		bw.flush();
	}
	
	public BufferedWriter open() throws IOException {
		if (lastModified != -1 && System.currentTimeMillis() - lastModified <= checkItv * 1000) {
            if (bw != null) {
                return bw;
            }
        }
        lastModified = System.currentTimeMillis();
        
        try {
	        	renameLock.lock();
	        	
	        	File f1 = new File(this.fileName);
	        	if (f1.getParent() != null) {
	        		File f2 = new File(f1.getParent());
	        		if (!f2.exists()) {
	        			f2.mkdirs();
	        		}
	        		f2 = null;
	        	}
	        	if( !f1.exists() ) {
	        		if( bw != null ) {
	        			bw.close();
	        			bw = null;
	        		}
	        		//文件不存在的情况下肯定会创建文件，此处设置文件生成时间
	        		fileCreateTime = System.currentTimeMillis();
	        	}
	        	long l = f1.length();
	        	if (l > limitSize || changeFile.compareAndSet(true, false)) {
	        		if( l > 0){
	        			if( bw != null ) {
	        				bw.close();
	        				bw = null;
	        			}
	        			SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
	        			File f2 = new File(this.fileName + "." + df.format(new Date()));
	        			f1.renameTo(f2);
	        			fileCreateTime = System.currentTimeMillis();//文件改名后肯定会新生成文件
	        			//确保变更文件名后，changFile的值一定是false
	        			changeFile.set(false);
	        			logger.info("~~File "+f1.getName()+" rename to "+f2.getName());
	        		}
	        	}
	        	f1 = null;
	        	if( bw == null ) {
	        		bw = new BufferedWriter(new FileWriter(this.fileName, true));
	        	}
	        	return bw;
	        	
        }finally{
        		renameLock.unlock();
        }
	}

	@Override
	public void close() throws IOException {
		if(null != bw){
			bw.flush();
			bw.close();
		}
	}
	
	/**
	 * 强制更新文件名
	 */
	public void changeFile(){
		try {
			lock.lock();
			File f1 = new File(this.fileName);
			if(!f1.exists() || f1.length()==0){	//文件不存在或者空文件直接返回
				return;
			}
			//再次判断下时间间隔,防止并发问题
			if(System.currentTimeMillis()-getFileCreateTime()<getForceChangeFileItv()*1000){
				return;
			}
			logger.debug("CurrentTime[ "+System.currentTimeMillis()+" ],createFileTime[ "+this.getFileCreateTime()
					+" ],internvl[ "+(System.currentTimeMillis()-this.getFileCreateTime())+" ]");
			logger.info("Force change file name.");
			changeFile.set(true);
			lastModified = -1;
			open();
		} catch (IOException e) {
			logger.warn("Unexpected err occur at forcing change file.",e);
		} finally{
			lock.unlock();
		}
	}
	
	/**
	 * 文件创建时间
	 * @return
	 */
	public long getFileCreateTime(){
		return fileCreateTime;
	}
	
	public int getForceChangeFileItv() {
		return forceChangeFileItv;
	}
	/**
	 * 如果没有可以处理的文件，强制将目前的dat文件改名为可以处理的文件
	 * 此时间间隔为dat文件生成多久后可以改名，单位是s
	 * @param forceChangeFileItv
	 */
	public void setForceChangeFileItv(int forceChangeFileItv) {
		this.forceChangeFileItv = forceChangeFileItv;
	}
	
}
