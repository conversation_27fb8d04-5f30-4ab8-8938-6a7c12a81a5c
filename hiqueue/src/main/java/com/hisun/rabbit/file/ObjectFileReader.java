package com.hisun.rabbit.file;

import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import com.hisun.exception.HiException;
import com.hisun.rabbit.file.IFileReader;
import com.hisun.rabbit.file.IFileWriter;
import com.hisun.rabbit.file.MsgProcessCallBack;
import com.hisun.rabbit.file.MsgStore;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.queue.constants.HiRabbitUtils;


/**
 * 读文件重发消息
 * .dat 正在记录的文件
 * .dat.yyyyMMddhhmmss 已经完成记录的文件
 * .dat.yyyyMMddhhmmss.current 正在进行消息重发处理的文件
 * ./backup/.dat.yyyyMMddhhmmss 成功处理重发后的文件
 * .error 失败处理重发后的文件
 * 处理失败文件后缀变化过程 .dat--> .dat.yyyyMMddhhmmss -->.dat.yyyyMMddhhmmss.current --> .error
 * 处理成功文件后缀变化过程 .dat--> .dat.yyyyMMddhhmmss -->.dat.yyyyMMddhhmmss.current --> ./backup/.dat.yyyyMMddhhmmss
 * <AUTHOR>
 *
 */
@Deprecated
public class ObjectFileReader implements IFileReader {
	
	private static final Logger logger = HiLog.getLogger("rabbitFileResendMonitor.trc");
	
	private MsgProcessCallBack callBack;
	private IFileWriter fileWriter;
	
	private int forceChangeFileItv = 60*10;//s
	private Lock lock = new ReentrantLock();
	
	private int maxConcurrent  = 1;
	private Semaphore permit = new Semaphore(maxConcurrent);	//同时执行该线程的并发量控制
	
	public ObjectFileReader(MsgProcessCallBack callBack){
		this.callBack = callBack;
	}
	
	public ObjectFileReader(MsgProcessCallBack callBack,IFileWriter fileWriter){
		this.callBack = callBack;
		this.fileWriter = fileWriter;
	}
	
	public ObjectFileReader(MsgProcessCallBack callBack,IFileWriter fileWriter,int maxConcurrent){
		this.callBack = callBack;
		this.fileWriter = fileWriter;
		this.maxConcurrent = maxConcurrent;
		permit = new Semaphore(maxConcurrent);
	}

	@Override
	public void run() {
		try{
			doRun();
		}catch(Throwable t){
			logger.error("Unexpected error occur at processing file.",t);
		}
	}
	
	private void doRun() throws Exception {
		logger.info("=======================================");
		boolean p = false;
		try{
			p = permit.tryAcquire();
			if(!p){
				logger.info("Already reached max concurrent "+maxConcurrent+" so stop now.");
				return ;
			}
			doResend();
		}finally{
			if(p){
				permit.release();
			}
		}
	}
	
	/**
	 * 测试rabbitmq是否已经连通
	 * @return
	 * @throws HiException 
	 */
	private boolean doTest() throws HiException{
		return callBack.doTest();
	}

	private void doResend() throws Exception {
		File currentFile = null;
		try{
			lock.lock();
			currentFile= getCurrentFile();
		}catch(RabbitMqNotAvailableException e){
			logger.info(e.getMessage());
			return;
		}finally{
			lock.unlock();
		}
		if(currentFile == null){
			logger.info("No file needed to be processed.");
			return;
		}
		
		logger.info("Start to process the file \""+currentFile.getPath()+"\"");
		
		ObjectInputStream ois = null;
		FileInputStream fis = null;
		try{
			try{
				fis = new FileInputStream(currentFile);
				ois = new ObjectInputStream(fis);
			}catch(IOException e){//此处发生异常，将文件名恢复
				renameCurrentToBefore(currentFile);
				logger.warn("Unexpected err occur at reading file \""+currentFile.getName()+"\",restroy the file name for retry next time.");
				throw e;
			}
			
			long successNum = 0;//处理成功
			long failureNum = 0;
			try{
				Object lastObj = null;
				while(true){
					Object obj = null;
					try{
						if((obj = ois.readObject())== null){
							break;
						}
						lastObj = obj;//出错时好查出错地方
						MsgStore ms = (MsgStore)obj;
						process(ms.getRoutingKey(),ms.getMessage());
						successNum ++;
					}catch(Exception e1){
						failureNum++;
						logger.error("Unexpected error occur at sending msg.","thisTime=="+obj,"lastSuccessTime=="+lastObj,e1);
					}
				}
				logger.info("Send completely,success num = "+successNum+",failure num = "+failureNum+",file="+currentFile.getName());
			}catch(Exception e){//此处发生异常需要进行处理，防止多次发送
				renameCurrentToError(currentFile);
				logger.error("Unexpected err occur at processing file "+currentFile.getName()+",this problem requires manual intervention to solve.");
				logger.info("Sending termination,success num = "+successNum+",failure num = "+failureNum+",file="+currentFile.getName());
				throw e;
			}
		}finally{
			if(null != ois){
				ois.close();
			}
			if(null != fis){
				fis.close();
			}
		}
		afterFileProcess(currentFile);
		
	}

	/**
	 * 将当前处理文件的文件名改为处理前的文件名
	 * @param currentFile
	 */
	private void renameCurrentToBefore(File currentFile) {
		String curFileName = currentFile.getName();
		String beforeFileName = curFileName.substring(0,curFileName.indexOf(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX));
		File beforeFile = new File(currentFile.getParent()+File.separator+beforeFileName );
		currentFile.renameTo(beforeFile);
	}
	
	/**
	 * 将当前处理文件的文件名改为处理错误的文件名
	 * @param currentFile
	 */
	private void renameCurrentToError(File currentFile){
		String curFileName = currentFile.getName();
		String errorFileName = curFileName.substring(0,
				curFileName.indexOf(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX))
				+HiRabbitUtils.RABBIT_MSG_FILE_ERROR_SUFFIX;
		File errorFile = new File(currentFile.getParent()+File.separator+errorFileName );
		currentFile.renameTo(errorFile);
	}

	/**
	 * 处理文件读取的字符串
	 * @param str
	 */
	private void process(String routingKey,String msg) {
		//回调对消息进行处理
		try{
			callBack.doMsgProcess(routingKey,msg);
		}catch(Throwable t){
			logger.error("msg process error.",routingKey,msg,t);
		}
		
	}

	/**
	 * 文件处理的后续操作
	 * 1，将文件名后缀去掉.current 并移动文件到backup目录
	 * @param currentFile
	 */
	private void afterFileProcess(File currentFile) {
		String curFileName = currentFile.getName();
		String fileName = curFileName.substring(0,curFileName.indexOf(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX));
		File afterFile = new File(HiRabbitUtils.getRabbitFileBackupDir()+File.separator+fileName );
		if(afterFile.getParent() != null){
			File afterParentFile = new File(afterFile.getParent());
			if(!afterParentFile.exists()){
				afterParentFile.mkdirs();
			}
		}
		currentFile.renameTo(afterFile);
		logger.info("After resending complete, the file \""+currentFile.getName() +"\" rename to \"backup/"+ afterFile.getName()+"\"");
	}

	/**
	 * 获取本次需要处理的文件名，没有返回null
	 * @return
	 * @throws HiException 
	 */
	private File getCurrentFile() throws HiException {
		File rabDir = new File(HiRabbitUtils.getRabbitFileDir());
		//查找合适的文件，1，非目录，2，不以.current、.dat 、.error 结尾
		File[] files = rabDir.listFiles(new FileFilter(){
			public boolean accept(File pathFile) {
				if(pathFile.isDirectory()){
					return false;
				}
				String pathName = pathFile.getName();
				if(pathName.endsWith(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX)
						|| pathName.endsWith(HiRabbitUtils.RABBIT_MSG_FILE_ERROR_SUFFIX)
						|| pathName.endsWith(HiRabbitUtils.RABBIT_MSG_FILE_SUFFIX)){
					return false;
				}
				return true;
			}});
		//没有合适的文件时，返回null，同时会发起强制修改目前的dat文件名称改为适合处理的文件
		if(files == null || files.length==0){
			if(fileWriter != null 
				&& System.currentTimeMillis()-fileWriter.getFileCreateTime()>getForceChangeFileItv()*1000){
				fileWriter.changeFile();//强制改文件名
			}
			return null;
		}
		
		File lastFile = null;
		//只有一个文件，直接返回
		if(files.length == 1){
			lastFile =  files[0];
			logger.info("Find a file \""+lastFile+"\"");
		}else{
			//多个文件排序
			List<File> fileList = Arrays.asList(files);
			Collections.sort(fileList, new Comparator<File>(){
				@Override
				public int compare(File o1, File o2) {
					return o1.getName().compareTo(o2.getName());
				}
			});
			lastFile = fileList.get(0);
			logger.info("Select a file\""+lastFile.getPath()+"\" from the lists \""+fileList+"\"");
		}
		
		//测试mq的连通性
		logger.info("Start to test connectivity of rabbitmq.");
		if(!doTest()){
			throw new RabbitMqNotAvailableException("Test failed so stop now,may be rabbit MQ blocked.");
		}
		logger.info("Test passed.");
		
		//更改文件名，后缀加上.current
		File f2 = new File(lastFile.getPath() +HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX );
		lastFile.renameTo(f2);
		logger.info("The File \""+lastFile.getName() +"\" rename to \""+f2.getName()+"\"");
		return f2;
	}
	
	/**
	 * 等待线程中止
	 * @return
	 */
	public boolean awaitTerminal(long timeout,TimeUnit timeUnit){
		long startTime = System.currentTimeMillis();
		long millTimeout = TimeUnit.MILLISECONDS.convert(timeout, timeUnit);
		while(true){
			if(permit.availablePermits() == this.maxConcurrent){
				logger.info("Successfully waited for the fileReader workers to finish.");
				return true;
			}
			if(System.currentTimeMillis() - startTime > millTimeout){
				logger.warn("FileReader workers not finished.");
				return false;
			}
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
			}
		}
		
	}
	
	public int getForceChangeFileItv() {
		return forceChangeFileItv;
	}
	/**
	 * 如果没有可以处理的文件，强制将目前的dat文件改名为可以处理的文件
	 * 此时间间隔为dat文件生成多久后可以改名，单位是s
	 * @param forceChangeFileItv
	 */
	public void setForceChangeFileItv(int forceChangeFileItv) {
		this.forceChangeFileItv = forceChangeFileItv;
	}
	
	/**
	 * 设置允许的最大线程并发数
	 * @param maxConcurrent
	 */
	public void setMaxConcurrent(int maxConcurrent){
		this.maxConcurrent = maxConcurrent;
		permit = new Semaphore(maxConcurrent);
	}
	
	@SuppressWarnings("serial")
	private static class RabbitMqNotAvailableException extends RuntimeException{

		public RabbitMqNotAvailableException(String message){
			super(message);
		}
	}

	@Override
	public void shutdown() {
		//TODO 未实现
	}

	@Override
	public boolean isShutdown() {
		// TODO 未实现
		return false;
	}

	@Override
	public boolean isTermianl() {
		// TODO Auto-generated method stub
		return false;
	}
	
}
