package com.hisun.rabbit.file;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import com.hisun.exception.HiException;
import com.hisun.rabbit.file.IFileReader;
import com.hisun.rabbit.file.IFileWriter;
import com.hisun.rabbit.file.MsgProcessCallBack;
import com.hisun.rabbit.file.MsgStore;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.queue.constants.HiRabbitUtils;


/**
 * 读文件抽象类
 * 实现了一些公共方法，抽象除doSend方法
 * <AUTHOR>
 *
 */
public abstract class AbstractFileReader implements IFileReader {
	
	protected static final Logger logger = HiLog.getLogger("rabbitFileResendMonitor.trc");
	protected final static Logger lostMsg = HiLog.getLogger("rabbitLostProducer.lst");
	protected final static Logger reWriteMsg = HiLog.getLogger("rabbitRewriter.lst");
	
	private MsgProcessCallBack callBack;
	private IFileWriter fileWriter;
	
	//强制修改文件名间隔，单位s
	private int forceChangeFileItv = HiRabbitUtils.RABBIT_MSG_FILE_FORCE_CHANGE_NAME_INTERVAL;//s
	protected Lock lock = new ReentrantLock();	//获取处理文件时候的锁
	
	private int maxConcurrent  = 1;
	private Semaphore permit = new Semaphore(maxConcurrent);	//同时执行该线程的并发量控制
	
	private int prelen = 8;
	
	private volatile boolean shutdown = false;//shutdown线程
	
	public AbstractFileReader(MsgProcessCallBack callBack){
		this.callBack = callBack;
	}
	
	public AbstractFileReader(MsgProcessCallBack callBack,IFileWriter fileWriter){
		this.callBack = callBack;
		this.fileWriter = fileWriter;
	}
	
	public AbstractFileReader(MsgProcessCallBack callBack,IFileWriter fileWriter,int maxConcurrent){
		this.callBack = callBack;
		this.fileWriter = fileWriter;
		this.maxConcurrent = maxConcurrent;
		permit = new Semaphore(maxConcurrent);
	}
	
	@Override
	public void run() {
		try{
			doRun();
		}catch(Throwable t){
			logger.error("Unexpected error occur at processing file.",t);
		}
	}
	
	private void doRun() throws Exception {
		logger.info("=======================================");
		long startTm = System.currentTimeMillis();
		if(this.isShutdown()){
			logger.warn("File reader state is shutdown so stop immediately.");
			return;
		}
		boolean p = false;
		try{
			p = permit.tryAcquire();
			if(!p){
				logger.info("Already reached max concurrent "+maxConcurrent+" so stop immediately.");
				return ;
			}
			if(this.isShutdown()){
				logger.warn("File reader state is shutdown so stop immediately.");
				return;
			}
			process();
		}finally{
			if(p){
				permit.release();
			}
		}
		logger.info("File Reader stoped. execTm="+(System.currentTimeMillis()-startTm)+" ms.");
	}
	
	private void process() throws Exception {
		//测试mq的连通性
		logger.info("Start to test connectivity of rabbitmq.");
		if(!doTest()){
			logger.warn("Test failed so stop immediately,may be rabbit MQ blocked.");
			return;
		}
		logger.info("Test passed.");
		
		//file reader 是否shutdown
		if(this.isShutdown()){
			logger.warn("File reader state is shutdown after the test passed, so stop immediately.");
			return;
		}
				
		//获取需要处理的文件
		File currentFile = getCurrentFile();
		
		if(currentFile == null){
			logger.info("No file need to be processed.");
			return;
		}
		logger.info("Start to process the file \""+currentFile.getPath()+"\"");
		
		doProcess(currentFile);
		
		afterFileProcess(currentFile);
	}
	
	/**
	 * 具体文件处理策略
	 * @throws Exception
	 */
	public abstract void doProcess(File file) throws Exception;

	/**
	 * 测试rabbitmq是否已经连通
	 * @return
	 * @throws HiException 
	 */
	protected boolean doTest() throws HiException{
		return callBack.doTest();
	}
	
	/**
	 * 获取本次需要处理的文件名，没有返回null
	 * @return
	 * @throws HiException 
	 */
	protected File getCurrentFile() throws HiException {
		try{
			lock.lock();
			SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
			Calendar cal = Calendar.getInstance();
			final String today = fmt.format(cal.getTime());
			cal.add(Calendar.DAY_OF_MONTH, -1);
			final String lastDay = fmt.format(cal.getTime());
			
			File rabDir = new File(HiRabbitUtils.getRabbitFileDir());
			//查找合适的文件，1，非目录，2，不以.current、.dat 、.error 结尾 3,当天和上一天的文件
			File[] files = rabDir.listFiles(new FileFilter(){
				public boolean accept(File pathFile) {
					if(pathFile.isDirectory()){
						return false;
					}
					String pathName = pathFile.getName();
					if(pathName.endsWith(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX)
							|| pathName.endsWith(HiRabbitUtils.RABBIT_MSG_FILE_ERROR_SUFFIX)
							|| pathName.endsWith(HiRabbitUtils.RABBIT_MSG_FILE_SUFFIX)){
						return false;
					}
					//后缀校验
					if(!pathName.startsWith(HiRabbitUtils.RABBIT_MSG_FILE_NAME)){
						return false;
					}
					//日期校验
					if(pathName.indexOf(today) == -1 && pathName.indexOf(lastDay) == -1){
						return false;
					}
					return true;
				}
			});
			//没有合适的文件时，返回null，同时会发起强制修改目前的dat文件名称改为适合处理的文件
			if(files == null || files.length==0){
				logger.debug("currentTimeMillis="+System.currentTimeMillis()+",fileCreateTime="+fileWriter.getFileCreateTime());
				if(fileWriter != null 
					&& System.currentTimeMillis()-fileWriter.getFileCreateTime()>getForceChangeFileItv()*1000){
					fileWriter.changeFile();//强制改文件名
				}
				return null;
			}
			
			File lastFile = null;
			//只有一个文件，直接返回
			if(files.length == 1){
				lastFile =  files[0];
				logger.info("Find a file \""+lastFile+"\"");
			}else{
				//多个文件排序
				List<File> fileList = Arrays.asList(files);
				Collections.sort(fileList, new Comparator<File>(){
					@Override
					public int compare(File o1, File o2) {
						return o1.getName().compareTo(o2.getName());
					}
				});
				lastFile = fileList.get(0);
				logger.debug("File lists=="+fileList);
				logger.info("A file \""+lastFile.getPath()+"\" was selected in the list，with a total of "+fileList.size() + " files in list.");
			}
			
			//更改文件名，后缀加上.current
			File f2 = new File(lastFile.getPath() +HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX );
			lastFile.renameTo(f2);
			logger.info("The File \""+lastFile.getName() +"\" rename to \""+f2.getName()+"\"");
			return f2;
			
		}finally{
			lock.unlock();
		}
	}
	
	/**
	 * 处理文件读取的字符串
	 * @param str
	 * @throws IOException 
	 */
	protected void process(String routingKey,String msg) throws Exception {
		//检查消息是否正确
		if(!callBack.check(routingKey, msg)){
			throw new CheckException(routingKey,msg);
		}
		//如果已经shutdown，直接写文件
		if(this.isShutdown()){
			try {
				this.fileWriter.write(new MsgStore(routingKey,msg));
				reWriteMsg.info("routingKey["+routingKey+"],msg["+msg+"]");
			} catch (IOException e) {
				lostMsg.error("reWriter|"+msg);
				throw new WriteFileException(msg,e);
			}
			return;
		}
		//回调对消息进行处理
		callBack.doMsgProcess(routingKey,msg);
		
	}
	
	/**
	 * 将当前处理文件的文件名改为处理前的文件名
	 * @param currentFile
	 */
	protected void renameCurrentToBefore(File currentFile) {
		String curFileName = currentFile.getName();
		String beforeFileName = curFileName.substring(0,curFileName.indexOf(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX));
		File beforeFile = new File(currentFile.getParent()+File.separator+beforeFileName );
		currentFile.renameTo(beforeFile);
	}
	
	/**
	 * 将当前处理文件的文件名改为处理错误的文件名
	 * @param currentFile
	 */
	protected void renameCurrentToError(File currentFile){
		String curFileName = currentFile.getName();
		String errorFileName = curFileName.substring(0,
				curFileName.indexOf(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX))
				+HiRabbitUtils.RABBIT_MSG_FILE_ERROR_SUFFIX;
		File errorFile = new File(currentFile.getParent()+File.separator+errorFileName );
		currentFile.renameTo(errorFile);
	}

	/**
	 * 文件处理的后续操作
	 * 1，将文件名后缀去掉.current 并移动文件到backup目录
	 * @param currentFile
	 */
	protected void afterFileProcess(File currentFile) {
		String curFileName = currentFile.getName();
		String fileName = curFileName.substring(0,curFileName.indexOf(HiRabbitUtils.RABBIT_MSG_FILE_CURRENT_PROCESS_SUFFIX));
		File afterFile = new File(HiRabbitUtils.getRabbitFileBackupDir()+File.separator+fileName );
		if(afterFile.getParent() != null){
			File afterParentFile = new File(afterFile.getParent());
			if(!afterParentFile.exists()){
				afterParentFile.mkdirs();
			}
		}
		currentFile.renameTo(afterFile);
		logger.info("The file \""+currentFile.getName() +"\" finally rename to \"backup/"+ afterFile.getName()+"\"");
	}

	/* 
	 * 可以从一下几个地方中止线程
	 * 1，进入doRun方式时,终止
	 * 2，doResend方法,终止
	 * 3，循环读取文件发送时，将文件直接写入文件，等待下次继续处理
	 * @see com.hisun.rabbit.file.IFileReader#terminalImmediate()
	 */
	@Override
	public void shutdown() {
		shutdown = true;
		if(logger.isInfoEnabled()){
			logger.info("File reader state change to shutdown.");
		}
	}
	
	@Override
	public boolean isShutdown() {
		return shutdown;
	}
	
	/* 
	 * 只能判断当前时刻没有一个线程进入run方法
	 * 如果希望之后也不会处理文件，调用此方法前必须调用shutdown()方法
	 * @see com.hisun.rabbit.file.IFileReader#isTermianl()
	 */
	@Override
	public boolean isTermianl() {
		if(permit.availablePermits() == this.maxConcurrent){
			return true;
		}
		return false;
	}
	
	/**
	 * 等待线程中止
	 * @return
	 */
	public boolean awaitTerminal(long timeout,TimeUnit timeUnit){
		//务必保证shutdown
		if(!this.isShutdown()){
			this.shutdown();
		}
		
		long startTime = System.currentTimeMillis();
		long millTimeout = TimeUnit.MILLISECONDS.convert(timeout, timeUnit);
		while(true){
			if(this.isTermianl()){
				logger.info("Successfully waited for the fileReader workers to finish.");
				return true;
			}
			if(System.currentTimeMillis() - startTime > millTimeout){
				logger.warn("FileReader was't workers to finish after wait for a few minutes.");
				return false;
			}
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
			}
		}
		
	}
	
	public int getForceChangeFileItv() {
		return forceChangeFileItv;
	}
	/**
	 * 如果没有可以处理的文件，强制将目前的dat文件改名为可以处理的文件
	 * 此时间间隔为dat文件生成多久后可以改名，单位是s
	 * @param forceChangeFileItv
	 */
	public void setForceChangeFileItv(int forceChangeFileItv) {
		this.forceChangeFileItv = forceChangeFileItv;
	}
	
	/**
	 * 设置允许的最大线程并发数
	 * @param maxConcurrent
	 */
	public void setMaxConcurrent(int maxConcurrent){
		this.maxConcurrent = maxConcurrent;
		permit = new Semaphore(maxConcurrent);
	}
	
	public int getPrelen() {
		return prelen;
	}

	public void setPrelen(int prelen) {
		this.prelen = prelen;
	}
	
	/**
	 * 写文件异常
	 * <AUTHOR>
	 *
	 */
	protected static class WriteFileException extends RuntimeException{
		private static final long serialVersionUID = 1L;
		private String msg;

		public WriteFileException(String msg,Exception e){
			super(e);
			this.msg = msg;
		}
		public String getMsg(){
			return msg;
		}
	}
	
	/**
	 * 写文件异常
	 * <AUTHOR>
	 *
	 */
	protected static class CheckException extends RuntimeException{
		private static final long serialVersionUID = 1L;
		private String routingKey ;
		private String msg;
		
		public CheckException(String routingKey,String msg){
			super();
			this.routingKey = routingKey;
			this.msg = msg;
		}

		public String getRoutingKey() {
			return routingKey;
		}

		public String getMsg() {
			return msg;
		}
		
	}

}
