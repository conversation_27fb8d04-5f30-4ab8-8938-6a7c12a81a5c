package com.hisun.rabbit.file;

import java.io.IOException;
import java.io.ObjectOutputStream;
import java.io.OutputStream;

/**
 * 在一个文件都有一个文件的头部和文件体。
 * 由于对多次使用FileOutputStream（文件名，true）构建的ObjectOutputStream对象向同一个文件写数据，
 * 在每次些数据的时候他都会向这个文件末尾先写入header在写入你要写的对象数据，
 * 在读取的时候遇到这个在文件体中的header就会报错。导致读出时，出现streamcorrput异常。
 * <AUTHOR>
 *
 */
@Deprecated
public class AppendObjectOutputStream extends ObjectOutputStream {

	protected AppendObjectOutputStream() throws IOException, SecurityException {
		super();
	}
	
	protected AppendObjectOutputStream(OutputStream out) throws IOException {
		super(out);
	} 

	/* (non-Javadoc)
	 *  不写文件头
	 * @see java.io.ObjectOutputStream#writeStreamHeader()
	 */
	protected void writeStreamHeader() throws IOException { 
		//return;
		super.reset(); 
	}
}
