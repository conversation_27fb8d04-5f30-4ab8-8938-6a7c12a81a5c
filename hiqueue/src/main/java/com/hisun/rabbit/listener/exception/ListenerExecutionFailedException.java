package com.hisun.rabbit.listener.exception;

import com.hisun.rabbit.core.AmqpException;
import com.hisun.rabbit.core.Message;


/**
 * Exception to be thrown when the execution of a listener method failed.
 *
 * @see MessageListenerAdapter
 */
@SuppressWarnings("serial")
public class ListenerExecutionFailedException extends AmqpException {

	private final Message failedMessage;

	/**
	 * Constructor for ListenerExecutionFailedException.
	 * @param msg the detail message
	 * @param cause the exception thrown by the listener method
	 */
	public ListenerExecutionFailedException(String msg, Throwable cause) {
		this(msg, cause, null);
	}

	/**
	 * Constructor for ListenerExecutionFailedException.
	 * @param msg the detail message
	 * @param cause the exception thrown by the listener method
	 * @param failedMessage the message that failed
	 *
	 */
	public ListenerExecutionFailedException(String msg, Throwable cause, Message failedMessage) {
		super(msg, cause);
		this.failedMessage = failedMessage;
	}

	public Message getFailedMessage() {
		return failedMessage;
	}

}
