package com.hisun.rabbit.listener;


import com.hisun.rabbit.core.Message;
import com.rabbitmq.client.Channel;

/**
 * A message listener that is aware of the Channel on which the message was received.
 *
 */
public interface ChannelAwareMessageListener {

	/**
	 * Callback for processing a received Rabbit message.
	 * <p>Implementors are supposed to process the given Message,
	 * typically sending reply messages through the given Session.
	 * @param message the received AMQP message (never <code>null</code>)
	 * @param channel the underlying Rabbit Channel (never <code>null</code>)
	 * @throws Exception Any.
	 */
	void onMessage(Message message, Channel channel) throws Exception;

}
