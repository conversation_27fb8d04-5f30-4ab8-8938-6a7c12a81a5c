package com.hisun.rabbit.connection;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.rabbit.support.CommonUtils;
import com.hisun.rabbit.support.RabbitExceptionTranslator;
import com.rabbitmq.client.Channel;

/**
 * Simply a Connection.
 */
public class SimpleConnection implements Connection {
	
	protected static Logger logger = HiLog.getLogger("rabbitMonitor.trc");

	private final com.rabbitmq.client.Connection delegate;

	private final int closeTimeout;
	
	private List<BlockedListener> blockedListeners = new CopyOnWriteArrayList<BlockedListener>(); 

	public SimpleConnection(com.rabbitmq.client.Connection delegate,
			int closeTimeout) {
		this.delegate = delegate;
		this.closeTimeout = closeTimeout;
	}

	public Channel createChannel(boolean transactional) {
		try {
			Channel channel = delegate.createChannel();
			if (transactional) {
				// Just created so we want to start the transaction
				channel.txSelect();
			}
			return channel;
		} catch (IOException e) {
			throw RabbitExceptionTranslator.convertRabbitAccessException(e);
		}
	}

	public void close() {
		try {
			this.blockedListeners.clear();
		} catch (Throwable e) {
		}
		
		try {
			// let the physical close time out if necessary
			delegate.close(closeTimeout);
		} catch (IOException e) {
			throw RabbitExceptionTranslator.convertRabbitAccessException(e);
		}
	}

	/* 自动恢复的连接一直返回true
	 * @see com.hisun.rabbit.connection.Connection#isOpen()
	 */
	public boolean isOpen() {
		return delegate != null
				&& (delegate.isOpen() || this.delegate.getClass().getSimpleName().contains("AutorecoveringConnection"));
	}

	@Override
	public String toString() {
		return "SimpleConnection@"+CommonUtils.getIdentityHexString(this)+"[delegate=" + delegate + "]";
	}

	@Override
	public void addBlockedListener(BlockedListener blockedListener) {
		if(this.blockedListeners.isEmpty()){
			final com.rabbitmq.client.Connection con = this.delegate;
			this.delegate.addBlockedListener(new com.rabbitmq.client.BlockedListener(){

				@Override
				public void handleBlocked(String arg0) throws IOException {
					logger.warn("BlockedListener.handBlocked="+arg0+"~~~delegate=="+con);
					for(BlockedListener bl : blockedListeners){
						bl.handleBlocked(arg0);
					}
				}

				@Override
				public void handleUnblocked() throws IOException {
					logger.warn("BlockedListener.handleUnblocked~~~delegate=="+con);
					for(BlockedListener bl : blockedListeners){
						bl.handleUnblocked();
					}
				}
			});
			logger.info("bare connection "+delegate+" add blocked listener.");
		}
		
		if(!this.blockedListeners.contains(blockedListener)){
			this.blockedListeners.add(blockedListener);
		}
	}

	@Override
	public boolean removeBlockedListener(BlockedListener blockedListener) {
		return blockedListeners.remove(blockedListener);
	}

}
