package com.hisun.rabbit.connection;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class CompositeConnectionListener implements ConnectionListener {

	private List<ConnectionListener> delegates = new CopyOnWriteArrayList<ConnectionListener>();

	public void onCreate(Connection connection) {
		for (ConnectionListener delegate : delegates) {
			delegate.onCreate(connection);
		}
	}

	public void onClose(Connection connection) {
		for (ConnectionListener delegate : delegates) {
			delegate.onClose(connection);
		}
	}

	public void setDelegates(List<? extends ConnectionListener> delegates) {
		this.delegates = new ArrayList<ConnectionListener>(delegates);
	}

	public void addDelegate(ConnectionListener delegate) {
		this.delegates.add(delegate);
	}

	public boolean removeDelegate(ConnectionListener delegate) {
		return this.delegates.remove(delegate);
	}

	public void clearDelegates() {
		this.delegates.clear();
	}

}
