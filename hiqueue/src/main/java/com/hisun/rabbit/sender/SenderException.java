package com.hisun.rabbit.sender;

import com.hisun.rabbit.core.AmqpException;

/**
 * 发送者异常
 * <AUTHOR>
 *
 */
public class SenderException extends AmqpException {

	private static final long serialVersionUID = 1L;
	
	public SenderException(String message, Throwable cause) {
		super(message, cause);
	}
	
	public SenderException(String message) {
		super(message);
	}
	
	public SenderException(Throwable cause) {
		super(cause);
	}
	
}
