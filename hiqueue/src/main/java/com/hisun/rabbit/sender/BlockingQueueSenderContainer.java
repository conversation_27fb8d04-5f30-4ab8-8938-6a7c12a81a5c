package com.hisun.rabbit.sender;

import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.queue.constants.HiRabbitUtils;
import com.hisun.rabbit.confirm.CorrelationData;
import com.hisun.rabbit.confirm.CorrelationDataIdGen;
import com.hisun.rabbit.core.Message;
import com.hisun.rabbit.core.RabbitTemplate;
import com.hisun.rabbit.file.IFileWriter;
import com.hisun.rabbit.file.MsgStore;
import com.hisun.rabbit.support.SimpleAsyncTaskExecutor;


/**
 * 异步消息处理容器
 * 
 * 工作流程如下：
 * 1，消息生产者将消息send到BlockingQueueSenderContainer的BlockingQueue
 * 2，BlockingQueueSenderContainer 的工作线程从BlockingQueue取消息发送到rabbitMQ
 * 3，容器关闭或者发送rabbitmq失败，消息会持久化到文件
 * 
 * <AUTHOR>
 *
 */
public class BlockingQueueSenderContainer {
	
	private final static Logger lostMsg = HiLog.getLogger("rabbitLostProducer.lst");
	private final static Logger rabbitSenderMonitor = HiLog.getLogger("rabbitSenderMonitor.trc");
	
	private static final int DEFAULT_SHUTDOWN_TIMEOUT= 5000;//默认值，3s
	public static final long DEFAULT_RECEIVE_TIMEOUT = 1000; //默认值，1s
	
	private static final long DEFAULT_START_SENDER_MIN_INTERVAL = 10000;//默认值，start Sender最小间隔时间

	private static final long DEFAULT_STOP_SENDER_MIN_INTERVAL = 20000;//默认值，stop Sender最小间隔时间

	private static final int DEFAULT_CONSECUTIVE_ACTIVE_TRIGGER = 10;//默认值，连续active次数，增加Sender

	private static final int DEFAULT_CONSECUTIVE_IDLE_TRIGGER = 10;//默认值，连续idle次数，减少Sender
	
	private static final int DEFAULT_BLOCKING_QUEUE_CAPACITY = 300;//默认值，blockingqueue capacity
	
	private RabbitTemplate template;
	
	private IFileWriter fileWriter;
	
	private volatile Executor taskExecutor = new SimpleAsyncTaskExecutor("hisun-queue-sender-");
	
	private volatile List<AsyncMessageProcessingSender> senders = new CopyOnWriteArrayList<AsyncMessageProcessingSender>();
	
    private Lock senderLock = new ReentrantLock();
	
	private final BlockingQueue<Delivery> queue;
	
	private volatile boolean active =false;
	
	private CountDownLatch waitQueueEmpty = new CountDownLatch(1);//等待BlockingQueueSender stop 
	
	private volatile int concurrentSenders = 1;//最少处理线程数
	
	private volatile Integer maxConcurrentSenders;//最大处理线程数
	
	private volatile long shutdownTimeout = DEFAULT_SHUTDOWN_TIMEOUT;// stop Blocking Queue Sender 时，等待队列数据处理完成的超时时间
	
	private volatile long receiveTimeout = DEFAULT_RECEIVE_TIMEOUT;//从队列获取消息的等待超时时间
	
	private volatile long lastSenderStarted;//last start Sender 的时间戳

	private volatile long lastSenderStopped;//last stop Sender 时间戳
	
	private volatile long startSenderMinInterval = DEFAULT_START_SENDER_MIN_INTERVAL;//start Sender最小间隔时间

	private volatile long stopSenderMinInterval = DEFAULT_STOP_SENDER_MIN_INTERVAL;//stop Sender 最小间隔时间

	private volatile int consecutiveActiveTrigger = DEFAULT_CONSECUTIVE_ACTIVE_TRIGGER;//连续active次数，start Sender

	private volatile int consecutiveIdleTrigger = DEFAULT_CONSECUTIVE_IDLE_TRIGGER;//连续idle次数，stop Sender
	
	private int blockingQueueCapacity = DEFAULT_BLOCKING_QUEUE_CAPACITY;
	
	private volatile boolean publisherConfirms = false;	//confirm 机制
	
	private volatile ConcurrentMap<String,String> pendingConfirms = null;//<CorrelationData,msg>
	
	/**
	 * @param template
	 * @param fileWriter
	 * @param logger 服务日志
	 */
	public BlockingQueueSenderContainer(RabbitTemplate template,IFileWriter fileWriter){
		this.template = template;
		this.queue = new LinkedBlockingQueue<Delivery>(this.blockingQueueCapacity);;
		this.fileWriter = fileWriter;
	}
	
	/**
	 * @param template
	 * @param fileWriter
	 * @param blockingQueueCapacity
	 * @param logger 服务日志
	 */
	public BlockingQueueSenderContainer(RabbitTemplate template,IFileWriter fileWriter,int blockingQueueCapacity){
		this.template = template;
		this.blockingQueueCapacity = blockingQueueCapacity;
		this.queue = new LinkedBlockingQueue<Delivery>(blockingQueueCapacity);;
		this.fileWriter = fileWriter;
	}
	
	/**
	 * @param template
	 * @param fileWriter
	 * @param queue
	 * @param logger 服务日志
	 */
	public BlockingQueueSenderContainer(RabbitTemplate template,IFileWriter fileWriter,BlockingQueue<Delivery> queue){
		this.template = template;
		this.queue = queue;
		this.fileWriter = fileWriter;
	}
	
	/**
	 * send msg to blockingQueue,producer invoke this method
	 * @param routingKey
	 * @param msg
	 */
	public void send(String routingKey,String msg){
		if(!this.isActive()){	//如果已经stop，禁止发送到queue
			throw new SenderException("Sender container is already stop.");
		}
		boolean flag = queue.offer(new Delivery(routingKey,msg));
		if(!flag){
			if(rabbitSenderMonitor.isWarnEnabled()){
				rabbitSenderMonitor.warn("BlockingQueue is full.");
			}
			writeToFile(routingKey,msg);
		}
		
	}
	
	/**
	 * waiting up to the specified wait time if necessary for space to become available.
	 * @param routingKey
	 * @param msg
	 * @param timeout
	 * @param unit
	 */
	public void send(String routingKey,String msg,long timeout, TimeUnit unit){
		if(!this.isActive()){	//如果已经stop，禁止发送到queue
			throw new SenderException("Sender Container is already stop.");
		}
		
		boolean interrupt = false;
		boolean flag = true;
		try {
			flag = queue.offer(new Delivery(routingKey,msg), timeout, unit);
		} catch (InterruptedException e) {
			if(rabbitSenderMonitor.isWarnEnabled()){
				rabbitSenderMonitor.warn("InterruptedException occur when the msg offer to queue .");
			}
			interrupt = true;
			flag = false;
		}
		if(!flag){
			if(rabbitSenderMonitor.isWarnEnabled() && !interrupt){
				rabbitSenderMonitor.warn("BlockingQueue is full.");
			}
			writeToFile(routingKey,msg);
		}
	}
	
	/**
	 * waiting if necessary for space to become available.
	 * @param routingKey
	 * @param msg
	 * @param timeout
	 * @param unit
	 */
	public void sendBlockedIfNecessary(String routingKey,String msg){
		if(!this.isActive()){	//如果已经stop，禁止发送到queue
			throw new SenderException("Sender Container is already stop.");
		}
		
		try {
			queue.put(new Delivery(routingKey,msg));
		} catch (InterruptedException e) {
			if(rabbitSenderMonitor.isWarnEnabled()){
				rabbitSenderMonitor.warn("InterruptedException occur when the msg offer to queue .");
			}
			writeToFile(routingKey,msg);
		}
	}
	
	/**
	 * 考虑停止一个Sender
	 * Sender的数目必须大于最小并发线程数
	 */
	private void consideStopASender() {
		if(senders != null && this.maxConcurrentSenders != null && senders.size() > this.concurrentSenders){
			try{
				senderLock.lock();
				if(senders.size() > this.concurrentSenders){
					long now = System.currentTimeMillis();
					if (this.lastSenderStopped + this.stopSenderMinInterval < now) {
						AsyncMessageProcessingSender sender = senders.remove(0);
						if(sender != null){
							sender.shutdown();
						}
						if (rabbitSenderMonitor.isInfoEnabled()) {
							rabbitSenderMonitor.info("Idle sender terminating: " + sender+",asycSenders size is "+this.senders.size()+", blockingQueue size is "+this.getQueue().size());
						}
						this.lastSenderStopped = now;
					}
				}
			}finally{
				senderLock.unlock();
			}
		}
	}

	/**
	 * 考虑增加一个Sender
	 * Sender的数量必须小于最大并发线程数，且blockingqueue不能为空
	 */
	private void consideAddASender() {
		if(senders != null && this.maxConcurrentSenders != null 
				&& senders.size() < this.maxConcurrentSenders
				&& this.hasDelivery()){
			try{
				senderLock.lock();
				if(senders.size() < this.maxConcurrentSenders){
					long now = System.currentTimeMillis();
					if (this.lastSenderStarted + startSenderMinInterval < now) {
						this.addAndStartSenders(1);
						this.lastSenderStarted = now;
					}
				}
			}finally{
				senderLock.unlock();
			}
		}
	}
	
	private void addAndStartSenders(int sum){
		for(int i = 0;i<sum;i++){
			AsyncMessageProcessingSender sender = new AsyncMessageProcessingSender();
			taskExecutor.execute(sender);
			senders.add(sender);
			
			if(rabbitSenderMonitor.isInfoEnabled()){
				rabbitSenderMonitor.info("Start a new Sender: " + sender+", asyncSenders size is "+this.senders.size()+", blockingQueue size is "+this.getQueue().size());
			}
		}
	}

	/**
	 * start sender
	 */
	public void start(){
		if(this.isActive()){
			return;
		}
		
		this.active = true;	//必须在addAndStartSenders前，否则Sender会退出
		
		if(this.concurrentSenders <1){
			this.concurrentSenders = 1;
		}
		this.addAndStartSenders(this.concurrentSenders);
		
		if(rabbitSenderMonitor.isInfoEnabled()){
			rabbitSenderMonitor.info("Container started. concurrentSenders["+concurrentSenders+
					"],maxConcurrentSenders["+maxConcurrentSenders+"],blockingQueueCapacity["+ blockingQueueCapacity+"]");
		}
	}
	
	/**
	 * stop sender
	 */
	public void stop(){
		if(!this.isActive()){
			return ;
		}
		
		this.active = false;
		
		boolean finished = false;
		try {
			finished = waitQueueEmpty.await(shutdownTimeout, TimeUnit.MILLISECONDS);
		} catch (InterruptedException e) {
		}
		
		if (finished) {
			if(rabbitSenderMonitor.isInfoEnabled()){
				rabbitSenderMonitor.info("Successfully wait for all the queue messages be send to finish.");
			}
		}else {
			if(rabbitSenderMonitor.isWarnEnabled()){
				rabbitSenderMonitor.warn("The queue messages were't send complete after wait for a few minutes.");
			}
		}
		
		shutdownAllSenders(); //Sender线程退出循环
		
		dumpQueueDataToFile();//将对列数据dump到文件;
		
		if(rabbitSenderMonitor.isInfoEnabled()){
			rabbitSenderMonitor.info("Container stoped.");
		}
		
	}
	
	/**
	 * 从queue接收消息并处理
	 * @return
	 */
	public boolean receiveAndExecute(){
		
		try{
			
			Delivery delivery = getQueue().poll(receiveTimeout, TimeUnit.MILLISECONDS);
			if(null == delivery){	
				return false;
			}
			
			sendToRabbitMq(delivery);
			
		}catch(Throwable t){
			if(rabbitSenderMonitor.isErrorEnabled()){
				rabbitSenderMonitor.error("Unexpected err occur at receive and execute message.",t);
			}
		}
		return true;
	}
	
	private AtomicInteger senderFlag = new AtomicInteger();//Sender 名称计数
	/**
	 * 异步发送者
	 * <AUTHOR>
	 *
	 */
	private class AsyncMessageProcessingSender implements Runnable{
		
		private String name;
		private boolean shutdown = false;
		
		public AsyncMessageProcessingSender(){
			try{
				this.name = "AsyncSender@"+senderFlag.incrementAndGet();
			}catch(Exception e){
				senderFlag.set(0);
				this.name = "AsyncSender@"+senderFlag.incrementAndGet();
			}
		}

		@Override
		public void run() {
			int consecutiveIdles = 0;
			int consecutiveMessages = 0;
			
			if(rabbitSenderMonitor.isInfoEnabled()){
				rabbitSenderMonitor.info(this+" started.");
			}
			
			try{
				while(isActive() || hasDelivery()){
					if(shutdown){
						break;
					}
					
					boolean receivedOk = receiveAndExecute();
					
					if(rabbitSenderMonitor.isDebugEnabled()){
						rabbitSenderMonitor.debug(this+" receivedOk="+receivedOk+",consecutiveIdles="+consecutiveIdles+",consecutiveMessages="+consecutiveMessages);
					}
					
					if (BlockingQueueSenderContainer.this.maxConcurrentSenders != null) {
						try{
							if (receivedOk) {
								consecutiveIdles = 0;
								if (consecutiveMessages++ > BlockingQueueSenderContainer.this.consecutiveActiveTrigger) {
									consideAddASender();
									consecutiveMessages = 0;
								}
							}else {
								consecutiveMessages = 0;
								if (consecutiveIdles++ > BlockingQueueSenderContainer.this.consecutiveIdleTrigger) {
									consideStopASender();
									consecutiveIdles = 0;
								}
							}
						}catch(Throwable t){	//防御性异常捕获
							if(rabbitSenderMonitor.isErrorEnabled()){
								rabbitSenderMonitor.error("Unexpected err occur at Add or stop AsyncSender.",t);
							}
						}
						
					}
					
				}//end while
				
				//确保线程结束一定删除
				BlockingQueueSenderContainer.this.senders.remove(this);
				//当接收到stop信号并且对列数据处理完毕
				if(!BlockingQueueSenderContainer.this.isActive() && !hasDelivery()){
					waitQueueEmpty.countDown();
				}
				
			}catch(Throwable t){
				if(rabbitSenderMonitor.isErrorEnabled()){
					rabbitSenderMonitor.error("Unexpected err occur and the thread \""+this+"\" will stop now. ",t);
				}
			}
			
			if(rabbitSenderMonitor.isInfoEnabled()){
				rabbitSenderMonitor.info(this+" stoped.");
			}
			
		}	//end run
		
		public void shutdown(){
			this.shutdown = true;
		}
		
		public String toString(){
			return name;
		}
		
	}
	
	/**
	 * 发送消息到mq，发送mq失败写重发文件，写文件失败抛出异常@WriteFileException
	 * @param delivery
	 */
	public void sendToRabbitMq(Delivery delivery){
		
		long startTime = System.currentTimeMillis();
		try{
			if(rabbitSenderMonitor.isDebugEnabled()){
				rabbitSenderMonitor.debug("Sending to rabbitMQ : routingKey["+delivery.getRoutingKey()+"],msg["+delivery.getContent()+"],publisherConfirms["+this.publisherConfirms+"]");
			}
			if(this.publisherConfirms){
				String correlationDataId = CorrelationDataIdGen.getCorrelationDataId();
				this.template.send(delivery.getRoutingKey(),
						new Message(delivery.getContent().getBytes(),HiRabbitUtils.getTextPlainMessageProperties(delivery.getRoutingKey())),
						new CorrelationData(correlationDataId));
				//this.pendingConfirms.put(correlationDataId, "");
				this.pendingConfirms.put(correlationDataId, delivery.getContent());
			}else{
				this.template.send(delivery.getRoutingKey(), new Message(delivery.getContent().getBytes(),HiRabbitUtils.getTextPlainMessageProperties(delivery.getRoutingKey())));
			}
			
		}catch(Exception e){
			
			if(rabbitSenderMonitor.isErrorEnabled()){
				rabbitSenderMonitor.error("Unexpected error occur at sending message to rabbitmq, this message will be write to file.",e);
			}
			
			writeToFile(delivery.getRoutingKey(),delivery.getContent());
			
		}finally{
			
			long execTm = System.currentTimeMillis() - startTime;
			
			if(execTm > 3000){
				if(rabbitSenderMonitor.isWarnEnabled()){
					rabbitSenderMonitor.warn("Took a long time to send message to rabbitmq. execTm="+execTm);
				}
			}
			
		}
	}
	
	/**
	 * write message to file when blocking queue no space or thread blocked
	 * @param msg
	 */
	public void writeToFile(String routingKey,String msg){
		try {
			this.fileWriter.write(new MsgStore(routingKey,msg));		//String
		} catch (Exception e) {
			if(lostMsg.isErrorEnabled()){
				lostMsg.error(msg);
			}
			throw new WriteFileException(e);
		}
	}
	
	public boolean hasDelivery(){
		return !this.queue.isEmpty();
	}
	
	/**
	 * 将所有的线程都shutdown
	 * 线程不会马上停止，但是循环到判断是否shutdown时将退出
	 */
	public void shutdownAllSenders(){
		if(this.senders.size() >0){
			for(AsyncMessageProcessingSender sender: senders){
				sender.shutdown();
			}
		}
		this.senders.clear();
		if(rabbitSenderMonitor.isInfoEnabled()){
			rabbitSenderMonitor.info("All asyncSenders state change to shutdown.");
		}
	}
	
	public void dumpQueueDataToFile(){
		if(this.getQueue().size()>0){
			if(rabbitSenderMonitor.isInfoEnabled()){
				rabbitSenderMonitor.info("Start to dump the queue messages to file,blocking queue size is "+this.getQueue().size());
			}
			int i = 0;
			while(true){
				Delivery delivery = this.getQueue().poll();
				if(null == delivery){
					break;
				}
				this.writeToFile(delivery.getRoutingKey(), delivery.getContent());
				i++;
			}
			if(rabbitSenderMonitor.isInfoEnabled()){
				rabbitSenderMonitor.info("The queue messages was completely dump to file,with a total of "+i);
			}
		}else{
			if(rabbitSenderMonitor.isInfoEnabled()){
				rabbitSenderMonitor.info("The blocking queue is empty.");
			}
		}
	}
	
	public RabbitTemplate getTemplate() {
		return template;
	}

	public void setTemplate(RabbitTemplate template) {
		this.template = template;
	}

	public BlockingQueue<Delivery> getQueue() {
		return queue;
	}

	public boolean isActive() {
		return active;
	}

	public int getConcurrentSenders() {
		return concurrentSenders;
	}

	public void setConcurrentSenders(int concurrentSenders) {
		this.concurrentSenders = concurrentSenders;
	}

	public Integer getMaxConcurrentSenders() {
		return maxConcurrentSenders;
	}

	public void setMaxConcurrentSenders(Integer maxConcurrentSenders) {
		this.maxConcurrentSenders = maxConcurrentSenders;
	}
	
	/**
	 * 设置confirm机制
	 * @param pendingConfirms
	 */
	public void setPendingConfirms(ConcurrentMap<String,String> pendingConfirms){
		this.pendingConfirms = pendingConfirms;
		this.publisherConfirms = true;
		
		if(rabbitSenderMonitor.isInfoEnabled()){
			rabbitSenderMonitor.info("BlockingQueueSenderContainer running on confirm mode.");
		}
	}

	/**
	 * Encapsulates an arbitrary message
	 * <AUTHOR>
	 *
	 */
	private static class Delivery {
		private final String routingKey;
		private final String content;
		
		public Delivery(String routingKey, String content) {
			this.routingKey = routingKey;
			this.content = content;
		}
		
		public String getRoutingKey() {
			return routingKey;
		}
		
		public String getContent() {
			return content;
		}
		
	}
	
	private static class WriteFileException extends SenderException{

		private static final long serialVersionUID = 1L;

		public WriteFileException(Throwable cause) {
			super(cause);
		}
		
	}

}
