apply plugin: 'java'
apply from: '../common.gradle'

//version = '1.0.0.SNAPSHOT'
version = '1.0.0.RELEASE'

dependencies {
    compile 'commons-fileupload:commons-fileupload:1.2'
    compile 'commons-httpclient:commons-httpclient:3.1-rc1'
    compile 'commons-lang:commons-lang:2.3'
    compile 'net.sf.json-lib:json-lib:2.2.2:jdk13'
    compile 'org.apache.httpcomponents:httpclient:4.2.3'
    compile 'javax.servlet:javax.servlet-api:3.1.0'

    compile 'com.symantec:SymJavaAPI:1.0'


    compile 'com.hisun:hiclientutil:1.0.0.RELEASE'
    compile 'com.hisun:hicommon:1.0.0.RELEASE'
    compile 'com.hisun:hiparam:1.0.0.RELEASE'
    compile 'com.hisun:hipubatc:1.0.0.RELEASE'
    compile 'com.hisun:hidbutil:1.0.0.RELEASE'
    compile 'com.hisun:hidispatcher:1.0.0.RELEASE'
    compile 'com.hisun:hijson:1.0.0.RELEASE'
}
