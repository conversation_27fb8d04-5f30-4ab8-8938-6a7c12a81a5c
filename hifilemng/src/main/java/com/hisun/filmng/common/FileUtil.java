package com.hisun.filmng.common;

import java.io.File;
import java.util.List;
import java.util.Vector;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang.StringUtils;

import com.symantec.scanengine.api.Policy;
import com.symantec.scanengine.api.Result;
import com.symantec.scanengine.api.ResultStatus;
import com.symantec.scanengine.api.ScanEngine;
import com.symantec.scanengine.api.ScanException;
import com.symantec.scanengine.api.ScanEngine.ScanEngineInfo;

public class FileUtil {
	private static int SUC = 0;
	private static int DEFAULT_ERR = -1;
	private static String fileName;
	
	public String getFileName() {
		return fileName;
	}
	
	public int fileUpload(HttpServletRequest request, String localFilePath, String newFileName, int maxSize, String encoding) {
		if (maxSize == 0) {
			maxSize = 102400;
		}
		maxSize *= 1024;
		
		// 1. 创建工厂类
		DiskFileItemFactory factory = new DiskFileItemFactory();
		factory.setSizeThreshold(4096);
		// 2. 创建FileUpload对象
		ServletFileUpload upload = new ServletFileUpload(factory);
		if(request.getCharacterEncoding() != null || !"".equals(request.getCharacterEncoding())) {
			upload.setHeaderEncoding(request.getCharacterEncoding());
		}else if(!StringUtils.isBlank(encoding)) {
			upload.setHeaderEncoding(encoding);
		}else {
			upload.setHeaderEncoding("GBK");
		}
		upload.setSizeMax(maxSize);
		// 3. 判断是否是上传表单
		boolean isMultipart = upload.isMultipartContent(request);
		
		// 不是文件上传
		if(!isMultipart) {
			//2：非文件上传表单
		    return 2;
		}
		
		// 4. 解析request，获得FileItem项
		List<FileItem> fileitems;
		try {
			fileitems = upload.parseRequest(request);
			int fileRecNum = 0;
			// 5. 遍历集合上传文件
			for(int i = 0; i < fileitems.size(); i++) {
				String oldFileName = null;
				FileItem item = fileitems.get(i);
				// 判断是不是普通字段
				if(item.isFormField()) {
					continue;
				}
				
				oldFileName = item.getName();
				if (StringUtils.isBlank(oldFileName)) {
		          continue;
		        }
				
				int idx = oldFileName.lastIndexOf('\\');
		        if (idx != -1) {
		        	oldFileName = oldFileName.substring(idx + 1);
		        }
				
				if(!localFilePath.startsWith("/")) {
					localFilePath = System.getProperty("HWORKDIR") + "/" + localFilePath;
				}
				
				if (!localFilePath.endsWith("/")) {
					localFilePath = localFilePath + "/";
			    }
				
	            File f = new File(localFilePath);
	            if (!f.exists()) {
	              f.mkdirs();
	            }
				
	            String localFile = "";
	            if(StringUtils.isBlank(newFileName)) {
	            	localFile = oldFileName;
	            }else {
	            	localFile = newFileName;
	            }
	            
	            fileName = localFile;
	            
	            if(StringUtils.isBlank(localFile)) {
	            	return DEFAULT_ERR;
	            }
	            
				File file = new File(localFilePath + localFile);
				item.write(file);
				fileRecNum++;
			}
		} catch (FileUploadException e) {
			return DEFAULT_ERR;
		} catch (Exception e) {
			return DEFAULT_ERR;
		}
		return SUC;
	}
	
	public static int fileScan(String scanningMode, String filePath, String fileName, String scanServer, String scanPolicy) {
		if(!filePath.startsWith("/")) {
			filePath = System.getProperty("HWORKDIR") + "/" + filePath;
		}
		
		if (!filePath.endsWith("/")) {
			filePath = filePath + "/";
	    }
		
		String fileForScan = filePath + fileName;
        File f = new File(fileForScan);
        //文件不存在
        if (!f.exists()) {
          return 2;
        }
        
        String streamFileLocal = fileForScan;
		String scanOutputFile = filePath + "O" + fileName;
		String scanFileOriginalName = fileForScan;
		
		Policy scPolicy = Policy.DEFAULT;
		if(scanPolicy != null && !"".equals(scanPolicy)){
			scPolicy = Policy.valueOf(scanPolicy.trim().toUpperCase());
		}
		
		try {
			VirusScan virusScan = VirusScan.getVirusScan();
			if(null == virusScan ){
				//配置扫描引擎
				Vector<ScanEngineInfo> scanEnginesForScanning = new Vector<ScanEngineInfo>();
				String ipPort[] = scanServer.split(";");
		        for(int k=0;k<ipPort.length;k++) {
		            if(!(ipPort[k].trim().length() == 0)) {
		                String ipPr[] = ipPort[k].split(":");
		                if(ipPr.length != 2) {
		                	return DEFAULT_ERR;
		                }else {
		                    ScanEngine.ScanEngineInfo scanEngTobeUsed = new ScanEngine.ScanEngineInfo(ipPr[0].trim(),Integer.parseInt(ipPr[1].trim()));
		                    scanEnginesForScanning.add(scanEngTobeUsed);
		                }
		            }else{
		            	return DEFAULT_ERR;
		            }
		        }
				virusScan = VirusScan.getVirusScan(scanEnginesForScanning);
			}
			Result result = null;
			//基于文件模式扫描
			if("0".equals(scanningMode)){
				result = virusScan.filebasedScanning(fileForScan, scPolicy);
			}else if("1".equals(scanningMode)){ //基于流模式扫描
				boolean isFileLocal = true;
				if("1".equals(streamFileLocal)){ //文件不在本地
					isFileLocal = false;
				}
				
				result = virusScan.streambasedScanning(fileForScan, scanFileOriginalName, scanOutputFile, scPolicy, isFileLocal);
			}else{
				return DEFAULT_ERR;
			}
			//扫描结果不存在
			if(null == result)
			{
				return DEFAULT_ERR;
			}
			
			if(result.getTotalInfection()==0&&ResultStatus.CLEAN.equals(result.getStatus())){
				File tmpFile = new File(scanOutputFile);
				if(tmpFile.exists()) {
					tmpFile.delete();
				}
				return SUC;
			}else {
				File tmpFile = new File(scanOutputFile);
				if(tmpFile.exists()) {
					tmpFile.delete();
				}
				return DEFAULT_ERR;
			}
		} catch (ScanException e) {
			return DEFAULT_ERR;
		} catch (Exception e2){
			return DEFAULT_ERR;
		} finally {
			File tmpFile = new File(scanOutputFile);
			if(tmpFile.exists()) {
				tmpFile.delete();
			}
		}
	}
}