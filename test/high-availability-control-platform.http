# 测试顺序，验证码-> 加密密码-> 登录
### 验证码
GET {{ url }}/v1/sys/captcha/getCaptcha?captchaReqId={{captchaReqId}}

### 获取密钥
GET {{ url }}/v1/sys/cipher/keys?username=admin&captchaReqId={{captchaReqId}}

### 加密密码
GET {{ url }}/v1/sys/encrypt-password?username=test&password=test012.&captchaReqId={{captchaReqId}}

> {%
    client.global.set("password",response.body.body)
%}

### 加密密码(非登录接口，容器等接口账号信息加密)
GET {{ url }}/v1/sys/encrypt-password?username={{captchaReqId}}&password=test&captchaReqId={{captchaReqId}}

> {%
    client.global.set("not_longin_password",response.body.body)
%}

### 登录
POST {{ url }}/v1/sys/login
Content-Type: application/json

{
  "username": "test",
  "password": "{{password}}",
  "captchaCode": "12345",
  "captchaReqId": "{{captchaReqId}}"
}

> {%
client.global.set("token",JSON.parse(response.body).body.sessionId)
 %}

### 退出登录
DELETE {{ url }}/v1/sys/logout
Token: {{token}}

### 获取用户信息
GET {{ url }}/v1/sys/user/info
Token: {{token}}

### 获取部门信息
GET {{ url }}/v1/sys/department/info/1
Token: {{token}}


### 获取权限分页列表
GET {{ url }}/v1/sys/role/page?pageSize=10&pageNum=1
Token: {{token}}


### 获取项目列表
GET {{ url }}/v1/tenant/workspace/own/list?tenantId=
Token: {{token}}

### 修改机房
POST {{ url }}/v1/dispatch-zone/update
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "id": 4,
  "zoneName": "xxxx",
  "zoneLabel": "dsd",
  "remarks": ""
}

### 获取机房详情
GET {{ url }}/v1/dispatch-zone/info?id=12
Token: {{token}}
workspaceId: {{workspaceId}}


### 删除机房
POST {{ url }}/v1/dispatch-zone/delete
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "id": 17
}

### 机房分页查询
POST {{ url }}/v1/dispatch-zone/page
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "pageNum":1,
  "pageSize": 10
}

### 用户管理的租户列表
GET {{ url }}/v1/tenant/own/list
Token: {{token}}

### 用户管理的虚列表
GET {{ url }}/v1/tenant/workspace/own/list?tenantId=1473a517cb9444c4aa3e10f5d043d59b
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

### 租户获取菜单
GET {{ url }}/v1/tenant/sys/menus
Token: {{token}}


### 查询标签集合
GET {{ url }}/v1/api-tag/list-all-tag
Token: {{token}}
workspaceId: {{workspaceId}}

### 查询接口详情
GET {{ url }}/v1/api-location/10
Token: {{token}}
workspaceId: {{workspaceId}}

### 查询接口列表
GET {{ url }}/v1/api-location/list
Token: {{token}}
workspaceId: {{workspaceId}}

### 获取调度规则详情-支持批量用于生成调度预览
POST {{ url }}/v1/dispatch-rule/orderGetRules
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

  [15]

### 调度节点
GET {{ url }}/v1/dispatch-node/list
Token: {{token}}
workspaceId: {{workspaceId}}


### 接口list
POST {{ url }}/v1/api-location/delete
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "id": 22
}

### 调度list
GET {{ url }}/v1/dispatch/list?pageNum=1&pageSize=10&dispatchName=&apiTag=&status=
Token: {{token}}
workspaceId: {{workspaceId}}


### 测试
GET {{ url }}/v1/dispatch-test/get?dispatchTestId=2
Token: {{token}}
workspaceId: {{workspaceId}}

### 调度机房
GET {{ url }}/v1/dashboard/zone-dispatch
Token: {{token}}
workspaceId: {{workspaceId}}

### 首页下拉
GET {{ url }}/v1/dashboard/drop-down
Token: {{token}}
workspaceId: {{workspaceId}}


### 获取规则
GET {{ url }}/v1/dispatch-rule/page?pageSize=10&pageNum=1
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
"pageSize": 10,
"pageNum": 1
}

### 新增应急机房
POST {{ url }}/v1/emergency-host/add
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "hostTag": "bus",
  "hostDesc": "desc",
  "hostUsername": "test",
  "hostPort": 22,
  "hostAddress": "***********",
  "hostType": "CL_HOST",
  "hostOS": "Linux"
}

### 查询应急机房
GET {{ url }}/v1/emergency-host/page?pageSize=10&pageNum=1&hostType=CL_HOST
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10
}

### 查询应急机房详情
GET {{ url }}/v1/emergency-host/info?hostId=3
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json


### 查询应急机房list
GET {{ url }}/v1/emergency-host/list
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

### 修改应急机房list
POST {{ url }}/v1/emergency-host/update
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{

  "hostId": 2,
  "hostTag": "bus-test",
  "hostDesc": "desc",
  "hostUsername": "test",
  "hostPassword": "test",
  "hostPort": 22,
  "hostAddress": "***********",
  "hostOS": "Linux",
  "hostTagIds": [5,7]
}

### 修改应急机房list
GET {{ url }}/v1/emergency-host/drop-down
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

### 删除应急机房
POST {{ url }}/v1/emergency-host/delete
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "hostId": 2
}

### 下载模板
GET {{ url }}/v1/emergency-host/template
Token: {{token}}
workspaceId: {{workspaceId}}

### 新增应用
POST {{ url }}/v1/emergency-app/add
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "hostApp": "test",
  "hostAppName": "test"
}

### 查询标签
GET {{ url }}/v1/emergency-tag/list
Token: {{token}}
workspaceId: {{workspaceId}}


### 删除
POST {{ url }}/v1/emergency-tag/delete
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "hostTagId": 1
}

### 查询首页
GET {{ url }}/v1/dashboard/panels
Token: {{token}}
workspaceId: {{workspaceId}}


### 更新容器
POST {{ url }}/v1/tenant/container/add-or-update
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
"containerId":null,
"clientId":"kubesphere",
"clientSecret":"kubesphere",
"grantType":"password",
"username":"lihuiquangy",
"password":"{{not_longin_password}}",
"rootUrl":"http://************:31407",
"uuid":"{{captchaReqId}}",
"workspaceId":"00beb8c872324670bf90ace9779d6c4b"
}

### 查询host列表
GET {{ url }}/v1/emergency-host/list
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "hostAppIds": [1,2]
}

### 获取未读消息数量
GET {{ url }}/v1/message/notice/number-of-unread-message
Token: {{token}}

### 获取消息列表
GET {{ url }}/v1/message/notice/get-messages?pageNum=1&pageSize=10
Token: {{token}}

### 单个已读
POST {{ url }}/v1/message/notice/read-message/1
Token: {{token}}

### 全部已读
POST {{ url }}/v1/message/notice/read-message-all
Token: {{token}}


### 发送消息
GET {{ url }}/demo/send
Token: {{token}}
workspaceId: {{workspaceId}}


###
GET {{ url }}/demo/getToken
Token: {{token}}
workspaceId: {{workspaceId}}

### 重试
POST {{ url }}/v1/emergency/process/retry-task
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
"processInstanceId": "23646848-8f90-11ef-bda1-e6fd456bedec"
}

### 重试
POST {{ url }}/v1/emergency/process/complete-task
Token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "processInstanceId": "2927b250-8f8e-11ef-bda1-e6fd456bedec",
  "result": "pass"
}

### 重试
POST {{ url }}/v1/emergency/process/retry-task
token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "processInstanceId": "eb5c9182-9768-11ef-9371-e4fd456bedf0",
  "businessKey": "98cac3db-7afe-47be-96e5-02a97cccba81"

,
  "activityId": "Activity_1qyjqy2",
  "skip": true
}

### 重试
POST {{ url }}/v1/emergency/process/get
token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "id": "18b4617f-975a-11ef-bf47-e4fd456bedf0",
  "businessKey": "3fe58698-ab04-443e-ab69-a83f36499f50"
}

### 查询日志
POST {{ url }}/v1/emergency/process/get-task-execute-log
token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "activityId":"Activity_1vnwj0l",
  "index":null,
  "businessKey":"47a96e00-7f9f-45fe-aedc-1e99ceba5ea0"
}


### 查询双活管控流量列表
POST {{ url }}/v1/emergency/extend/cmft-dispatch/query-strategy-page
token: {{token}}
workspaceId: {{workspaceId}}
Content-Type: application/json

{
  "pageNo": "1",
  "pageSize": "50"
}



### 查询双活管控流量列表12
GET {{ url }}/v1/emergency/extend/cmft-dispatch/get
token: {{token}}
workspaceId: {{workspaceId}}



### 查询双活管控流量列表2
GET {{ url }}/v1/emergency/extend/cmft-dispatch/get1
token: {{token}}
workspaceId: {{workspaceId}}

###
GET {{ url }}/v1/scim/sync