package com.hisun.mod.client;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;

public class HttpConnector {

    public static final String METHOD_TYPE_GET = "GET";
    public static final String METHOD_TYPE_POST = "POST";

    public static final String RET_HEAD = "HEAD";
    public static final String RET_STATUS = "STATUS";
    public static final String RET_MESSAGE = "MESSAGE";

    public static Map request(String url, Map data, String methodType,
            int timeout, JSONObject header) throws HttpException, IOException {

        timeout = timeout * 1000;
        Map response = new HashMap();

        HttpClient client = new HttpClient();
        client.getParams().setSoTimeout(timeout);
        client.getHttpConnectionManager().getParams().setConnectionTimeout(
                timeout);

        NameValuePair[] reqParas = new NameValuePair[0];
        if (null != data) {
            // HTTP PARAMETER
            reqParas = new NameValuePair[data.size()];
            Iterator it = data.keySet().iterator();
            String key = null;
            String value = null;
            for (int i = 0; it.hasNext(); i++) {
                key = (String) it.next();
                value = (String) data.get(key);
                reqParas[i] = new NameValuePair(key, value);
            }
        }

        HttpMethod method = null;
        try {
            if (METHOD_TYPE_GET.equals(methodType)) {
                // GET
                GetMethod get = new GetMethod(url);
                get.setQueryString(reqParas);
                method = get;
            } else {
                // DEFAULT POST
                PostMethod post = new PostMethod(url);
                post.setQueryString(reqParas);
                method = post;
            }

            method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER,
                    new DefaultHttpMethodRetryHandler(0, false));

            if (null != header) {
                // HTTP HEADER
                Iterator i = header.keySet().iterator();
                String key = null;
                String value = null;
                while (i.hasNext()) {
                    key = (String) i.next();
                    value = (String) header.get(key);
                    method.setRequestHeader(key, value);
                }
            }

            int status = client.executeMethod(method);
            response.put(RET_STATUS, status);
            response.put(RET_MESSAGE, method.getResponseBody());
            response.put(RET_HEAD, header2Map(method.getResponseHeaders()));
        } finally {
            if (null != method) {
                method.releaseConnection();
            }
            client = null;
        }

        return response;

    }

    public static Map request(String url, Map data, String methodType,
            int timeout, String jsonFmtHeaders) throws HttpException,
            IOException {

        return request(url, data, methodType, timeout, JSONObject
                .fromObject(jsonFmtHeaders));
    }

    public static Map request(String url, Map data, String methodType,
            int timeout) throws HttpException, IOException {

        return request(url, data, methodType, timeout, (JSONObject) null);
    }

    private static Map header2Map(Header[] headers) {
        Map tmp = null;
        if (null != headers) {
            tmp = new HashMap();
            for (Header header : headers) {
                tmp.put(header.getName(), header.getValue());
            }
        }
        return tmp;
    }

}
