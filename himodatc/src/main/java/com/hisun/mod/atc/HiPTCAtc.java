package com.hisun.mod.atc;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.hisun.atc.HiATCConstants;
import com.hisun.atc.common.HiArgUtils;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessageContext;

public class HiPTCAtc {

	/**
	 * 10位随机数，7位序号，1位校验位
	 * 10 + 5 位数字，跟时间戳异或 + 2位序号末两位 + 1位校验位
	 * 
	 * @param args
	 * @param ctx
	 * @return
	 */
	public int GetVCHNo(HiATLParam args, HiMessageContext ctx) throws HiException{
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		String dstFld = HiArgUtils.getStringNotNull(args, "DST_FLD");
		
		//-- 取序号
		int seq_no = GetSeqNo(ctx);
		String strSeq = StringUtils.reverse(StringUtils.leftPad(Long.toString(seq_no), 7, '0'));
		String strRandom = intArrayToString(GetRandomSequence3(10));
		String t = strRandom + StringUtils.substring(strSeq, 0, 5);
		long l0 = NumberUtils.toLong(t);
		String dt = DateFormatUtils.format(System.currentTimeMillis(), "yyMMddhhmmssSSS");

		//-- 15位异或运算
		long l1 = NumberUtils.toLong(dt);
		l0 = l0 + l1;
		long l = l0 ^ l1;
		
		String sl = Long.toString(l);
		if(sl.length() > 15) {
			sl = StringUtils.substring(sl, sl.length() - 15, sl.length());
		}
		else if(sl.length() < 15) {
			sl = StringUtils.rightPad(sl, 15, '0');
		}
		String ss = sl + StringUtils.substring(strSeq, strSeq.length()-2, strSeq.length());
		//-- 计算校验位
		String retVal = Cal(ss);
		
		ctx.getCurrentMsg().getETFBody().setChildValue(dstFld, retVal);
		return HiATCConstants.SUCC;
	}
	
	private int GetSeqNo(HiMessageContext ctx) throws HiException{
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		int seq_no = 0;
		String seqsql = "SELECT PTCSEQNO.NEXTVAL FROM DUAL";
		List queryRs = ctx.getDataBaseUtil().execQuery(seqsql);
		//如果没有记录
		if (queryRs != null && queryRs.size() == 0) {
			log.error("获取序列值失败");
			return -1;
		}
		else{
			Map queryRec = (HashMap) queryRs.get(0);

			Map.Entry recEntry = null;
			Iterator recIt = queryRec.entrySet().iterator();

			while (recIt.hasNext()) {
				recEntry = (Map.Entry) recIt.next();
				int val = NumberUtils.toInt( (String)recEntry.getValue());
				
				seq_no = val + 1;
				break;
			}
		}
		
		return seq_no;
	}
	
	private int[] GetRandomSequence3(int total)
    {

        int[] sequence = new int[total];
        int[] output = new int[total];

        for (int i = 0; i < total; i++)
        {
            sequence[i] = i;
        }

        Random random = new Random();

        int end = total - 1;

        for (int i = 0; i < total; i++)
        {
            int num = random.nextInt(end + 1);
            
            output[i] = sequence[num];
            sequence[num] = sequence[end];
            end--;
        }

        return output;
    }
    
	/**
	 * 计算校验位
	 * @param src
	 * @return
	 */
    private String Cal(String src) {
    	char[] srcArr = src.toCharArray();

		int n = 0;
		for (int i = 0; i < srcArr.length; i++) {
			String t = String.valueOf(srcArr[i]);
			int m = NumberUtils.toInt(t);

			if ((i + 1) % 2 == 0) {
				String strM = Integer.toString(m * 2);
				char[] strArr = strM.toCharArray();

				for (int j = 0; j < strArr.length; j++) {
					String t0 = String.valueOf(strArr[j]);
					int k = NumberUtils.toInt(t0);
					n = n + k;
				}
			} else {
				n = n + m;
			}
		}

		String dst = "";
		if (n % 10 == 0) {
			dst = "0";
		} else {
			int j = n / 10 + 1;
			j = j * 10;
			dst = String.valueOf(j - n);
		}

		src = src + dst;
		return src;
    }
    
    private String intArrayToString(int[] a) {
    	if(a == null) return "";
    	
    	StringBuffer str = new StringBuffer();
    	for(int i =0 ; i< a.length; i++) {
    		str.append(a[i]);
    	}
    	
    	return str.toString();
    }
}
