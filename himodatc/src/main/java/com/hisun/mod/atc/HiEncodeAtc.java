package com.hisun.mod.atc;

import org.apache.commons.lang.StringUtils;

import com.hisun.atc.HiATCConstants;
import com.hisun.atc.common.HiArgUtils;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.message.HiETF;
import com.hisun.message.HiMessageContext;

public class HiEncodeAtc {

	public int ToDBC(HiATLParam argsMap, HiMessageContext ctx)
			throws HiException {

		String str = HiArgUtils.getStringNotNull(argsMap, "srcStr");
		String dstFld = argsMap.get("dstFld");
		if (StringUtils.isEmpty(dstFld)) {
			dstFld = "OUT_STR";
		}

		String outStr = SBCtoDBC(str);
		HiETF etfBody = ctx.getCurrentMsg().getETFBody();
		etfBody.setChildValue(dstFld, outStr);

		return HiATCConstants.SUCC;
	}

	public int ToSBC(HiATLParam argsMap, HiMessageContext ctx)
			throws HiException {

		String str = HiArgUtils.getStringNotNull(argsMap, "srcStr");
		String dstFld = argsMap.get("dstFld");
		if (StringUtils.isEmpty(dstFld)) {
			dstFld = "OUT_STR";
		}

		String outStr = DBCtoSBC(str);
		HiETF etfBody = ctx.getCurrentMsg().getETFBody();
		etfBody.setChildValue(dstFld, outStr);

		return HiATCConstants.SUCC;
	}

	public static String SBCtoDBC(String str) throws HiException {

		StringBuffer outStr = new StringBuffer();
		String tStr = "", oStr = "";
		byte[] b = null;

		try {
			for (int i = 0; i < str.length(); i++) {
				tStr = str.substring(i, i + 1);
				b = tStr.getBytes("unicode");
				if (b[2] == 48 && b[3] == 0) {
					b[2] = 0;
					b[3] = 32;
					oStr = new String(b, "unicode");
				} else if (b[2] == -1) {
					b[3] = (byte) (b[3] + 32);
					b[2] = 0;
					oStr = new String(b, "unicode");
				} else {
					oStr = tStr;
				}
				outStr.append(oStr);
			}
		} catch (java.io.UnsupportedEncodingException e) {
			throw new HiException(e);
		}

		return outStr.toString();
	}

	public static String DBCtoSBC(String str) throws HiException {

		StringBuffer outStr = new StringBuffer();
		String tStr = "", oStr = "";
		byte[] b = null;

		try {
			for (int i = 0; i < str.length(); i++) {
				tStr = str.substring(i, i + 1);
				b = tStr.getBytes("unicode");
				if (b[2] == 0 && b[3] == 32) {
					b[2] = 48;
					b[3] = 0;
					oStr = new String(b, "unicode");
				} else if (b[2] == 0) {
					b[3] = (byte) (b[3] - 32);
					b[2] = -1;
					oStr = new String(b, "unicode");
				} else {
					oStr = tStr;
				}
				outStr.append(oStr);
			}
		} catch (java.io.UnsupportedEncodingException e) {
			throw new HiException(e);
		}

		return outStr.toString();
	}

}
