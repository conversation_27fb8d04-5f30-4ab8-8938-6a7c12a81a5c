package com.hisun.mod.atc;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.SystemUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.hisun.atc.common.HiArgUtils;
import com.hisun.atc.common.HiFileOpts;
import com.hisun.constants.HiMessageCode;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiContext;
import com.hisun.message.HiMessageContext;
import com.hisun.util.HiThreadPool;
import com.hisun.util.HiWorker;

public class DumpThreadInfo {

    Logger log = HiLog.getLogger("S.MONTHD.trc");

    private static int WARN_PERCENT = 70;

    public int execute(HiATLParam argsMap, HiMessageContext ctx)
            throws HiException {
        String threadPool = HiArgUtils.getStringNotNull(argsMap, "ThreadPool");
        String totSize = HiArgUtils.getStringNotNull(argsMap, "TOT_SIZE");
        String stuckTm = argsMap.get("STK_TM");
        String warnLevel = argsMap.get("WARN_LEVEL");
        int iStuckTm = 10;
        if (!StringUtils.isEmpty(stuckTm)) {
            iStuckTm = NumberUtils.toInt(stuckTm);
        }

        int warnPercent = 0;
        if (StringUtils.isEmpty(warnLevel)) {
            warnPercent = WARN_PERCENT;
        } else {
            warnPercent = NumberUtils.toInt(warnLevel);
        }

        int tSize = NumberUtils.toInt(totSize);
        HashMap<String, ThreadInfo> map = new HashMap<String, ThreadInfo>();

        HiThreadPool threadPoolExecutor = (HiThreadPool) HiThreadPool
                .getThreadPoolMap().get(threadPool);

        int totThreadSize = threadPoolExecutor.getWorkers().size();

        if (log.isInfoEnabled()) {
            log.info("Pool:[" + threadPool + "] size:[" + totSize
                    + "] currSize:[" + totThreadSize + "] PERCENT:["
                    + warnPercent + "] queueCurrSize:["
                    + threadPoolExecutor.getQueue().size() + "] remainSize:["
                    + threadPoolExecutor.getQueue().remainingCapacity() + "]");
        }

        for (HiWorker worker : threadPoolExecutor.getWorkers()) {
            HiContext wctx = worker.getContext();
            String context = null;
            if (null != wctx) {
                context = wctx.toString();
            }
            // String context = HiThreadLocalExt
            // .getThreadLocal(worker.getThread()).toString();

            if (StringUtils.isEmpty(context)) {
                continue;
            }

            int i = StringUtils.indexOf(context, "STC");
            // int i = StringUtils.indexOf(context, "Transaction:[");
            if (i < 0) {
                continue;
            }
            int l = StringUtils.indexOf(context, "\t", i + 3);
            // int l = StringUtils.indexOf(context, "]", i);

            String txnCd = StringUtils.substring(context, i + 3, l);
            // String txnCd = StringUtils.substring(context, i + 13, l);

            // log.info("txnCd:[" + txnCd + "]");
            /*
             * i = StringUtils.indexOf(context, "SRT"); String SRT = ""; if(i >
             * 0) { int k = StringUtils.lastIndexOf(context, "SRT"); int j =
             * StringUtils.indexOf(context, "   ", k); SRT =
             * StringUtils.substring(context, i + 3, j); }
             */

            ThreadInfo info = map.get(txnCd);
            long thdTm = System.currentTimeMillis() - worker.getStartTm();
            if (info == null) {
                info = new ThreadInfo(txnCd, tSize, "");
            }
            info.sumTm(thdTm);
            info.count();
            map.put(txnCd, info);

            // -- 判断时间
            // if ( (thdTm > iStuckTm * 1000) ||
            // (totThreadSize >= tSize * warnPercent / 100) ) {
            // long tm = System.currentTimeMillis();
            // String stm = DateFormatUtils.format(new Date(tm),
            // "yyyyMMddHHmmss");
            // String file = System.getenv("HWORKDIR") +
            // "/dump/"+threadPool+"_STUCK_"+stm+".dmp";
            // log.info("file=["+file+"]");
            // logStuck("true", file, worker);
            // }
        }

        // -- dump map
        for (ThreadInfo info : map.values()) {
            log.info(info);
        }

        map = null;
        return 0;
    }

    public class ThreadInfo {
        String txnCd;
        int size = 0;
        int totSize = 0;
        long totTm = 0;
        String src; // 来源

        public ThreadInfo(String txnCd, int totSize, String src) {
            this.txnCd = txnCd;
            this.totSize = totSize;
            // this.src = src;
        }

        public void count() {
            size++;
        }

        public void sumTm(long tm) {
            totTm = totTm + tm;
        }

        public String toString() {
            double percent = 0;
            if (totSize > 0) {
                percent = (double) size / (double) totSize;
            }

            String level = "INFO";
            if (percent >= 0.3 && percent < 0.6) {
                level = "WARN";
            } else if (percent >= 0.6 && percent < 0.8) {
                level = "CRITIC WARN";
            } else if (percent >= 0.8) {
                level = "FALTAL";
            }

            double avgTm = (double) totTm / (double) size;
            BigDecimal t = new BigDecimal(avgTm);
            t = t.setScale(2, BigDecimal.ROUND_HALF_UP);

            return level + " txnCd:[" + txnCd + "] size:[" + size
                    + "] percent:[" + percent * 100 + " %] avgTm:[" + t
                    + " ms]";
        }
    }

    private void logStuck(String mode, String filNam, HiWorker worker)
            throws HiException {
        FileWriter fw = null;

        try {
            if ("true".equalsIgnoreCase(mode)) {
                fw = new FileWriter(new File(filNam), true);
            } else {
                fw = new FileWriter(new File(filNam), false);
            }
            HiFileOpts.writeLineWithTMS(fw, "THREAD STUCK <<<");
            HiFileOpts.writeLine(fw, "stuck thread:[" + worker.getThread()
                    + "] has been busy :["
                    + (System.currentTimeMillis() - worker.getStartTm()) / 1000
                    + "] seconds.");
            HiFileOpts.writeLine(fw, "context:[" + worker.getContext() + "]");
            HiFileOpts.writeLine(fw, "exception stack:");
            for (StackTraceElement element : worker.getThread().getStackTrace()) {
                fw.write("        at ");
                fw.write(element.toString());
                fw.write(SystemUtils.LINE_SEPARATOR);
            }
            HiFileOpts.writeLine(fw, ">>>");
        } catch (IOException e) {
            throw new HiException(HiMessageCode.ERR_FILE_IO, filNam, e);
        } finally {
            if (fw != null) {
                try {
                    fw.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void main(String[] args) {
        String context = "msg:SIDOPTSPTC1WEB237000000000030       STPPLTOUT       SSZ000000004192 STSN    MON0    STF1    STM1318126274046        SIP222.240.192.202      STE[Application:[pes](TID000000000000342520), Transaction:[5011000](T";
        int i = StringUtils.indexOf(context, "Transaction:[");
        String txnCd = StringUtils.substring(context, i + 13, i + 20);
        System.out.println("txnCd:=" + txnCd);

        double percent = 0;
        int size = 1;
        int totSize = 400;
        if (totSize > 0) {
            percent = (double) size / (double) totSize;
        }

        long tm = 100000;
        long s = 111l;

        double avg = (double) tm / (double) s;
        BigDecimal t = new BigDecimal(avg);
        t = t.setScale(2, BigDecimal.ROUND_HALF_UP);
        System.out.println("avg:" + t);
        System.out.println("percent:" + percent * 100 + "%");
    }
}
