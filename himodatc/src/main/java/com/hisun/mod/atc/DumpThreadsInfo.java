package com.hisun.mod.atc;

import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiContext;
import com.hisun.message.HiMessageContext;
import com.hisun.util.HiThreadPool;
import com.hisun.util.HiWorker;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * User: QC
 * Date: 2015-06-26
 * Time: 17:47
 */
public class DumpThreadsInfo {
    private static final String SEP = "|";
    private Logger log = HiLog.getLogger("S.MONTHDS.trc");

    private void dumpAll(ConcurrentHashMap threadPoolMap) {
        for (Object entry : threadPoolMap.entrySet()) {
            Map.Entry poolEntry = (Map.Entry) entry;
            dump((String) poolEntry.getKey(), (HiThreadPool) poolEntry.getValue());
        }
    }

    private void dumpByName(String name, ConcurrentHashMap threadPoolMap) {
        if (threadPoolMap.containsKey(name)) {
            dump(name, (HiThreadPool) threadPoolMap.get(name));
        } else {
            log.error("Pool:[" + name + "] does not exist");
        }
    }

    private void dumpByNames(String[] names, ConcurrentHashMap threadPoolMap) {
        for (String name : names) {
            dumpByName(name, threadPoolMap);
        }
    }

    private void dump(String name, HiThreadPool threadPoolExecutor) {
        if (log.isInfoEnabled()) {
            StringBuffer sb = new StringBuffer();
            sb.append("Pool:").append(name).append(SEP)
                    .append("coreSize:").append(threadPoolExecutor.getCorePoolSize()).append(SEP)
                    .append("maxSize:").append(threadPoolExecutor.getMaximumPoolSize()).append(SEP)
                    .append("largestSize:").append(threadPoolExecutor.getLargestPoolSize()).append(SEP)
                    .append("activeCount:").append(threadPoolExecutor.getActiveCount()).append(SEP)
                    .append("queueCurrSize:").append(threadPoolExecutor.getQueue().size()).append(SEP)
                    .append("queueRemainSize:").append(threadPoolExecutor.getQueue().remainingCapacity()).append(SEP);
            log.info(sb);
        }
        dumpTrans(threadPoolExecutor);
    }

    private void dumpTrans(HiThreadPool threadPoolExecutor) {
        HashMap<String, ThreadInfo> map = new HashMap<String, ThreadInfo>();
        int totSize = threadPoolExecutor.getMaximumPoolSize();
        long currentTimeMillis = System.currentTimeMillis();
        for (HiWorker worker : threadPoolExecutor.getWorkers()) {
            HiContext ctx = worker.getContext();
            String context = null;
            if (null != ctx) {
                context = ctx.toString();
            }
            if (StringUtils.isEmpty(context)) {
                continue;
            }
            int i = StringUtils.indexOf(context, "STC");
            if (i < 0) {
                continue;
            }
            int l = StringUtils.indexOf(context, "\t", i + 3);
            String txnCd = StringUtils.substring(context, i + 3, l);
            ThreadInfo info = map.get(txnCd);
            if (info == null) {
                info = new ThreadInfo(txnCd, totSize);
            }
            info.sum(currentTimeMillis - worker.getStartTm());
            map.put(txnCd, info);
        }
        for (ThreadInfo info : map.values()) {
            log.info(info);
        }
    }

    public int execute(HiATLParam argsMap, HiMessageContext ctx)
            throws HiException {
        String name = argsMap.get("ThreadPool");
        ConcurrentHashMap threadPoolMap = HiThreadPool.getThreadPoolMap();
        if (StringUtils.isBlank(name)) {
            dumpAll(threadPoolMap);
        } else if (name.contains("|")) {
            dumpByNames(StringUtils.split(name, "|"), threadPoolMap);
        } else {
            dumpByName(name, threadPoolMap);
        }
        return 0;
    }

    private class ThreadInfo {
        private static final double WARN_LIMIT = 0.3;
        private static final double CRITICAL_WARN_LIMIT = 0.6;
        private static final double FATAL_LIMIT = 0.8;
        private String txnCd;
        private int count = 0;
        private int totSize = 0;
        private long totTm = 0;

        public ThreadInfo(String txnCd, int totSize) {
            this.txnCd = txnCd;
            this.totSize = totSize;
        }

        public void sum(long tm) {
            totTm = totTm + tm;
            count++;
        }

        public String toString() {
            double percent = 0;
            if (totSize > 0) {
                percent = (double) count / (double) totSize;
            }
            String level = "INFO";
            if (percent >= WARN_LIMIT && percent < CRITICAL_WARN_LIMIT) {
                level = "WARN";
            } else if (percent >= CRITICAL_WARN_LIMIT && percent < FATAL_LIMIT) {
                level = "CRITICAL WARN";
            } else if (percent >= FATAL_LIMIT) {
                level = "FATAL";
            }
            double avgTm = (double) totTm / (double) count;
            BigDecimal t = new BigDecimal(avgTm);
            t = t.setScale(2, BigDecimal.ROUND_HALF_UP);
            return level + " txnCd:" + txnCd + "|count:" + count
                    + "|percent:" + percent * 100 + "%|avgTm:" + t + "|";
        }
    }
}
