package com.hisun.mod.atc;

import com.hisun.atc.HiATCConstants;
import com.hisun.atc.common.HiArgUtils;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;

/**
 * 声音序列产生
 *
 * <AUTHOR> 曾益
 * @date : 2018/9/21
 */
public class SoundSerialNoProduction {
    private final static int DEFAULT_LENGTH = 7;
    private final static Random r = new Random();
    private final static Set<Integer> books = new HashSet<Integer>(10);

    static {
        books.add(0);
        books.add(1);
        books.add(2);
        books.add(3);
        books.add(4);
        books.add(5);
        books.add(6);
        books.add(7);
        books.add(8);
        books.add(9);
    }

    public int execute(HiATLParam params, HiMessageContext ctx) throws HiException {
        HiMessage mess = ctx.getCurrentMsg();
        String inputLen = params.get("Length");
        String outNode = HiArgUtils.getStringNotNull(params, "OutNode");
        int length = 0;
        if (StringUtils.isBlank(inputLen)) {
            length = DEFAULT_LENGTH;
        } else if (Integer.valueOf(inputLen) <= DEFAULT_LENGTH) {
            length = DEFAULT_LENGTH;
        } else {
            length = Integer.valueOf(inputLen);
        }

        String result = produce(length);
        mess.getETFBody().setGrandChildNodeBase(outNode, result);
        return HiATCConstants.SUCC;
    }

    /**
     * 产生随机数
     * @param len
     * @return
     */
    private String produce(int len) {
        Set<Integer> firstSet = getFirstSet(books);
        Integer firstNum = (Integer) firstSet.toArray()[r.nextInt(firstSet.size())];
        StringBuilder sb = new StringBuilder(len);
        sb.append(firstNum);
        Integer point = firstNum;
        for (int i = 0; i < len - 1; i++) {
            Set<Integer> numberSet = getSetByNumber(books, point);
            Integer number = (Integer) numberSet.toArray()[r.nextInt(numberSet.size())];
            sb.append(number);
            point = number;
        }
        return sb.toString();
    }

    /**
     * 根据上一个数字获取Set集合
     * @param orginList
     * @param num
     * @return
     */
    private Set<Integer> getSetByNumber(Set<Integer> orginList, int num) {
        Set<Integer> tmpList = getCopySet(orginList);
        if (num == 0) {
            tmpList.remove(num);
            tmpList.remove(num + 1);
        } else if (num > 0 && num < 9) {
            tmpList.remove(num - 1);
            tmpList.remove(num);
            tmpList.remove(num + 1);
        } else {
            tmpList.remove(num - 1);
            tmpList.remove(num);
        }
        return tmpList;
    }

    /**
     * 获取首位数字集合
     * @param orginSet
     * @return
     */
    private Set<Integer> getFirstSet(Set<Integer> orginSet) {
        Set<Integer> tmpList = getCopySet(orginSet);
        tmpList.remove(0);
        return tmpList;
    }

    /**
     * 拷贝Set
     * @param orginSet
     * @return
     */
    private Set<Integer> getCopySet(Set<Integer> orginSet) {
        Set<Integer> desList = new HashSet<Integer>(orginSet.size());
        desList.addAll(orginSet);
        return desList;
    }
}
