package com.hisun.mod.atc;

import java.security.Key;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;

import org.apache.commons.lang.StringUtils;

import sun.misc.BASE64Decoder;

import com.hisun.atc.HiATCConstants;
import com.hisun.atc.common.HiArgUtils;
import com.hisun.essc.atc.HiEHCacheEsscUtil;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiETF;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;

public class ThreeDesDecrypt {
	/**
     * 3DES解密,不要IV
     * @param KeyId 密钥
     * @param data Base64编码的密文
     * @return 明文
     * @throws Exception
     */
	
	public int execute(HiATLParam args, HiMessageContext ctx)
			throws Exception {
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		String KeyFlag = args.get("KeyFlag");
		if(KeyFlag==null){
			KeyFlag = "Y";
		}
		String KeyId = HiArgUtils.getStringNotNull(args, "KeyId");
		String decryptData = HiArgUtils.getStringNotNull(args, "decryptData");
		String decodeResult = args.get("decodeResult");
		if (StringUtils.isBlank(decodeResult)) {
			decodeResult = "ChkValue";
		}
		
		HiMessage msg = ctx.getCurrentMsg();
		String decoded = null;
		
		if(KeyFlag.equals("Y")){
			//从EHCASHE获取3DES加解密相应秘钥  add20140912
			args.put("ParamNam", "DES_KEYS");
			args.put("KeyNam", KeyId);
			args.put("GrpNam", "DES_KEYNOD");
			HiEHCacheEsscUtil ehkey = HiEHCacheEsscUtil.getInstance();
		    int ret = ehkey.GetFromEHCache(args, ctx);
		    if(ret!=0){
		    	log.error("get des key failed");
		    	return HiATCConstants.NFND_REC;
		    }
		    
		    HiETF keyBody = ctx.getETFNode("DES_KEYNOD");
			if(keyBody==null){
				log.error("get des key failed");
				return HiATCConstants.NFND_REC;
			}
			String deskey = keyBody.getChildNode("KEY_VALUE").getValue();
			decoded = ThreeDesDecrypt.decryptMode(deskey,decryptData);
		}else{
			decoded = ThreeDesDecrypt.decryptMode(KeyId,decryptData);
		}
		msg.getETFBody().setChildValue(decodeResult, decoded);
		return HiATCConstants.SUCC;
	}
	
    public static String decryptMode(String skey, String sdata)
            throws Exception {
    	byte[] key= new BASE64Decoder().decodeBuffer(skey);
        byte[] data= new BASE64Decoder().decodeBuffer(sdata);
    	
        Key deskey = null;
        DESedeKeySpec spec = new DESedeKeySpec(key);
        SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
        deskey = keyfactory.generateSecret(spec);
        Cipher cipher = Cipher.getInstance("desede" + "/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, deskey);
        byte[] bOut = cipher.doFinal(data);
        return new String(bOut, "UTF-8");
    }
}

