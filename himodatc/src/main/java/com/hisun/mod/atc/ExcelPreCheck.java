package com.hisun.mod.atc;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.fileupload.DiskFileUpload;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dom4j.Element;

import com.hisun.atc.common.HiArgUtils;
import com.hisun.atc.dto.HiExcelDataItemDto;
import com.hisun.constants.HiConstants;
import com.hisun.constants.HiMessageCode;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiETF;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;

public class ExcelPreCheck {

    /**
     * @功能： Excel 金额总金额和交易总笔数的预检查
     * @param FormatConfig
     *            Format配置名
     * @param FormatName
     *            Format配置项
     * @param TxnType
     *            交易类型1：取服务器本地文件 2：客户端上传文件
     * @param ExcelPath
     *            服务器本地文件路径
     * @return 0 成功 其他失败
     * @throws HiException
     */
    public int execute(HiATLParam argsMap, HiMessageContext context)
            throws HiException {
        int result = -1;
        HiMessage message = context.getCurrentMsg();
        HiETF etf = (HiETF) message.getBody();
        if (argsMap == null || argsMap.size() == 0) {
            throw new HiException(HiMessageCode.ERR_PARAM, "argsMap is Empty");
        }
        if (etf == null) {
            throw new HiException(HiMessageCode.ERR_PARAM, "etf is Empty");
        }
        Logger logger = HiLog.getLogger(message);
        String formatConfig = HiArgUtils.getStringNotNull(argsMap,
                "FormatConfig");
        String formatName = HiArgUtils.getStringNotNull(argsMap, "FormatName");
        String txnType = HiArgUtils.getStringNotNull(argsMap, "TxnType");
        String excelPath = argsMap.get("ExcelPath");
        String amtFromPage = argsMap.get("AmtFromPage");
        String cntFromPage = argsMap.get("CntFromPage");
        InputStream is = null;
        try {
            is = null;
            if ("1".equals(txnType)) {
                File file = new File(excelPath);
                if (!file.exists()
                        || (!excelPath.endsWith(".xls") && !excelPath
                                .endsWith(".xlsx"))) {
                    logger.error("无效的Excel文件路径");
                    return -1;
                }
                is = new FileInputStream(file);
            } else {
                HttpServletRequest request = (HttpServletRequest) message
                        .getObjectHeadItem(HiConstants.WEB_REQUEST);
                DiskFileUpload fu = new DiskFileUpload();
                fu.setSizeMax(1024 * 1024 * 200);
                fu.setSizeThreshold(1024 * 1024);
                fu.setHeaderEncoding("UTF-8");
                List<FileItem> fileItemList = fu.parseRequest(request);
                for (FileItem fileItem : fileItemList) {
                    if (!fileItem.isFormField()) {
                        if (fileItem.getName().trim().endsWith(".xls")
                                || fileItem.getName().trim().endsWith(".xlsx")) {
                            is = fileItem.getInputStream();
                            break;
                        }
                    }
                }
                if (is == null) {
                    throw new HiException("EXCEL00001");
                }
            }
            Workbook book = null;
            try {
                // 2007+格式
                book = new XSSFWorkbook(is);
            } catch (Exception e) {
                // 97-2003格式
                // 重新打开
                if ("1".equals(txnType)) {
                    is.close();
                    is = new FileInputStream(excelPath);
                }
                book = new HSSFWorkbook(is);
            }
            Element root = (Element) context.getProperty(
                    HiConstants.NodeNAME_ConfigDeclare, formatConfig);
            if (root == null) {
                logger.error("Format配置正确配置");
                return -1;
            }
            HashMap<String, Object> formatMap = getFmtInfo(formatName, root,
                    logger);
            if (formatMap == null) {
                return -1;
            }
            formatMap.put("amtFromPage", amtFromPage);
            formatMap.put("cntFromPage", cntFromPage);
            if (excelPreCheck(book, formatMap, context, logger)) {
                result = 0;
            }
        } catch (Exception e) {
            etf.addNode("EXCEL_TO_DB_ERROR", e.getMessage());
            throw HiException.makeException(e);
        } finally {
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
        }
        return result;
    }

    /**
     * @funtion : 解析fmt节点信息
     * 
     * @param formatName
     *            format节点名
     * @param root
     *            formatconfig根节点
     * @return HashMap 包含数据表名，读取sheet号 等信息
     * @throws HiException
     */
    private HashMap<String, Object> getFmtInfo(String formatName, Element root,
            Logger logger) {
        HashMap<String, Object> formatMap = new HashMap<String, Object>();
        List<Element> processes = root.elements("Process");
        Element process = null;
        for (Object format : processes) {
            process = (Element) format;
            if (process.attributeValue("name").equals(formatName)) {
                break;
            }
        }
        if (process == null) {
            logger.error("Process 节点配置错误");
            return null;
        } else {
            String sheetNumber = "0";
            Element excel = process.element("Excel");
            if (excel != null) {
                String sheetNum = excel.attributeValue("sheet_number");
                sheetNumber = (StringUtils.isEmpty(sheetNum)) ? sheetNumber
                        : sheetNum;
            }
            formatMap.put("sheetNumber", sheetNumber);

            String headRecordLength = "0";
            Element head = process.element("Head");
            if (head != null) {
                String recordLength = head.attributeValue("record_length");
                headRecordLength = (StringUtils.isEmpty(recordLength)) ? headRecordLength
                        : recordLength;
            }
            formatMap.put("headRecordLength", headRecordLength);
            List<Element> checkItems = head.elements("Item");
            int checkItemSize = checkItems.size();
            if (checkItemSize > 0) {
                List<String> checkItemList = new ArrayList<String>();
                String checkItemName;
                String extendItemName;
                for (Element checkItem : checkItems) {
                    checkItemName = checkItem.attributeValue("name");
                    if ("ORN_CNT".equals(checkItemName)) {
                        checkItemList.add(checkItemName);
                    }
                    if ("ORN_AMT".equals(checkItemName)) {
                        extendItemName = checkItem.attributeValue("extendItem");
                        checkItemName = (StringUtils.isEmpty(extendItemName)) ? checkItemName
                                : (checkItemName + "," + extendItemName);
                        checkItemList.add(checkItemName);
                    }
                }
                if (checkItemList.size() > 0) {
                    formatMap.put("checkItems", checkItemList);
                }
            }
            Element data = process.element("Data");
            if (data == null) {
                logger.error("Data节点没有配置");
                return null;
            } else {
                List<Element> items = data.elements("Item");
                List<Element> addItems = data.elements("AddItem");
                int itemSize = items.size();
                int addItemSize = addItems.size();
                int size = itemSize + addItemSize;
                if (size == 0) {
                    logger.error("Data节点没有配置Item选项");
                    return null;
                } else {
                    HiExcelDataItemDto[] itemArray = new HiExcelDataItemDto[size];
                    Element item = null;
                    String defaultValue = null;
                    String name = null;
                    for (int i = 0; i < itemSize; i++) {
                        item = (Element) items.get(i);
                        name = item.attributeValue("name");
                        defaultValue = item.attributeValue("default");
                        itemArray[i] = new HiExcelDataItemDto(name, "1",
                                defaultValue, null, null);
                    }
                    Element addItem = null;
                    String expression = null;
                    String value = null;
                    for (int i = 0; i < addItemSize; i++) {
                        addItem = (Element) addItems.get(i);
                        name = addItem.attributeValue("name");
                        value = addItem.attributeValue("value");
                        if (StringUtils.isEmpty(value)) {
                            expression = addItem.attributeValue("expression");
                            if (StringUtils.isEmpty(expression)) {
                                logger.error("Item节点" + name
                                        + "没有配置value属性和expression属性");
                                return null;
                            }
                        }
                        itemArray[itemSize + i] = new HiExcelDataItemDto(name,
                                "2", null, value, expression);
                    }
                    formatMap.put("items", itemArray);
                    formatMap.put("itemSize", String.valueOf(itemSize));
                    formatMap.put("addItemSize", String.valueOf(addItemSize));
                }

                String recordLength = data.attributeValue("record_length");
                if (!StringUtils.isEmpty(recordLength)) {
                    formatMap.put("dataRecordLength", recordLength);
                }
                String tableName = data.attributeValue("table_name");
                if (StringUtils.isEmpty(tableName)) {
                    logger.error("Data节点没有配置tableName属性");
                }
                formatMap.put("tableName", tableName);
            }
        }
        return formatMap;
    }

    /**
     * @function 将Excel中数据导入到数据库表
     * @param book
     *            Excel解析得到的Workbook对象
     * @param formatMap
     *            Format配置信息
     * @param context
     *            消息上下文
     * @return true成功 false失败
     * @throws Exception
     */
    private boolean excelPreCheck(Workbook book,
            HashMap<String, Object> formatMap, HiMessageContext context,
            Logger logger) throws HiException {
        HiETF etf = context.getCurrentMsg().getETFBody();
        // 创建数据库操作SQL
        HiExcelDataItemDto[] itemArray = (HiExcelDataItemDto[]) formatMap
                .get("items");

        Sheet sheet = book.getSheetAt(Integer.parseInt((String) formatMap
                .get("sheetNumber")));
        if (null == sheet) {
            logger.error("Sheet[" + formatMap.get("sheetNumber") + "] is NULL!");
            return false;
        }

        // 获取待校验的交易笔数 和交易金额
        int txnCnt = 0;
        double txnAmt = 0;
        String extendItemName = null;
        Object checkItems = formatMap.get("checkItems");
        Row firstRow = sheet.getRow(0);
        if (null == firstRow) {
            logger.error("firstRow is NULL!");
            return false;
        }
        if (checkItems != null) {
            List<String> checkItemList = (List<String>) checkItems;
            Cell tmp = null;
            for (int m = 0; m < checkItemList.size(); m++) {
                if ("ORN_CNT".equals(checkItemList.get(m))) {
                    tmp = firstRow.getCell(m);
                    if (null == tmp) {
                        logger.error("checkItems ORN_CNT cell is NULL!");
                        return false;
                    }
                    txnCnt = Integer.parseInt(ExcelPoiUtil.getCellValue(tmp));
                }
                if (checkItemList.get(m).indexOf("ORN_AMT") >= 0) {
                    String[] array = checkItemList.get(m).split(",");
                    tmp = firstRow.getCell(m);
                    if (null == tmp) {
                        logger.error("checkItems ORN_AMT cell is NULL!");
                        return false;
                    }
                    txnAmt = Double.parseDouble(ExcelPoiUtil.getCellValue(tmp));
                    if (array.length > 1) {
                        extendItemName = array[1];
                    } else {
                        extendItemName = "ORN_AMT";
                    }
                }
            }
        }
        // 交易总笔数预校验
        String cntFromPage = (String) formatMap.get("cntFromPage");
        if (!StringUtils.isEmpty(cntFromPage)) {
            Cell tmp = firstRow.getCell(0);
            if (null == tmp) {
                logger.error("Cell[0,0] cntFromPage is NULL!");
                return false;
            }
            int cntFromExcel = Integer.parseInt(ExcelPoiUtil.getCellValue(tmp));
            if (cntFromExcel > 0) {

                if (Integer.parseInt(cntFromPage) != cntFromExcel) {
                    throw new HiException("EXCEL00002");
                }
            } else {
                throw new HiException("EXCEL00003");
            }
        }
        // 交易总金额预校验
        String amtFromPage = (String) formatMap.get("amtFromPage");
        if (!StringUtils.isEmpty(amtFromPage)) {
            Cell tmp = firstRow.getCell(1);
            if (null == tmp) {
                logger.error("Cell[1,0] cntFromPage is NULL!");
                return false;
            }
            String amtFromExcel = ExcelPoiUtil.getCellValue(tmp);
            if (!amtFromPage.equals(amtFromExcel)) {
                throw new HiException("EXCEL00004");
            }
        }
        // 获取Excel读取起始行和结束行
        int count = sheet.getPhysicalNumberOfRows();
        int beginRow = Integer.parseInt((String) formatMap
                .get("headRecordLength"));
        if (beginRow > count) {
            logger.error("head_length配置错误");
            return false;
        }
        int endRow = 0;
        Object dataRecordLength = formatMap.get("dataRecordLength");
        if (dataRecordLength != null) {
            int dataLength = Integer.parseInt((String) dataRecordLength);
            if ((endRow = dataLength + beginRow) > count) {
                endRow = count;
            }
        } else {
            endRow = count;
        }
        String insertValue = null;
        int itemSize = Integer.parseInt(formatMap.get("itemSize").toString());
        int addItemSize = Integer.parseInt(formatMap.get("addItemSize")
                .toString());

        // 解析Excel，并插入数据库表
        int checkTxnCnt = 0;
        double checkTxnAmt = 0;
        Row row = null;
        for (int r = beginRow; r < endRow; r++) {
            row = sheet.getRow(r);
            if (null == row
                    || StringUtils.isEmpty(ExcelPoiUtil.getCellValue(row
                            .getCell(0)))) {
                break;
            }
            for (int c = 1; c <= itemSize; c++) {
                insertValue = ExcelPoiUtil.getCellValue(row.getCell(c - 1));
                if (StringUtils.isEmpty(insertValue)) {
                    insertValue = HiExcelDataItemDto.getInsertObject(etf,
                            itemArray[c - 1], checkTxnCnt);
                }
                if (extendItemName != null
                        && itemArray[c - 1].getName().equals(extendItemName)) {
                    checkTxnAmt = add(checkTxnAmt, insertValue);
                }
            }
            for (int c = (itemSize + 1); c <= (itemSize + addItemSize); c++) {
                insertValue = HiExcelDataItemDto.getInsertObject(etf,
                        itemArray[c - 1], checkTxnCnt);
                if (extendItemName != null
                        && itemArray[c - 1].getName().equals(extendItemName)) {
                    checkTxnAmt = add(checkTxnAmt, insertValue);
                }
            }
            checkTxnCnt++;
        }
        if (txnCnt != 0 && txnCnt != checkTxnCnt) {
            throw new HiException("EXCEL00005");
        }
        if (extendItemName != null && txnAmt != checkTxnAmt) {
            throw new HiException("EXCEL00006");
        }
        return true;
    }

    /**
     * @功能: 金额相加
     * @param v1
     *            金额值1
     * @param v2
     *            金额值2
     * @return 相加后的金额
     */
    private double add(double v1, String v2) throws HiException {
        BigDecimal b1 = null;
        BigDecimal b2 = null;
        try {
            b1 = new BigDecimal(Double.toString(v1));
            b2 = new BigDecimal(v2);
        } catch (NumberFormatException e) {
            throw new HiException("EXCEL00007");
        }
        return b1.add(b2).doubleValue();
    }
}
