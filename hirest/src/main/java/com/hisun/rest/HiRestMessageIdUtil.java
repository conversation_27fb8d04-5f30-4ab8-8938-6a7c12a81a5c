package com.hisun.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hisun.message.HiMessage;
import org.apache.commons.lang.StringUtils;

public class HiRestMessageIdUtil {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static String resolveLSN(HiMessage message) {
        String lsn = message.getHeadItem(HiMessage.LSN);
        if (StringUtils.isBlank(lsn)) {
            lsn = resolveSID(message);
        }
        return lsn;
    }

    public static String resolveSID(HiMessage message) {
        String sid = message.getHeadItem(HiMessage.SID);
        if (StringUtils.isBlank(sid)) {
            sid = message.getRequestId();
        }
        return sid;
    }

    public static String resolveTracing(HiMessage message) {
        ObjectNode tracingNode = objectMapper.createObjectNode();
        tracingNode.put("csn", message.getHeadItem(HiMessage.CSN));
        tracingNode.put("psn", message.getHeadItem(HiMessage.PSN));
        tracingNode.put("tsn", message.getHeadItem(HiMessage.TSN));
        return tracingNode.toString();
    }

}
