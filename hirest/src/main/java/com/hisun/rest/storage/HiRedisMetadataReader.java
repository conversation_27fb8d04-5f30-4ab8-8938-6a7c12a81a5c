package com.hisun.rest.storage;

import com.hisun.constants.HiMessageCode;
import com.hisun.exception.HiException;
import com.hisun.message.HiMessage;
import com.hisun.redis.HiRedisException;
import com.hisun.redis.command.HiRedisCommand;
import com.hisun.redis.command.string.HiRedisGet;
import com.hisun.redis.util.HiRedisClientATCUtil;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */
public class HiRedisMetadataReader implements HiMetadataReader {

    private String redisServer = "S.RDSCAH";

    public void setRedisServer(String redisServer) {
        this.redisServer = redisServer;
    }

    public String getMetadata(String key, HiMessage message) throws HiException {
        ArrayList<Object> arguments = new ArrayList<Object>();
        arguments.add(key);
        String result;
        try {
            HiRedisCommand command = new HiRedisGet(arguments);
            result = (String) HiRedisClientATCUtil.invoke(message, redisServer, command);
            if (!command.isSuccess(result)) {
                throw new HiException(HiMessageCode.ERR_NFND, "the key [" + key + "] cannot be found from the redis [" + redisServer + "]");
            }
        } catch (HiRedisException e) {
            throw new HiException(e);
        }

        return result;
    }
}
