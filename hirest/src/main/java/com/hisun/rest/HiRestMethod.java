package com.hisun.rest;

/**
 * Created by hailong on 2018/3/27 15:00
 */
public enum HiRestMethod {
    /**
     * GET（SELECT）：从服务器取出资源（一项或多项）。
     */
    GET,
    /**
     * POST（CREATE）：在服务器新建一个资源。
     */
    POST,
    /**
     * PATCH（UPDATE）：在服务器更新资源（客户端提供改变的属性）。
     */
    PATCH,
    /**
     * PUT（UPDATE）：在服务器更新资源（客户端提供改变后的完整资源）。
     */
    PUT,
    /**
     * DELETE（DELETE）：从服务器删除资源。
     */
    DELETE
}
