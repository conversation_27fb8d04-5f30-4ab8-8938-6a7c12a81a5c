package com.hisun.rest.adaptor;

/**
 * Created on 2018/4/12
 *
 * <AUTHOR>
 */
public class Metadata {
    private String aliasName;
    private ICSElementType type;
    private String className;

    Metadata(String aliasName, ICSElementType type) {
        this.aliasName = aliasName;
        this.type = type;
    }

    Metadata(String aliasName, ICSElementType type, String className) {
        this(aliasName, type);
        this.className = className;
    }

    public Metadata() {
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public ICSElementType getType() {
        return type;
    }

    public void setType(ICSElementType type) {
        this.type = type;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }
}
