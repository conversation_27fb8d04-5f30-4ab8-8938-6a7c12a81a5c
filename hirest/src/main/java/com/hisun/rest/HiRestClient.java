package com.hisun.rest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hisun.constants.HiMessageCode;
import com.hisun.exception.HiException;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import org.apache.commons.lang.StringUtils;
import org.apache.http.*;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.BasicClientConnectionManager;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.util.Map;

import static com.hisun.rest.HiRestMessageIdUtil.resolveLSN;

/**
 * Created by hailong on 2018/3/27 14:38
 */
public class HiRestClient {

    private static ObjectMapper objectMapper = new ObjectMapper();
    private DefaultHttpClient httpClient;
    private String encoding;
    private int connectionTimeout = 10 * 1000;
    private int soTimeout = 10 * 1000;
    private Boolean logIgnore;
    private int logIgnoreLength;

    /**
     * a) 由于性能问题 屏蔽 https Scheme
     * b) 暂不支持池化，使用BasicClientConnectionManager，避免负载超时导致交易频繁超时报错
     */
    public HiRestClient() {
        SchemeRegistry schemeRegistry = new SchemeRegistry();
        schemeRegistry.register(new Scheme("http", 80, PlainSocketFactory.getSocketFactory()));
        BasicClientConnectionManager cm = new BasicClientConnectionManager(schemeRegistry);
        this.httpClient = new DefaultHttpClient(cm);

        HttpParams params = this.httpClient.getParams();
        HttpConnectionParams.setConnectionTimeout(params, connectionTimeout);
        HttpConnectionParams.setSoTimeout(params, soTimeout);
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
        HttpParams params = this.httpClient.getParams();
        HttpConnectionParams.setConnectionTimeout(params, connectionTimeout);
    }

    public void setSoTimeout(int soTimeout) {
        this.soTimeout = soTimeout;
        HttpParams params = this.httpClient.getParams();
        HttpConnectionParams.setSoTimeout(params, soTimeout);
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public void setLogIgnore(Boolean logIgnore) {
        this.logIgnore = logIgnore;
    }

    public void setLogIgnoreLength(int logIgnoreLength) {
        this.logIgnoreLength = logIgnoreLength;
    }

    public URI buildUri(String host, int port, String endpoint, Map<String, String> queryParams)
            throws HiException {
        URIBuilder builder = new URIBuilder();
        URI uri;
        builder.setScheme("http");
        builder.setHost(host);
        builder.setPort(port);
        builder.setPath(endpoint);

        if (queryParams != null) {
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                builder.addParameter(entry.getKey(), entry.getValue());
            }
        }

        try {
            uri = builder.build();
        } catch (URISyntaxException e) {
            throw new HiException(e);
        }

        return uri;
    }

    public HiRestResponse getResponse(HttpResponse response) throws HiException {
        int statusCode = response.getStatusLine().getStatusCode();

        Header[] headers = response.getAllHeaders();

        String responseBody = "";

        HttpEntity entity = response.getEntity();
        if (entity != null) {
            try {
                responseBody = EntityUtils.toString(entity, Charset.forName(encoding));
            } catch (IOException e) {
                throw new HiException(e);
            }
        }

        return new HiRestResponse(statusCode, headers, responseBody);
    }

    public HiRestResponse get(HiRestRequest request, HiMessage message, Logger logger)
            throws HiException {

        URI uri = buildUri(request.getHost(), request.getPort(), request.getEndpoint(), request.getQueryParams());
        HttpGet httpGet = new HttpGet(uri.toString());

        buildHeader(request, httpGet);

        logRequest(httpGet, message, logger);

        return executeRestCall(httpGet);
    }

    public HiRestResponse post(HiRestRequest request, HiMessage message, Logger logger)
            throws HiException {

        URI uri = buildUri(request.getHost(), request.getPort(), request.getEndpoint(), request.getQueryParams());
        HttpPost httpPost = new HttpPost(uri.toString());

        buildHeader(request, httpPost);

        httpPost.setEntity(new StringEntity(request.getBody(), Charset.forName(encoding)));

        writeContentTypeIfNeeded(request, httpPost);

        logRequest(httpPost, message, logger);

        return executeRestCall(httpPost);
    }

    public HiRestResponse patch(HiRestRequest request, HiMessage message, Logger logger)
            throws HiException {

        URI uri = buildUri(request.getHost(), request.getPort(), request.getEndpoint(), request.getQueryParams());
        HttpPatch httpPatch = new HttpPatch(uri.toString());

        buildHeader(request, httpPatch);

        httpPatch.setEntity(new StringEntity(request.getBody(), Charset.forName(encoding)));

        writeContentTypeIfNeeded(request, httpPatch);

        logRequest(httpPatch, message, logger);

        return executeRestCall(httpPatch);
    }

    public HiRestResponse put(HiRestRequest request, HiMessage message, Logger logger)
            throws HiException {

        URI uri = buildUri(request.getHost(), request.getPort(), request.getEndpoint(), request.getQueryParams());
        HttpPut httpPut = new HttpPut(uri.toString());

        buildHeader(request, httpPut);

        httpPut.setEntity(new StringEntity(request.getBody(), Charset.forName(encoding)));
        writeContentTypeIfNeeded(request, httpPut);

        logRequest(httpPut, message, logger);

        return executeRestCall(httpPut);
    }

    public HiRestResponse delete(HiRestRequest request, HiMessage message, Logger logger)
            throws HiException {

        URI uri = buildUri(request.getHost(), request.getPort(), request.getEndpoint(), request.getQueryParams());
        HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(uri.toString());

        buildHeader(request, httpDelete);

        httpDelete.setEntity(new StringEntity(request.getBody(), Charset.forName(encoding)));
        writeContentTypeIfNeeded(request, httpDelete);

        logRequest(httpDelete, message, logger);

        return executeRestCall(httpDelete);
    }

    private void buildHeader(HiRestRequest request, HttpRequestBase httpRequestBase) {
        if (request.getHeaders() != null) {
            for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
                httpRequestBase.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    private void writeContentTypeIfNeeded(HiRestRequest request, HttpMessage httpMessage) {
        if (StringUtils.isNotEmpty(request.getBody())) {
            httpMessage.setHeader("Content-Type", "application/json");
        }
    }

    private void logRequest(HttpRequestBase httpRequestBase, HiMessage message, Logger logger) {
        if (!logger.isInfoEnabled()) {
            return;
        }
        ObjectNode node = objectMapper.createObjectNode();
        node.put("msgId", resolveLSN(message));
        node.put("type", "request");
        node.put("method", httpRequestBase.getMethod());
        node.put("endpoint", httpRequestBase.getURI().getAuthority());
        node.put("uri", httpRequestBase.getURI().getPath());
        node.put("query", getIgnoreLoggerData(httpRequestBase.getURI().getQuery()));
        Header[] headers = httpRequestBase.getAllHeaders();
        if (headers != null && headers.length > 0) {
            ObjectNode herdersNode = node.putObject("headers");
            for (Header header : headers) {
                herdersNode.put(header.getName(), getIgnoreLoggerData(header.getValue()));
            }
        }
        if (httpRequestBase instanceof HttpEntityEnclosingRequest) {
            try {
                String entity = EntityUtils.toString(((HttpEntityEnclosingRequest) httpRequestBase).getEntity());
                node.put("entity", getIgnoreLoggerData(entity));
            } catch (IOException e) {
                logger.error(e.getMessage());
            }
        }
        try {
            logger.info("rest: " + objectMapper.writeValueAsString(node));
        } catch (JsonProcessingException e) {
            logger.error(e);
        }
    }

    private void logResponse(long start, HiRestResponse restResponse, HiMessage message, Logger logger) {
        if (!logger.isInfoEnabled()) {
            return;
        }

        ObjectNode node = objectMapper.createObjectNode();
        node.put("msgId", resolveLSN(message));
        node.put("type", "response");
        node.put("duration", System.currentTimeMillis() - start);
        node.put("code", restResponse.getStatusCode());
        node.put("body", getIgnoreLoggerData(restResponse.getBody()));
        try {
            logger.info("rest: " + objectMapper.writeValueAsString(node));
        } catch (JsonProcessingException e) {
            logger.error(e);
        }

    }

    private HiRestResponse executeRestCall(HttpRequestBase httpRequestBase) throws HiException {
        HttpResponse serverResponse;
        try {
            serverResponse = httpClient.execute(httpRequestBase);
        } catch (IOException e) {
            throw new HiException(HiMessageCode.ERR_SYSTEM, e.getMessage() + "[" + httpRequestBase.getURI() + "]", e);
        }
        return getResponse(serverResponse);
    }

    public HiRestResponse rest(HiRestRequest request, HiMessage message, Logger log) throws HiException {
        HiRestResponse restResponse = null;
        long start = System.currentTimeMillis();
        try {
            if (request.getMethod() == null) {
                throw new HiException("Only support GET, PUT, PATCH, POST and DELETE.");
            }

            switch (request.getMethod()) {
                case GET:
                    restResponse = get(request, message, log);
                    break;
                case POST:
                    restResponse = post(request, message, log);
                    break;
                case PUT:
                    restResponse = put(request, message, log);
                    break;
                case PATCH:
                    restResponse = patch(request, message, log);
                    break;
                case DELETE:
                    restResponse = delete(request, message, log);
                    break;
                default:
                    throw new HiException("Only support GET, PUT, PATCH, POST and DELETE.");
            }
        } catch (HiException e) {
            throw new HiException(e);
        } finally {
            if (restResponse != null) {
                logResponse(start, restResponse, message, log);
            }
            httpClient.getConnectionManager().shutdown();
        }
        return restResponse;
    }

    private String getIgnoreLoggerData(String data) {
        if (!this.logIgnore) {
            return data;
        }
        if (null != data && data.length() > this.logIgnoreLength) {
            return "Ignore Biggest Logger";
        }
        return data;
    }
}

@NotThreadSafe
class HttpDeleteWithBody extends HttpEntityEnclosingRequestBase {

    public static final String METHOD_NAME = "DELETE";

    public HttpDeleteWithBody() {
    }

    public HttpDeleteWithBody(URI uri) {
        this.setURI(uri);
    }

    public HttpDeleteWithBody(String uri) {
        this.setURI(URI.create(uri));
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }
}
