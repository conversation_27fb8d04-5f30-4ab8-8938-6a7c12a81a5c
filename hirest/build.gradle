apply plugin: 'java'
apply from: '../common.gradle'

//version = '1.0.0.SNAPSHOT'
version = '1.0.1.RELEASE'

sourceCompatibility = 1.5
targetCompatibility = 1.5

dependencies {
    compile 'com.fasterxml.jackson.core:jackson-databind:2.12.6'
    compile 'org.apache.httpcomponents:httpclient:4.2.3'
    compile 'commons-lang:commons-lang:2.3'
    compile 'commons-io:commons-io:1.4'
    compile 'dom4j:dom4j:1.6.1'

    compile 'com.hisun:hiframework:1.0.0.RELEASE'
    compile 'com.hisun:hiredis:1.0.0.RELEASE'
    compile 'com.hisun:hiredisatc:1.0.0.RELEASE'
    compile 'com.hisun:hiehcacheext:1.0.0.RELEASE'
    compile 'com.hisun:hiconstant:1.0.0.RELEASE'
    compile 'com.hisun:hicommon:1.0.3.RELEASE'
    compile 'com.hisun:hihub:1.0.0.RELEASE'
    compile project(":hialertingtransfer")
}


