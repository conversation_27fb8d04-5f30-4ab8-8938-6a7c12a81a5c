package com.cmpay.lemon.framework.security;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2017年9月12日
 * @time 上午10:06:44
 */
@JsonDeserialize
public class LemonUsernamePasswordAuthenticationToken extends UsernamePasswordAuthenticationToken {
    private static final long serialVersionUID = 3879132540866188019L;
    private String random;

    public LemonUsernamePasswordAuthenticationToken(Object principal, Object credentials, String random) {
        super(principal, credentials);
        this.random = random;
    }

    @JsonCreator
    public LemonUsernamePasswordAuthenticationToken(@JsonProperty("principal") Object principal,
                                                    @JsonProperty("credentials") Object credentials,
                                                    @JsonProperty("authorities") Collection<? extends GrantedAuthority> authorities) {
        super(principal, credentials, authorities);
    }

    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        try {
            super.setAuthenticated(isAuthenticated);
        } catch (IllegalArgumentException e) {
        }
    }

    @JsonIgnore
    public String getRandom() {
        return random;
    }

    public void setRandom(String random) {
        this.random = random;
    }

}
