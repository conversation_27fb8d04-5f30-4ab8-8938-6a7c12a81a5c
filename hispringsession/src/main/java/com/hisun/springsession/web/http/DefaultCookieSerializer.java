package com.hisun.springsession.web.http;

import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.springsession.SessionConstants;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class DefaultCookieSerializer implements CookieSerializer {
    private Logger logger = HiLog.getLogger(SessionConstants.LOGGER_NAME);
    private String cookieName = "SESSION";
    private Boolean useSecureCookie;
    private boolean useHttpOnlyCookie;
    private String cookiePath;
    private int cookieMaxAge = -1;
    private String domainName;
    private boolean useBase64Encoding;

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    private void debug(HttpServletRequest request, String content) {
        if (logger.isDebugEnabled()) {
            logger.debug(this + "[" + request.getRequestURI() + "] " + content);
        }
    }

    public List<String> readCookieValues(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        List<String> matchingCookieValues = new ArrayList<String>();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (this.cookieName.equals(cookie.getName())) {
                    String sessionId = this.useBase64Encoding
                            ? base64Decode(cookie.getValue()) : cookie.getValue();
                    if (sessionId == null) {
                        continue;
                    }
                    matchingCookieValues.add(sessionId);
                }
            }
        }
        debug(request, "readCookieValues: " + matchingCookieValues);
        return matchingCookieValues;
    }

    public void writeCookieValue(CookieValue cookieValue) {
        HttpServletRequest request = cookieValue.getRequest();
        HttpServletResponse response = cookieValue.getResponse();
        String requestedCookieValue = cookieValue.getCookieValue();
        String actualCookieValue = this.useBase64Encoding ? base64Encode(requestedCookieValue) : requestedCookieValue;

        StringBuilder sb = new StringBuilder();
        sb.append(this.cookieName).append("=").append(actualCookieValue);
        if (getCookiePath(request) != null)
            sb.append("; Path=").append(getCookiePath(request));
        if (getDomainName(request) != null)
            sb.append("; Domain=").append(getDomainName(request));
        if (this.cookieMaxAge >= 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.SECOND, this.cookieMaxAge);
            Date date = calendar.getTime();
            String expires = (new SimpleDateFormat(
                    "EEE, dd-MMM-yyyy HH:mm:ss z", Locale.US)).format(date);
            expires = expires.replaceAll("CST", "GMT");
            sb.append("; Expires=").append(expires);
            sb.append("; Max-Age=").append(this.cookieMaxAge);
        }
        if (isSecureCookie(request))
            sb.append("; Secure");
        if (this.useHttpOnlyCookie)
            sb.append("; HttpOnly");
        response.addHeader("Set-Cookie", sb.toString());
        debug(request, "writeCookieValue: " + sb.toString());
    }

    private String base64Decode(String base64Value) {
        try {
            byte[] decodedCookieBytes = Base64.decode(base64Value.getBytes());
            return new String(decodedCookieBytes);
        } catch (Exception e) {
            return null;
        }
    }

    private String base64Encode(String value) {
        byte[] encodedCookieBytes = Base64.encode(value.getBytes());
        return new String(encodedCookieBytes);
    }

    public void setUseSecureCookie(boolean useSecureCookie) {
        this.useSecureCookie = useSecureCookie;
    }

    public void setUseHttpOnlyCookie(boolean useHttpOnlyCookie) {
        this.useHttpOnlyCookie = useHttpOnlyCookie;
    }

    private boolean isSecureCookie(HttpServletRequest request) {
        if (this.useSecureCookie == null) {
            return request.isSecure();
        }
        return this.useSecureCookie;
    }

    public void setCookiePath(String cookiePath) {
        this.cookiePath = cookiePath;
    }

    public void setCookieName(String cookieName) {
        if (cookieName == null) {
            throw new IllegalArgumentException("cookieName cannot be null");
        }
        this.cookieName = cookieName;
    }

    public void setCookieMaxAge(int cookieMaxAge) {
        this.cookieMaxAge = cookieMaxAge;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public void setUseBase64Encoding(boolean useBase64Encoding) {
        this.useBase64Encoding = useBase64Encoding;
    }

    private String getDomainName(HttpServletRequest request) {
        if (this.domainName != null) {
            return this.domainName;
        }
        return null;
    }

    private String getCookiePath(HttpServletRequest request) {
        if (this.cookiePath == null) {
            return request.getContextPath() + "/";
        }
        return this.cookiePath;
    }
}
