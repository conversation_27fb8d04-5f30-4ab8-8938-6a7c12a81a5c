package com.hisun.springsession.web.http;

import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.springsession.Session;
import com.hisun.springsession.SessionConstants;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class HeaderHttpSessionStrategy implements HttpSessionStrategy {
    private Logger logger = HiLog.getLogger(SessionConstants.LOGGER_NAME);
    private String headerName = "x-auth-token";

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    private void debug(HttpServletRequest request, String content) {
        if (logger.isDebugEnabled()) {
            logger.debug(this + "[" + request.getRequestURI() + "] " + content);
        }
    }

    public String getRequestedSessionId(HttpServletRequest request) {
        return request.getHeader(this.headerName);
    }

    public void onNewSession(Session session, HttpServletRequest request,
                             HttpServletResponse response) {
        String sessionId = session.getId();
        debug(request, "onNewSession: [" + sessionId + "]");
        response.setHeader(this.headerName, sessionId);
    }

    public void onInvalidateSession(HttpServletRequest request,
                                    HttpServletResponse response) {
        debug(request, "onInvalidateSession");
        response.setHeader(this.headerName, "");
    }

    public void setHeaderName(String headerName) {
        this.headerName = headerName;
    }

    public String getHeaderName() {
        return headerName;
    }
}
