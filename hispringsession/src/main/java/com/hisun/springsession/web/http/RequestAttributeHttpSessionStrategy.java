package com.hisun.springsession.web.http;

import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.springsession.Session;
import com.hisun.springsession.SessionConstants;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class RequestAttributeHttpSessionStrategy implements HttpSessionStrategy {
    private Logger logger = HiLog.getLogger(SessionConstants.LOGGER_NAME);
    private String attrName = "sid";

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    private void debug(HttpServletRequest request, String content) {
        if (logger.isDebugEnabled()) {
            logger.debug(this + "[" + request.getRequestURI() + "] " + content);
        }
    }

    public String getRequestedSessionId(HttpServletRequest request) {
        return (String) request.getAttribute(this.attrName);
    }

    public void onNewSession(Session session, HttpServletRequest request,
                             HttpServletResponse response) {
        String sessionId = session.getId();
        debug(request, "onNewSession: [" + sessionId + "]");
        request.setAttribute(this.attrName, sessionId);
    }

    public void onInvalidateSession(HttpServletRequest request,
                                    HttpServletResponse response) {
        debug(request, "onInvalidateSession");
        request.removeAttribute(this.attrName);
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName;
    }
}
