package com.hisun.springsession.data.redis;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.redis.command.HiRedisCommand;
import com.hisun.redis.command.hash.HiRedisHgetAll;
import com.hisun.redis.command.hash.HiRedisHmset;
import com.hisun.redis.command.key.HiRedisDel;
import com.hisun.redis.command.set.HiRedisSadd;
import com.hisun.redis.command.set.HiRedisSmembers;
import com.hisun.redis.command.set.HiRedisSrem;
import com.hisun.redis.command.util.HiRedisCommandBuilder;
import com.hisun.springsession.*;
import com.hisun.springsession.data.redis.serializer.Jackson2JsonRedisSerializer;
import com.hisun.springsession.data.redis.serializer.RedisSerializer;
import com.hisun.springsession.data.redis.serializer.SimpleStringSerializer;
import com.hisun.springsession.ics.HiRedisServerInvoker;
import org.springframework.security.jackson2.CoreJackson2Module;

import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@SuppressWarnings("unchecked")
public class RedisOperationsSessionRepository implements
        FindByIndexNameSessionRepository<RedisOperationsSessionRepository.RedisSession> {
    private Logger logger = HiLog.getLogger(SessionConstants.LOGGER_NAME);
    private static final String SPRING_SECURITY_CONTEXT = "SPRING_SECURITY_CONTEXT";
    static PrincipalNameResolver PRINCIPAL_NAME_RESOLVER = new PrincipalNameResolver();
    static final String DEFAULT_SPRING_SESSION_REDIS_PREFIX = "spring:session:";
    static final String CREATION_TIME_ATTR = "creationTime";
    static final String MAX_INACTIVE_ATTR = "maxInactiveInterval";
    static final String LAST_ACCESSED_ATTR = "lastAccessedTime";
    static final String SESSION_ATTR_PREFIX = "sessionAttr:";

    private String keyPrefix = DEFAULT_SPRING_SESSION_REDIS_PREFIX;
    private final RedisSessionExpirationPolicy expirationPolicy;
    private Integer defaultMaxInactiveInterval;
    private RedisSerializer<Object> defaultSerializer;
    private RedisFlushMode redisFlushMode = RedisFlushMode.ON_SAVE;
    private HiRedisServerInvoker redisServerInvoker;

    public RedisOperationsSessionRepository(String redisServerName) {
        this.redisServerInvoker = new HiRedisServerInvoker(redisServerName);
        this.defaultSerializer = initRedisSerializer();
        this.expirationPolicy = new RedisSessionExpirationPolicy(redisServerInvoker, this);
        this.expirationPolicy.setLogger(logger);
//        initScheduledExecutorService();
    }

    private RedisSerializer<Object> initRedisSerializer() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.disable(SerializationFeature.WRITE_NULL_MAP_VALUES);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.enable(MapperFeature.IGNORE_DUPLICATE_MODULE_REGISTRATIONS);
        mapper.registerModules(new com.cmpay.lemon.framework.security.jackson2.CoreJackson2Module(),
                new CoreJackson2Module());
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        jackson2JsonRedisSerializer.setObjectMapper(mapper);
        return jackson2JsonRedisSerializer;
    }

    private void initScheduledExecutorService() {
        final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
        scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                cleanupExpiredSessions();
            }
        }, 1, 1, TimeUnit.MINUTES);
        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                scheduledExecutorService.shutdownNow();
            }
        });
    }

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    public void setDefaultMaxInactiveInterval(int defaultMaxInactiveInterval) {
        this.defaultMaxInactiveInterval = defaultMaxInactiveInterval;
    }

    public void setRedisFlushMode(RedisFlushMode redisFlushMode) {
        this.redisFlushMode = redisFlushMode;
    }

    public void setRedisKeyNamespace(String namespace) {
        this.keyPrefix = DEFAULT_SPRING_SESSION_REDIS_PREFIX + namespace + ":";
    }

    public void save(RedisSession session) {
        session.saveDelta();
        session.setNew(false);
    }

    //    @Scheduled(cron = "${spring.session.cleanup.cron.expression:0 * * * * *}")
    public void cleanupExpiredSessions() {
        this.expirationPolicy.cleanExpiredSessions();
    }

    public RedisSession getSession(String id) {
        return getSession(id, false);
    }

    public Map<String, RedisSession> findByIndexNameAndIndexValue(String indexName, String indexValue) {
        if (logger.isDebugEnabled()) {
            logger.debug(this + " - findByIndexNameAndIndexValue indexName["
                    + indexName + "] indexValue[" + indexValue + "]");
        }
        if (!PRINCIPAL_NAME_INDEX_NAME.equals(indexName)) {
            return Collections.emptyMap();
        }
        String principalKey = getPrincipalKey(indexValue);
        try {
            HiRedisCommand command = HiRedisCommandBuilder.start(HiRedisSmembers.class)
                    .arg(principalKey).build().get(0);
            Set<String> sessionIds = (Set<String>)
                    redisServerInvoker.execute("findByIndexNameAndIndexValue", command);
            if (!command.isSuccess(sessionIds)) {
                return Collections.emptyMap();
            }
            Map<String, RedisSession> sessions = new HashMap<String, RedisSession>(
                    sessionIds.size());
            for (Object id : sessionIds) {
                RedisSession session = getSession(SimpleStringSerializer.deserialize((String) id));
                if (session != null) {
                    sessions.put(session.getId(), session);
                }
            }
            return sessions;
        } catch (Exception e) {
            logger.warn("findByIndexNameAndIndexValue error:[" + principalKey + "]", e);
            return Collections.emptyMap();
        }
    }

    private RedisSession getSession(String id, boolean allowExpired) {
        try {
            HiRedisCommand command = HiRedisCommandBuilder.start(HiRedisHgetAll.class)
                    .arg(getSessionKey(id)).build().get(0);
            Map<String, String> entries = (Map<String, String>)
                    redisServerInvoker.execute("getSession[" + id + "]", command);
            if (entries == null || entries.isEmpty()) {
                return null;
            }
            MapSession loaded = loadSession(id, entries);
            if (!allowExpired && loaded.isExpired()) {
                if (logger.isDebugEnabled()) {
                    logger.warn(this + " - [" + id + "] getSession expired: " + entries);
                }
                return null;
            }
            RedisSession result = new RedisSession(loaded);
            result.originalLastAccessTime = loaded.getLastAccessedTime();
            return result;
        } catch (Exception e) {
            logger.warn("getSession error:[" + id + "]", e);
            return null;
        }
    }

    private MapSession loadSession(String id, Map<String, String> entries) {
        MapSession loaded = new MapSession(id);
        for (Map.Entry<String, String> entry : entries.entrySet()) {
            String key = entry.getKey();
            if (CREATION_TIME_ATTR.equals(key)) {
                loaded.setCreationTime(Long.parseLong(entry.getValue()));
            } else if (MAX_INACTIVE_ATTR.equals(key)) {
                loaded.setMaxInactiveIntervalInSeconds(Integer.parseInt(entry.getValue()));
            } else if (LAST_ACCESSED_ATTR.equals(key)) {
                loaded.setLastAccessedTime(Long.parseLong(entry.getValue()));
            } else if (key.startsWith(SESSION_ATTR_PREFIX)) {
                try {
                    Object v = defaultSerializer.deserialize(entry.getValue().getBytes("UTF-8"));
                    loaded.setAttribute(key.substring(SESSION_ATTR_PREFIX.length()), v);
                } catch (Exception e) {
                    logger.debug(this + " - [" + id + "] loadSession deserialize error: ["
                            + key + "][" + entry.getValue() + "] " + e.getMessage());
//                    loaded.setAttribute(key.substring(SESSION_ATTR_PREFIX.length()), entry.getValue());
                }
            }
        }
        return loaded;
    }

    public void delete(String sessionId) {
        RedisSession session = getSession(sessionId, true);
        if (session == null) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(this + " - [" + session.getId() + "] delete");
        }

        cleanupPrincipalIndex(session);
        this.expirationPolicy.onDelete(session);

        String expireKey = getExpiredKey(session.getId());
        try {
            HiRedisCommand command = HiRedisCommandBuilder.start(HiRedisDel.class)
                    .arg(expireKey).build().get(0);
            redisServerInvoker.execute("delete[" + sessionId + "]", command);
        } catch (Exception e) {
            logger.warn("delete error:[" + expireKey + "]", e);
        }

        session.setMaxInactiveIntervalInSeconds(0);
        save(session);
    }

    public RedisSession createSession() {
        RedisSession redisSession = new RedisSession();
        if (this.defaultMaxInactiveInterval != null) {
            redisSession.setMaxInactiveIntervalInSeconds(this.defaultMaxInactiveInterval);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(this + " - [" + redisSession.getId() + "] createSession");
        }
        return redisSession;
    }

    private void cleanupPrincipalIndex(RedisSession session) {
        String sessionId = session.getId();
        String principal = PRINCIPAL_NAME_RESOLVER.resolvePrincipal(session);
        if (principal != null) {
            try {
                HiRedisCommand command = HiRedisCommandBuilder.start(HiRedisSrem.class)
                        .arg(getPrincipalKey(principal), SimpleStringSerializer.serialize(sessionId)).build().get(0);
                redisServerInvoker.execute("cleanupPrincipalIndex[" + sessionId + "]", command);
            } catch (Exception e) {
                logger.warn("cleanupPrincipalIndex error:[" + getPrincipalKey(principal) + "][" + sessionId + "]", e);
            }
        }
    }

    String getSessionKey(String sessionId) {
        return this.keyPrefix + "sessions:" + sessionId;
    }

    String getPrincipalKey(String principalName) {
        return this.keyPrefix + "index:"
                + FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME + ":"
                + principalName;
    }

    String getExpirationsKey(long expiration) {
        return this.keyPrefix + "expirations:" + expiration;
    }

    private String getExpiredKey(String sessionId) {
        return getExpiredKeyPrefix() + sessionId;
    }

    private String getExpiredKeyPrefix() {
        return this.keyPrefix + "sessions:" + "expires:";
    }

    static String getSessionAttrNameKey(String attributeName) {
        return SESSION_ATTR_PREFIX + attributeName;
    }

    /**
     * A custom implementation of {@link Session} that uses a {@link MapSession} as the
     * basis for its mapping. It keeps track of any attributes that have changed. When
     * {@link RedisOperationsSessionRepository.RedisSession#saveDelta()}
     * is invoked all the attributes that have been changed will be persisted.
     *
     * <AUTHOR> Winch
     * @since 1.0
     */
    public final class RedisSession implements ExpiringSession {
        private final MapSession cached;
        private Long originalLastAccessTime;
        private Map<String, Object> delta = new HashMap<String, Object>();
        private boolean isNew;
        private String originalPrincipalName;

        /**
         * Creates a new instance ensuring to mark all of the new attributes to be
         * persisted in the next save operation.
         */
        RedisSession() {
            this(new MapSession());
            this.delta.put(CREATION_TIME_ATTR, getCreationTime());
            this.delta.put(MAX_INACTIVE_ATTR, getMaxInactiveIntervalInSeconds());
            this.delta.put(LAST_ACCESSED_ATTR, getLastAccessedTime());
            this.isNew = true;
            this.flushImmediateIfNecessary();
        }

        /**
         * Creates a new instance from the provided {@link MapSession}.
         *
         * @param cached the {@link MapSession} that represents the persisted session that
         *               was retrieved. Cannot be null.
         */
        RedisSession(MapSession cached) {
            this.cached = cached;
            this.originalPrincipalName = PRINCIPAL_NAME_RESOLVER.resolvePrincipal(this);
        }

        public void setNew(boolean isNew) {
            this.isNew = isNew;
        }

        public void setLastAccessedTime(long lastAccessedTime) {
            this.cached.setLastAccessedTime(lastAccessedTime);
            this.putAndFlush(LAST_ACCESSED_ATTR, getLastAccessedTime());
        }

        public boolean isExpired() {
            return this.cached.isExpired();
        }

        public boolean isNew() {
            return this.isNew;
        }

        public long getCreationTime() {
            return this.cached.getCreationTime();
        }

        public String getId() {
            return this.cached.getId();
        }

        public long getLastAccessedTime() {
            return this.cached.getLastAccessedTime();
        }

        public void setMaxInactiveIntervalInSeconds(int interval) {
            this.cached.setMaxInactiveIntervalInSeconds(interval);
            this.putAndFlush(MAX_INACTIVE_ATTR, getMaxInactiveIntervalInSeconds());
        }

        public int getMaxInactiveIntervalInSeconds() {
            return this.cached.getMaxInactiveIntervalInSeconds();
        }

        public <T> T getAttribute(String attributeName) {
            return this.cached.getAttribute(attributeName);
        }

        public Set<String> getAttributeNames() {
            return this.cached.getAttributeNames();
        }

        public void setAttribute(String attributeName, Object attributeValue) {
            this.cached.setAttribute(attributeName, attributeValue);
            this.putAndFlush(getSessionAttrNameKey(attributeName), attributeValue);
        }

        public void removeAttribute(String attributeName) {
            this.cached.removeAttribute(attributeName);
            this.putAndFlush(getSessionAttrNameKey(attributeName), null);
        }

        private void flushImmediateIfNecessary() {
            if (RedisOperationsSessionRepository.this.redisFlushMode == RedisFlushMode.IMMEDIATE) {
                saveDelta();
            }
        }

        private void putAndFlush(String a, Object v) {
            this.delta.put(a, v);
            this.flushImmediateIfNecessary();
        }

        private Map<String, String> serializeDelta() {
            Map<String, String> res = new HashMap<>(this.delta.size());
            for (Map.Entry<String, Object> entry : this.delta.entrySet()) {
                try {
                    res.put(entry.getKey(), new String(defaultSerializer.serialize(entry.getValue()), "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    logger.error(this + " - serializeDelta error: " + entry.getValue(), e);
                }
            }
            return res;
        }

        /**
         * Saves any attributes that have been changed and updates the expiration of this
         * session.
         */
        private void saveDelta() {
            if (this.delta.isEmpty()) {
                return;
            }
            String sessionId = getId();
            if (logger.isDebugEnabled()) {
                logger.debug(this + " - [" + sessionId + "] saveDelta: " + this.delta);
            }
            try {
                HiRedisCommandBuilder builder = HiRedisCommandBuilder.start(HiRedisHmset.class)
                        .arg(getSessionKey(sessionId), serializeDelta());
                String principalSessionKey = getSessionAttrNameKey(
                        FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME);
                String securityPrincipalSessionKey = getSessionAttrNameKey(
                        SPRING_SECURITY_CONTEXT);
                if (this.delta.containsKey(principalSessionKey)
                        || this.delta.containsKey(securityPrincipalSessionKey)) {
                    if (this.originalPrincipalName != null) {
                        String originalPrincipalRedisKey = getPrincipalKey(this.originalPrincipalName);
                        builder = builder.command(HiRedisSrem.class)
                                .arg(originalPrincipalRedisKey, SimpleStringSerializer.serialize(sessionId));
                    }
                    String principal = PRINCIPAL_NAME_RESOLVER.resolvePrincipal(this);
                    this.originalPrincipalName = principal;
                    if (principal != null) {
                        String principalRedisKey = getPrincipalKey(principal);
                        builder = builder.command(HiRedisSadd.class)
                                .arg(principalRedisKey, SimpleStringSerializer.serialize(sessionId));
                    }
                }
                redisServerInvoker.execute("saveDelta[" + sessionId + "]", builder.build());
            } catch (Exception e) {
                logger.warn("saveDelta error:[" + sessionId + "]", e);
            }
            this.delta = new HashMap<String, Object>(this.delta.size());
            Long originalExpiration = this.originalLastAccessTime == null ? null
                    : this.originalLastAccessTime + TimeUnit.SECONDS
                    .toMillis(getMaxInactiveIntervalInSeconds());
            RedisOperationsSessionRepository.this.expirationPolicy
                    .onExpirationUpdated(originalExpiration, this);
        }
    }

    /**
     * Principal name resolver helper class.
     */
    static class PrincipalNameResolver {
        public String resolvePrincipal(Session session) {
            String principalName = session.getAttribute(PRINCIPAL_NAME_INDEX_NAME);
            if (principalName != null) {
                return SimpleStringSerializer.deserialize(principalName);
            }
            return null;
        }
    }
}
