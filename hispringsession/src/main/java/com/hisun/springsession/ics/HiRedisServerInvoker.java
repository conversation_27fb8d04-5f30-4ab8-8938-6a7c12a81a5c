package com.hisun.springsession.ics;

import com.hisun.exception.HiException;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.redis.HiRedisAtcConstants;
import com.hisun.redis.command.HiRedisCommand;
import com.hisun.redis.ics.HiRedisServerManager;

import java.util.List;

/**
 * User: QC
 * Date: 2018-03-27
 * Time: 19:35
 */
public class HiRedisServerInvoker {
    private String serverName;

    public HiRedisServerInvoker(String serverName) {
        this.serverName = serverName;
    }

    private HiMessageContext newContext(String requestId) {
        HiMessageContext ctx = new HiMessageContext();
        HiMessage msg = new HiMessage(serverName, HiRedisAtcConstants.REDIS_MSG_TYPE);
        msg.setRequestId(requestId);
        msg.setHeadItem(HiMessage.STC, HiRedisAtcConstants.TXN_CD_CMD);
        msg.setHeadItem(HiMessage.SDT, serverName);
        msg.setHeadItem(HiMessage.REQUEST_RESPONSE, HiMessage.TYPE_REQUEST);
        ctx.setCurrentMsg(msg);
        return ctx;
    }

    public Object execute(String requestId, HiRedisCommand command) throws HiException {
        HiMessageContext ctx = newContext(requestId);
        HiMessage msg = ctx.getCurrentMsg();
        msg.setHeadItem(HiRedisAtcConstants.COMMAND_TYPE, HiRedisAtcConstants.COMMAND_TYPE_SINGLE);
        msg.setHeadItem(HiRedisAtcConstants.COMMAND_OBJECT, command);
        HiRedisServerManager.process(serverName + "|" + msg.getType(), ctx);
        return msg.getObjectHeadItem(HiRedisAtcConstants.RESPONSE_OBJECT);
    }

    public List execute(String requestId, List<HiRedisCommand> commands) throws HiException {
        HiMessageContext ctx = newContext(requestId);
        HiMessage msg = ctx.getCurrentMsg();
        msg.setHeadItem(HiRedisAtcConstants.COMMAND_TYPE, HiRedisAtcConstants.COMMAND_TYPE_MULTI);
        msg.setHeadItem(HiRedisAtcConstants.COMMAND_TYPE_MULTI_CONTINUE, true);
        msg.setHeadItem(HiRedisAtcConstants.COMMAND_OBJECT, commands);
        HiRedisServerManager.process(serverName + "|" + msg.getType(), ctx);
        return (List) msg.getObjectHeadItem(HiRedisAtcConstants.RESPONSE_OBJECT);
    }
}
