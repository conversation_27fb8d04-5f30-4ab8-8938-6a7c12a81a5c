package hiunit.dialogs;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

public class AddUnitCaseDialog  extends Dialog {
	private Text textCaseName;
	private Text textCaseInputData;
	
	private String name;
	private String inputData;
	
	public AddUnitCaseDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container  = (Composite)super.createDialogArea(parent);
		final GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 2;
		container.setLayout(gridLayout);
		
		final Label nameLabel = new Label(container, SWT.NONE);
		nameLabel.setLayoutData(new GridData(GridData.END, GridData.CENTER, false, false));
		nameLabel.setText("案例名称");
		
		textCaseName = new Text(container, SWT.BORDER);
		textCaseName.setLayoutData(new GridData(GridData.FILL, GridData.CENTER, true, false));
		
		final Label inputDataLabel = new Label(container, SWT.NONE);
		final GridData gridData = new GridData(GridData.END, GridData.CENTER, false, false);
		gridData.horizontalIndent = 20;
		
		inputDataLabel.setLayoutData(gridData);
		inputDataLabel.setText("输入数据");
		
		textCaseInputData = new Text(container, SWT.BORDER|SWT.MULTI|SWT.WRAP|SWT.V_SCROLL|SWT.H_SCROLL);
		//textCaseInputData.setBounds(40, 10, 200, 100);
		final GridData gridData1 = new GridData(GridData.HORIZONTAL_ALIGN_FILL
				| GridData.VERTICAL_ALIGN_FILL);
		gridData1.horizontalSpan = 1;
		gridData1.grabExcessVerticalSpace = true;
		//textCaseInputData.setLayoutData(new GridData(GridData.FILL, GridData.CENTER, true, false));
		textCaseInputData.setLayoutData(gridData1);
		
		//return super.createDialogArea(parent);
		return container;
	}

	/**
	 * 调整对话框大小
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(400, 200);
	}

	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("添加测试案例");
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if(buttonId == IDialogConstants.OK_ID) {
			this.name = textCaseName.getText();
			this.inputData = textCaseInputData.getText();
		}
		super.buttonPressed(buttonId);
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.textCaseName.setText(name);
	}

	public String getInputData() {
		return inputData;
	}

	public void setInputData(String inputData) {
		this.textCaseInputData.setText(inputData);
	}
}
