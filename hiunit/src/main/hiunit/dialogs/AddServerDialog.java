package hiunit.dialogs;

import org.eclipse.jface.dialogs.IInputValidator;
import org.eclipse.jface.dialogs.InputDialog;
import org.eclipse.swt.widgets.Shell;

public class AddServerDialog extends InputDialog {

	private String server;
	
	public AddServerDialog(Shell parentShell, String dialogTitle,
			String dialogMessage, String initialValue, IInputValidator validator) {
		
		super(parentShell, dialogTitle, dialogMessage, initialValue, validator);
	}
	
	public String getServer(){
		return this.server;
	}

}
