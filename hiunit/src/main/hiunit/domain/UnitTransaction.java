package hiunit.domain;

import java.util.HashMap;

public class UnitTransaction implements IDomain {

	private String txnCd;
	
	private UnitServer unitServer;
	private HashMap<String, UnitCase> unitCases = new HashMap<String, UnitCase>();

	public UnitTransaction(String txnCd) {
		this.txnCd = txnCd;
	}
	
	public String getTxnCd() {
		return txnCd;
	}

	public void setTxnCd(String txnCd) {
		this.txnCd = txnCd;
	}

	public UnitServer getUnitServer() {
		return unitServer;
	}

	public void setUnitServer(UnitServer unitServer) {
		this.unitServer = unitServer;
	}
	
	public void addCase(UnitCase unitCase) {
		unitCases.put(unitCase.getName(), unitCase);
	}
}
