package hiunit.domain;

import java.util.HashMap;

public class UnitServer implements IDomain {

	private String name;
	
	private String type; //CCA,SCA,OTA
	
	private boolean isTcp = false;
	
	private String path;
	
	private HashMap<String, UnitTransaction> transMap = new HashMap<String, UnitTransaction>();

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public boolean isTcp() {
		return isTcp;
	}

	public void setTcp(boolean isTcp) {
		this.isTcp = isTcp;
	}
	
	public void addTran(UnitTransaction tran) {
		transMap.put(tran.getTxnCd(), tran);
	}
	
	public HashMap getChildren() {
		return this.transMap;
	}
	
	public boolean exitTrans(String txnCd){
		return this.transMap.containsKey(txnCd);
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}
	
}
