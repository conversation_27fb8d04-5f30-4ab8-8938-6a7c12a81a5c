package hiunit.popup.actions;

import hiunit.Activator;
import hiunit.ics.IcsUtils;
import hiunit.views.TreeParent;
import hiunit.views.UnitView;
import hiunit.views.UnitView.ViewContentProvider;

import java.io.File;
import java.net.MalformedURLException;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.ILog;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.preference.IPreferenceStore;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.ITreeSelection;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.IObjectActionDelegate;
import org.eclipse.ui.IWorkbenchPart;

import com.hisun.exception.HiException;
import com.hisun.hilog4j.Logger;

public class StartServerActionMenu implements IObjectActionDelegate {

	private Shell shell;
	private ISelection selection;
	
	private ILog log = Activator.getDefault().getLog();
	
	public void run(IAction arg0) {
		//System.out.println("Start Server.....");
		File directory = null;
		String serverName = "";
		if(this.selection instanceof ITreeSelection) {
			org.eclipse.core.internal.resources.File f = (org.eclipse.core.internal.resources.File)((ITreeSelection) selection).getFirstElement();
			serverName = f.getName();
			directory = f.getProjectRelativePath().toFile();
		}
		String path = directory.toString();
		if(StringUtils.indexOf(path, "ATR") < 0) {
			return;
		}
		
		serverName = StringUtils.substring(serverName, 0, StringUtils.indexOf(serverName, "_"));
		try {
			log.log(new Status(IStatus.OK, Activator.PLUGIN_ID, "启动服务:["+serverName+"] path:["+path+"]"));
			IcsUtils.InitialICSContext();
			IcsUtils.BootServer(serverName, path);
			
			IPreferenceStore store = Activator.getDefault().getPreferenceStore();
			UnitView unitView = (UnitView) Activator
					.getDefault()
					.getWorkbench()
					.getActiveWorkbenchWindow().getActivePage()
					.findView("hiunit.views.UnitView");
			ViewContentProvider provider = (ViewContentProvider) unitView
					.getViewer()
					.getContentProvider();
			
			TreeParent parent = new TreeParent(serverName);
			if(!provider.exists(parent) ){
				provider.addChild(parent);
				unitView.getViewer().setInput(unitView.getViewSite());
			}
			
			showMessage("启动服务{"+serverName+"}成功");
		} catch (MalformedURLException e) {
			//e.printStackTrace();
			log.log(new Status(IStatus.OK, Activator.PLUGIN_ID, e.getMessage()));
		} catch (HiException e) {
			//e.printStackTrace();
			StackTraceElement[] els = e.getStackTrace();
			for(StackTraceElement el : els) {
				log.log(new Status(IStatus.OK, Activator.PLUGIN_ID, el.toString()));
			}
			
			log.log(new Status(IStatus.OK, Activator.PLUGIN_ID, "error:["+e.toString()+"]"));
			showMessage("启动服务{"+serverName+"}失败,error:"+e.toString());
		}
	}

	public void selectionChanged(IAction arg0, ISelection selection) {
		this.selection = selection;
	}

	public void setActivePart(IAction arg0, IWorkbenchPart targetPart) {
		shell = targetPart.getSite().getShell();
	}
	
	private void showMessage(String message) {
		MessageDialog.openInformation(shell, "Info", message);
	}
}
