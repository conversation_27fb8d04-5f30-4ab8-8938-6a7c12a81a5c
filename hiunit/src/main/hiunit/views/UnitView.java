package hiunit.views;

import hiunit.Activator;
import hiunit.domain.UnitServer;
import hiunit.domain.UnitTransaction;
import hiunit.preferences.PreferenceConstants;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IMenuListener;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.jface.preference.IPreferenceStore;
import org.eclipse.jface.viewers.DoubleClickEvent;
import org.eclipse.jface.viewers.IDoubleClickListener;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredContentProvider;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.ITreeContentProvider;
import org.eclipse.jface.viewers.LabelProvider;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.jface.viewers.Viewer;
import org.eclipse.jface.viewers.ViewerSorter;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.ui.IActionBars;
import org.eclipse.ui.ISelectionListener;
import org.eclipse.ui.ISharedImages;
import org.eclipse.ui.IWorkbenchActionConstants;
import org.eclipse.ui.IWorkbenchPart;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.part.DrillDownAdapter;
import org.eclipse.ui.part.ViewPart;

/**
 * This sample class demonstrates how to plug-in a new workbench view. The view
 * shows data obtained from the model. The sample creates a dummy model on the
 * fly, but a real implementation would connect to the model available either in
 * this or another plug-in (e.g. the workspace). The view is connected to the
 * model using a content provider.
 * <p>
 * The view uses a label provider to define how model objects should be
 * presented in the view. Each view can present the same model objects using
 * different labels and icons, if needed. Alternatively, a single label provider
 * can be shared between views in order to ensure that objects of the same type
 * are presented in the same way everywhere.
 * <p>
 */

public class UnitView extends ViewPart implements ISelectionListener {

	/**
	 * The ID of the view as specified by the extension.
	 */
	public static final String ID = "hiunit.views.UnitView";

	private TreeViewer viewer;
	private DrillDownAdapter drillDownAdapter;
	private Action runUnitCase;
	private Action addUnitCaseAction;
	private Action removeUnitAction;
	private Action rebootServerAction;
	private Action doubleClickAction;
	private Action listAllServersAction;
	private Action reloadServerAction;
	private Action reloadUnitCaseAction;
	
	private ISelection selection;

	private String homePath;
	
	public class ViewContentProvider implements IStructuredContentProvider,
			ITreeContentProvider {
		private TreeParent invisibleRoot;

		public void inputChanged(Viewer v, Object oldInput, Object newInput) {
		}
		
		public void dispose() {

		}

		public Object[] getElements(Object parent) {
			//System.out.println("getElements processing, parent:[" + parent+ "]");
			if (parent.equals(getViewSite())) {
				if (invisibleRoot == null)
					initialize();
				return getChildren(invisibleRoot);
			} else if (parent instanceof UnitServer) {
				return getChildren(invisibleRoot);
			}
			else if (parent instanceof UnitTransaction) {
				return getChildren(invisibleRoot);
			}
			return getChildren(parent);
		}

		public Object getParent(Object child) {
			if (child instanceof TreeObject) {
				return ((TreeObject) child).getParent();
			}
			return null;
		}

		public Object[] getChildren(Object parent) {
			if (parent instanceof TreeObject) {
				return ((TreeObject) parent).getChildren();
			}
			return new Object[0];
		}

		public boolean hasChildren(Object parent) {
			if (parent instanceof TreeObject)
				return ((TreeObject) parent).hasChildren();
			return false;
		}

		public void addChild(TreeObject object) {
			Object o = invisibleRoot.getFirstChildren(); // root
			if (o instanceof TreeParent) {
				((TreeParent) o).addChild(object);
			} else {
				invisibleRoot.addChild(object);
			}
		}
		
		public boolean exists(TreeObject object) {
			TreeObject[] children = invisibleRoot.getFirstChildren().getChildren();
			for(TreeObject o : children) {
				if(o.equals(object)) {
					return true;
				}
			}
			
			return false;
		}

		/*
		 * We will set up a dummy model to initialize tree heararchy. In a real
		 * code, you will connect to a real model and expose its hierarchy.
		 */
		private void initialize() {
			// -- 初始化加载测试案例树
			TreeParent root = new TreeParent("Root");
			invisibleRoot = new TreeParent("");
			invisibleRoot.addChild(root);
		}
	}
	
	

	public class ViewLabelProvider extends LabelProvider {
		public String getText(Object obj) {
			return obj.toString();
		}

		public Image getImage(Object obj) {
			String imageKey = ISharedImages.IMG_OBJ_ELEMENT;
			if (obj instanceof TreeParent)
				imageKey = ISharedImages.IMG_OBJ_FOLDER;
			if (obj instanceof TreeLeaf) {
				imageKey = ISharedImages.IMG_OBJ_FILE;
			}
			return PlatformUI.getWorkbench().getSharedImages().getImage(imageKey);
		}
	}

	class NameSorter extends ViewerSorter {

	}

	/**
	 * The constructor.
	 */
	public UnitView() {
		IPreferenceStore store = Activator.getDefault().getPreferenceStore();
	    homePath = store.getString(PreferenceConstants.P_STR_PATH);
	}

	/**
	 * This is a callback that will allow us to create the viewer and initialize
	 * it.
	 */
	public void createPartControl(Composite parent) {
		viewer = new TreeViewer(parent, SWT.MULTI | SWT.H_SCROLL | SWT.V_SCROLL);
		drillDownAdapter = new DrillDownAdapter(viewer);
		viewer.setContentProvider(new ViewContentProvider());
		viewer.setLabelProvider(new ViewLabelProvider());
		viewer.setSorter(new NameSorter());
		viewer.setInput(getViewSite());

		// Create the help context id for the viewer's control
		PlatformUI.getWorkbench().getHelpSystem()
				.setHelp(viewer.getControl(), "hiunit.viewer");
		makeActions();
		hookContextMenu();
		hookDoubleClickAction();
		contributeToActionBars();

		// getSite().getPage().addSelectionListener((ISelectionListener)this);
	}

	private void hookContextMenu() {
		MenuManager menuMgr = new MenuManager("#PopupMenu");
		menuMgr.setRemoveAllWhenShown(true);
		menuMgr.addMenuListener(new IMenuListener() {
			public void menuAboutToShow(IMenuManager manager) {
				UnitView.this.fillContextMenu(manager);
			}
		});
		Menu menu = menuMgr.createContextMenu(viewer.getControl());
		viewer.getControl().setMenu(menu);
		getSite().registerContextMenu(menuMgr, viewer);
	}

	private void contributeToActionBars() {
		IActionBars bars = getViewSite().getActionBars();
		fillLocalPullDown(bars.getMenuManager());
		fillLocalToolBar(bars.getToolBarManager());
	}

	private void fillLocalPullDown(IMenuManager manager) {
//		manager.add(addUnitCaseAction);
//		manager.add(new Separator());
//		manager.add(runUnitCase);
//		manager.add(new Separator());
//		manager.add(removeUnitAction);
	}

	private void fillContextMenu(IMenuManager manager) {
		ISelection selection = viewer.getSelection();
		Object obj = ((IStructuredSelection) selection).getFirstElement();
		TreeObject treeObj = (TreeObject) obj;
		manager.add(new Separator());
		drillDownAdapter.addNavigationActions(manager);
		// Other plug-ins can contribute there actions here
		manager.add(new Separator(IWorkbenchActionConstants.MB_ADDITIONS));
	}

	private void fillLocalToolBar(IToolBarManager manager) {
		//manager.add(runUnitCase);
		//manager.add(addUnitCaseAction);
		//manager.add(listAllServersAction);
		manager.add(new Separator());
		drillDownAdapter.addNavigationActions(manager);
	}

	private void makeActions() {
		/*
		runUnitCase = new RunUnitCaseAction(viewer);
		runUnitCase.setText("运行测试案例");
		runUnitCase.setToolTipText("运行测试案例");
		runUnitCase.setImageDescriptor(PlatformUI.getWorkbench()
				.getSharedImages()
				.getImageDescriptor(ISharedImages.IMG_OBJS_INFO_TSK));

		// -- 添加测试案例
		addUnitCaseAction = new Action() {
			public void run() {
				ISelection selection = viewer.getSelection();
				Object obj = ((IStructuredSelection) selection).getFirstElement();
				TreeObject treeObj = (TreeObject) obj;
				if (treeObj.getDomain() instanceof UnitTransaction) {
					AddUnitCaseDialog dialog = new AddUnitCaseDialog(viewer.getControl().getShell());
					if (dialog.open() != InputDialog.OK) {
						return;
					}
					String caseName = dialog.getName();
					String inputData = dialog.getInputData();

					TreeLeaf leaf = new TreeLeaf(caseName);
					UnitCase unitCase = new UnitCase();
					unitCase.setName(caseName);
					unitCase.setInputData(inputData);
					leaf.setDomain(unitCase);

					treeObj.addChild(leaf);
					viewer.setInput(getViewSite());
				}
			}
		};
		addUnitCaseAction.setText("添加测试案例");
		addUnitCaseAction.setToolTipText("添加测试案例");
		addUnitCaseAction.setImageDescriptor(PlatformUI.getWorkbench()
				.getSharedImages()
				.getImageDescriptor(ISharedImages.IMG_OBJS_INFO_TSK));
		doubleClickAction = new Action() {
			public void run() {
				ISelection selection = viewer.getSelection();
				Object obj = ((IStructuredSelection) selection).getFirstElement();
				TreeObject treeObj = (TreeObject) obj;
				showMessage("Double-click detected on" + obj + " " + obj.toString());
				if (treeObj.getDomain() instanceof UnitCase) {
					// -- 双击修改案例
					UnitCase unitCase = (UnitCase) treeObj.getDomain();
					AddUnitCaseDialog dialog = new AddUnitCaseDialog(viewer
							.getControl().getShell());
					dialog.setName(unitCase.getName());
					dialog.setInputData(unitCase.getInputData());

					if (dialog.open() != InputDialog.OK) {
						return;
					}

				}
			}
		};
		
		//--
		removeUnitAction = new Action(){
			public void run() {
				ISelection selection = viewer.getSelection();
				Object obj = ((IStructuredSelection) selection).getFirstElement();
				TreeObject treeObj = (TreeObject) obj;
				if(treeObj.getDomain() instanceof UnitCase) {
					TreeObject p = treeObj.getParent();
					p.removeChild(treeObj);
					viewer.setInput(p.getDomain());
				}
			}
		};
		removeUnitAction.setText("删除案例");
		removeUnitAction.setImageDescriptor(PlatformUI.getWorkbench()
				.getSharedImages()
				.getImageDescriptor(ISharedImages.IMG_OBJS_INFO_TSK));
		
		//-- 重启服务
		rebootServerAction = new Action(){
			public void run() {
				ISelection selection = viewer.getSelection();
				Object obj = ((IStructuredSelection) selection).getFirstElement();
				TreeObject treeObj = (TreeObject) obj;
				if(treeObj.getDomain() instanceof UnitServer) {
					UnitServer unitServer = (UnitServer)treeObj.getDomain();
					try {
						IcsUtils.InitialICSContext();
						IcsUtils.BootServer(unitServer.getName(), unitServer.getPath());
					} catch (MalformedURLException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					} catch (HiException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		};
		rebootServerAction.setText("重启服务");
		rebootServerAction.setImageDescriptor(PlatformUI.getWorkbench()
				.getSharedImages()
				.getImageDescriptor(ISharedImages.IMG_OBJS_INFO_TSK));
		
		//-- 添加交易
		reloadServerAction = new ReloadServerAction(viewer);
		reloadServerAction.setText("重加载服务");
		reloadServerAction.setImageDescriptor(PlatformUI.getWorkbench()
				.getSharedImages()
				.getImageDescriptor(ISharedImages.IMG_OBJS_INFO_TSK));
		
		//-- 重载测试案例
		reloadUnitCaseAction = new Action(){
			public void run() {
				
			}
		};
		reloadUnitCaseAction.setText("重加载案例");
		reloadUnitCaseAction.setImageDescriptor(PlatformUI.getWorkbench()
				.getSharedImages()
				.getImageDescriptor(ISharedImages.IMG_OBJS_INFO_TSK));
		
		//--
		listAllServersAction = new Action() {
			public void run() {
				try {
					List servers = IcsUtils.listStartedServer();
					System.out.println("servers:["+servers+"]");
					
					if(servers == null) {
						return;
					}
					for(Iterator it = servers.iterator(); it.hasNext(); ) {
						Object o = it.next();
						System.out.println("Object:"+o.getClass());
					}
					
				} catch (HiException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		};
		listAllServersAction.setText("服务列表");
		listAllServersAction.setImageDescriptor(PlatformUI.getWorkbench()
				.getSharedImages()
				.getImageDescriptor(ISharedImages.IMG_OBJS_INFO_TSK));
				*/
	}

	private void hookDoubleClickAction() {
		viewer.addDoubleClickListener(new IDoubleClickListener() {
			public void doubleClick(DoubleClickEvent event) {
				doubleClickAction.run();
			}
		});
	}

	private void showMessage(String message) {
		MessageDialog.openInformation(viewer.getControl().getShell(), "UnitView", message);
	}

	/**
	 * Passing the focus request to the viewer's control.
	 */
	public void setFocus() {
		viewer.getControl().setFocus();
	}

	public TreeViewer getViewer() {
		return viewer;
	}

	public void selectionChanged(IWorkbenchPart arg0, ISelection arg1) {
		this.selection = arg1;
	}
}