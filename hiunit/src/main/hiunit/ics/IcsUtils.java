package hiunit.ics;

import hiunit.Activator;
import hiunit.domain.UnitServer;
import hiunit.domain.UnitTransaction;
import hiunit.preferences.PreferenceConstants;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.dom4j.DocumentException;
import org.eclipse.core.runtime.ILog;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.preference.IPreferenceStore;

import com.hisun.constants.HiConstants;
import com.hisun.dispatcher.HiRouterOut;
import com.hisun.exception.HiException;
import com.hisun.framework.HiConfigParser;
import com.hisun.framework.HiDefaultServer;
import com.hisun.framework.HiFrameworkBuilder;
import com.hisun.loader.HiClassLoaderFactory;
import com.hisun.message.HiETF;
import com.hisun.message.HiETFFactory;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.startup.HiStartup;
import com.hisun.util.HiICSProperty;
import com.hisun.util.HiResource;
import com.hisun.util.HiServiceLocator;

public class IcsUtils {
	//private static IPreferenceStore store = Activator.getDefault().getPreferenceStore();
	//public static final String HOME_PATH = store.getString(PreferenceConstants.P_STR_PATH);
	
	private static ILog log = Activator.getDefault().getLog();
	public static ClassLoader InitialICSContext() throws MalformedURLException, HiException {
		IPreferenceStore store = Activator.getDefault().getPreferenceStore();
		String HOME_PATH = store.getString(PreferenceConstants.P_STR_PATH);
		//String homePath = 
		log.log(new Status(IStatus.OK, Activator.PLUGIN_ID, "HomePath:["+HOME_PATH+"]"));
		URL hmUrl = new URL("file://" + HOME_PATH);
		URL appUrl = new URL("file://" + HOME_PATH + "/app");
		URL baseUrl = new URL("file://"+HOME_PATH+"/app/base");

		System.setProperty(HiConstants.ICS_HOME, HOME_PATH);
		System.setProperty(HiConstants.ICS_ENV, HiConstants.ICS_JUNIT_ENV);
		HiICSProperty.setProperty(HiConstants.ICS_HOME, HOME_PATH);
		
		String dbUrl = store.getString(PreferenceConstants.P_STR_URL);
		String dbUsr = store.getString(PreferenceConstants.P_STR_USR);
		String dbPwd = store.getString(PreferenceConstants.P_STR_PWD);
		String dbDrv = store.getString(PreferenceConstants.P_STR_DRV);
		
		System.setProperty("db_url", dbUrl);
		System.setProperty("userId", dbUsr);
		System.setProperty("password", dbPwd);
		System.setProperty("db_driver", dbDrv);

		ClassLoader parent = Thread.currentThread().getContextClassLoader();
		//ClassLoader system = parent.getSystemClassLoader();
		URLClassLoader loader = new URLClassLoader(new URL[] {
				new File(HOME_PATH).toURL(),
				new File(HOME_PATH + "/app").toURL() ,
				new File(HOME_PATH + "/app/base").toURL()}, 
				parent);
		
		
		Thread.currentThread().setContextClassLoader(loader);
		HiICSProperty.reload();
		return loader;
	}

	public static List parseServer(String path, ClassLoader parent) throws HiException {
		IPreferenceStore store = Activator.getDefault().getPreferenceStore();
		String HOME_PATH = store.getString(PreferenceConstants.P_STR_PATH);
		
		int start = StringUtils.indexOf(path, "app"+File.separator);
		int end   = StringUtils.indexOf(path, File.separator, start+4);
		String appName = StringUtils.substring(path, start+4, end);
		
		log.log(new Status(IStatus.OK, Activator.PLUGIN_ID, "parseServer:["+path+"] appName:"+appName));
		ClassLoader appLoader = HiClassLoaderFactory.createClassLoader("application", appName, parent);
		Thread.currentThread().setContextClassLoader(appLoader);
		HiConfigParser parser = HiFrameworkBuilder.getParser();
		InputStream is = null;
		List retList = new ArrayList();
		HiDefaultServer server = null;
		try {
			is = new FileInputStream(HOME_PATH + File.separator+path);
		    server = parser.parseServerXML(is);
			Object obj = server.getDeclare("ctlengine");

			UnitServer unitSvr = new UnitServer();
			unitSvr.setName(server.getName());
			unitSvr.setType(server.getType());
			unitSvr.setPath(path);

			retList.add(unitSvr);
			Class clazz = HiResource.loadClass("com.hisun.engine.HiEngine");
			Method m = clazz.getMethod("listAllTransaction", null);
			m.setAccessible(true);
			Object o = m.invoke(obj, null);
			List list = (List) o;
			if (list != null) {
				for (int i = 0; i < list.size(); i++) {
					String txnCd = (String) list.get(i);
					UnitTransaction unitTran = new UnitTransaction(txnCd);
					unitTran.setUnitServer(unitSvr);
					unitSvr.addTran(unitTran);
				}
			}
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (ClassNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SecurityException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (NoSuchMethodException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			try {
				is.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		return retList;
	}
	
	public static void BootServer(String serverName, String path) throws HiException {
		
		HiStartup startup = HiStartup.getInstance(serverName);
		int start = StringUtils.indexOf(path, "app/");
		int end   = StringUtils.indexOf(path, "/", start+4);
		String appName = StringUtils.substring(path, start+4, end);
		
		log.log(new Status(IStatus.OK, Activator.PLUGIN_ID,
				"BootServer:serverName["+serverName+"] path:["+path+"] appName:"+appName));
		HiMessage msg = new HiMessage();
		msg.setHeadItem("CMD", "start");
		HiETF etf = HiETFFactory.createETF();
		etf.setChildValue("APP_NM", appName);
		etf.setChildValue("SERVER", serverName);
		etf.setChildValue("CONFIG_FILE", path);
		etf.setChildValue("FORCE_FLG", "Y");
		msg.setBody(etf);
		startup.manage(msg);
		
		String jndi = "ibs/ejb/" + serverName;
		HiServiceLocator locator = HiServiceLocator.getInstance();
		locator.bind(jndi, startup);
	}
	
	public static HiMessage runTransaction(String objSvr, String txnCd, HiETF data) throws MalformedURLException, HiException {
		InitialICSContext();
		
		//--调用交易
		HiMessage msg = new HiMessage(objSvr, "DEFAULT");
		msg.setHeadItem(HiMessage.REQUEST_RESPONSE, "rq");
		msg.setHeadItem(HiMessage.STM, new Long(System.currentTimeMillis()));
		msg.setHeadItem(HiMessage.STC, txnCd);
		msg.setHeadItem(HiMessage.SDT, objSvr);
		msg.setHeadItem(HiMessage.STF, "1");
		msg.setHeadItem(HiMessage.TEXT_TYPE, HiMessage.TEXT_TYPE_ETF);
		msg.setBody(data);
		
		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(msg);
		HiMessageContext.setCurrentContext(ctx);
		msg = HiRouterOut.syncProcess(msg);
		return msg;
	}
	
	public static void saveResport(HiETF requestETF, HiETF responseETF, String desc, String reportName) throws DocumentException{
		IPreferenceStore store = Activator.getDefault().getPreferenceStore();
		String HOME_PATH = store.getString(PreferenceConstants.P_STR_PATH);
		
		StringBuffer content = new StringBuffer();
		content.append("<html>\r\n<head>\r\n");
		content.append("<meta http-equiv=\"content-type\" content=\"text/html; charset=gb2312\" />\r\n");
		content.append("<title>"+reportName+"</title>\r\n</head>\r\n");
		content.append("<body>");
		content.append("<p>");
		content.append(desc);
		content.append("</p>\r\n");
		String in = requestETF.toString();
		in = StringUtils.replace(in, "<", "&lt;");
		in = StringUtils.replace(in, ">", "&gt;");
		String ou = responseETF.toString();
		ou = StringUtils.replace(ou, "<", "&lt;");
		ou = StringUtils.replace(ou, ">", "&gt;");
		content.append("<p>输入：\r\n"+in+"</p>\r\n");
		content.append("<p>输出：\r\n"+ou+"</p>\r\n");
		content.append("</body>\r\n");
		content.append("</html>");
		try {
			File rpFile = new File(HOME_PATH+File.separator+reportName);
			FileWriter writer = new FileWriter(rpFile);
			writer.write(content.toString());
			writer.flush();
			writer.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public static List listStartedServer() throws HiException{
		HiServiceLocator locator = HiServiceLocator.getInstance();
		return locator.list("ibs/ejb");
		//return null;
	}
}
