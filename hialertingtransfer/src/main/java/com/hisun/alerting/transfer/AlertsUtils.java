package com.hisun.alerting.transfer;

import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiETF;
import com.hisun.message.HiMessage;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;

import static com.hisun.alerting.transfer.AlertTransferConstants.HTTP_HEADER_CMPAY_IGW_ALERTS;

public class AlertsUtils {
    private static void log(HiMessage message, String content) {
        Logger logger = HiLog.getLogger(message);
        if (logger.isInfoEnabled()) {
            logger.info(content);
        }
    }

    public static void addAlertsContextFromGWA(HiMessage message) {
        if (message == null) {
            return;
        }
        HiETF root = message.getETFBody();
        String msgCd = root.getGrandChildValue("GWA.MSG_CD");
        if (StringUtils.isBlank(msgCd)) {
            msgCd = root.getGrandChildValue("MSG_CD");
        }
        if (StringUtils.isBlank(msgCd) || StringUtils.contains(msgCd, "00000")) {
            return;
        }
        String msgInf = root.getGrandChildValue("GWA.MSG_INF");
        if (StringUtils.isBlank(msgInf)) {
            msgInf = root.getGrandChildValue("MSG_INF");
        }
        log(message, "Add alerts from GWA, MSG_CD: " + msgCd + ", MSG_INF: " + msgInf);
        AlertsContext.from(message)
                .addAlert(msgCd, msgInf)
                .persist();
    }

    public static void addAlertsContextFromHeader(HiMessage message, Header[] headers) {
        if (message == null || headers == null || headers.length == 0) {
            return;
        }
        String alertHeader = null;
        for (Header header : headers) {
            if (!StringUtils.equalsIgnoreCase(header.getName(), HTTP_HEADER_CMPAY_IGW_ALERTS)) {
                continue;
            }
            alertHeader = header.getValue();
        }
        if (StringUtils.isBlank(alertHeader)) {
            return;
        }
        log(message, "Add alerts from http header, value: " + alertHeader);
        AlertsContext.from(message)
                .addSerializedAlerts(alertHeader)
                .persist();
    }

    private AlertsUtils() {
    }
}
