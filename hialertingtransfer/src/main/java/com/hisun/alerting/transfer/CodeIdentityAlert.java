package com.hisun.alerting.transfer;

public class CodeIdentityAlert {
    private String msgCd;
    private String msgInfo;

    public CodeIdentityAlert() {
    }

    public CodeIdentityAlert(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    public String getMsgCd() {
        return this.msgCd;
    }

    public String getMsgInfo() {
        return this.msgInfo;
    }

    public String toString() {
        return "[" + this.getMsgCd() + "]" + this.getMsgInfo();
    }

    private void setMsgCd(String msgCd) {
        this.msgCd = msgCd;
    }

    private void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof CodeIdentityAlert)) return false;
        final CodeIdentityAlert other = (CodeIdentityAlert) o;
        if (!other.canEqual((Object) this)) return false;
        final Object this$msgCd = this.getMsgCd();
        final Object other$msgCd = other.getMsgCd();
        if (this$msgCd == null ? other$msgCd != null : !this$msgCd.equals(other$msgCd)) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof CodeIdentityAlert;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        final Object $msgCd = this.getMsgCd();
        result = result * PRIME + ($msgCd == null ? 43 : $msgCd.hashCode());
        return result;
    }
}
