package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.executor.config.PrometheusProperties;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusToken;
import com.cmpay.hacp.inspection.infrastructure.executor.cache.PrometheusTokenCache;
import com.cmpay.lemon.common.exception.BusinessException;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * PrometheusGatewayImpl单元测试
 * 使用MockWebServer模拟HTTP服务器
 */
@ExtendWith(MockitoExtension.class)
class PrometheusGatewayImplTest {

    @Mock
    private PrometheusTokenCache prometheusTokenCache;

    private PrometheusGatewayImpl prometheusGateway;
    private MockWebServer mockWebServer;
    private PrometheusProperties properties;

    @BeforeEach
    void setUp() throws Exception {
        // 启动MockWebServer
        mockWebServer = new MockWebServer();
        mockWebServer.start();

        // 创建真实的PrometheusProperties对象
        properties = new PrometheusProperties();
        properties.setBaseUrl(mockWebServer.url("/").toString().replaceAll("/$", ""));
        properties.setIndicatorUrl("/api/indicators");

        PrometheusProperties.Auth auth = new PrometheusProperties.Auth();
        auth.setUserId("testUser");
        auth.setUserKey("testKey");
        auth.setTokenUrl("/api/token");
        properties.setAuth(auth);

        PrometheusProperties.Http http = new PrometheusProperties.Http();
        http.setConnectTimeout(10000);
        http.setReadTimeout(30000);
        http.setWriteTimeout(30000);
        http.setMaxIdleConnections(10);
        http.setKeepAliveDuration(5);
        http.setEnableSslCheck(false);
        properties.setHttp(http);

        // 创建PrometheusGatewayImpl实例
        prometheusGateway = new PrometheusGatewayImpl(prometheusTokenCache, properties);

        // 初始化组件
        prometheusGateway.afterPropertiesSet();
    }

    @AfterEach
    void tearDown() throws IOException {
        if (mockWebServer != null) {
            mockWebServer.shutdown();
        }
        if (prometheusGateway != null) {
            prometheusGateway.destroy();
        }
    }

    @Test
    void testGetAuthToken_FromCache_Success() {
        // Given
        PrometheusToken cachedToken = PrometheusToken.builder()
                .accessToken("cached-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(cachedToken);

        // When
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("cached-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(prometheusTokenCache).getValidToken("testUser");
        // 验证没有发起HTTP请求
        assertEquals(0, mockWebServer.getRequestCount());
    }

    @Test
    void testGetAuthToken_FetchNew_Success() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        // Mock HTTP response
        String tokenResponseJson = "{\n" +
                "    \"token\": \"new-access-token\",\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 3600,\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\"\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(tokenResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("new-access-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(prometheusTokenCache, times(2)).getValidToken("testUser");
        verify(prometheusTokenCache).cacheToken(eq("testUser"), any(PrometheusToken.class));

        // 验证请求
        RecordedRequest request = mockWebServer.takeRequest();
        assertEquals("POST", request.getMethod());
        assertTrue(request.getPath().contains("/api/token"));
    }

    @Test
    void testGetAuthToken_HttpRequestFailed() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> prometheusGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR.getMsgCd(), exception.getMsgCd());
        verify(prometheusTokenCache, times(2)).getValidToken("testUser");
        verifyNoMoreInteractions(prometheusTokenCache);
    }

    @Test
    void testGetAuthToken_AuthenticationFailed() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        String errorResponseJson = "{\n" +
                "    \"token\": null,\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 0,\n" +
                "    \"msgCd\": \"FFM80001\",\n" +
                "    \"msgInfo\": \"Authentication failed\"\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(errorResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> prometheusGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_UserStatusError() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        String errorResponseJson = "{\n" +
                "    \"token\": null,\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 0,\n" +
                "    \"msgCd\": \"FFM80002\",\n" +
                "    \"msgInfo\": \"User status error\"\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(errorResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> prometheusGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_InvalidJsonResponse() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        mockWebServer.enqueue(new MockResponse()
                .setBody("invalid json")
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> prometheusGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_ConcurrentAccess() throws InterruptedException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser"))
                .thenReturn(null)  // 第一次调用返回null
                .thenReturn(PrometheusToken.builder()  // 后续调用返回缓存的token
                        .accessToken("concurrent-token")
                        .tokenType("Bearer")
                        .expiresAt(LocalDateTime.now().plusHours(1))
                        .build());

        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // When
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    PrometheusToken token = prometheusGateway.getAuthToken();
                    assertNotNull(token);
                } finally {
                    latch.countDown();
                }
            });
        }

        // Then
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        executor.shutdown();

        // 验证只有一个线程真正获取了新token，其他线程从缓存获取
        verify(prometheusTokenCache, atLeast(threadCount)).getValidToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_Success() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        String responseJson = "{\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\",\n" +
                "    \"data\": [],\n" +
                "    \"total\": 0,\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 100\n" +
                "}";
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result);
        assertEquals("FFM00000", result.getMsgCd());
        assertEquals("Success", result.getMsgInfo());
        assertTrue(result.isSuccess());

        // 验证请求
        RecordedRequest recordedRequest = mockWebServer.takeRequest();
        assertEquals("POST", recordedRequest.getMethod());
        assertTrue(recordedRequest.getPath().contains("/api/indicators"));
        assertTrue(Objects.requireNonNull(recordedRequest.getHeader("Authorization")).contains("Bearer valid-token"));
    }

    @Test
    void testGetIndicatorDataList_InvalidRequest() {
        // Given
        IndicatorQueryRequest invalidRequest = IndicatorQueryRequest.builder()
                .build(); // 缺少必要字段

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(invalidRequest);

        // Then
        assertNotNull(result);
        assertEquals("FFM80003", result.getMsgCd());
        assertEquals("请求参数验证失败", result.getMsgInfo());
        assertFalse(result.isSuccess());
        // 验证没有发起HTTP请求
        assertEquals(0, mockWebServer.getRequestCount());
    }

    @Test
    void testGetIndicatorDataList_TokenExpired() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(401)
                .setBody("Unauthorized"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> prometheusGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.PROMETHEUS_TOKEN_EXPIRED.getMsgCd(), exception.getMsgCd());
        verify(prometheusTokenCache).evictToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_QueryError() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> prometheusGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.PROMETHEUS_QUERY_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetIndicatorDataList_WithCompleteRequest() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .zones(Arrays.asList("zone1", "zone2"))
                .specialNameOne("special1")
                .specialNameTwo("special2")
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        String responseJson = "{\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"indicatorName\": \"cpu_usage\",\n" +
                "            \"timestamp\": \"2024-01-01T10:00:00\",\n" +
                "            \"value\": \"75.5\",\n" +
                "            \"zone\": \"zone1\",\n" +
                "            \"one\": \"special1\",\n" +
                "            \"two\": \"special2\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"total\": 1,\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 100\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result);
        assertEquals("FFM00000", result.getMsgCd());
        assertEquals("Success", result.getMsgInfo());
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals(Long.valueOf(1), result.getTotal());
        assertEquals(Integer.valueOf(1), result.getPageNum());
        assertEquals(Integer.valueOf(100), result.getPageSize());
    }

    @Test
    void testGetIndicatorDataList_InvalidJsonResponse() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        mockWebServer.enqueue(new MockResponse()
                .setBody("invalid json response")
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> prometheusGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE.getMsgCd(), exception.getMsgCd());
    }

    /**
     * 辅助方法：创建有效的PrometheusToken
     */
    private PrometheusToken createValidToken() {
        return PrometheusToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
    }

    /**
     * 辅助方法：创建过期的PrometheusToken
     */
    private PrometheusToken createExpiredToken() {
        return PrometheusToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().minusHours(1))
                .createdAt(LocalDateTime.now().minusHours(2))
                .build();
    }

    /**
     * 辅助方法：创建基本的IndicatorQueryRequest
     */
    private IndicatorQueryRequest createBasicRequest() {
        return IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();
    }
}
