package com.cmpay.hacp.inspection.infrastructure.executor.cache;

import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusToken;
import com.cmpay.hacp.inspection.infrastructure.config.PrometheusCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PrometheusTokenCache集成测试
 * 使用真实的Spring Boot应用上下文测试缓存功能
 * 
 * 注意：这个测试需要在有完整Spring Boot应用配置的环境下运行
 * 如果在CI/CD环境中运行，请确保Hazelcast配置正确
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.cache.type=hazelcast",
    "hazelcast.cluster-name=test-integration-cluster",
    "logging.level.com.hazelcast=WARN"
})
class PrometheusTokenCacheIntegrationTest {

    @Autowired
    private PrometheusTokenCache prometheusTokenCache;

    @Autowired
    private CacheManager cacheManager;

    private static final String TEST_USER_ID = "integrationTestUser";

    @BeforeEach
    void setUp() {
        // 清理缓存，确保每个测试都是独立的
        if (prometheusTokenCache != null) {
            prometheusTokenCache.evictAllTokens();
        }
        
        // 等待缓存清理完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 测试完整的缓存流程
     */
    @Test
    void testFullCacheFlow() {
        log.info("🧪 测试完整的缓存流程");
        
        // 创建测试token
        PrometheusToken testToken = createTestToken("integration-test-token");
        
        // 1. 第一次获取 - 应该返回null
        PrometheusToken result1 = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNull(result1, "第一次获取应该返回null");
        
        // 2. 缓存token
        prometheusTokenCache.cacheToken(TEST_USER_ID, testToken);
        
        // 3. 再次获取 - 应该从缓存返回
        PrometheusToken result2 = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNotNull(result2, "缓存后应该能获取到token");
        assertEquals(testToken.getAccessToken(), result2.getAccessToken(), 
            "缓存的token应该与原始token相同");
        
        // 4. 测试getValidToken
        PrometheusToken validToken = prometheusTokenCache.getValidToken(TEST_USER_ID);
        assertNotNull(validToken, "有效token应该被返回");
        assertEquals(testToken.getAccessToken(), validToken.getAccessToken());
        
        // 5. 清除缓存
        prometheusTokenCache.evictToken(TEST_USER_ID);
        
        // 6. 验证缓存被清除
        PrometheusToken result3 = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNull(result3, "清除后应该返回null");
        
        log.info("✅ 完整的缓存流程测试通过");
    }

    /**
     * 测试缓存注解的实际效果
     */
    @Test
    void testCacheAnnotationsEffect() {
        log.info("🧪 测试缓存注解的实际效果");
        
        // 获取Spring缓存实例
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        assertNotNull(cache, "缓存实例应该存在");
        
        // 创建测试token
        PrometheusToken testToken = createTestToken("annotation-test-token");
        
        // 使用@CacheEvict注解的方法缓存token
        prometheusTokenCache.cacheToken(TEST_USER_ID, testToken);
        
        // 验证Spring缓存中确实有数据
        Cache.ValueWrapper valueWrapper = cache.get(TEST_USER_ID);
        assertNotNull(valueWrapper, "Spring缓存中应该有数据");
        
        PrometheusToken cachedToken = (PrometheusToken) valueWrapper.get();
        assertNotNull(cachedToken, "缓存的token不应为null");
        assertEquals(testToken.getAccessToken(), cachedToken.getAccessToken(), 
            "缓存的token应该正确");
        
        // 使用@Cacheable注解的方法获取token
        PrometheusToken retrievedToken = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNotNull(retrievedToken, "通过@Cacheable获取的token不应为null");
        assertEquals(testToken.getAccessToken(), retrievedToken.getAccessToken(), 
            "@Cacheable应该返回正确的token");
        
        // 使用@CacheEvict注解清除缓存
        prometheusTokenCache.evictToken(TEST_USER_ID);
        
        // 验证缓存被清除
        Cache.ValueWrapper clearedWrapper = cache.get(TEST_USER_ID);
        assertNull(clearedWrapper, "清除后Spring缓存应该为空");
        
        log.info("✅ 缓存注解效果测试通过");
    }

    /**
     * 测试缓存的unless条件
     */
    @Test
    void testCacheUnlessCondition() {
        log.info("🧪 测试缓存的unless条件");
        
        // 获取不存在的token（应该返回null且不被缓存）
        PrometheusToken result = prometheusTokenCache.getCachedToken("nonexistent-user");
        assertNull(result, "不存在的token应该返回null");
        
        // 验证null结果没有被缓存
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        Cache.ValueWrapper valueWrapper = cache.get("nonexistent-user");
        assertNull(valueWrapper, "null结果不应该被缓存（unless条件生效）");
        
        log.info("✅ 缓存unless条件测试通过");
    }

    /**
     * 测试token有效性验证
     */
    @Test
    void testTokenValidation() {
        log.info("🧪 测试token有效性验证");
        
        // 测试有效token
        PrometheusToken validToken = createTestToken("valid-token");
        prometheusTokenCache.cacheToken(TEST_USER_ID, validToken);
        
        assertTrue(prometheusTokenCache.isTokenValid(TEST_USER_ID), 
            "有效token应该通过验证");
        
        PrometheusToken retrievedValidToken = prometheusTokenCache.getValidToken(TEST_USER_ID);
        assertNotNull(retrievedValidToken, "getValidToken应该返回有效token");
        
        // 测试即将过期的token
        PrometheusToken expiringSoonToken = PrometheusToken.builder()
            .accessToken("expiring-token")
            .tokenType("Bearer")
            .expiresAt(LocalDateTime.now().plusMinutes(3)) // 3分钟后过期，小于5分钟阈值
            .createdAt(LocalDateTime.now())
            .build();
        
        String expiringSoonUserId = "expiring-user";
        prometheusTokenCache.cacheToken(expiringSoonUserId, expiringSoonToken);
        
        assertFalse(prometheusTokenCache.isTokenValid(expiringSoonUserId), 
            "即将过期的token应该被认为无效");
        
        PrometheusToken retrievedExpiringSoonToken = prometheusTokenCache.getValidToken(expiringSoonUserId);
        assertNull(retrievedExpiringSoonToken, "getValidToken应该对即将过期的token返回null");
        
        log.info("✅ token有效性验证测试通过");
    }

    /**
     * 测试批量清除缓存
     */
    @Test
    void testEvictAllTokens() {
        log.info("🧪 测试批量清除缓存");
        
        // 缓存多个token
        PrometheusToken token1 = createTestToken("token-1");
        PrometheusToken token2 = createTestToken("token-2");
        PrometheusToken token3 = createTestToken("token-3");
        
        prometheusTokenCache.cacheToken("user1", token1);
        prometheusTokenCache.cacheToken("user2", token2);
        prometheusTokenCache.cacheToken("user3", token3);
        
        // 验证所有token都被缓存
        assertNotNull(prometheusTokenCache.getCachedToken("user1"), "user1的token应该被缓存");
        assertNotNull(prometheusTokenCache.getCachedToken("user2"), "user2的token应该被缓存");
        assertNotNull(prometheusTokenCache.getCachedToken("user3"), "user3的token应该被缓存");
        
        // 清除所有缓存
        prometheusTokenCache.evictAllTokens();
        
        // 验证所有缓存都被清除
        assertNull(prometheusTokenCache.getCachedToken("user1"), "user1的token应该被清除");
        assertNull(prometheusTokenCache.getCachedToken("user2"), "user2的token应该被清除");
        assertNull(prometheusTokenCache.getCachedToken("user3"), "user3的token应该被清除");
        
        // 验证Spring缓存也被清除
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        assertNull(cache.get("user1"), "Spring缓存中user1应该被清除");
        assertNull(cache.get("user2"), "Spring缓存中user2应该被清除");
        assertNull(cache.get("user3"), "Spring缓存中user3应该被清除");
        
        log.info("✅ 批量清除缓存测试通过");
    }

    /**
     * 创建测试用的PrometheusToken
     */
    private PrometheusToken createTestToken(String accessToken) {
        return PrometheusToken.builder()
            .accessToken(accessToken)
            .tokenType("Bearer")
            .expiresAt(LocalDateTime.now().plusHours(1))
            .createdAt(LocalDateTime.now())
            .build();
    }
}
