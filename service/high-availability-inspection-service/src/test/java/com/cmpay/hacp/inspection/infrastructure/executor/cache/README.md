# PrometheusTokenCache 单元测试说明

## 概述

本目录包含了对 `PrometheusTokenCache` 类的完整单元测试，主要验证以下功能：

1. **@Cacheable 注解是否生效** - 验证缓存读取功能
2. **@CacheEvict 注解是否生效** - 验证缓存清除功能
3. **PrometheusCacheConfig 自定义配置是否生效** - 验证 Hazelcast 缓存配置
4. **缓存的并发安全性** - 验证多线程环境下的缓存行为
5. **缓存TTL配置** - 验证缓存过期时间设置

## 测试文件说明

### 1. PrometheusTokenCacheTest.java
**主要的单元测试类**，包含以下测试方法：

- `testPrometheusCacheConfig_Configuration()` - 验证 PrometheusCacheConfig 配置
- `testCacheable_GetCachedToken()` - 测试 @Cacheable 注解
- `testCacheEvict_CacheToken()` - 测试 @CacheEvict 注解（cacheToken方法）
- `testCacheEvict_EvictToken()` - 测试 @CacheEvict 注解（evictToken方法）
- `testCacheEvict_EvictAllTokens()` - 测试 @CacheEvict 注解（evictAllTokens方法）
- `testCacheable_UnlessCondition()` - 测试 @Cacheable 的 unless 条件
- `testGetValidToken_Logic()` - 测试 getValidToken 方法逻辑
- `testHazelcastCache_TTL()` - 测试 Hazelcast 缓存 TTL 配置
- `testCache_ConcurrentAccess()` - 测试缓存并发访问安全性
- `testCacheManager_Integration()` - 测试缓存管理器集成
- `testIsTokenValid_Scenarios()` - 测试 isTokenValid 方法各种场景

### 2. PrometheusTokenCacheIntegrationTest.java
**集成测试类**，在真实的 Spring Boot 环境中测试：

- `testFullCacheFlow()` - 测试完整的缓存流程
- `testCacheAnnotationsEffect()` - 测试缓存注解的实际效果
- `testCacheUnlessCondition()` - 测试缓存的 unless 条件
- `testTokenValidation()` - 测试 token 有效性验证
- `testEvictAllTokens()` - 测试批量清除缓存

### 3. PrometheusTokenCacheTestConfiguration.java
**测试配置类**，提供测试环境下的 Hazelcast 和缓存配置。

## 运行测试

### 前提条件

1. 确保项目已正确配置 Hazelcast 依赖
2. 确保 `application-test.yml` 或测试配置中包含正确的缓存配置

### 运行单个测试类

```bash
# 运行主要单元测试
mvn test -Dtest=PrometheusTokenCacheTest

# 运行集成测试
mvn test -Dtest=PrometheusTokenCacheIntegrationTest
```

### 运行所有缓存相关测试

```bash
mvn test -Dtest="*PrometheusTokenCache*Test"
```

### 在IDE中运行

1. 在 IntelliJ IDEA 或 Eclipse 中打开测试类
2. 右键点击类名或方法名
3. 选择 "Run Test" 或 "Debug Test"

## 测试配置

### 必要的配置属性

测试需要以下配置属性：

```yaml
spring:
  cache:
    type: hazelcast

hazelcast:
  cluster-name: test-cluster
```

### 测试环境配置

测试使用 `@ActiveProfiles("test")` 激活测试配置文件，确保：

1. 使用独立的 Hazelcast 集群名称
2. 配置适当的日志级别
3. 避免与其他测试或开发环境冲突

## 验证要点

### 1. @Cacheable 注解验证

- 第一次调用返回 null 时不会被缓存（unless 条件）
- 后续调用会从缓存返回数据
- 缓存 key 使用 userId 参数

### 2. @CacheEvict 注解验证

- `cacheToken()` 方法会清除指定 key 的缓存
- `evictToken()` 方法会清除指定 key 的缓存
- `evictAllTokens()` 方法会清除所有缓存（allEntries = true）

### 3. PrometheusCacheConfig 配置验证

- TTL 设置为 60 分钟（3600秒）
- 最大空闲时间为 30 分钟（1800秒）
- 最大条目数为 1000
- 缓存名称为 "prometheusTokens"

### 4. 并发安全性验证

- 多线程同时访问缓存不会出现异常
- 缓存操作是线程安全的
- 不会出现数据竞争或不一致状态

## 故障排除

### 常见问题

1. **Hazelcast 端口冲突**
   - 确保测试配置使用不同的端口
   - 检查是否有其他 Hazelcast 实例在运行

2. **缓存配置未生效**
   - 检查 `@EnableCaching` 注解是否存在
   - 验证 CacheManager Bean 是否正确配置

3. **测试超时**
   - 增加测试等待时间
   - 检查 Hazelcast 网络配置

### 调试建议

1. 启用 Hazelcast 调试日志：
   ```yaml
   logging:
     level:
       com.hazelcast: DEBUG
   ```

2. 检查缓存统计信息：
   ```java
   IMap<String, PrometheusToken> map = hazelcastInstance.getMap("prometheusTokens");
   System.out.println("Cache size: " + map.size());
   ```

3. 验证缓存配置：
   ```java
   Config config = hazelcastInstance.getConfig();
   MapConfig mapConfig = config.getMapConfig("prometheusTokens");
   System.out.println("TTL: " + mapConfig.getTimeToLiveSeconds());
   ```

## 最佳实践

1. **测试隔离**：每个测试方法开始前清理缓存
2. **异步等待**：缓存操作后适当等待确保操作完成
3. **资源清理**：测试结束后清理 Hazelcast 资源
4. **配置分离**：使用独立的测试配置避免影响其他环境

## 扩展测试

如需添加更多测试场景，可以考虑：

1. 测试缓存序列化/反序列化
2. 测试网络分区场景下的缓存行为
3. 测试缓存性能和内存使用
4. 测试缓存监控和统计功能
