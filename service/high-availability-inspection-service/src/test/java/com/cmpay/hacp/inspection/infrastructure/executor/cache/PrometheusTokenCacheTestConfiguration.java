package com.cmpay.hacp.inspection.infrastructure.executor.cache;

import com.cmpay.hacp.inspection.infrastructure.config.PrometheusCacheConfig;
import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.spring.cache.HazelcastCacheManager;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * PrometheusTokenCache测试配置类
 * 提供测试环境下的Hazelcast和缓存配置
 */
@TestConfiguration
@EnableCaching
public class PrometheusTokenCacheTestConfiguration {

    /**
     * 创建测试用的Hazelcast实例
     */
    @Bean
    @Primary
    public HazelcastInstance testHazelcastInstance() {
        Config config = new Config();
        config.setClusterName("test-cluster");
        
        // 配置网络设置，避免端口冲突
        config.getNetworkConfig().setPort(5701);
        config.getNetworkConfig().setPortAutoIncrement(true);
        config.getNetworkConfig().setPortCount(10);
        
        // 禁用多播发现，使用TCP-IP
        config.getNetworkConfig().getJoin().getMulticastConfig().setEnabled(false);
        config.getNetworkConfig().getJoin().getTcpIpConfig().setEnabled(true);
        config.getNetworkConfig().getJoin().getTcpIpConfig().addMember("127.0.0.1");
        
        // 应用PrometheusCacheConfig的配置
        PrometheusCacheConfig cacheConfig = new PrometheusCacheConfig();
        cacheConfig.customize(config);
        
        return Hazelcast.newHazelcastInstance(config);
    }

    /**
     * 创建测试用的缓存管理器
     */
    @Bean
    @Primary
    public CacheManager testCacheManager(HazelcastInstance hazelcastInstance) {
        return new HazelcastCacheManager(hazelcastInstance);
    }

    /**
     * 创建PrometheusCacheConfig Bean
     */
    @Bean
    public PrometheusCacheConfig prometheusCacheConfig() {
        return new PrometheusCacheConfig();
    }
}
