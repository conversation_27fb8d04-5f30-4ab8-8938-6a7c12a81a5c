package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.executor.config.PrometheusProperties;
import com.cmpay.hacp.inspection.domain.model.prometheus.*;
import com.cmpay.hacp.inspection.infrastructure.executor.cache.PrometheusTokenCache;
import com.cmpay.lemon.common.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PrometheusGatewayImpl简化单元测试
 * 主要测试缓存逻辑和参数验证
 */
@ExtendWith(MockitoExtension.class)
class PrometheusGatewaySimpleTest {

    @Mock
    private PrometheusTokenCache prometheusTokenCache;

    @Mock
    private PrometheusProperties properties;

    @Mock
    private PrometheusProperties.Auth authProperties;

    @Mock
    private PrometheusProperties.Http httpProperties;

    private PrometheusGatewayImpl prometheusGateway;

    @BeforeEach
    void setUp() {
        // 设置默认的properties mock行为
        when(properties.getAuth()).thenReturn(authProperties);
        when(properties.getHttp()).thenReturn(httpProperties);
        when(authProperties.getUserId()).thenReturn("testUser");
        when(authProperties.getUserKey()).thenReturn("testKey");
        when(authProperties.getTokenUrl()).thenReturn("/api/token");
        when(properties.getBaseUrl()).thenReturn("http://localhost:9090");
        when(properties.getIndicatorUrl()).thenReturn("/api/indicators");
        
        // 设置HTTP配置默认值
        when(httpProperties.getConnectTimeout()).thenReturn(10000);
        when(httpProperties.getReadTimeout()).thenReturn(30000);
        when(httpProperties.getWriteTimeout()).thenReturn(30000);
        when(httpProperties.getMaxIdleConnections()).thenReturn(10);
        when(httpProperties.getKeepAliveDuration()).thenReturn(5);
        when(httpProperties.isEnableSslCheck()).thenReturn(false);
        
        prometheusGateway = new PrometheusGatewayImpl(prometheusTokenCache, properties);
    }

    @Test
    void testGetAuthToken_FromCache_Success() {
        // Given
        PrometheusToken cachedToken = PrometheusToken.builder()
                .accessToken("cached-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
        
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(cachedToken);

        // When
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("cached-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(prometheusTokenCache).getValidToken("testUser");
    }

    @Test
    void testGetAuthToken_CacheReturnsNull() {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        // When & Then
        // 由于没有初始化HTTP客户端，这里会抛出异常，这是预期的
        assertThrows(Exception.class, () -> prometheusGateway.getAuthToken());
        
        verify(prometheusTokenCache).getValidToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_InvalidRequest() {
        // Given
        IndicatorQueryRequest invalidRequest = IndicatorQueryRequest.builder()
                .build(); // 缺少必要字段

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(invalidRequest);

        // Then
        assertNotNull(result);
        assertEquals("FFM80003", result.getMsgCd());
        assertEquals("请求参数验证失败", result.getMsgInfo());
        assertFalse(result.isSuccess());
    }

    @Test
    void testGetIndicatorDataList_ValidRequest_NoToken() {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        // When & Then
        // 由于没有初始化HTTP客户端，这里会抛出异常，这是预期的
        assertThrows(Exception.class, () -> prometheusGateway.getIndicatorDataList(request));
    }

    @Test
    void testPrometheusToken_Methods() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        PrometheusToken token = PrometheusToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(now.plusHours(1))
                .createdAt(now)
                .build();

        // When & Then
        assertFalse(token.isExpired());
        assertFalse(token.isExpiringSoon());
        assertEquals("Bearer test-token", token.getAuthorizationHeader());
        
        // Test expired token
        PrometheusToken expiredToken = PrometheusToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(now.minusHours(1))
                .createdAt(now.minusHours(2))
                .build();
        
        assertTrue(expiredToken.isExpired());
    }

    @Test
    void testPrometheusToken_ExpiringSoon() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        PrometheusToken token = PrometheusToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(now.plusMinutes(3)) // 3分钟后过期，小于5分钟阈值
                .createdAt(now)
                .build();

        // When & Then
        assertTrue(token.isExpiringSoon());
        assertFalse(token.isExpired());
    }

    @Test
    void testPrometheusToken_DefaultTokenType() {
        // Given
        PrometheusToken token = PrometheusToken.builder()
                .accessToken("test-token")
                .tokenType(null) // 没有设置tokenType
                .build();

        // When & Then
        assertEquals("Bearer test-token", token.getAuthorizationHeader());
    }

    @Test
    void testIndicatorQueryRequest_SpecialFields() {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .specialNameOne("value1")
                .specialNameTwo("value2")
                .specialNameThree("value3")
                .build();

        // When
        var specialFields = request.getSpecialFields();

        // Then
        assertEquals(3, specialFields.size());
        assertEquals("value1", specialFields.get("specialNameOne"));
        assertEquals("value2", specialFields.get("specialNameTwo"));
        assertEquals("value3", specialFields.get("specialNameThree"));
    }

    @Test
    void testPrometheusTokenResponse_Methods() {
        // Given
        PrometheusTokenResponse response = new PrometheusTokenResponse();
        
        // Test success
        response.setMsgCd("FFM00000");
        assertTrue(response.isSuccess());
        assertFalse(response.isAuthError());
        assertFalse(response.isUserStatusError());
        
        // Test auth error
        response.setMsgCd("FFM80001");
        assertFalse(response.isSuccess());
        assertTrue(response.isAuthError());
        assertFalse(response.isUserStatusError());
        
        // Test user status error
        response.setMsgCd("FFM80002");
        assertFalse(response.isSuccess());
        assertFalse(response.isAuthError());
        assertTrue(response.isUserStatusError());
    }

    @Test
    void testIndicatorQueryResponse_Methods() {
        // Given
        IndicatorQueryResponse response = IndicatorQueryResponse.builder()
                .msgCd("FFM00000")
                .msgInfo("Success")
                .build();

        // When & Then
        assertTrue(response.isSuccess());
        
        // Test failure
        response.setMsgCd("FFM80003");
        assertFalse(response.isSuccess());
    }

    @Test
    void testErrorCodeEnum_PrometheusErrors() {
        // Test all Prometheus related error codes
        assertEquals("HAI50001", ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR.getMsgCd());
        assertEquals("Prometheus连接失败", ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR.getMsgInfo());
        
        assertEquals("HAI50002", ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgCd());
        assertEquals("Prometheus认证失败", ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgInfo());
        
        assertEquals("HAI50003", ErrorCodeEnum.PROMETHEUS_QUERY_ERROR.getMsgCd());
        assertEquals("Prometheus查询失败", ErrorCodeEnum.PROMETHEUS_QUERY_ERROR.getMsgInfo());
        
        assertEquals("HAI50004", ErrorCodeEnum.PROMETHEUS_TOKEN_EXPIRED.getMsgCd());
        assertEquals("Prometheus认证令牌已过期", ErrorCodeEnum.PROMETHEUS_TOKEN_EXPIRED.getMsgInfo());
        
        assertEquals("HAI50005", ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE.getMsgCd());
        assertEquals("Prometheus响应格式无效", ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE.getMsgInfo());
    }

    /**
     * 辅助方法：创建有效的PrometheusToken
     */
    private PrometheusToken createValidToken() {
        return PrometheusToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
    }

    /**
     * 辅助方法：创建基本的IndicatorQueryRequest
     */
    private IndicatorQueryRequest createBasicRequest() {
        return IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();
    }
}
