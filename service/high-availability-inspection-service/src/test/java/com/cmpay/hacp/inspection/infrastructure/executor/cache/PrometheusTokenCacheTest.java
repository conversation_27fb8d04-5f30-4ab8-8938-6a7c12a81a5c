package com.cmpay.hacp.inspection.infrastructure.executor.cache;

import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusToken;
import com.cmpay.hacp.inspection.infrastructure.config.PrometheusCacheConfig;
import com.hazelcast.config.Config;
import com.hazelcast.config.MapConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PrometheusTokenCache单元测试
 * 测试@Cacheable、@CacheEvict注解以及PrometheusCacheConfig配置是否生效
 */
@Slf4j
@SpringBootTest(classes = {
    PrometheusTokenCache.class,
    PrometheusTokenCacheTestConfiguration.class
})
@ContextConfiguration(classes = PrometheusTokenCacheTestConfiguration.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.cache.type=hazelcast",
    "hazelcast.cluster-name=test-cluster"
})
class PrometheusTokenCacheTest {

    @Autowired
    private PrometheusTokenCache prometheusTokenCache;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private HazelcastInstance hazelcastInstance;

    @Autowired
    private PrometheusCacheConfig prometheusCacheConfig;

    private static final String TEST_USER_ID = "testUser";
    private static final String TEST_USER_ID_2 = "testUser2";

    @BeforeEach
    void setUp() {
        // 清理缓存，确保每个测试都是独立的
        prometheusTokenCache.evictAllTokens();
        
        // 等待缓存清理完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 测试PrometheusCacheConfig配置是否正确应用到Hazelcast
     */
    @Test
    void testPrometheusCacheConfig_Configuration() {
        log.info("🧪 测试PrometheusCacheConfig配置");
        
        // 验证Hazelcast配置
        Config config = hazelcastInstance.getConfig();
        assertNotNull(config, "Hazelcast配置不应为null");
        
        // 验证prometheusTokens缓存的MapConfig
        MapConfig mapConfig = config.getMapConfig(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        assertNotNull(mapConfig, "prometheusTokens缓存配置不应为null");
        
        // 验证TTL配置（60分钟 = 3600秒）
        assertEquals(60 * 60, mapConfig.getTimeToLiveSeconds(), 
            "TTL应该设置为60分钟（3600秒）");
        
        // 验证最大空闲时间配置（30分钟 = 1800秒）
        assertEquals(30 * 60, mapConfig.getMaxIdleSeconds(), 
            "最大空闲时间应该设置为30分钟（1800秒）");
        
        // 验证最大条目数配置
        assertEquals(1000, mapConfig.getEvictionConfig().getSize(), 
            "最大条目数应该设置为1000");
        
        log.info("✅ PrometheusCacheConfig配置验证通过");
    }

    /**
     * 测试@Cacheable注解是否生效
     */
    @Test
    void testCacheable_GetCachedToken() {
        log.info("🧪 测试@Cacheable注解是否生效");
        
        // 创建测试token
        PrometheusToken testToken = createTestToken("test-token-123");
        
        // 第一次调用 - 应该返回null（缓存中没有数据）
        PrometheusToken result1 = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNull(result1, "第一次调用应该返回null");
        
        // 手动向内部Map添加数据（模拟数据存在）
        prometheusTokenCache.cacheToken(TEST_USER_ID, testToken);
        
        // 等待缓存操作完成
        waitForCacheOperation();
        
        // 第二次调用 - 应该从缓存返回数据
        PrometheusToken result2 = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNotNull(result2, "第二次调用应该从缓存返回数据");
        assertEquals(testToken.getAccessToken(), result2.getAccessToken(), 
            "缓存返回的token应该与原始token相同");
        
        // 验证缓存确实被使用
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        assertNotNull(cache, "缓存实例不应为null");
        
        Cache.ValueWrapper valueWrapper = cache.get(TEST_USER_ID);
        assertNotNull(valueWrapper, "缓存中应该存在该用户的数据");
        
        log.info("✅ @Cacheable注解测试通过");
    }

    /**
     * 测试@CacheEvict注解 - cacheToken方法
     */
    @Test
    void testCacheEvict_CacheToken() {
        log.info("🧪 测试@CacheEvict注解 - cacheToken方法");
        
        // 创建测试token
        PrometheusToken oldToken = createTestToken("old-token");
        PrometheusToken newToken = createTestToken("new-token");
        
        // 先缓存一个token
        prometheusTokenCache.cacheToken(TEST_USER_ID, oldToken);
        waitForCacheOperation();
        
        // 验证缓存中有数据
        PrometheusToken cachedToken = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNotNull(cachedToken, "缓存中应该有数据");
        assertEquals("old-token", cachedToken.getAccessToken());
        
        // 缓存新token（应该触发@CacheEvict）
        prometheusTokenCache.cacheToken(TEST_USER_ID, newToken);
        waitForCacheOperation();
        
        // 验证缓存被清除并重新缓存
        PrometheusToken updatedToken = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNotNull(updatedToken, "更新后缓存中应该有新数据");
        assertEquals("new-token", updatedToken.getAccessToken(), 
            "缓存应该包含新的token");
        
        log.info("✅ @CacheEvict注解 - cacheToken方法测试通过");
    }

    /**
     * 测试@CacheEvict注解 - evictToken方法
     */
    @Test
    void testCacheEvict_EvictToken() {
        log.info("🧪 测试@CacheEvict注解 - evictToken方法");
        
        // 创建测试token
        PrometheusToken testToken = createTestToken("test-token");
        
        // 先缓存token
        prometheusTokenCache.cacheToken(TEST_USER_ID, testToken);
        waitForCacheOperation();
        
        // 验证缓存中有数据
        PrometheusToken cachedToken = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNotNull(cachedToken, "缓存中应该有数据");
        
        // 清除指定用户的缓存
        prometheusTokenCache.evictToken(TEST_USER_ID);
        waitForCacheOperation();
        
        // 验证缓存被清除
        PrometheusToken evictedToken = prometheusTokenCache.getCachedToken(TEST_USER_ID);
        assertNull(evictedToken, "缓存应该被清除");
        
        // 验证Spring缓存也被清除
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        Cache.ValueWrapper valueWrapper = cache.get(TEST_USER_ID);
        assertNull(valueWrapper, "Spring缓存中也不应该有数据");
        
        log.info("✅ @CacheEvict注解 - evictToken方法测试通过");
    }

    /**
     * 测试@CacheEvict注解 - evictAllTokens方法
     */
    @Test
    void testCacheEvict_EvictAllTokens() {
        log.info("🧪 测试@CacheEvict注解 - evictAllTokens方法");
        
        // 创建多个测试token
        PrometheusToken token1 = createTestToken("token-1");
        PrometheusToken token2 = createTestToken("token-2");
        
        // 缓存多个token
        prometheusTokenCache.cacheToken(TEST_USER_ID, token1);
        prometheusTokenCache.cacheToken(TEST_USER_ID_2, token2);
        waitForCacheOperation();
        
        // 验证缓存中有数据
        assertNotNull(prometheusTokenCache.getCachedToken(TEST_USER_ID), 
            "用户1的缓存应该存在");
        assertNotNull(prometheusTokenCache.getCachedToken(TEST_USER_ID_2), 
            "用户2的缓存应该存在");
        
        // 清除所有缓存
        prometheusTokenCache.evictAllTokens();
        waitForCacheOperation();
        
        // 验证所有缓存都被清除
        assertNull(prometheusTokenCache.getCachedToken(TEST_USER_ID), 
            "用户1的缓存应该被清除");
        assertNull(prometheusTokenCache.getCachedToken(TEST_USER_ID_2), 
            "用户2的缓存应该被清除");
        
        // 验证Spring缓存也被清除
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        assertNotNull(cache, "缓存实例应该存在");
        
        // 检查缓存是否为空（通过Hazelcast IMap）
        IMap<Object, Object> hazelcastMap = hazelcastInstance.getMap(
            PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        assertTrue(hazelcastMap.isEmpty(), "Hazelcast缓存应该为空");
        
        log.info("✅ @CacheEvict注解 - evictAllTokens方法测试通过");
    }

    /**
     * 测试缓存的unless条件（#result == null）
     */
    @Test
    void testCacheable_UnlessCondition() {
        log.info("🧪 测试@Cacheable的unless条件");
        
        // 第一次调用，返回null，不应该被缓存
        PrometheusToken result1 = prometheusTokenCache.getCachedToken("nonexistent-user");
        assertNull(result1, "不存在的用户应该返回null");
        
        // 验证null结果没有被缓存
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        Cache.ValueWrapper valueWrapper = cache.get("nonexistent-user");
        assertNull(valueWrapper, "null结果不应该被缓存");
        
        log.info("✅ @Cacheable的unless条件测试通过");
    }

    /**
     * 测试getValidToken方法的逻辑
     */
    @Test
    void testGetValidToken_Logic() {
        log.info("🧪 测试getValidToken方法逻辑");
        
        // 测试有效token
        PrometheusToken validToken = createTestToken("valid-token");
        prometheusTokenCache.cacheToken(TEST_USER_ID, validToken);
        waitForCacheOperation();
        
        PrometheusToken result = prometheusTokenCache.getValidToken(TEST_USER_ID);
        assertNotNull(result, "有效token应该被返回");
        assertEquals("valid-token", result.getAccessToken());
        
        // 测试即将过期的token
        PrometheusToken expiringSoonToken = PrometheusToken.builder()
            .accessToken("expiring-token")
            .tokenType("Bearer")
            .expiresAt(LocalDateTime.now().plusMinutes(3)) // 3分钟后过期，小于5分钟阈值
            .createdAt(LocalDateTime.now())
            .build();
        
        prometheusTokenCache.cacheToken(TEST_USER_ID_2, expiringSoonToken);
        waitForCacheOperation();
        
        PrometheusToken expiringSoonResult = prometheusTokenCache.getValidToken(TEST_USER_ID_2);
        assertNull(expiringSoonResult, "即将过期的token应该返回null");
        
        log.info("✅ getValidToken方法逻辑测试通过");
    }

    /**
     * 创建测试用的PrometheusToken
     */
    private PrometheusToken createTestToken(String accessToken) {
        return PrometheusToken.builder()
            .accessToken(accessToken)
            .tokenType("Bearer")
            .expiresAt(LocalDateTime.now().plusHours(1))
            .createdAt(LocalDateTime.now())
            .build();
    }

    /**
     * 测试Hazelcast缓存TTL配置是否生效
     */
    @Test
    void testHazelcastCache_TTL() {
        log.info("🧪 测试Hazelcast缓存TTL配置");

        // 获取Hazelcast IMap
        IMap<String, PrometheusToken> hazelcastMap = hazelcastInstance.getMap(
            PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);

        // 创建测试token
        PrometheusToken testToken = createTestToken("ttl-test-token");

        // 直接向Hazelcast缓存添加数据，设置较短的TTL用于测试
        hazelcastMap.put(TEST_USER_ID, testToken, 2, TimeUnit.SECONDS);

        // 立即验证数据存在
        assertTrue(hazelcastMap.containsKey(TEST_USER_ID), "数据应该立即存在于缓存中");

        // 等待TTL过期
        try {
            Thread.sleep(3000); // 等待3秒，超过2秒的TTL
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证数据已过期
        assertFalse(hazelcastMap.containsKey(TEST_USER_ID), "数据应该因TTL过期而被清除");

        log.info("✅ Hazelcast缓存TTL配置测试通过");
    }

    /**
     * 测试缓存的并发访问安全性
     */
    @Test
    void testCache_ConcurrentAccess() throws InterruptedException {
        log.info("🧪 测试缓存并发访问安全性");

        final int threadCount = 10;
        final int operationsPerThread = 50;

        // 创建测试token
        PrometheusToken testToken = createTestToken("concurrent-test-token");

        // 使用CountDownLatch确保所有线程同时开始
        java.util.concurrent.CountDownLatch startLatch = new java.util.concurrent.CountDownLatch(1);
        java.util.concurrent.CountDownLatch endLatch = new java.util.concurrent.CountDownLatch(threadCount);

        // 创建多个线程进行并发操作
        for (int i = 0; i < threadCount; i++) {
            final String userId = "user-" + i;
            new Thread(() -> {
                try {
                    startLatch.await(); // 等待开始信号

                    for (int j = 0; j < operationsPerThread; j++) {
                        // 执行缓存操作
                        prometheusTokenCache.cacheToken(userId, testToken);
                        PrometheusToken cached = prometheusTokenCache.getCachedToken(userId);
                        assertNotNull(cached, "并发访问时缓存应该正常工作");

                        if (j % 10 == 0) {
                            prometheusTokenCache.evictToken(userId);
                        }
                    }
                } catch (Exception e) {
                    log.error("并发测试中发生异常", e);
                    fail("并发测试不应该发生异常: " + e.getMessage());
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        // 开始所有线程
        startLatch.countDown();

        // 等待所有线程完成
        boolean completed = endLatch.await(30, TimeUnit.SECONDS);
        assertTrue(completed, "所有线程应该在30秒内完成");

        log.info("✅ 缓存并发访问安全性测试通过");
    }

    /**
     * 测试缓存配置的完整性
     */
    @Test
    void testCacheManager_Integration() {
        log.info("🧪 测试缓存管理器集成");

        // 验证CacheManager配置
        assertNotNull(cacheManager, "CacheManager不应为null");

        // 验证prometheusTokens缓存存在
        Cache cache = cacheManager.getCache(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE);
        assertNotNull(cache, "prometheusTokens缓存应该存在");

        // 验证缓存名称
        assertEquals(PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE, cache.getName(),
            "缓存名称应该正确");

        // 测试直接通过CacheManager操作缓存
        PrometheusToken testToken = createTestToken("cache-manager-test");
        cache.put(TEST_USER_ID, testToken);

        Cache.ValueWrapper valueWrapper = cache.get(TEST_USER_ID);
        assertNotNull(valueWrapper, "通过CacheManager存储的数据应该能够获取");

        PrometheusToken retrievedToken = (PrometheusToken) valueWrapper.get();
        assertNotNull(retrievedToken, "获取的token不应为null");
        assertEquals("cache-manager-test", retrievedToken.getAccessToken(),
            "获取的token应该与存储的相同");

        // 清除缓存
        cache.clear();
        assertNull(cache.get(TEST_USER_ID), "清除后缓存应该为空");

        log.info("✅ 缓存管理器集成测试通过");
    }

    /**
     * 测试isTokenValid方法的各种场景
     */
    @Test
    void testIsTokenValid_Scenarios() {
        log.info("🧪 测试isTokenValid方法各种场景");

        // 场景1：不存在的用户
        assertFalse(prometheusTokenCache.isTokenValid("nonexistent-user"),
            "不存在的用户应该返回false");

        // 场景2：有效的token
        PrometheusToken validToken = createTestToken("valid-token");
        prometheusTokenCache.cacheToken(TEST_USER_ID, validToken);
        waitForCacheOperation();

        assertTrue(prometheusTokenCache.isTokenValid(TEST_USER_ID),
            "有效token应该返回true");

        // 场景3：即将过期的token
        PrometheusToken expiringSoonToken = PrometheusToken.builder()
            .accessToken("expiring-token")
            .tokenType("Bearer")
            .expiresAt(LocalDateTime.now().plusMinutes(3)) // 3分钟后过期
            .createdAt(LocalDateTime.now())
            .build();

        prometheusTokenCache.cacheToken(TEST_USER_ID_2, expiringSoonToken);
        waitForCacheOperation();

        assertFalse(prometheusTokenCache.isTokenValid(TEST_USER_ID_2),
            "即将过期的token应该返回false");

        // 场景4：已过期的token
        PrometheusToken expiredToken = PrometheusToken.builder()
            .accessToken("expired-token")
            .tokenType("Bearer")
            .expiresAt(LocalDateTime.now().minusHours(1)) // 1小时前过期
            .createdAt(LocalDateTime.now().minusHours(2))
            .build();

        prometheusTokenCache.cacheToken("expired-user", expiredToken);
        waitForCacheOperation();

        assertFalse(prometheusTokenCache.isTokenValid("expired-user"),
            "已过期的token应该返回false");

        log.info("✅ isTokenValid方法各种场景测试通过");
    }

    /**
     * 等待缓存操作完成
     */
    private void waitForCacheOperation() {
        try {
            Thread.sleep(200); // 等待200ms确保缓存操作完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
