package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.executor.config.PrometheusProperties;
import com.cmpay.hacp.inspection.domain.model.prometheus.*;
import com.cmpay.hacp.inspection.infrastructure.executor.cache.PrometheusTokenCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PrometheusGateway集成测试
 * 使用真实的Prometheus HTTP接口进行测试
 * 配置来自dev环境
 */
@ExtendWith(MockitoExtension.class)
class PrometheusGatewayIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(PrometheusGatewayIntegrationTest.class);

    @Mock
    private PrometheusTokenCache prometheusTokenCache;

    private PrometheusGatewayImpl prometheusGateway;
    private PrometheusProperties properties;

    @BeforeEach
    void setUp() throws Exception {
        // 使用dev环境的真实配置
        properties = createDevPrometheusProperties();
        
        // 创建PrometheusGatewayImpl实例
        prometheusGateway = new PrometheusGatewayImpl(prometheusTokenCache, properties);
        
        // 初始化组件
        prometheusGateway.afterPropertiesSet();
        
        log.info("PrometheusGateway集成测试初始化完成，使用配置：");
        log.info("Base URL: {}", properties.getBaseUrl());
        log.info("User ID: {}", properties.getAuth().getUserId());
    }

    /**
     * 创建dev环境的Prometheus配置
     */
    private PrometheusProperties createDevPrometheusProperties() {
        PrometheusProperties props = new PrometheusProperties();
        
        // 基础配置
        props.setBaseUrl("http://10.176.156.3:1980");
        props.setIndicatorUrl("/openability/v1/firefly/busOperation/getIndicatorDataList");
        
        // 认证配置
        PrometheusProperties.Auth auth = new PrometheusProperties.Auth();
        auth.setUserId("business_indicators-15002");
        auth.setUserKey("YnxnGt39ZGAFfkUsG50W");
        auth.setTokenUrl("/openability/v1/firefly/getToken");
        auth.setTokenCacheMinutes(60);
        props.setAuth(auth);
        
        // HTTP配置
        PrometheusProperties.Http http = new PrometheusProperties.Http();
        http.setConnectTimeout(10000);
        http.setReadTimeout(30000);
        http.setWriteTimeout(30000);
        http.setMaxIdleConnections(10);
        http.setKeepAliveDuration(5);
        http.setEnableSslCheck(false);
        props.setHttp(http);
        
        return props;
    }

    @Test
    void testGetAuthToken_RealAPI_Success() {
        // Given
        when(prometheusTokenCache.getValidToken(anyString())).thenReturn(null);

        // When
        log.info("开始测试获取认证令牌...");
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result, "获取的token不应为null");
        assertNotNull(result.getAccessToken(), "accessToken不应为null");
        assertNotNull(result.getTokenType(), "tokenType不应为null");
        assertNotNull(result.getExpiresAt(), "expiresAt不应为null");
        assertNotNull(result.getCreatedAt(), "createdAt不应为null");
        
        assertFalse(result.isExpired(), "新获取的token不应该已过期");
        assertFalse(result.isExpiringSoon(), "新获取的token不应该即将过期");
        
        log.info("成功获取认证令牌:");
        log.info("Token Type: {}", result.getTokenType());
        log.info("Access Token: {}...", result.getAccessToken().substring(0, Math.min(10, result.getAccessToken().length())));
        log.info("Expires At: {}", result.getExpiresAt());
        log.info("Authorization Header: {}...", result.getAuthorizationHeader().substring(0, Math.min(20, result.getAuthorizationHeader().length())));
        
        // 验证缓存操作
        verify(prometheusTokenCache).getValidToken(properties.getAuth().getUserId());
        verify(prometheusTokenCache).cacheToken(eq(properties.getAuth().getUserId()), any(PrometheusToken.class));
    }

    @Test
    void testGetAuthToken_FromCache_Success() {
        // Given
        PrometheusToken cachedToken = PrometheusToken.builder()
                .accessToken("cached-test-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
        
        when(prometheusTokenCache.getValidToken(properties.getAuth().getUserId())).thenReturn(cachedToken);

        // When
        log.info("开始测试从缓存获取令牌...");
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("cached-test-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        
        log.info("成功从缓存获取令牌: {}", result.getAccessToken());
        
        // 验证只调用了缓存，没有发起HTTP请求
        verify(prometheusTokenCache).getValidToken(properties.getAuth().getUserId());
        verifyNoMoreInteractions(prometheusTokenCache);
    }

    @Test
    void testGetIndicatorDataList_RealAPI_Success() {
        // Given
        // 首先获取真实的token
        when(prometheusTokenCache.getValidToken(anyString())).thenReturn(null);
        PrometheusToken token = prometheusGateway.getAuthToken();
        assertNotNull(token, "需要先获取有效的token");
        
        // 重置mock，让后续调用返回真实token
        reset(prometheusTokenCache);
        when(prometheusTokenCache.getValidToken(anyString())).thenReturn(token);
        
        // 创建查询请求
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage_rate")
                .pageNum(1)
                .pageSize(10)
                .zones(Arrays.asList("zone1"))
                .build();

        // When
        log.info("开始测试查询指标数据...");
        log.info("查询参数: indicatorType={}, indicatorName={}, pageSize={}", 
                request.getIndicatorType(), request.getIndicatorName(), request.getPageSize());
        
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result, "查询结果不应为null");
        assertNotNull(result.getMsgCd(), "返回码不应为null");
        assertNotNull(result.getMsgInfo(), "返回信息不应为null");
        
        log.info("查询结果:");
        log.info("返回码: {}", result.getMsgCd());
        log.info("返回信息: {}", result.getMsgInfo());
        log.info("是否成功: {}", result.isSuccess());
        
        if (result.getData() != null) {
            log.info("数据条数: {}", result.getData().size());
            log.info("总记录数: {}", result.getTotal());
            log.info("当前页码: {}", result.getPageNum());
            log.info("页大小: {}", result.getPageSize());
            
            // 如果有数据，打印第一条记录的详细信息
            if (!result.getData().isEmpty()) {
                IndicatorQueryResponse.IndicatorData firstData = result.getData().get(0);
                log.info("第一条数据详情:");
                log.info("  指标名称: {}", firstData.getIndicatorName());
                log.info("  时间戳: {}", firstData.getTimestamp());
                log.info("  指标值: {}", firstData.getValue());
                log.info("  机房区域: {}", firstData.getZone());
            }
        } else {
            log.info("返回数据为空");
        }
        
        // 基本断言
        assertTrue(result.getMsgCd() != null && !result.getMsgCd().isEmpty(), "返回码不应为空");
        
        // 如果查询成功，验证数据结构
        if (result.isSuccess()) {
            assertNotNull(result.getPageNum(), "页码不应为null");
            assertNotNull(result.getPageSize(), "页大小不应为null");
            assertEquals(Integer.valueOf(1), result.getPageNum(), "页码应为1");
            assertEquals(Integer.valueOf(10), result.getPageSize(), "页大小应为10");
        }
    }

    @Test
    void testGetIndicatorDataList_InvalidRequest() {
        // Given
        IndicatorQueryRequest invalidRequest = IndicatorQueryRequest.builder()
                .build(); // 缺少必要字段

        // When
        log.info("开始测试无效请求参数...");
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(invalidRequest);

        // Then
        assertNotNull(result);
        assertEquals("FFM80003", result.getMsgCd());
        assertEquals("请求参数验证失败", result.getMsgInfo());
        assertFalse(result.isSuccess());
        
        log.info("无效请求测试通过: {}", result.getMsgInfo());
    }

    @Test
    void testGetIndicatorDataList_DifferentIndicatorTypes() {
        // Given
        // 首先获取真实的token
        when(prometheusTokenCache.getValidToken(anyString())).thenReturn(null);
        PrometheusToken token = prometheusGateway.getAuthToken();
        assertNotNull(token, "需要先获取有效的token");
        
        // 重置mock
        reset(prometheusTokenCache);
        when(prometheusTokenCache.getValidToken(anyString())).thenReturn(token);
        
        // 测试不同的指标类型
        String[] indicatorTypes = {"host", "mid", "container"};
        String[] indicatorNames = {"cpu_usage_rate", "memory_usage_rate", "disk_usage_rate"};
        
        for (int i = 0; i < indicatorTypes.length; i++) {
            String indicatorType = indicatorTypes[i];
            String indicatorName = indicatorNames[i % indicatorNames.length];
            
            IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                    .startTime(LocalDateTime.now().minusHours(1))
                    .endTime(LocalDateTime.now())
                    .indicatorType(indicatorType)
                    .indicatorName(indicatorName)
                    .pageNum(1)
                    .pageSize(5)
                    .build();

            // When
            log.info("测试指标类型: {}, 指标名称: {}", indicatorType, indicatorName);
            IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(request);

            // Then
            assertNotNull(result, "查询结果不应为null");
            log.info("指标类型 {} 查询结果: 返回码={}, 信息={}", 
                    indicatorType, result.getMsgCd(), result.getMsgInfo());
        }
    }

    @Test
    void testTokenCaching_Behavior() {
        // Given
        when(prometheusTokenCache.getValidToken(anyString()))
                .thenReturn(null)  // 第一次返回null，需要获取新token
                .thenReturn(PrometheusToken.builder()  // 第二次返回缓存的token
                        .accessToken("cached-token")
                        .tokenType("Bearer")
                        .expiresAt(LocalDateTime.now().plusHours(1))
                        .createdAt(LocalDateTime.now())
                        .build());

        // When & Then
        log.info("测试token缓存行为...");
        
        // 第一次调用 - 应该获取新token
        PrometheusToken firstToken = prometheusGateway.getAuthToken();
        assertNotNull(firstToken);
        log.info("第一次获取token: {}...", firstToken.getAccessToken().substring(0, 10));
        
        // 第二次调用 - 应该从缓存获取
        PrometheusToken secondToken = prometheusGateway.getAuthToken();
        assertNotNull(secondToken);
        assertEquals("cached-token", secondToken.getAccessToken());
        log.info("第二次获取token: {}", secondToken.getAccessToken());
        
        // 验证缓存调用
        verify(prometheusTokenCache, times(2)).getValidToken(properties.getAuth().getUserId());
        verify(prometheusTokenCache, times(1)).cacheToken(eq(properties.getAuth().getUserId()), any(PrometheusToken.class));
    }

    void tearDown() throws Exception {
        if (prometheusGateway != null) {
            prometheusGateway.destroy();
            log.info("PrometheusGateway资源已清理");
        }
    }
}
