package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.executor.config.PrometheusProperties;
import com.cmpay.hacp.inspection.domain.model.prometheus.*;
import com.cmpay.hacp.inspection.infrastructure.executor.cache.PrometheusTokenCache;

import static org.mockito.Mockito.*;

/**
 * 测试运行器 - 用于手动验证Prometheus接口
 * 可以直接运行main方法进行快速测试
 */
public class TestRunner {

    public static void main(String[] args) {
        System.out.println("=== Prometheus接口测试开始 ===");
        
        try {
            // 创建配置
            PrometheusProperties properties = createDevConfig();
            System.out.println("配置创建完成:");
            System.out.println("  服务器地址: " + properties.getBaseUrl());
            System.out.println("  用户ID: " + properties.getAuth().getUserId());
            
            // 创建mock缓存
            PrometheusTokenCache mockCache = mock(PrometheusTokenCache.class);
            when(mockCache.getValidToken(anyString())).thenReturn(null);
            
            // 创建gateway
            PrometheusGatewayImpl gateway = new PrometheusGatewayImpl(mockCache, properties);
            gateway.afterPropertiesSet();
            System.out.println("Gateway初始化完成");
            
            // 测试获取token
            System.out.println("\n--- 测试获取认证令牌 ---");
            PrometheusToken token = gateway.getAuthToken();
            
            if (token != null) {
                System.out.println("✅ Token获取成功!");
                System.out.println("  Token类型: " + token.getTokenType());
                System.out.println("  Token长度: " + token.getAccessToken().length());
                System.out.println("  过期时间: " + token.getExpiresAt());
                System.out.println("  是否有效: " + !token.isExpired());
                
                // 测试查询指标
                System.out.println("\n--- 测试查询指标数据 ---");
                testIndicatorQuery(gateway, token);
                
            } else {
                System.out.println("❌ Token获取失败");
            }
            
            // 清理资源
            gateway.destroy();
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testIndicatorQuery(PrometheusGatewayImpl gateway, PrometheusToken token) {
        try {
            // 重新设置mock以返回获取到的token
            PrometheusTokenCache mockCache = mock(PrometheusTokenCache.class);
            when(mockCache.getValidToken(anyString())).thenReturn(token);
            
            // 创建查询请求
            IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                    .startTime(java.time.LocalDateTime.now().minusHours(2))
                    .endTime(java.time.LocalDateTime.now().minusHours(1))
                    .indicatorType("host")
                    .indicatorName("cpu_usage_rate")
                    .pageNum(1)
                    .pageSize(5)
                    .build();
            
            System.out.println("查询参数:");
            System.out.println("  时间范围: " + request.getStartTime() + " 到 " + request.getEndTime());
            System.out.println("  指标类型: " + request.getIndicatorType());
            System.out.println("  指标名称: " + request.getIndicatorName());
            
            IndicatorQueryResponse response = gateway.getIndicatorDataList(request);
            
            if (response != null) {
                System.out.println("✅ 指标查询成功!");
                System.out.println("  返回码: " + response.getMsgCd());
                System.out.println("  返回信息: " + response.getMsgInfo());
                System.out.println("  是否成功: " + response.isSuccess());
                
                if (response.getData() != null) {
                    System.out.println("  数据条数: " + response.getData().size());
                    System.out.println("  总记录数: " + response.getTotal());
                    
                    // 显示前3条数据
                    response.getData().stream()
                            .limit(3)
                            .forEach(data -> {
                                System.out.println("  数据项: 指标=" + data.getIndicatorName() + 
                                        ", 值=" + data.getValue() + 
                                        ", 时间=" + data.getTimestamp() + 
                                        ", 区域=" + data.getZone());
                            });
                } else {
                    System.out.println("  返回数据为空");
                }
            } else {
                System.out.println("❌ 指标查询失败 - 响应为null");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 指标查询异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static PrometheusProperties createDevConfig() {
        PrometheusProperties props = new PrometheusProperties();
        props.setBaseUrl("http://10.29.132.10:80");
        props.setIndicatorUrl("/openability/v1/firefly/busOperation/getIndicatorDataList");
        
        PrometheusProperties.Auth auth = new PrometheusProperties.Auth();
        auth.setUserId("business_indicators-15002");
        auth.setUserKey("YnxnGt39ZGAFfkUsG50W");
        auth.setTokenUrl("/openability/v1/firefly/getToken");
        props.setAuth(auth);
        
        PrometheusProperties.Http http = new PrometheusProperties.Http();
        http.setConnectTimeout(15000);
        http.setReadTimeout(30000);
        http.setWriteTimeout(30000);
        http.setEnableSslCheck(false);
        props.setHttp(http);
        
        return props;
    }
}
