# 集成测试配置文件
# 使用dev环境的真实Prometheus配置

spring:
  profiles:
    active: integration-test

# Prometheus配置 - 使用dev环境的真实配置
inspection:
  prometheus:
    base-url: "http://10.176.156.3:1980"
    auth:
      user-id: "business_indicators-15002"
      user-key: "YnxnGt39ZGAFfkUsG50W"
      token-cache-minutes: 60
      token-url: "/openability/v1/firefly/getToken"
    indicator-url: "/openability/v1/firefly/busOperation/getIndicatorDataList"
    http:
      connect-timeout: 10000
      read-timeout: 30000
      write-timeout: 30000
      max-idle-connections: 10
      keep-alive-duration: 5
      enable-ssl-check: false

# 日志配置
logging:
  level:
    com.cmpay.hacp.inspection: DEBUG
    okhttp3: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
