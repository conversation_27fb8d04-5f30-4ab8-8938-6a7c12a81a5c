package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 判断条件
 */
@Getter
public enum RuleComparisonOperator {
    GREATER_THAN(1, "大于"),
    LESS_THAN(2, "小于"),
    GREATER_THAN_OR_EQUAL(3, "大于等于"),
    LESS_THAN_OR_EQUAL(4, "小于等于"),
    EQUAL(5, "等于");

    @JsonValue
    private final Integer code;
    private final String desc;

    RuleComparisonOperator(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
