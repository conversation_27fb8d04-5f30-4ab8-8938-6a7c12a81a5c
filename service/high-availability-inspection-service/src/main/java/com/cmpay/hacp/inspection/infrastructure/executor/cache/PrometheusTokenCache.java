package com.cmpay.hacp.inspection.infrastructure.executor.cache;

import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusToken;
import com.cmpay.hacp.inspection.infrastructure.config.PrometheusCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Prometheus Token 缓存实现
 * 用于缓存Prometheus认证token，避免频繁调用认证接口
 */
@Component
@Slf4j
public class PrometheusTokenCache {
    private final Map<String, PrometheusToken> tokenCache = new ConcurrentHashMap<>();

    /**
     * 获取缓存的token
     * @param userId 用户ID
     * @return PrometheusToken
     */
    @Cacheable(value = PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE, key = "#userId", unless = "#result == null")
    public PrometheusToken getCachedToken(String userId) {
        return tokenCache.get(userId);
    }

    public PrometheusToken getValidToken(String userId) {
        PrometheusToken token = getCachedToken(userId);
        return (token != null && isTokenValid(token)) ? token : null;
    }

    /**
     * 缓存token
     * @param userId 用户ID
     * @param token PrometheusToken对象
     */
    @CacheEvict(value = PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE, key = "#userId")
    public void cacheToken(String userId, PrometheusToken token) {
        tokenCache.put(userId, token);
    }

    /**
     * 清除指定用户的token缓存
     * @param userId 用户ID
     */
    @CacheEvict(value = PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE, key = "#userId")
    public void evictToken(String userId) {
        log.info("Evicting cached Prometheus token for user: {}", userId);
        tokenCache.remove(userId);
    }

    /**
     * 清除所有token缓存
     */
    @CacheEvict(value = PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE, allEntries = true)
    public void evictAllTokens() {
        tokenCache.clear();
    }

    /**
     * 检查token是否存在且有效
     * @param userId 用户ID
     * @return boolean
     */
    public boolean isTokenValid(String userId) {
        PrometheusToken token = tokenCache.get(userId);
        return token != null && !token.isExpiringSoon();
    }
}
