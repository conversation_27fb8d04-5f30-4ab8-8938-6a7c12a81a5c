package com.cmpay.hacp.inspection.application.executor;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptParameterRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptOutputFiledRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PythonPluginExecutor extends AbstractPluginExecutor {
    public PythonPluginExecutor(RemoteExecutionService remoteExecutionService,
                               PluginScriptParameterRepository pluginScriptParameterRepository,
                               PluginScriptRepository pluginScriptRepository,
                               PluginScriptOutputFiledRepository pluginScriptOutputFiledRepository,
                               RuleMatchingService ruleMatchingService) {
        super(remoteExecutionService, pluginScriptParameterRepository, pluginScriptRepository, pluginScriptOutputFiledRepository, ruleMatchingService);
    }

    @Override
    public String getName() {
        return "Python Script Inspection";
    }

    @Override
    public boolean supports(PluginType delimiter) {
        return delimiter == PluginType.PYTHON_SCRIPT;
    }
}
