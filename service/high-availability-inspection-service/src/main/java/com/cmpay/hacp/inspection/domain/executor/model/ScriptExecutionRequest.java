package com.cmpay.hacp.inspection.domain.executor.entity;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.Map;

@Data
@Builder
public class ScriptExecutionRequest {
    private String scriptContent;
    private ScriptType scriptType;
    private Map<String, String> environment;
    private String workingDirectory;
    private int timeoutSeconds;

    @Getter
    public enum ScriptType {
        SHELL("sh", ".sh"),
        PYTHON("python", ".py");

        private final String interpreter;
        private final String extension;

        ScriptType(String interpreter, String extension) {
            this.interpreter = interpreter;
            this.extension = extension;
        }
    }
}
