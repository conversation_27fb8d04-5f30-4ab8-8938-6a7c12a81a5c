package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorDefinitionDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IndicatorDefinitionConverter {
    List<IndicatorDefinition> toIndicatorDefinitionList(List<PrometheusIndicatorDefinitionDO> indicatorDefinitionDOList);

    @Mapping(target = "inputParams", ignore = true)
    IndicatorDefinition toIndicatorDefinition(PrometheusIndicatorDefinitionDO indicatorDefinitionDO);
}
