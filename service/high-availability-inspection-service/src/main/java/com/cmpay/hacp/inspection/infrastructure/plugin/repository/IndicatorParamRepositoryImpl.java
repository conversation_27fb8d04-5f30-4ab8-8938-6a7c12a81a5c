package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.repository.IndicatorDefinitionRepository;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.converter.IndicatorDefinitionConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.converter.IndicatorParamConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorDefinitionDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorParamDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PrometheusIndicatorDefinitionMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class IndicatorDefinitionRepositoryImpl extends CrudRepository<PrometheusIndicatorDefinitionMapper, PrometheusIndicatorDefinitionDO> implements IndicatorDefinitionRepository {
    private final IndicatorDefinitionConverter indicatorDefinitionConverter;
    private final IndicatorParamConverter indicatorParamConverter;

    @Override
    public List<IndicatorDefinition> getIndicatorDefinitionsByType(IndicatorType indicatorType) {
        List<PrometheusIndicatorDefinitionDO> indicatorDefinitionDOList = list(Wrappers.lambdaQuery(PrometheusIndicatorDefinitionDO.class)
                .eq(PrometheusIndicatorDefinitionDO::getIndicatorType, indicatorType));
        return indicatorDefinitionConverter.toIndicatorDefinitionList(indicatorDefinitionDOList);
    }

    @Override
    public IndicatorDefinition getIndicatorParams(IndicatorDefinition indicatorDefinition) {
        List<PrometheusIndicatorParamDO> indicatorParamDOList = list(Wrappers.lambdaQuery(PrometheusIndicatorParamDO.class)
                .eq(PrometheusIndicatorParamDO::getIndicatorId, indicatorDefinition.getIndicatorId()));
        return indicatorDefinition.setInputParams(indicatorParamConverter.toIndicatorParamList(indicatorParamDOList));
    }
}
