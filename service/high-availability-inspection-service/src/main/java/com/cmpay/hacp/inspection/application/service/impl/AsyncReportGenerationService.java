package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.DailyInspectionReportGenerationService;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportGenerationService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncReportGenerationService {
    private final DailyInspectionReportGenerationService dailyInspectionReportGenerationService;
    private final PerInspectionReportGenerationService perInspectionReportGenerationService;

    @Async("asyncInspectionReportExecutor")
    public void generateDailyReportAsync(LocalDate reportDate, Integer triggerType) {
        dailyInspectionReportGenerationService.generateDailyReport(reportDate, triggerType);
    }


    @Async("asyncInspectionReportExecutor")
    public void generatePerInspectionReportAsync(String taskId) {
        PerInspectionReportDO report = perInspectionReportGenerationService.generatePerInspectionReport(taskId);
    }
}
