package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.RuleConditionMapper;
import org.springframework.stereotype.Repository;

@Repository
public class RuleConditionRepository extends CrudRepository<RuleConditionMapper, RuleConditionDO> {
}
