package com.cmpay.hacp.inspection.domain.executor.gateway;

import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryResponse;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusToken;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryResponse;

/**
 * Prometheus网关接口
 * 提供与Prometheus系统交互的能力
 */
public interface PrometheusGateway {

    /**
     * 获取认证令牌
     *
     * @return Prometheus认证令牌
     * @throws RuntimeException 当认证失败时抛出异常
     */
    PrometheusToken getAuthToken();

    /**
     * 执行Prometheus查询
     *
     * @param request 查询请求
     * @return 查询响应
     * @throws RuntimeException 当查询失败时抛出异常
     */
    PrometheusQueryResponse query(PrometheusQueryRequest request);

    /**
     * 执行Prometheus范围查询
     *
     * @param request 查询请求
     * @param start 开始时间（Unix时间戳）
     * @param end 结束时间（Unix时间戳）
     * @param step 步长（秒）
     * @return 查询响应
     * @throws RuntimeException 当查询失败时抛出异常
     */
    PrometheusQueryResponse queryRange(PrometheusQueryRequest request, long start, long end, String step);
}
