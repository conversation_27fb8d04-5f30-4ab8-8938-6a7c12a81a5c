package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 插件标签关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_plugin_tags")
public class PluginTagsDO extends BaseDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 标签ID
     */
    private Long tagId;
}
