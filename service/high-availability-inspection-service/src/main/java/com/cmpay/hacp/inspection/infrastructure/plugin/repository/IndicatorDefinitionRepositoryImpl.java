package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.PrometheusIndicatorDefinitionRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorDefinitionDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PrometheusIndicatorDefinitionMapper;
import org.springframework.stereotype.Repository;

@Repository
public class PrometheusIndicatorDefinitionRepositoryImpl extends CrudRepository<PrometheusIndicatorDefinitionMapper, PrometheusIndicatorDefinitionDO> implements PrometheusIndicatorDefinitionRepository {
}
