package com.cmpay.hacp.inspection.infrastructure.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.stereotype.Component;

@Component
public class InspectionTenantHandler implements TenantLineHandler {
//    @Override
//    public Expression getTenantId() {
//        return new StringValue(TenantUtils.getWorkspaceId());
//    }

    /**
     * 获取当前租户ID
     */
    @Override
    public Expression getTenantId() {
        String currentTenantId = TenantContext.getCurrentTenantId();
        if (StringUtils.isEmpty(currentTenantId)) {
            throw new RuntimeException("获取租户ID失败");
        }
        return new StringValue(currentTenantId);
    }
    @Override
    public String getTenantIdColumn() {
        return "workspace_id";
    }

    // 公共数据的租户ID标识
    private static final String PUBLIC_TENANT_ID = "0";

    /**
     * 获取租户字段名
     */
    @Override
    public String getTenantIdColumn() {
        return "tenant_id";
    }

    /**
     * 过滤不需要多租户的表
     */
    @Override
    public boolean ignoreTable(String tableName) {
        // 系统配置表等不需要租户隔离的表
        Set<String> ignoreTables = Sets.newHashSet(
                "sys_config", "sys_dict", "sys_log"
        );
        return ignoreTables.contains(tableName);
    }

    /**
     * 自定义租户查询条件
     * 关键：支持查询公共数据和当前租户数据
     */
    @Override
    public Expression getTenantId(boolean where) {
        String currentTenantId = TenantContext.getCurrentTenantId();
        if (StringUtils.isEmpty(currentTenantId)) {
            throw new RuntimeException("获取租户ID失败");
        }

        // 构建条件：tenant_id = '当前租户ID' OR tenant_id = '0'（公共数据）
        Expression tenantCondition = new StringValue(currentTenantId);
        Expression publicCondition = new StringValue(PUBLIC_TENANT_ID);

        return new Parenthesis(
                new OrExpression(
                        new EqualsTo(new Column(getTenantIdColumn()), tenantCondition),
                        new EqualsTo(new Column(getTenantIdColumn()), publicCondition)
                )
        );
    }
}
