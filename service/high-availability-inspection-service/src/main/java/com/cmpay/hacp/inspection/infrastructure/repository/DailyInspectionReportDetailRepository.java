package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.DailyInspectionReportDetailMapper;
import org.springframework.stereotype.Repository;

/**
 * 按日巡检报告数据访问层
 */
@Repository
public class DailyReportDetailRepository extends CrudRepository<DailyInspectionReportDetailMapper, DailyInspectionReportDetailDO> {
}
