package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 告警等级
 */
@Getter
public enum RuleLevelEnum {

    LOW(1, "低"),
    MEDIUM(2, "中"),
    HIGH(3, "高"),
    CRITICAL(4, "严重");

    @JsonValue
    private final Integer code;
    private final String desc;

    RuleLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
