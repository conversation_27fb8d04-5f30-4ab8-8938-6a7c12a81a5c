package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.assembler.TagMapper;
import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.domain.model.common.Tag;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginTagsDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleTagsDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TagDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginTagsRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleTagsRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.TagsRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TagServiceImpl implements TagService {

    private final TagsRepository tagsRepository;
    private final TagMapper tagMapper;
    private final PluginTagsRepository pluginTagsRepository;
    private final RuleTagsRepository ruleTagsRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTag(String name) {
        // 防止标签重名
        TagDO tag = tagsRepository.getOne(Wrappers.lambdaQuery(TagDO.class)
                .eq(TagDO::getName, name));
        // 不为空，直接返回标签id
        if (tag != null) {
            return tag.getId();
        }

        // 否则保存标签信息
        TagDO tagDO = new TagDO();
        tagDO.setName(name);
        tagsRepository.save(tagDO);

        return tagDO.getId();
    }

    @Override
    public List<Tag> getTagByTagIds(List<Long> tagIds) {
        List<TagDO> tagDOS = tagsRepository.list(
                Wrappers.lambdaQuery(TagDO.class)
                        .in(TagDO::getId, tagIds));
        if (CollectionUtils.isEmpty(tagDOS)) {
            return new ArrayList<>();
        }
        return tagMapper.toTagList(tagDOS);
    }

    @Override
    public Map<Long, String> pluginTags() {
        Map<Long, String> pluginTags = new HashMap<>();
        List<PluginTagsDO> list = pluginTagsRepository.list();
        if (CollectionUtils.isEmpty(list)) {
            return pluginTags;
        }
        // 提取所有tagId
        List<Long> tagIds = list.stream()
                .map(PluginTagsDO::getTagId)
                .collect(Collectors.toList());

        // 批量获取标签信息
        List<Tag> tags = getTagByTagIds(tagIds);

        // 填充结果map
        if (tags != null) {
            tags.forEach(tag -> pluginTags.put(tag.getTagId(), tag.getName()));
        }
        return pluginTags;
    }

    @Override
    public Map<Long, String> ruleTags() {
        Map<Long, String> ruleTags = new HashMap<>();
        List<RuleTagsDO> list = ruleTagsRepository.list();

        if (CollectionUtils.isEmpty(list)) {
            return ruleTags;
        }
        // 提取所有tagId
        List<Long> tagIds = list.stream()
                .map(RuleTagsDO::getTagId)
                .collect(Collectors.toList());

        // 批量获取标签信息
        List<Tag> tags = getTagByTagIds(tagIds);

        // 填充结果map
        if (tags != null) {
            tags.forEach(tag -> ruleTags.put(tag.getTagId(), tag.getName()));
        }
        return ruleTags;
    }

}
