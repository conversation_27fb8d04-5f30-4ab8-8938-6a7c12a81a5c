package com.cmpay.hacp.inspection.application.common.enums;

import com.cmpay.lemon.common.AlertCapable;
import lombok.Getter;

@Getter
public enum ErrorCodeEnum implements AlertCapable {
    DATA_PUBLIC_KEY_IS_NULL("HAI00001", "数据加密公钥为空"),
    DATA_PRIVATE_KEY_IS_NULL("HAI00002", "数据解密私钥为空"),
    // 插件模块
    PLUGIN_ID_REQUIRED("HAI10001", "插件ID不能为空"),
    PLUGIN_UPDATE_FAILED("HAI10002", "更新插件失败"),
    PLUGIN_IS_NULL("HAI10003", "插件不存在"),
    PLUGIN_NAME_EXIST("HAI10004", "插件名称已存在！"),
    PLUGIN_PARAMS_IS_NOT_NULL("HAI10005", "插件参数不能为空！"),
    PLUGIN_PARAMS_VALUE_IS_NOT_NULL("HAI10006", "插件参数值不能为空！"),
    PLUGIN_DURATION_IS_NOT_NULL("HAI10007", "持续时间不能为空！"),
    PLUGIN_NAME_IS_NOT_NULL("HAI10008", "插件名称不能为空"),
    PLUGIN_NAME_MAX_LENGTH_EXCEED("HAI10009", "插件名称长度不能超过128个字符"),
    PLUGIN_TYPE_IS_NOT_NULL("HAI10010", "插件类型不能为空！"),
    PLUGIN_DESC_MAX_LENGTH_EXCEED("HAI10011", "插件描述长度不能超过500个字符"),
    PLUGIN_SCRIPT_CONTENT_IS_NOT_NULL("HAI10012", "脚本内容不能为空"),
    PLUGIN_INFO_NOT_EXIST("HAI10013", "插件详情不存在"),
    TAG_ID_NOT_EXIST("HAI10014", "存在不存在的标签ID"),

    // 规则模块
    RULE_ID_REQUIRED("HAI20001", "规则ID不能为空"),
    RULE_UPDATE_FAILED("HAI20002", "更新规则失败"),
    TASK_RULE_EXECUTIONS_IS_NOT_NULL("HAI20003", "规则执行配置不能为空"),
    RULE_NAME_IS_NOT_NULL("HAI20004", "规则名称不能为空"),
    RULE_NAME_MAX_LENGTH_EXCEED("HAI20005", "规则名称长度不能超过128个字符"),
    RULE_STATUS_IS_NOT_NULL("HAI20006", "规则状态不能为空"),
    // 任务模块
    TASK_ID_REQUIRED("HAI30001", "任务ID不能为空"),
    TASK_CREATE_ERROR("HAI30002", "创建任务失败"),
    TASK_UPDATE_FAILED("HAI30003", "更新任务失败"),
    TASK_NOT_FOUND("HAI30004", "任务不存在"),
    TASK_EXECUTION_ERROR("HAI30005", "执行任务失败"),
    TASK_NAME_IS_NOT_NULL("HAI30006", "任务名称不能为空"),
    TASK_NAME_MAX_LENGTH_EXCEED("HAI30007", "任务名称长度不能超过128个字符"),
    TASK_DESC_MAX_LENGTH_EXCEED("HAI30008", "任务描述长度不能超过500个字符！"),
    TASK_NAME_EXIST("HAI30009", "任务已存在！"),
    TASK_NAME_NOT_EXIST("HAI30010", "任务不存在！"),
    // 报告模块
    REPORT_NOT_FOUND("HAI40001", "报告不存在"),
    REPORT_DETAIL_NOT_FOUND("HAI40002", "报告详细内容不存在"),
    // Prometheus模块
    PROMETHEUS_CONNECTION_ERROR("HAI50001", "Prometheus连接失败"),
    PROMETHEUS_AUTH_ERROR("HAI50002", "Prometheus认证失败"),
    PROMETHEUS_QUERY_ERROR("HAI50003", "Prometheus查询失败"),
    PROMETHEUS_TOKEN_EXPIRED("HAI50004", "Prometheus认证令牌已过期"),
    PROMETHEUS_INVALID_RESPONSE("HAI50005", "Prometheus响应格式无效"),
    REPORT_DETAIL_NOT_FOUND("HAI40002", "报告详细内容不存在"),

    // 调度
    CRON_EXPRESSION_IS_NOT_NULL("HAI50001", "CRON表达式不能为空"),
    TASK_EXECUTE_DATE_IS_NOT_NULL("HAI50100", "执行日期不能为空"),
    TASK_EXECUTE_TIME_IS_NOT_NULL("HAI50101", "执行时间不能为空"),

    // 告警模块
    ALARM_LEVEL_REQUIRED("HAI50302", "告警等级不能为空"),
    FUNCTION_PERSPECTIVE_REQUIRED("HAI50304", "功能视角不能为空"),
    DEPLOY_ENV_REQUIRED("HAI50305", "部署环境不能为空"),
    MONITOR_FIELD_REQUIRED("HAI50306", "监控字段不能为空"),

    // 调度模块
    TASK_SCHEDULE_ERROR("HAI50405", "调度任务错误"),
    SCHEDULE_CONFIG_REQUIRED("HAI50406", "调度配置不能为空"),
    INTERVAL_VALUE_REQUIRED("HAI50407", "间隔值不能为空"),
    INTERVAL_VALUE_MIN("HAI50408", "间隔值必须大于0"),
    INTERVAL_UNIT_REQUIRED("HAI50409", "间隔单位不能为空"),

    // 参数模块
    PARAM_NAME_REQUIRED("HAI50500", "参数名称不能为空"),
    PARAM_TYPE_REQUIRED("HAI50501", "参数类型不能为空"),
    REGEX_REQUIRED("HAI50502", "正则表达式不能为空"),
    EXAMPLE_REQUIRED("HAI50503", "示例不能为空"),
    PARAM_DESC_REQUIRED("HAI50504", "参数描述不能为空"),
    FIELD_NAME_REQUIRED("HAI50505", "字段名称不能为空"),
    FIELD_TYPE_REQUIRED("HAI50506", "字段类型不能为空"),
    CONDITION_REQUIRED("HAI50507", "判断条件不能为空"),
    JUDGE_VALUE_REQUIRED("HAI50508", "判断数值不能为空"),
    PEAK_CHECK_REQUIRED("HAI50509", "仅检查峰值不能为空"),
    GOVERNANCE_ADVICE_REQUIRED("HAI50510", "治理建议不能为空"),
    TARGET_ENV_IDS_REQUIRED("HAI50511", "目标环境ID列表不能为空");

    private final String msgCd;
    private final String msgInfo;

    ErrorCodeEnum(String code, String message) {
        this.msgCd = code;
        this.msgInfo = message;
    }

    @Override
    public String getMsgCd() {
        return msgCd;
    }

    @Override
    public String getMsgInfo() {
        return msgInfo;
    }
}
