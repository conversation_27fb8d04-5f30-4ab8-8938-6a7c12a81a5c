package com.cmpay.hacp.inspection.domain.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RuleType {
    METRIC_INSPECTION(1, "指标检查"),
    LOG_INSPECTION(2, "日志检查"),
    AVAILABILITY_INSPECTION(3, "可用性检查");

    private final Integer code;
    private final String displayName;

    private static final Map<Integer, RuleType> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleType::getCode, type -> type));

    RuleType(Integer code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    public static RuleType getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
}
