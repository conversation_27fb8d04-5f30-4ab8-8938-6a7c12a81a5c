package com.cmpay.hacp.inspection.domain.model.rule;

import com.cmpay.hacp.inspection.domain.model.common.AuditInfo;
import com.cmpay.hacp.inspection.domain.model.enums.*;
import lombok.Data;

import java.util.List;

/**
 * 巡检规则业务对象
 */
@Data
public class InspectionRule {

    /**
     * 规则ID（更新时使用）
     */
    private String ruleId;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 告警等级（低、中、高、严重）
     * 建议使用枚举类映射（如 RuleLevelEnum），增强类型安全
     */
    private RuleLevelEnum level;

    /**
     * 插件状态（0启用，1禁用）
     */
    private RuleStatusEnum status;

    /**
     * 规则类型（指标、日志、可用性）
     */
    private RuleType type;

    /**
     * 标签ID列表
     */
    private List<Long> tagIds;

    /**
     * 规则关联的插件ID
     */
    private String pluginId;

    /**
     * 功能视角
     */
    private RuleAngleViewEnum angleView;

    /**
     * 部署环境
     */
    private RuleDeployEnvEnum deployEnv;

    /**
     * 规则插件参数配置
     */
    private List<RulePluginParam> pluginParams;

    /**
     * 规则执行条件
     */
    private RulePluginResult pluginResult;

    private AuditInfo auditInfo;

}
