package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;

import java.time.LocalDate;

/**
 * 按日报告生成服务接口
 * 负责按日巡检报告的生成、调度和管理
 */
public interface DailyInspectionReportGenerationService {

    /**
     * 生成指定日期的按日报告
     *
     * @param reportDate  报告日期
     * @param triggerType 触发类型（0:定时触发 1:手动触发 2:补偿触发）
     * @return 生成的报告对象
     */
    DailyInspectionReportDO generateDailyReport(LocalDate reportDate, Integer triggerType);

    /**
     * 重新生成指定日期的按日报告
     *
     * @param reportDate 报告日期
     * @param force      是否强制重新生成（忽略已存在的报告）
     * @return 生成的报告对象
     */
    DailyInspectionReportDO regenerateDailyReport(LocalDate reportDate, boolean force);

    /**
     * 检查并补偿缺失的按日报告
     * 扫描指定日期范围内缺失的报告并自动生成
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 补偿生成的报告数量
     */
    int compensateMissingReports(LocalDate startDate, LocalDate endDate);

    /**
     * 取消正在执行的报告生成任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelGenerationTask(String taskId);
}
