package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;

import java.util.List;

public interface PrometheusIndicatorDefinitionRepository {
    /**
     * 根据指标类型查询指标定义列表
     *
     * @param indicatorType 指标类型
     * @return 指标定义列表
     */
    List<IndicatorDefinition> getIndicatorDefinitionsByType(String indicatorType);
}
