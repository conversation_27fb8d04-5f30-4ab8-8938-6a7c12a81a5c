package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 插件类型
 */
@Getter
public enum PluginType {
    SHELL_SCRIPT(1, "SHELL脚本"),
    PYTHON_SCRIPT(2, "PYTHON脚本"),
    PAGE_CHECK(3, "页面检查"),
    AUTOMATION_TEST(4, "自动化测试"),
    METRIC(5,"系统指标");

    @JsonValue
    private final Integer code;
    private final String desc;

    PluginType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
