package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ExecutionResult {
//    SUCCESS(1, "全部成功"),
//    PARTIAL_SUCCESS(2, "部分成功"),
//    FAILED(3, "全部失败");
    PASS(1, "通过"),
    FAIL(0, "失败");

    @JsonValue
    private final Integer code;
    private final String desc;

    ExecutionResult(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
