package com.cmpay.hacp.inspection.domain.model.rule;

import com.cmpay.hacp.inspection.domain.model.enums.RuleCheckPeakEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 规则监控字段
 */
@Data
public class RulePluginResult {

    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 监控字段（插件输出字段ID）
     */
    private Long pluginResultId;

    /**
     * 判断条件(大小等于)
     * {@link RuleComparisonOperator}
     */
    private Integer judge;

    /**
     * 数值
     */
    private BigDecimal judgeValue;

    /**
     * 持续时间（秒）
     */
    private Long duration;

    /**
     * 检查间隔（秒）
     */
    private Integer checkInterval;

    /**
     * 仅检查峰值
     * {@link RuleCheckPeakEnum}
     */
    private Integer checkPeak;

    /**
     * 检查特定进程（逗号分隔）
     */
    private Integer specificProcesses;

    /**
     * 规则预览
     */
    private String rulePreview;

    /**
     * 治理建议
     */
    private String suggest;

}
