package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ResourceType {
    VIRTUAL_MACHINE(1,"vm"),
    CONTAINER(2,"container");

    @JsonValue
    private final Integer code;
    private final String desc;

    ResourceType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
