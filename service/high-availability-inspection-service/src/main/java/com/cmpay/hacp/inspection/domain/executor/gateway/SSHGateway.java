package com.cmpay.hacp.inspection.domain.executor.gateway;

import com.cmpay.hacp.inspection.application.executor.RemoteExecutionResult;
import com.cmpay.hacp.inspection.application.executor.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.application.executor.SshConnectionConfig;

import java.util.Map;

public interface SSHGateway {
    RemoteExecutionResult executeScript(SshConnectionConfig connectionConfig,
                                        ScriptExecutionRequest request);

    RemoteExecutionResult executeCommand(SshConnectionConfig connectionConfig,
                                         String command);
    
}
