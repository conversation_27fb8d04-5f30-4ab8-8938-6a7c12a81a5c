package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.PerInspectionReportGenerationService;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportQueryService;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 按次巡检报告服务实现（适配器模式）
 *
 * @deprecated 该实现类作为适配器保持向后兼容性
 * 实际功能委托给分离的生成服务和查询服务：
 * - {@link PerInspectionReportGenerationService} 负责报告生成
 * - {@link PerInspectionReportQueryService} 负责报告查询
 *
 * 建议直接使用分离的服务接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Deprecated
public class PerInspectionReportServiceImpl implements PerInspectionReportService {

    private final PerInspectionReportGenerationService generationService;
    private final PerInspectionReportQueryService queryService;

    @Override
    @Async("asyncInspectionReportExecutor")
    public void generatePerInspectionReportAsync(String taskId) {
        try {
            log.info("Starting async per-inspection report generation for task: {}", taskId);
            ReportDO report = generatePerInspectionReport(taskId);
            if (report != null) {
                log.info("Per-inspection report generated successfully for task: {}, reportId: {}", 
                        taskId, report.getReportId());
            }
        } catch (Exception e) {
            log.error("Failed to generate per-inspection report for task: {}", taskId, e);
        }
    }

    @Override
    public ReportDO generatePerInspectionReport(String taskId) {
        log.info("Generating per-inspection report for task: {}", taskId);

        // 1. 获取任务执行记录
        // FIXME 传 execution ID
        TaskExecutionDO taskExecution = taskExecutionRepository.getOne(
                Wrappers.lambdaQuery(TaskExecutionDO.class)
                        .eq(TaskExecutionDO::getTaskId, taskId)
                        .orderByDesc(TaskExecutionDO::getExecutionTime)
                        .last("LIMIT 1")
        );

        if (taskExecution == null) {
            log.warn("Task execution not found for taskId: {}", taskId);
            return null;
        }

        // 2. 获取规则执行记录
        List<RuleExecutionDO> ruleExecutions = ruleExecutionRepository.list(
                Wrappers.lambdaQuery(RuleExecutionDO.class)
                        .eq(RuleExecutionDO::getTaskExecutionId, taskExecution.getId())
                        .orderBy(true, true, RuleExecutionDO::getExecutionTime)
        );

        // 3. 检查是否已存在报告
        ReportDO existingReport = getReportByTaskExecutionId(taskExecution.getId());
        if (existingReport != null) {
            log.info("Report already exists for task: {}, reportId: {}", taskId, existingReport.getReportId());
            return existingReport;
        }

        // 4. 使用MapStruct进行基础映射
        ReportDO report = reportMapper.toReportDO(taskExecution, ruleExecutions);
        
        // 5. 生成报告ID
        report.setReportId(generateReportId(taskExecution.getExecutionTime()));
        
        // 6. 构建详细的报告内容
        ReportDetail content = buildDetailedReportContent(taskExecution, ruleExecutions);
        report.setContent(content);

        // 7. 保存报告
        try {
            reportRepository.save(report);
            log.info("Per-inspection report saved successfully for task: {}, reportId: {}", 
                    taskId, report.getReportId());
            return report;
        } catch (Exception e) {
            log.error("Failed to save per-inspection report for task: {}", taskId, e);
            throw new RuntimeException("Failed to save inspection report", e);
        }
    }

    @Override
    public ReportDO getReportByTaskId(String taskId) {
        return reportRepository.getOne(
                Wrappers.lambdaQuery(ReportDO.class)
                        .eq(ReportDO::getTaskId, taskId)
                        .orderByDesc(ReportDO::getGenerateTime)
                        .last("LIMIT 1")
        );
    }

    @Override
    public ReportDO getReportByTaskExecutionId(Long taskExecutionId) {
        return reportRepository.getOne(
                Wrappers.lambdaQuery(ReportDO.class)
                        .eq(ReportDO::getTaskExecutionId, taskExecutionId)
        );
    }

    @Override
    public ReportDO getReportByReportId(String reportId) {
        return reportRepository.getOne(
                Wrappers.lambdaQuery(ReportDO.class)
                        .eq(ReportDO::getReportId, reportId)
        );
    }

    @Override
    public boolean deleteReportByTaskId(String taskId) {
        try {
            boolean deletedCount = reportRepository.remove(
                    Wrappers.lambdaQuery(ReportDO.class)
                            .eq(ReportDO::getTaskId, taskId)
            );
            log.info("Deleted {} reports for task: {}", deletedCount, taskId);
            return deletedCount;
        } catch (Exception e) {
            log.error("Failed to delete reports for task: {}", taskId, e);
            return false;
        }
    }

    /**
     * 生成报告ID
     * 格式：RPT-YYYYMMDDHHMMSS-序号
     */
    private String generateReportId(LocalDateTime executionTime) {
        String timestamp = executionTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String sequence = String.format("%03d", new Random().nextInt(1000));
        return "RPT-" + timestamp + "-" + sequence;
    }

    /**
     * 构建详细的报告内容
     */
    private ReportDetail buildDetailedReportContent(TaskExecutionDO taskExecution, List<RuleExecutionDO> ruleExecutions) {
        // 1. 构建执行概况
        ReportDetail.ExecutionSummary executionSummary = buildExecutionSummary(taskExecution, ruleExecutions);
        
        // 2. 构建执行结果分布
        ReportDetail.ExecutionDistribution executionDistribution = buildExecutionDistribution(ruleExecutions);
        
        // 3. 构建执行时间分布
        ReportDetail.ExecutionTimeDistribution timeDistribution = buildTimeDistribution(ruleExecutions);
        
        // 4. 构建规则检查详情
        List<ReportDetail.RuleCheckDetail> ruleCheckDetails = buildRuleCheckDetails(ruleExecutions);
        
        // 5. 构建异常详情
        List<ReportDetail.ExceptionDetail> exceptionDetails = buildExceptionDetails(ruleExecutions);

        return ReportDetail.builder()
                .executionSummary(executionSummary)
                .executionDistribution(executionDistribution)
                .timeDistribution(timeDistribution)
                .ruleCheckDetails(ruleCheckDetails)
                .exceptionDetails(exceptionDetails)
                .build();
    }

    /**
     * 构建执行概况
     */
    private ReportDetail.ExecutionSummary buildExecutionSummary(TaskExecutionDO taskExecution, List<RuleExecutionDO> ruleExecutions) {
        int totalRules = ruleExecutions.size();
        int successRules = (int) ruleExecutions.stream()
                .filter(rule -> "1".equals(rule.getExecutionStatus()))
                .count();
        int failedRules = totalRules - successRules;

        Long duration = taskExecution.getDuration() != null ? taskExecution.getDuration().longValue() : 0L;
        String durationStr = formatDurationString(duration);

        return ReportDetail.ExecutionSummary.builder()
                .startTime(taskExecution.getStartTime())
                .endTime(taskExecution.getEndTime())
                .duration(duration)
                .durationStr(durationStr)
                .totalRules(totalRules)
                .successRules(successRules)
                .build();
    }

    /**
     * 构建执行结果分布
     */
    private ReportDetail.ExecutionDistribution buildExecutionDistribution(List<RuleExecutionDO> ruleExecutions) {
        int total = ruleExecutions.size();
        if (total == 0) {
            return ReportDetail.ExecutionDistribution.builder()
                    .total(0)
                    .successRate(0)
                    .statusCounts(new ArrayList<>())
                    .build();
        }

        int successCount = (int) ruleExecutions.stream()
                .filter(rule -> "1".equals(rule.getExecutionStatus()))
                .count();
        int failedCount = total - successCount;
        int successRate = (successCount * 100) / total;

        List<ReportDetail.ExecutionDistribution.StatusCount> statusCounts = Arrays.asList(
                ReportDetail.ExecutionDistribution.StatusCount.builder()
                        .status("通过")
                        .statusCode("passed")
                        .count(successCount)
                        .build(),
                ReportDetail.ExecutionDistribution.StatusCount.builder()
                        .status("失败")
                        .statusCode("failed")
                        .count(failedCount)
                        .build()
        );

        return ReportDetail.ExecutionDistribution.builder()
                .total(total)
                .successRate(successRate)
                .statusCounts(statusCounts)
                .build();
    }

    /**
     * 构建执行时间分布
     */
    private ReportDetail.ExecutionTimeDistribution buildTimeDistribution(List<RuleExecutionDO> ruleExecutions) {
        Map<String, Integer> timeRangeCounts = new HashMap<>();
        timeRangeCounts.put("<1秒", 0);
        timeRangeCounts.put("1-3秒", 0);
        timeRangeCounts.put("3-5秒", 0);
        timeRangeCounts.put("5-10秒", 0);
        timeRangeCounts.put(">10秒", 0);

        for (RuleExecutionDO rule : ruleExecutions) {
            Long duration = rule.getExecutionDuration();
            if (duration == null) continue;

            double seconds = duration / 1000.0;
            String range;
            if (seconds < 1) {
                range = "<1秒";
            } else if (seconds < 3) {
                range = "1-3秒";
            } else if (seconds < 5) {
                range = "3-5秒";
            } else if (seconds < 10) {
                range = "5-10秒";
            } else {
                range = ">10秒";
            }
            timeRangeCounts.put(range, timeRangeCounts.get(range) + 1);
        }

        List<ReportDetail.ExecutionTimeDistribution.TimeRange> timeRanges = timeRangeCounts.entrySet().stream()
                .map(entry -> ReportDetail.ExecutionTimeDistribution.TimeRange.builder()
                        .label(entry.getKey())
                        .count(entry.getValue())
                        .build())
                .collect(Collectors.toList());

        return ReportDetail.ExecutionTimeDistribution.builder()
                .timeRanges(timeRanges)
                .build();
    }

    /**
     * 构建规则检查详情
     */
    private List<ReportDetail.RuleCheckDetail> buildRuleCheckDetails(List<RuleExecutionDO> ruleExecutions) {
        return ruleExecutions.stream()
                .map(this::buildRuleCheckDetail)
                .collect(Collectors.toList());
    }

    /**
     * 构建单个规则检查详情
     */
    private ReportDetail.RuleCheckDetail buildRuleCheckDetail(RuleExecutionDO ruleExecution) {
        String status = "1".equals(ruleExecution.getExecutionStatus()) ? "passed" : "failed";

        return ReportDetail.RuleCheckDetail.builder()
                .ruleId(ruleExecution.getRuleId())
                .ruleName(ruleExecution.getRuleId()) // 可以后续从规则表获取真实名称
                .resourceName(StringUtils.hasText(ruleExecution.getResourceName()) ? 
                        ruleExecution.getResourceName() : ruleExecution.getResourceId())
                .resourceType("HOST") // 可以后续从资源表获取真实类型
                .status(status)
                .checkResult(ruleExecution.getExecutionResult())
                .detailInfo(ruleExecution.getExecutionResult())
                .build();
    }

    /**
     * 构建异常详情
     */
    private List<ReportDetail.ExceptionDetail> buildExceptionDetails(List<RuleExecutionDO> ruleExecutions) {
        return ruleExecutions.stream()
                .filter(rule -> !"1".equals(rule.getExecutionStatus()))
                .map(this::buildExceptionDetail)
                .collect(Collectors.toList());
    }

    /**
     * 构建单个异常详情
     */
    private ReportDetail.ExceptionDetail buildExceptionDetail(RuleExecutionDO ruleExecution) {
        return ReportDetail.ExceptionDetail.builder()
                .ruleId(ruleExecution.getRuleId())
                .ruleName(ruleExecution.getRuleId()) // 可以后续从规则表获取真实名称
                .description(ruleExecution.getExecutionResult())
                .resourceName(ruleExecution.getResourceName())
                .suggestion("请检查相关配置和网络连接") // 可以后续根据具体错误类型提供建议
                .build();
    }

    /**
     * 格式化持续时间字符串
     */
    private String formatDurationString(Long durationSeconds) {
        if (durationSeconds == null || durationSeconds == 0) {
            return "0秒";
        }
        
        if (durationSeconds < 60) {
            return durationSeconds + "秒";
        } else if (durationSeconds < 3600) {
            long minutes = durationSeconds / 60;
            long seconds = durationSeconds % 60;
            return minutes + "分" + (seconds > 0 ? seconds + "秒" : "");
        } else {
            long hours = durationSeconds / 3600;
            long minutes = (durationSeconds % 3600) / 60;
            long seconds = durationSeconds % 60;
            StringBuilder sb = new StringBuilder();
            sb.append(hours).append("小时");
            if (minutes > 0) sb.append(minutes).append("分");
            if (seconds > 0) sb.append(seconds).append("秒");
            return sb.toString();
        }
    }

    /**
     * 格式化响应时间
     */
    private String formatResponseTime(Long responseTimeMs) {
        if (responseTimeMs == null) {
            return "N/A";
        }
        if (responseTimeMs < 1000) {
            return responseTimeMs + "ms";
        } else {
            return String.format("%.1fs", responseTimeMs / 1000.0);
        }
    }
}
