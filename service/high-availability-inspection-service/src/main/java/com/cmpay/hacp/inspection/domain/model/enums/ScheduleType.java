package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 调度类型
 */
public enum ScheduleType {

    CRON_EXPRESSION(1, "CRON表达式"),
    FIXED_INTERVAL(2, "固定间隔"),
    FIXED_TIME(3, "固定时间");

    @JsonValue
    private final Integer code;
    private final String desc;

    ScheduleType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
