package com.cmpay.hacp.inspection.application.executor;

import com.cmpay.hacp.inspection.domain.executor.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.executor.model.InspectionExecutor;
import com.cmpay.hacp.inspection.domain.executor.gateway.SSHGateway;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.enums.ResourceType;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class SSHExecutor implements InspectionExecutor {
    private final SSHGateway sshGateway;

    @Override
    public InspectionResult execute(InspectionTask task) {
        // TODO SSH执行逻辑
        // sshGateway.executeScript()
        return InspectionResult.builder().build();
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return (context.getPluginType() == PluginType.SHELL_SCRIPT ||
                context.getPluginType() == PluginType.PYTHON_SCRIPT) &&
                context.getResourceType() == ResourceType.VIRTUAL_MACHINE;
    }
}
