package com.cmpay.hacp.inspection.domain.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RuleCheckPeakEnum {

    NO(0, "否"),
    YES(1, "是");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, RuleCheckPeakEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleCheckPeakEnum::getCode, type -> type));


    RuleCheckPeakEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RuleCheckPeakEnum getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
}
