package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.DailyInspectionReportMapper;
import org.springframework.stereotype.Repository;

/**
 * 按日巡检报告数据访问层
 */
@Repository
public class DailyReportRepository extends CrudRepository<DailyInspectionReportMapper, DailyInspectionReportDO> {
}
