package com.cmpay.hacp.inspection.domain.plugin.service;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;

import java.util.List;

public interface IndicatorService {
    List<IndicatorDefinition> getIndicatorDefinitionsByType(IndicatorType indicatorType);

    IndicatorDefinition getIndicatorDefinitionById(String indicatorId);
}
