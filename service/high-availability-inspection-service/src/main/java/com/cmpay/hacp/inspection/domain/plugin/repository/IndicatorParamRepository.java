package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;

import java.util.List;

public interface IndicatorParamRepository {
    /**
     * 根据指标定义查询指标参数
     *
     * @param indicatorDefinition 指标定义
     * @return 指标参数列表
     */
    IndicatorDefinition getIndicatorParams(IndicatorDefinition indicatorDefinition);
}
