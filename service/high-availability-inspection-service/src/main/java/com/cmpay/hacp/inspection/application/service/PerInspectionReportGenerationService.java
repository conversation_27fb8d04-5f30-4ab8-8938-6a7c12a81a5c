package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;

/**
 * 按次巡检报告生成服务接口
 * 负责单次巡检任务报告的生成、重新生成和管理
 */
public interface PerInspectionReportGenerationService {

    /**
     * 异步生成按次巡检报告
     * 在任务执行完成后调用，生成详细的巡检报告
     *
     * @param taskId 任务ID
     * @return 任务ID（用于跟踪异步任务）
     */
    void generatePerInspectionReportAsync(String taskId);

    /**
     * 同步生成按次巡检报告
     * 立即生成并返回报告对象
     *
     * @param taskId 任务ID
     * @return 生成的报告对象
     */
    ReportDO generatePerInspectionReport(String taskId);
}
