package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.assembler.InspectionPluginMapper;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionCipherService;
import com.cmpay.hacp.inspection.application.service.InspectionPluginService;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptParameter;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.*;
import com.cmpay.hacp.inspection.infrastructure.repository.*;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 巡检插件服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionPluginServiceImpl implements InspectionPluginService {

    private final PluginRepository pluginRepository;
    private final PluginScriptRepository pluginScriptRepository;
    private final PluginTagsRepository pluginTagsRepository;
    private final PluginScriptOutputFiledRepository scriptResultRepository;
    private final PluginScriptParameterRepository scriptParameterRepository;
    private final InspectionPluginMapper inspectionPluginMapper;
    private final SystemCipherService systemCipherService;
    private final InspectionCipherService inspectionCipherService;
    private final TagsRepository tagsRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPlugin(InspectionPlugin inspectionPlugin) {
        // 新增重名校验
        if (pluginRepository.getOne(
                Wrappers.lambdaQuery(PluginDO.class)
                        .eq(PluginDO::getName, inspectionPlugin.getName())) != null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_NAME_EXIST);
        }

        // 标签校验
        if (CollectionUtils.isNotEmpty(inspectionPlugin.getTagIds())) {
            checkTagIds(inspectionPlugin.getTagIds());
        }

        // 保存插件基本信息
        PluginDO pluginDO = inspectionPluginMapper.toInspectionPluginDO(inspectionPlugin);
        pluginDO.setPluginId("TEMP_PLUGIN" + System.currentTimeMillis());
        pluginRepository.save(pluginDO);
        // 业务标识符
        String pluginId = String.format("PLUGIN-%06d", pluginDO.getId());
        pluginDO.setPluginId(pluginId);
        // 只更新业务标识符字段，减少更新范围
        pluginRepository.update(Wrappers.lambdaUpdate(PluginDO.class)
                .eq(PluginDO::getId, pluginDO.getId())
                .set(PluginDO::getPluginId, pluginDO.getPluginId()));

        inspectionPlugin.setPluginId(pluginId);

        // 如果有标签，保存标签关联
        if (CollectionUtils.isNotEmpty(inspectionPlugin.getTagIds())) {
            // 保存新标签关联
            List<PluginTagsDO> mappings = new ArrayList<>();
            for (Long tagId : inspectionPlugin.getTagIds()) {
                PluginTagsDO mapping = new PluginTagsDO();
                mapping.setPluginId(pluginDO.getPluginId());
                mapping.setTagId(tagId);
                mappings.add(mapping);
            }

            pluginTagsRepository.saveBatch(mappings, 10);
        }

        // 保存脚本内容
        PluginScriptDO pluginScriptDO = inspectionPluginMapper.toPluginScriptDO(inspectionPlugin);
        pluginScriptRepository.save(pluginScriptDO);

        // 保存输出字段定义
        if (CollectionUtils.isNotEmpty(inspectionPlugin.getResults())) {
            List<PluginScriptOutputFieldDO> results = inspectionPlugin.getResults().stream()
                    .map(scriptResult -> inspectionPluginMapper.toPluginScriptResultDO(scriptResult, pluginId))
                    .collect(Collectors.toList());
            scriptResultRepository.saveBatch(results, 10);
        }

        // 保存参数定义
        if (CollectionUtils.isNotEmpty(inspectionPlugin.getParameters())) {
            String uuid = inspectionPlugin.getKey();
            List<PluginScriptParameterDO> parameters = inspectionPlugin.getParameters().stream()
                    .map(scriptParameter -> inspectionPluginMapper.toPluginScriptParameterDO(scriptParameter, pluginId))
                    .peek(scriptParameter -> {
                        if(scriptParameter.getIsEncrypted() != null && scriptParameter.getIsEncrypted()){
                            scriptParameter.setParamValue(inspectionCipherService.dataEncrypt(systemCipherService.decryptData(uuid, scriptParameter.getParamValue())));
                        }
                    })
                    .collect(Collectors.toList());
            scriptParameterRepository.saveBatch(parameters, 10);
        }

        return inspectionPlugin.getPluginId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlugin(InspectionPlugin inspectionPlugin) {
        if(JudgeUtils.isNull(pluginRepository.getOne(Wrappers.lambdaQuery(PluginDO.class).
                eq(PluginDO::getPluginId, inspectionPlugin.getPluginId())))){
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_IS_NULL);
        }

        // 更新重名校验
        if (pluginRepository.getOne(
                Wrappers.lambdaQuery(PluginDO.class)
                        .eq(PluginDO::getName, inspectionPlugin.getName())
                        .ne(PluginDO::getPluginId, inspectionPlugin.getPluginId())) != null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_NAME_EXIST);
        }

        // 标签校验
        if (CollectionUtils.isNotEmpty(inspectionPlugin.getTagIds())) {
            checkTagIds(inspectionPlugin.getTagIds());
        }

        // 1. 更新插件基本信息
        PluginDO pluginDO = inspectionPluginMapper.toInspectionPluginDO(inspectionPlugin);
        pluginDO.setPluginId(inspectionPlugin.getPluginId());
        boolean updatePlugin = pluginRepository.update(Wrappers.lambdaUpdate(PluginDO.class)
                .eq(PluginDO::getPluginId, pluginDO.getPluginId())
                .set(PluginDO::getName, pluginDO.getName())
                .set(PluginDO::getType, pluginDO.getType())
                .set(PluginDO::getStatus, pluginDO.getStatus())
                .set(PluginDO::getDescription, pluginDO.getDescription()));

        if (!updatePlugin) {
            log.error("Failed to update plugin with ID: {}", inspectionPlugin.getPluginId());
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_UPDATE_FAILED);
        }

        // 2. 更新脚本内容
        PluginScriptDO pluginScriptDO = inspectionPluginMapper.toPluginScriptDO(inspectionPlugin);
        // 查询现有脚本
        PluginScriptDO existingScript = pluginScriptRepository.getOne(
                Wrappers.lambdaQuery(PluginScriptDO.class)
                        .eq(PluginScriptDO::getPluginId, inspectionPlugin.getPluginId()));

        // 更新脚本内容
        if (existingScript != null) {
            existingScript.setScriptContent(pluginScriptDO.getScriptContent());
            existingScript.setScriptResultType(pluginScriptDO.getScriptResultType());
            pluginScriptRepository.updateById(existingScript);
        } else {
            // 如果不存在则创建新脚本
            pluginScriptDO.setPluginId(inspectionPlugin.getPluginId());
            pluginScriptRepository.save(pluginScriptDO);
        }

        // 3. 更新标签关联
        if (CollectionUtils.isEmpty(inspectionPlugin.getTagIds())) {
            // 如果标签为空，直接删除所有关联
            pluginTagsRepository.remove(
                    Wrappers.lambdaQuery(PluginTagsDO.class)
                            .eq(PluginTagsDO::getPluginId, inspectionPlugin.getPluginId()));
        } else {
            // 获取原有标签关联
            List<PluginTagsDO> originList = pluginTagsRepository.list(
                    Wrappers.lambdaQuery(PluginTagsDO.class)
                            .eq(PluginTagsDO::getPluginId, inspectionPlugin.getPluginId()));

            // 将原有标签ID转换为Set提高查找效率
            Set<Long> existingTagIds = originList.stream()
                    .map(PluginTagsDO::getTagId)
                    .collect(Collectors.toSet());

            // 将需要保留的标签ID转为Set
            Set<Long> retainedTagIds = new HashSet<>(inspectionPlugin.getTagIds());

            // 准备插入列表 - 找出新增的标签ID
            List<PluginTagsDO> tagsToInsert = new ArrayList<>();
            for (Long tagId : retainedTagIds) {
                if (!existingTagIds.contains(tagId)) {
                    PluginTagsDO newTag = new PluginTagsDO();
                    newTag.setPluginId(inspectionPlugin.getPluginId());
                    newTag.setTagId(tagId);
                    tagsToInsert.add(newTag);
                }
            }

            // 因标签关联对象只有插件ID和标签ID，所以更新列表无需更新

            // 准备删除列表 - 找出需要删除的标签
            List<Long> idsToDelete = new ArrayList<>();
            for (PluginTagsDO existingTag : originList) {
                if (!retainedTagIds.contains(existingTag.getTagId())) {
                    idsToDelete.add(existingTag.getId());
                }
            }

            if (!tagsToInsert.isEmpty()) {
                pluginTagsRepository.saveBatch(tagsToInsert, 10);
            }
            if (!idsToDelete.isEmpty()) {
                pluginTagsRepository.removeByIds(idsToDelete);
            }
        }

        // 4. 更新参数定义
        if (CollectionUtils.isEmpty(inspectionPlugin.getParameters())) {
            // 如果参数为空，直接删除所有相关参数
            scriptParameterRepository.remove(
                    Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                            .eq(PluginScriptParameterDO::getPluginId, inspectionPlugin.getPluginId()));
        } else {
            // 获取当前所有参数
            List<PluginScriptParameterDO> originList = scriptParameterRepository.list(
                    Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                            .eq(PluginScriptParameterDO::getPluginId, inspectionPlugin.getPluginId()));
            // 创建名称到参数对象的映射，提高查找效率
            Map<String, PluginScriptParameterDO> originParamMap = originList.stream()
                    .collect(Collectors.toMap(PluginScriptParameterDO::getParamName, Function.identity()));

            List<PluginScriptParameterDO> insertList = new ArrayList<>();
            List<PluginScriptParameterDO> updateList = new ArrayList<>();
            List<Long> deleteIds = new ArrayList<>(originParamMap.size());

            // 设置将要保留的参数名集合，用于判断删除
            Set<String> retainedParamNames = new HashSet<>();
            // 处理新增和更新
            for (PluginScriptParameter scriptParameter : inspectionPlugin.getParameters()) {
                String key = inspectionPlugin.getKey();
                String paramName = scriptParameter.getParamName();
                retainedParamNames.add(paramName);

                PluginScriptParameterDO paramDO = inspectionPluginMapper.toPluginScriptParameterDO(scriptParameter, inspectionPlugin.getPluginId());

                PluginScriptParameterDO existingParam = originParamMap.get(paramName);

                // 值是否加密
                if(paramDO.getIsEncrypted()){
                    paramDO.setParamValue(inspectionCipherService.dataEncrypt(systemCipherService.decryptData(key,paramDO.getParamValue())));
                }
                if (existingParam == null) {
                    // 新增参数
                    insertList.add(paramDO);
                } else {
                    // 更新参数
                    paramDO.setId(existingParam.getId());
                    updateList.add(paramDO);
                }
            }

            // 确定要删除的参数
            originParamMap.forEach((paramName, param) -> {
                if (!retainedParamNames.contains(paramName)) {
                    deleteIds.add(param.getId());
                }
            });

            if (!insertList.isEmpty()) {
                scriptParameterRepository.saveBatch(insertList, 10);
            }
            if (!updateList.isEmpty()) {
                scriptParameterRepository.updateBatchById(updateList, 10);
            }
            if (!deleteIds.isEmpty()) {
                scriptParameterRepository.removeByIds(deleteIds);
            }
        }

        // 5. 更新输出字段定义
        if (CollectionUtils.isEmpty(inspectionPlugin.getResults())) {
            // 如果输出字段为空，直接删除所有相关字段
            scriptResultRepository.remove(
                    Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                            .eq(PluginScriptOutputFieldDO::getPluginId, inspectionPlugin.getPluginId()));
        } else {
            // 获取当前所有输出字段
            List<PluginScriptOutputFieldDO> originList = scriptResultRepository.list(
                    Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                            .eq(PluginScriptOutputFieldDO::getPluginId, inspectionPlugin.getPluginId()));
            // 创建字段名称到字段对象的映射，提高查找效率
            Map<String, PluginScriptOutputFieldDO> originResultMap = originList.stream()
                    .collect(Collectors.toMap(PluginScriptOutputFieldDO::getFieldName, Function.identity()));

            List<PluginScriptOutputFieldDO> insertList = new ArrayList<>();
            List<PluginScriptOutputFieldDO> updateList = new ArrayList<>();
            List<Long> deleteIds = new ArrayList<>();

            // 设置将要保留的字段名集合，用于判断删除
            Set<String> retainedFieldNames = new HashSet<>();

            // 处理新增和更新
            for (PluginScriptResult scriptResult : inspectionPlugin.getResults()) {
                String fieldName = scriptResult.getFieldName();
                retainedFieldNames.add(fieldName);

                PluginScriptOutputFieldDO resultDO = inspectionPluginMapper.toPluginScriptResultDO(scriptResult, inspectionPlugin.getPluginId());

                PluginScriptOutputFieldDO existingResult = originResultMap.get(fieldName);
                if (existingResult == null) {
                    // 新增字段
                    insertList.add(resultDO);
                } else {
                    // 更新字段
                    resultDO.setId(existingResult.getId());
                    resultDO.setCreatedBy(existingResult.getCreatedBy());
                    resultDO.setCreatedByName(existingResult.getCreatedByName());
                    resultDO.setCreatedTime(existingResult.getCreatedTime());
                    updateList.add(resultDO);
                }
            }

            // 确定要删除的字段
            originResultMap.forEach((fieldName, result) -> {
                if (!retainedFieldNames.contains(fieldName)) {
                    deleteIds.add(result.getId());
                }
            });

            if (!insertList.isEmpty()) {
                scriptResultRepository.saveBatch(insertList, 10);
            }
            if (!updateList.isEmpty()) {
                scriptResultRepository.updateBatchById(updateList, 10);
            }
            if (!deleteIds.isEmpty()) {
                scriptResultRepository.removeByIds(deleteIds);
            }
        }
    }

    /**
     * 标签校验
     * @param tagIds
     */
    /**
     * 标签校验
     * @param tagIds 待校验的标签ID列表
     */
    public void checkTagIds(List<Long> tagIds) {
        // 查询数据库中存在哪些标签ID
        List<TagDO> list = tagsRepository.list(Wrappers.lambdaQuery(TagDO.class).in(TagDO::getId, tagIds));

        // 提取查询结果中的ID集合
        Set<Long> existIds = list.stream().map(TagDO::getId).collect(Collectors.toSet());

        // 检查传入的ID是否全部存在于数据库
        if(tagIds.size() != existIds.size()) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TAG_ID_NOT_EXIST);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlugin(String pluginId) {
        pluginRepository.remove(
                Wrappers.lambdaQuery(PluginDO.class)
                        .eq(PluginDO::getPluginId, pluginId));

        pluginScriptRepository.remove(
                Wrappers.lambdaQuery(PluginScriptDO.class)
                        .eq(PluginScriptDO::getPluginId, pluginId));

        pluginTagsRepository.remove(
                Wrappers.lambdaQuery(PluginTagsDO.class)
                        .eq(PluginTagsDO::getPluginId, pluginId));

        scriptResultRepository.remove(
                Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                        .eq(PluginScriptOutputFieldDO::getPluginId, pluginId));

        scriptParameterRepository.remove(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .eq(PluginScriptParameterDO::getPluginId, pluginId));
    }

    @Override
    public InspectionPlugin getPluginDetail(String pluginId) {
        PluginDO pluginDO = pluginRepository.getOne(
                Wrappers.lambdaQuery(PluginDO.class)
                        .eq(PluginDO::getPluginId, pluginId));
        if (pluginDO == null) {
            return null;
        }

        PluginScriptDO pluginScriptDO = pluginScriptRepository.getOne(
                Wrappers.lambdaQuery(PluginScriptDO.class)
                        .eq(PluginScriptDO::getPluginId, pluginId));

        List<PluginScriptOutputFieldDO> pluginScriptOutputFieldDO = scriptResultRepository.list(
                Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                        .eq(PluginScriptOutputFieldDO::getPluginId, pluginId));

        List<PluginScriptParameterDO> pluginScriptParameterDO = scriptParameterRepository.list(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .eq(PluginScriptParameterDO::getPluginId, pluginId));

        List<PluginTagsDO> pluginTagsDO = pluginTagsRepository.list(
                Wrappers.lambdaQuery(PluginTagsDO.class)
                        .eq(PluginTagsDO::getPluginId, pluginId));

        // 返回前端解密
        if(JudgeUtils.isNotEmpty(pluginScriptParameterDO)){
            pluginScriptParameterDO = pluginScriptParameterDO.stream().peek(scriptParameterDO -> {
                if(scriptParameterDO.getIsEncrypted()){
                    scriptParameterDO.setParamValue(inspectionCipherService.dataDecrypt(scriptParameterDO.getParamValue()));
                }
            }).collect(Collectors.toList());
        }

        InspectionPlugin inspectionPlugin = inspectionPluginMapper.toInspectionPlugin(pluginDO, pluginScriptDO, pluginScriptOutputFieldDO, pluginScriptParameterDO);
        inspectionPlugin.setTagIds(pluginTagsDO.stream().map(PluginTagsDO::getTagId).collect(Collectors.toList()));
        return inspectionPlugin;
    }

    @Override
    public IPage<InspectionPlugin> getPluginPage(IPage<?> page, InspectionPlugin inspectionPlugin) {
        if (page == null) {
            page = new PageDTO<>();
            page.setCurrent(1);
            page.setSize(10);
        }
        PluginDO pluginDO = inspectionPluginMapper.toInspectionPluginDO(inspectionPlugin);
        Page<PluginDO> queryPage = new Page<>(page.getCurrent(), page.getSize());

        LambdaQueryWrapper<PluginDO> queryWrapper = Wrappers.lambdaQuery(PluginDO.class)
                .like(StringUtils.isNotBlank(pluginDO.getName()), PluginDO::getName, pluginDO.getName())
                .eq(pluginDO.getType() != null, PluginDO::getType, pluginDO.getType())
                .eq(pluginDO.getStatus() != null, PluginDO::getStatus, pluginDO.getStatus())
                .between(inspectionPlugin.getStartTime() != null && inspectionPlugin.getEndTime() != null,
                        PluginDO::getCreatedTime, inspectionPlugin.getStartTime(), inspectionPlugin.getEndTime())
                .orderByAsc(PluginDO::getId);
        if(JudgeUtils.isNotEmpty(inspectionPlugin.getTagIds())){
            LambdaQueryWrapper<PluginTagsDO> queryTagWrapper = Wrappers.lambdaQuery(PluginTagsDO.class)
                    .select(PluginTagsDO::getPluginId)
                    .in(PluginTagsDO::getTagId, inspectionPlugin.getTagIds());
            List<PluginTagsDO> list = pluginTagsRepository.list(queryTagWrapper);
            if(JudgeUtils.isEmpty(list)){
                return new Page<>() ;
            }
            Set<String> pluginIdSet = list.stream().map(PluginTagsDO::getPluginId).collect(Collectors.toSet());
            queryWrapper.in(PluginDO::getPluginId,pluginIdSet);
        }
        IPage<PluginDO> pluginPage = pluginRepository.page(queryPage, queryWrapper);
        IPage<InspectionPlugin> pluginIPage = pluginPage.convert(inspectionPluginMapper::toInspectionPlugin);

        if (!pluginIPage.getRecords().isEmpty()) {
            enrichInspectionPlugins(pluginIPage.getRecords());
        }
        return pluginIPage;
    }


    private void enrichInspectionPlugins(List<InspectionPlugin> records) {
        List<String> plugins = records.stream()
                .map(InspectionPlugin::getPluginId)
                .collect(Collectors.toList());

        // 添加标签
        List<PluginTagsDO> tagsDOS = pluginTagsRepository.list(
                Wrappers.lambdaQuery(PluginTagsDO.class).in(PluginTagsDO::getPluginId, plugins));

        // 构建关联数据映射
        Map<String, List<PluginTagsDO>> tagsMap = tagsDOS.stream()
                .collect(Collectors.groupingBy(PluginTagsDO::getPluginId));

        records.forEach(record -> {
            String pluginId = record.getPluginId();

            // 设置标签ID列表
            List<Long> tagIds = Optional.ofNullable(tagsMap.get(pluginId))
                    .map(list -> list.stream().map(PluginTagsDO::getTagId).collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            record.setTagIds(tagIds);

        });
    }

}
