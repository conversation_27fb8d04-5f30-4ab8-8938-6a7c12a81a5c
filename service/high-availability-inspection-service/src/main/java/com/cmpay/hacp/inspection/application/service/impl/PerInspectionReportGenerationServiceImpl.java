package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.assembler.ReportMapper;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportGenerationService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PerInspectionReportDetailRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PerInspectionReportRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskExecutionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 按次巡检报告服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerInspectionReportGenerationServiceImpl implements PerInspectionReportGenerationService {

    private final TaskExecutionRepository taskExecutionRepository;
    private final RuleExecutionRepository ruleExecutionRepository;
    private final PerInspectionReportRepository perInspectionReportRepository;
    private final PerInspectionReportDetailRepository perInspectionReportDetailRepository;
    private final ReportMapper reportMapper;

    @Override
    public PerInspectionReportDO generatePerInspectionReport(String executionId) {
        log.info("Generating per-inspection report for task: {}", executionId);

        // 1. 获取任务执行记录
        TaskExecutionDO taskExecution = taskExecutionRepository.getOne(
                Wrappers.lambdaQuery(TaskExecutionDO.class)
                        .eq(TaskExecutionDO::getExecutionId, executionId)
        );

        if (taskExecution == null) {
            log.warn("Task execution not found for taskId: {}", executionId);
            return null;
        }

        // 2. 获取规则执行记录
        List<RuleExecutionDO> ruleExecutions = ruleExecutionRepository.list(
                Wrappers.lambdaQuery(RuleExecutionDO.class)
                        .eq(RuleExecutionDO::getTaskExecutionId, taskExecution.getExecutionId())
                        .orderBy(true, true, RuleExecutionDO::getExecutionTime)
        );

        // 3. 检查是否已存在报告
        PerInspectionReportDO existingReport = getReportByTaskExecutionId(taskExecution.getExecutionId());
        if (existingReport != null) {
            log.info("Report already exists for task: {}, reportId: {}", executionId, existingReport.getReportId());
            return existingReport;
        }

        // 4. 使用MapStruct进行基础映射
        PerInspectionReportDO report = reportMapper.toReportDO(taskExecution, ruleExecutions);

        // 5. 生成报告ID
        report.setReportId(generateReportId(taskExecution.getExecutionTime()));

        // 6. 构建详细的报告内容
        PerInspectionReportDetailDO detail = buildDetailedReportContent(taskExecution, ruleExecutions);
        detail.setReportId(report.getReportId());

        // 7. 保存报告
        try {
            perInspectionReportRepository.save(report);
            perInspectionReportDetailRepository.save(detail);
            log.info("Per-inspection report saved successfully for task: {}, reportId: {}",
                    executionId, report.getReportId());
            return report;
        } catch (Exception e) {
            log.error("Failed to save per-inspection report for task: {}", executionId, e);
            throw new RuntimeException("Failed to save inspection report", e);
        }
    }

    public PerInspectionReportDO getReportByTaskExecutionId(String taskExecutionId) {
        return perInspectionReportRepository.getOne(
                Wrappers.lambdaQuery(PerInspectionReportDO.class)
                        .eq(PerInspectionReportDO::getTaskExecutionId, taskExecutionId)
        );
    }

    /**
     * 生成报告ID
     * 格式：RPT-YYYYMMDDHHMMSS-序号
     */
    private String generateReportId(LocalDateTime executionTime) {
        String timestamp = executionTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String sequence = String.format("%03d", new Random().nextInt(1000));
        return "RPT-" + timestamp + "-" + sequence;
    }

    /**
     * 构建详细的报告内容
     */
    private PerInspectionReportDetailDO buildDetailedReportContent(TaskExecutionDO taskExecution, List<RuleExecutionDO> ruleExecutions) {
        // 1. 构建执行概况
        PerInspectionReportDetailDO.ExecutionSummary executionSummary = buildExecutionSummary(taskExecution, ruleExecutions);

        // 2. 构建执行结果分布
        PerInspectionReportDetailDO.ExecutionDistribution executionDistribution = buildExecutionDistribution(ruleExecutions);

        // 3. 构建执行时间分布
        PerInspectionReportDetailDO.ExecutionTimeDistribution timeDistribution = buildTimeDistribution(ruleExecutions);

        // 4. 构建规则检查详情
        List<PerInspectionReportDetailDO.RuleCheckDetail> ruleCheckDetails = buildRuleCheckDetails(ruleExecutions);

        // 5. 构建异常详情
        List<PerInspectionReportDetailDO.ExceptionDetail> exceptionDetails = buildExceptionDetails(ruleExecutions);

        return PerInspectionReportDetailDO.builder()
                .executionSummary(executionSummary)
                .executionDistribution(executionDistribution)
                .timeDistribution(timeDistribution)
                .ruleCheckDetails(ruleCheckDetails)
                .exceptionDetails(exceptionDetails)
                .build();
    }

    /**
     * 构建执行概况
     */
    private PerInspectionReportDetailDO.ExecutionSummary buildExecutionSummary(TaskExecutionDO taskExecution, List<RuleExecutionDO> ruleExecutions) {
        int totalRules = ruleExecutions.size();
        int successRules = (int) ruleExecutions.stream()
                .filter(rule -> "1".equals(rule.getExecutionStatus()))
                .count();
        int failedRules = totalRules - successRules;

        Long duration = taskExecution.getDuration() != null ? taskExecution.getDuration().longValue() : 0L;
        String durationStr = formatDurationString(duration);

        return PerInspectionReportDetailDO.ExecutionSummary.builder()
                .startTime(taskExecution.getStartTime())
                .endTime(taskExecution.getEndTime())
                .duration(duration)
                .durationStr(durationStr)
                .totalRules(totalRules)
                .successRules(successRules)
                .build();
    }

    /**
     * 构建执行结果分布
     */
    private PerInspectionReportDetailDO.ExecutionDistribution buildExecutionDistribution(List<RuleExecutionDO> ruleExecutions) {
        int total = ruleExecutions.size();
        if (total == 0) {
            return PerInspectionReportDetailDO.ExecutionDistribution.builder()
                    .total(0)
                    .successRate(0)
                    .statusCounts(new ArrayList<>())
                    .build();
        }

        int successCount = (int) ruleExecutions.stream()
                .filter(rule -> "1".equals(rule.getExecutionStatus()))
                .count();
        int failedCount = total - successCount;
        int successRate = (successCount * 100) / total;

        List<PerInspectionReportDetailDO.ExecutionDistribution.StatusCount> statusCounts = Arrays.asList(
                PerInspectionReportDetailDO.ExecutionDistribution.StatusCount.builder()
                        .status("通过")
                        .statusCode("passed")
                        .count(successCount)
                        .build(),
                PerInspectionReportDetailDO.ExecutionDistribution.StatusCount.builder()
                        .status("失败")
                        .statusCode("failed")
                        .count(failedCount)
                        .build()
        );

        return PerInspectionReportDetailDO.ExecutionDistribution.builder()
                .total(total)
                .successRate(successRate)
                .statusCounts(statusCounts)
                .build();
    }

    /**
     * 构建执行时间分布
     */
    private PerInspectionReportDetailDO.ExecutionTimeDistribution buildTimeDistribution(List<RuleExecutionDO> ruleExecutions) {
        Map<String, Integer> timeRangeCounts = new HashMap<>();
        timeRangeCounts.put("<1秒", 0);
        timeRangeCounts.put("1-3秒", 0);
        timeRangeCounts.put("3-5秒", 0);
        timeRangeCounts.put("5-10秒", 0);
        timeRangeCounts.put(">10秒", 0);

        for (RuleExecutionDO rule : ruleExecutions) {
            Long duration = rule.getExecutionDuration();
            if (duration == null) continue;

            double seconds = duration / 1000.0;
            String range;
            if (seconds < 1) {
                range = "<1秒";
            } else if (seconds < 3) {
                range = "1-3秒";
            } else if (seconds < 5) {
                range = "3-5秒";
            } else if (seconds < 10) {
                range = "5-10秒";
            } else {
                range = ">10秒";
            }
            timeRangeCounts.put(range, timeRangeCounts.get(range) + 1);
        }

        List<PerInspectionReportDetailDO.ExecutionTimeDistribution.TimeRange> timeRanges = timeRangeCounts.entrySet().stream()
                .map(entry -> PerInspectionReportDetailDO.ExecutionTimeDistribution.TimeRange.builder()
                        .label(entry.getKey())
                        .count(entry.getValue())
                        .build())
                .collect(Collectors.toList());

        return PerInspectionReportDetailDO.ExecutionTimeDistribution.builder()
                .timeRanges(timeRanges)
                .build();
    }

    /**
     * 构建规则检查详情
     */
    private List<PerInspectionReportDetailDO.RuleCheckDetail> buildRuleCheckDetails(List<RuleExecutionDO> ruleExecutions) {
        return ruleExecutions.stream()
                .map(this::buildRuleCheckDetail)
                .collect(Collectors.toList());
    }

    /**
     * 构建单个规则检查详情
     */
    private PerInspectionReportDetailDO.RuleCheckDetail buildRuleCheckDetail(RuleExecutionDO ruleExecution) {
        String status = "1".equals(ruleExecution.getExecutionStatus()) ? "passed" : "failed";

        return PerInspectionReportDetailDO.RuleCheckDetail.builder()
                .ruleId(ruleExecution.getRuleId())
                .ruleName(ruleExecution.getRuleId()) // 可以后续从规则表获取真实名称
                .resourceName(StringUtils.hasText(ruleExecution.getResourceName()) ?
                        ruleExecution.getResourceName() : ruleExecution.getResourceId())
                .resourceType("HOST") // 可以后续从资源表获取真实类型
                .status(status)
                .checkResult(ruleExecution.getExecutionResult())
                .detailInfo(ruleExecution.getExecutionResult())
                .build();
    }

    /**
     * 构建异常详情
     */
    private List<PerInspectionReportDetailDO.ExceptionDetail> buildExceptionDetails(List<RuleExecutionDO> ruleExecutions) {
        return ruleExecutions.stream()
                .filter(rule -> !"1".equals(rule.getExecutionStatus()))
                .map(this::buildExceptionDetail)
                .collect(Collectors.toList());
    }

    /**
     * 构建单个异常详情
     */
    private PerInspectionReportDetailDO.ExceptionDetail buildExceptionDetail(RuleExecutionDO ruleExecution) {
        return PerInspectionReportDetailDO.ExceptionDetail.builder()
                .ruleId(ruleExecution.getRuleId())
                .ruleName(ruleExecution.getRuleId()) // 可以后续从规则表获取真实名称
                .description(ruleExecution.getExecutionResult())
                .resourceName(ruleExecution.getResourceName())
                .suggestion("请检查相关配置和网络连接") // 可以后续根据具体错误类型提供建议
                .build();
    }

    /**
     * 格式化持续时间字符串
     */
    private String formatDurationString(Long durationSeconds) {
        if (durationSeconds == null || durationSeconds == 0) {
            return "0秒";
        }

        if (durationSeconds < 60) {
            return durationSeconds + "秒";
        } else if (durationSeconds < 3600) {
            long minutes = durationSeconds / 60;
            long seconds = durationSeconds % 60;
            return minutes + "分" + (seconds > 0 ? seconds + "秒" : "");
        } else {
            long hours = durationSeconds / 3600;
            long minutes = (durationSeconds % 3600) / 60;
            long seconds = durationSeconds % 60;
            StringBuilder sb = new StringBuilder();
            sb.append(hours).append("小时");
            if (minutes > 0) sb.append(minutes).append("分");
            if (seconds > 0) sb.append(seconds).append("秒");
            return sb.toString();
        }
    }
}
