package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;

public interface InspectionTaskService {
    String createTask(InspectionTask inspectionTask);

    boolean updateTask(InspectionTask inspectionTask);

    void deleteTask(String taskId);

    InspectionTask getTaskDetail(String taskId);

    IPage<InspectionTask> getTaskPage(IPage<?> page, InspectionTask inspectionTask);

    // 立即执行一次
    void executeTaskNow(String taskId);
}
