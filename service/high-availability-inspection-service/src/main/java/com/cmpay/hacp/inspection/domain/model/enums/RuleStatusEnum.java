package com.cmpay.hacp.inspection.domain.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RuleStatusEnum {

    ENABLED(0, "启用"),
    DISABLED(1, "禁用");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, RuleStatusEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleStatusEnum::getCode, type -> type));


    RuleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RuleStatusEnum getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
}
