package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.ApiLocationApiTagBO;
import com.cmpay.hacp.dispatch.bo.ApiLocationBO;
import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.hacp.dispatch.dao.IApiLocationExtDao;
import com.cmpay.hacp.dispatch.entity.ApiLocationDO;
import com.cmpay.hacp.dispatch.service.ApiLocationApiTagService;
import com.cmpay.hacp.dispatch.service.ApiLocationService;
import com.cmpay.hacp.dispatch.service.DispatchService;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ApiLocationServiceImpl implements ApiLocationService {


    @Autowired
    private IApiLocationExtDao apiLocationDao;
    @Autowired
    private ApiLocationApiTagService apiLocationApiTagService;

    @Autowired
    DispatchService dispatchService;
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void addApiLocation(ApiLocationBO apiLocationBO) {
        apiLocationBO.check();
        apiLocationBO.convertUriMatchToJson();

        existFind(apiLocationBO);
        apiLocationDao.insert(apiLocationBO);
        if(StringUtils.isBlank(apiLocationBO.getApiLocationName())){
            apiLocationBO.setApiLocationName("API_"+ apiLocationBO.getApiLocationId());
            apiLocationDao.update(apiLocationBO);
        }

        if(apiLocationBO.getTagIds() != null){
            for (Integer tagId : apiLocationBO.getTagIds()) {
                ApiLocationApiTagBO apiLocationApiTagBO = new ApiLocationApiTagBO();
                apiLocationApiTagBO.setWorkspaceId(apiLocationBO.getWorkspaceId());
                TenantSecurityUtils.copyOperator(apiLocationApiTagBO);
                apiLocationApiTagBO.setApiLocationId(apiLocationBO.getApiLocationId());
                apiLocationApiTagBO.setApiTagId(tagId);
                apiLocationApiTagService.addApiLocationApiTag(apiLocationApiTagBO);
            }
        }

    }

    private void existFind(ApiLocationBO apiLocationBO) {
        ApiLocationBO findBO = new ApiLocationBO();
        findBO.setApiLocationId(apiLocationBO.getApiLocationId());
        findBO.setWorkspaceId(apiLocationBO.getWorkspaceId());
        findBO.setApiLocationName(apiLocationBO.getApiLocationName());
        if(StringUtils.isNotBlank(apiLocationBO.getApiLocationName())
                && JudgeUtils.isNotEmpty(apiLocationDao.existFind(findBO))){
            BusinessException.throwBusinessException(DispatchMsgEnum.API_NAME_EXISTS);
        }
    }

    @Override
    public void deleteApiLocation(ApiLocationBO apiLocationBO) {

        ApiLocationDO apiLocationDO = apiLocationDao.get(apiLocationBO.getApiLocationId());

        if(apiLocationDO.getApiLocationId() != null && apiLocationDO.getApiLocationId() > 0) {
            DispatchBO dispatchBO = new DispatchBO();
            dispatchBO.setWorkspaceId(apiLocationBO.getWorkspaceId());
            dispatchBO.setStatus(ByteStatusEnum.ENABLE.getValue());
            dispatchBO.setApiLocationId(apiLocationDO.getApiLocationId().intValue() + "");
            if(dispatchService.existBind(dispatchBO)){
                BusinessException.throwBusinessException(DispatchMsgEnum.EXIST_API_LOCATION_BIND_GROUP);
            }
        }
        ApiLocationApiTagBO apiLocationApiTagBO = new ApiLocationApiTagBO();
        apiLocationApiTagBO.setWorkspaceId(apiLocationBO.getWorkspaceId());
        apiLocationApiTagBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        apiLocationApiTagBO.setApiLocationId(apiLocationBO.getApiLocationId());
        List<ApiLocationApiTagBO> apiLocationApiTagList = apiLocationApiTagService.getApiLocationApiTagList(apiLocationApiTagBO);
        if(JudgeUtils.isNotEmpty(apiLocationApiTagList)) {
            for (ApiLocationApiTagBO locationApiTagBO : apiLocationApiTagList) {
                DispatchBO dispatchBO = new DispatchBO();
                dispatchBO.setWorkspaceId(apiLocationBO.getWorkspaceId());
                dispatchBO.setStatus(ByteStatusEnum.ENABLE.getValue());
                dispatchBO.setApiLocationId(locationApiTagBO.getApiLocationId().intValue() + "");
                if(dispatchService.existBind(dispatchBO)){
                    BusinessException.throwBusinessException(DispatchMsgEnum.EXIST_API_LOCATION_BIND_GROUP);
                }
            }
        }

        apiLocationDao.delete(apiLocationBO.getApiLocationId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void updateApiLocation(ApiLocationBO apiLocationBO) {
        apiLocationBO.check();
        apiLocationBO.convertUriMatchToJson();
        existFind(apiLocationBO);
        apiLocationApiTagService.deleteByApiLocationId(apiLocationBO.getApiLocationId(),apiLocationBO.getWorkspaceId());
        apiLocationDao.update(apiLocationBO);
        if(apiLocationBO.getTagIds() != null){
            for (Integer tagId : apiLocationBO.getTagIds()) {
                ApiLocationApiTagBO apiLocationApiTagBO = new ApiLocationApiTagBO();
                apiLocationApiTagBO.setWorkspaceId(apiLocationBO.getWorkspaceId());
                TenantSecurityUtils.copyOperator(apiLocationApiTagBO);
                apiLocationApiTagBO.setApiLocationId(apiLocationBO.getApiLocationId());
                apiLocationApiTagBO.setApiTagId(tagId);
                apiLocationApiTagService.addApiLocationApiTag(apiLocationApiTagBO);
            }
        }
    }

    public boolean existApiLocationBind(ApiLocationBO apiLocationBO){
        PageInfo<ApiLocationBO> apiLocationPage = getApiLocationPage(1, 2, apiLocationBO);
        return apiLocationPage.hasContent();
    }

    @Override
    public PageInfo<ApiLocationBO> getApiLocationPage(int pageNum, int pageSize, ApiLocationBO apiLocationBO) {
        PageInfo<ApiLocationBO> pageInfo = PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> apiLocationDao.likeFind(apiLocationBO));
        if(pageInfo.getList() != null) {
            pageInfo.getList().stream().forEach(x -> x.convertUriMatchToList());
        }
        return pageInfo;
    }

    @Override
    public List<ApiLocationBO> getApiLocationList(String workspaceId) {
        ApiLocationDO entity = new ApiLocationDO();
        entity.setWorkspaceId(workspaceId);
        entity.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<ApiLocationDO> apiLocationDOS = apiLocationDao.find(entity);
        if(JudgeUtils.isEmpty(apiLocationDOS)){
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(apiLocationDOS, ApiLocationBO.class);
    }

    @Override
    public ApiLocationBO getInfo(Integer apiLocationId, String workspaceId) {
        ApiLocationDO entity = new ApiLocationDO();
        entity.setWorkspaceId(workspaceId);
        entity.setApiLocationId(apiLocationId);
        List<ApiLocationDO> apiLocationDOS = apiLocationDao.find(entity);
        if(JudgeUtils.isEmpty(apiLocationDOS)){
            BusinessException.throwBusinessException(MsgEnum.DB_SELECT_FAILED);
        }

        ApiLocationBO apiLocationBO = BeanUtils.copyPropertiesReturnDest(new ApiLocationBO(), apiLocationDao.get(apiLocationId));
        apiLocationBO.convertUriMatchToList();
        ApiLocationApiTagBO apiLocationApiTagBO = new ApiLocationApiTagBO();
        apiLocationApiTagBO.setApiLocationId(apiLocationId);
        apiLocationApiTagBO.setWorkspaceId(workspaceId);
        List<ApiLocationApiTagBO> apiLocationApiTagList = apiLocationApiTagService.getApiLocationApiTagList(apiLocationApiTagBO);
        if(JudgeUtils.isNotEmpty(apiLocationApiTagList)){
            List<Integer> tagList = apiLocationApiTagList.stream()
                    .map(ApiLocationApiTagBO::getApiTagId)
                    .collect(Collectors.toList());
            apiLocationBO.setTagIds(tagList);
        }
        return apiLocationBO;
    }
}
