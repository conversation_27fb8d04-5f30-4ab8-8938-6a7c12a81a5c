package com.cmpay.hacp.dispatch.service.camunda;

import com.alibaba.fastjson2.JSON;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.task.DispatchParamBO;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.dispatch.bo.GenerateDispatchConfigBO;
import com.cmpay.hacp.dispatch.service.DispatchConfigService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.StatusEnum;
import com.cmpay.hacp.service.EmergencyProcessService;
import com.cmpay.hacp.service.HacpCaseService;
import com.cmpay.hacp.service.HacpNodeRecodeService;
import com.cmpay.hacp.service.camunda.ServiceTaskTemplateDelegate;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 应急调度或者调度回滚
 * @date 2024/5/15 9:56
 */
@Service("doDispatch")
@Slf4j
public class DispatchDelegateService extends ServiceTaskTemplateDelegate {

    private final DispatchConfigService dispatchConfigService;

    public DispatchDelegateService(HacpCaseService caseService,
            HacpNodeRecodeService nodeRecodeService,
            RuntimeService runtimeService,
            SystemCacheService cacheService,
            EmergencyProcessService emergencyProcessService,
            DispatchConfigService dispatchConfigService) {
        super(caseService, nodeRecodeService, runtimeService, cacheService, emergencyProcessService);
        this.dispatchConfigService = dispatchConfigService;
    }

    @Override
    protected void processTask(){
        DelegateExecution delegateExecution = execution.get();
        HacpEmergencyTaskBO hacpEmergencyTaskBO = taskInfoHolder.get();

        if (JudgeUtils.isNull(hacpEmergencyTaskBO)) {
            BusinessException.throwBusinessException(MsgEnum.TASK_NOT_EXIST);
        }
        // 获取调度ID 可能会是一个JSON格式，则需要进行类型转换
        String taskParam = hacpEmergencyTaskBO.getTaskParam();
        DispatchParamBO dispatchParamBO = JSON.parseObject(taskParam, DispatchParamBO.class);
        Object dispatchId = JSON.parseObject(taskParam).get("dispatchId");
        List<String> dispatchIds = JSON.parseArray(dispatchId.toString(), String.class);
        HashSet<String> set = new HashSet<>(dispatchIds);
        GenerateDispatchConfigBO generateDispatchConfigBO = new GenerateDispatchConfigBO();
        generateDispatchConfigBO.setConfigDesc((String) delegateExecution.getVariable(EmergencyConstant.PROCESS_INSTANCE_NAME));
        generateDispatchConfigBO.setOperatorName(SecurityUtils.getLoginName());
        generateDispatchConfigBO.setOperatorId(SecurityUtils.getLoginUserId());
        generateDispatchConfigBO.setWorkspaceId(delegateExecution.getTenantId());
        if (JudgeUtils.equals(dispatchParamBO.getDispatchStatus(), StatusEnum.DISABLE.getValue())) {
            dispatchConfigService.openDispatches(generateDispatchConfigBO, set);
        } else {
            dispatchConfigService.closeDispatches(generateDispatchConfigBO, set);
        }

        //CamundaUtil.setExecuted(execution, cacheService, CamundaUtil.EXECUTE_SUCCESS);
    }
}
