package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.DashboardDispatchBO;
import com.cmpay.hacp.dispatch.bo.DispatchNodeBO;
import com.cmpay.hacp.dispatch.bo.DispatchPushHistoryBO;
import com.cmpay.hacp.dispatch.dao.IDispatchConfigExtDao;
import com.cmpay.hacp.dispatch.dao.IDispatchNodeExtDao;
import com.cmpay.hacp.dispatch.dao.IDispatchPushHistoryExtDao;
import com.cmpay.hacp.dispatch.entity.DispatchConfigDO;
import com.cmpay.hacp.dispatch.entity.DispatchNodeDO;
import com.cmpay.hacp.dispatch.service.DispatchNodeService;
import com.cmpay.hacp.dispatch.service.DispatchZoneBaseService;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.tenant.service.WorkspaceBaseService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DispatchNodeServiceImpl implements DispatchNodeService, DispatchZoneBaseService, WorkspaceBaseService {
    @Autowired
    private IDispatchNodeExtDao dispatchNodeDao;
    @Autowired
    private IDispatchPushHistoryExtDao dispatchPushHistoryExtDao;
    @Autowired
    private IDispatchConfigExtDao dispatchConfigExtDao;
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addDispatchNode(DispatchNodeBO dispatchNodeBO) {
        dispatchNodeBO.check();
        List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeDao.find(dispatchNodeBO);
        if(JudgeUtils.isNotEmpty(dispatchNodeDOS)){
            BusinessException.throwBusinessException(MsgEnum.DB_THE_SAME_DATA_EXISTS);
        }
        dispatchNodeDao.insert(dispatchNodeBO);
        if(StringUtils.isBlank(dispatchNodeBO.getDispatchNodeName())){
            dispatchNodeBO.setDispatchNodeName("C"+ StringUtils.leftPad(""+dispatchNodeBO.getDispatchNodeId(), 3, "0"));
            dispatchNodeDao.update(dispatchNodeBO);
        }
        DispatchConfigDO dispatchConfigBO = new DispatchConfigDO();
        dispatchConfigBO.setWorkspaceId(dispatchNodeBO.getWorkspaceId());
        dispatchConfigBO.setIsCurrentVersion(Byte.valueOf((byte)1));
        List<DispatchConfigDO> dispatchConfigDOS = dispatchConfigExtDao.find(dispatchConfigBO);
        if (dispatchConfigDOS != null && !dispatchConfigDOS.isEmpty()) {
            dispatchConfigBO = dispatchConfigDOS.get(0);
            dispatchConfigBO.setPushEndTime(null);
            dispatchConfigExtDao.pushUpdate(dispatchConfigBO);
            dispatchNodeBO.setDispatchVersion(dispatchConfigBO.getDispatchVersion());
            dispatchNodeDao.update(dispatchNodeBO);
            DispatchPushHistoryBO dispatchPushHistoryBO = BeanConvertUtil.convert(dispatchNodeBO, DispatchPushHistoryBO.class);
            dispatchPushHistoryBO.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
            dispatchPushHistoryBO.setPushTimes(dispatchConfigBO.getPushTimes());
            dispatchPushHistoryBO.setConfigDesc(dispatchConfigBO.getConfigDesc());
            dispatchPushHistoryBO.setPushStartTime(LocalDateTime.now());
            dispatchPushHistoryBO.setPushStatus(ByteStatusEnum.DISABLE.getValue());
            dispatchPushHistoryExtDao.insert(dispatchPushHistoryBO);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateDispatchNode(DispatchNodeBO dispatchNodeBO) {
        dispatchNodeBO.check();
        DispatchNodeDO entity = new DispatchNodeDO();
        entity.setIpPort(dispatchNodeBO.getIpPort());
        entity.setZoneId(dispatchNodeBO.getZoneId());
        List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeDao.find(entity).stream()
                .filter(f->!f.getDispatchNodeId().equals(dispatchNodeBO.getDispatchNodeId()))
                .collect(Collectors.toList());
        if(JudgeUtils.isNotEmpty(dispatchNodeDOS)){
            BusinessException.throwBusinessException(MsgEnum.DB_THE_SAME_DATA_EXISTS);
        }
        dispatchNodeDao.update(dispatchNodeBO);
        DispatchConfigDO dispatchConfigBO = new DispatchConfigDO();
        dispatchConfigBO.setWorkspaceId(dispatchNodeBO.getWorkspaceId());
        dispatchConfigBO.setIsCurrentVersion(Byte.valueOf((byte)1));
        List<DispatchConfigDO> dispatchConfigDOS = dispatchConfigExtDao.find(dispatchConfigBO);
        if (dispatchConfigDOS != null && !dispatchConfigDOS.isEmpty()) {
            dispatchConfigBO = dispatchConfigDOS.get(0);
            dispatchConfigBO.setPushEndTime(null);
            dispatchConfigExtDao.pushUpdate(dispatchConfigBO);
            DispatchPushHistoryBO dispatchPushHistoryBO = new DispatchPushHistoryBO();
            dispatchPushHistoryBO.setDispatchNodeId(dispatchNodeBO.getDispatchNodeId());
            dispatchPushHistoryBO.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
            dispatchPushHistoryBO.setPushTimes(dispatchConfigBO.getPushTimes());
            dispatchPushHistoryBO.setPushStatus(ByteStatusEnum.DISABLE.getValue());
            dispatchPushHistoryExtDao.update(dispatchPushHistoryBO);
        }
    }

    @Override
    public void deleteDispatchNode(DispatchNodeBO dispatchNodeBO) {
        dispatchNodeDao.delete(dispatchNodeBO.getDispatchNodeId());
        DispatchConfigDO dispatchConfigBO = new DispatchConfigDO();
        dispatchConfigBO.setWorkspaceId(dispatchNodeBO.getWorkspaceId());
        dispatchConfigBO.setIsCurrentVersion(Byte.valueOf((byte)1));
        List<DispatchConfigDO> dispatchConfigDOS = dispatchConfigExtDao.find(dispatchConfigBO);
        if (dispatchConfigDOS != null && !dispatchConfigDOS.isEmpty()) {
            dispatchConfigBO = dispatchConfigDOS.get(0);
            dispatchConfigBO.setPushEndTime(null);
            dispatchConfigExtDao.pushUpdate(dispatchConfigBO);
            DispatchPushHistoryBO dispatchPushHistoryBO = new DispatchPushHistoryBO();
            dispatchPushHistoryBO.setDispatchNodeId(dispatchNodeBO.getDispatchNodeId());
            dispatchPushHistoryBO.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
            dispatchPushHistoryBO.setPushTimes(dispatchConfigBO.getPushTimes());
            dispatchPushHistoryBO.setPushStatus(ByteStatusEnum.MIDDLE.getValue());
            dispatchPushHistoryBO.setMsgCd("Deleting");
            dispatchPushHistoryBO.setMsgInfo("删除中");
            dispatchPushHistoryExtDao.update(dispatchPushHistoryBO);
        }
    }

    @Override
    public List<DispatchNodeBO> getDispatchNodeList(String workspaceId) {
        DispatchNodeDO entity = new DispatchNodeDO();
        entity.setWorkspaceId(workspaceId);
        entity.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<DispatchNodeBO> dispatchNodeBOS = BeanConvertUtil.convertList(dispatchNodeDao.find(entity), DispatchNodeBO.class);
        return dispatchNodeBOS;
    }

    @Override
    public DispatchNodeBO randGetNode(DispatchNodeBO dispatchNode) {
        return BeanConvertUtil.convert(dispatchNodeDao.randGetNode(dispatchNode), DispatchNodeBO.class);
    }

    @Override
    public PageInfo<DispatchNodeBO> getDispatchNodePage(int pageNum, int pageSize, DispatchNodeBO dispatchNodeBO) {
        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> BeanConvertUtil.convertList(dispatchNodeDao.likeFind(dispatchNodeBO), DispatchNodeBO.class));
    }

    @Override
    public List<DashboardDispatchBO> getZoneDispatchList(String workspaceId) {
        return dispatchNodeDao.getZoneDispatchList(workspaceId);
    }

    @Override
    public List<String> getRunningVersionList(String workspaceId) {
        DispatchNodeDO entity = new DispatchNodeDO();
        entity.setWorkspaceId(workspaceId);
        entity.setStatus(ByteStatusEnum.ENABLE.getValue());
        return dispatchNodeDao.getRunningVersionList(entity);
    }


    @Override
    public Boolean existUseZoneByZoneId(Long zoneId) {
        DispatchNodeDO entity = new DispatchNodeDO();
        entity.setZoneId(zoneId);
        List<DispatchNodeDO> list = dispatchNodeDao.find(entity);
        return JudgeUtils.isNotEmpty(list);
    }

    @Override
    public boolean whetherOrNotToAllowItemsToBeDeleted(String workspaceId) {
        DispatchNodeDO entity = new DispatchNodeDO();
        entity.setWorkspaceId(workspaceId);
        return JudgeUtils.isEmpty( dispatchNodeDao.find(entity));
    }
}
