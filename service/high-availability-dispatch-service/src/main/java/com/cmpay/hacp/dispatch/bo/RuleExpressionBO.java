package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.RuleExpressionDO;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
public class RuleExpressionBO extends RuleExpressionDO implements TenantCapable {

    private List<TypeValueExt> zoneWeightList;

    /*private List<TypeValue> children;*/

    /**
     * @Fields dispatchRuleType 分流类型中文名
     */
    private String dispatchRuleTypeName;

    /**
     * @Fields calcOperator 计算操作符中文名
     */
    private String calcOperatorName;

    public void convertZoneWeightListToJson(){
        this.setZoneWeights(TypeValueUtils.toString(zoneWeightList.stream().filter(v->!"0".equals(v.getValue())).collect(Collectors.toList())));
    }

    public void convertZoneWeightsToList(){
        this.setZoneWeightList(TypeValueUtils.toExtList(this.getZoneWeights()));
        this.setZoneWeights(null);
    }

    public Map<String, Integer> convertZoneWeightsToMap(){

        if(JudgeUtils.isEmpty(this.getZoneWeightList())) {
            convertZoneWeightsToList();
        }

        if(JudgeUtils.isNotEmpty(this.getZoneWeightList())) {
            Map<String, Integer> result = new HashMap<>();
            for (TypeValue typeValue : this.getZoneWeightList()) {
                result.put(typeValue.getTypeLabel(), Integer.valueOf(typeValue.getValue()));
            }
            return result;
        }
        return null;
    }

}
