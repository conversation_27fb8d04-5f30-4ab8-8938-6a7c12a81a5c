package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.ApiLocationBO;
import com.cmpay.hacp.dispatch.bo.AppInstanceGroupBO;
import com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO;
import com.cmpay.hacp.dispatch.bo.AppInstanceZoneBO;
import com.cmpay.hacp.dispatch.dao.IAppInstanceGroupExtDao;
import com.cmpay.hacp.dispatch.dao.IAppInstanceMachineExtDao;
import com.cmpay.hacp.dispatch.dao.IAppInstanceZoneExtDao;
import com.cmpay.hacp.dispatch.entity.AppInstanceGroupDO;
import com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO;
import com.cmpay.hacp.dispatch.entity.AppInstanceZoneDO;
import com.cmpay.hacp.dispatch.service.ApiLocationService;
import com.cmpay.hacp.dispatch.service.AppInstanceGroupService;
import com.cmpay.hacp.dispatch.service.AppInstanceZoneService;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


@Service
public class AppInstanceGroupServiceImpl implements AppInstanceGroupService {


    @Autowired
    private IAppInstanceGroupExtDao appInstanceGroupDao;
    @Autowired
    private IAppInstanceZoneExtDao appInstanceZoneDao;
    @Autowired
    private IAppInstanceMachineExtDao appInstanceMachineDao;
    @Autowired
    private AppInstanceZoneService appInstanceZoneService;
    @Autowired
    private ApiLocationService apiLocationService;
    @Override
    @Transactional
    public void addAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO) {
        appInstanceGroupBO.check();
        if(StringUtils.isNotBlank(appInstanceGroupBO.getInstanceGroupName())){
            existFind(appInstanceGroupBO);
        }

        if(appInstanceGroupBO.getInstanceGroupId() == null || appInstanceGroupBO.getInstanceGroupId() < 1) {
            appInstanceGroupDao.insert(appInstanceGroupBO);
            if(StringUtils.isBlank(appInstanceGroupBO.getInstanceGroupName())) {
                appInstanceGroupBO.setInstanceGroupName("G"+appInstanceGroupBO.getInstanceGroupId());
                appInstanceGroupDao.update(appInstanceGroupBO);
            }
        }

        List<AppInstanceZoneBO> zoneAppInstances = appInstanceGroupBO.getZoneAppInstances();
        if(zoneAppInstances != null) {
            for (AppInstanceZoneBO zoneAppInstance : zoneAppInstances) {
                zoneAppInstance.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
                zoneAppInstance.setOperatorId(appInstanceGroupBO.getOperatorId());
                zoneAppInstance.setOperatorName(appInstanceGroupBO.getOperatorName());
                TenantSecurityUtils.copyTenantSecurity(zoneAppInstance);
                zoneAppInstance.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
                appInstanceZoneService.addAppInstanceZone(zoneAppInstance);
            }
        }
    }

    @Override
    @Transactional
    public void deleteAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO) {
        ApiLocationBO apiLocationBO = new ApiLocationBO();
        apiLocationBO.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
        apiLocationBO.setAppInstanceGroup(appInstanceGroupBO.getInstanceGroupId());
        apiLocationBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        if(apiLocationService.existApiLocationBind(apiLocationBO)){
            BusinessException.throwBusinessException(DispatchMsgEnum.EXIST_API_LOCATION_BIND_GROUP);
        }
        appInstanceGroupDao.delete(appInstanceGroupBO.getInstanceGroupId());
        AppInstanceZoneBO findZone = new AppInstanceZoneBO();
        findZone.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
        findZone.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
        List<AppInstanceZoneBO> appInstanceZoneList = appInstanceZoneService.getAppInstanceZoneList(findZone);
        if(appInstanceZoneList != null) {
            for (AppInstanceZoneBO appInstanceZoneBO : appInstanceZoneList) {
                appInstanceZoneService.deleteAppInstanceZone(appInstanceZoneBO);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO) {
        appInstanceGroupBO.check();
        existFind(appInstanceGroupBO);
        appInstanceGroupDao.update(appInstanceGroupBO);
        List<Integer> deleteZones = appInstanceGroupBO.getDeleteZones();
        if(deleteZones != null) {
            for (Integer deleteZoneId : deleteZones) {
                if(deleteZoneId != null && deleteZoneId > 0) {
                    AppInstanceZoneBO deleteZoneBO = new AppInstanceZoneBO();
                    deleteZoneBO.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
                    deleteZoneBO.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
                    deleteZoneBO.setInstanceZoneId(deleteZoneId);
                    appInstanceZoneService.deleteAppInstanceZone(deleteZoneBO);
                }
            }
        }
        List<AppInstanceZoneBO> zoneAppInstances = appInstanceGroupBO.getZoneAppInstances();
        if(zoneAppInstances != null) {
            for (AppInstanceZoneBO zoneAppInstance : zoneAppInstances) {
                TenantSecurityUtils.copyOperator(zoneAppInstance);
                zoneAppInstance.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
                zoneAppInstance.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
                zoneAppInstance.setOperatorId(appInstanceGroupBO.getOperatorId());
                zoneAppInstance.setOperatorName(appInstanceGroupBO.getOperatorName());
                if(zoneAppInstance.getInstanceZoneId() == null || zoneAppInstance.getInstanceZoneId() < 1) {
                    appInstanceZoneService.addAppInstanceZone(zoneAppInstance);
                } else {
                    appInstanceZoneService.updateAppInstanceZone(zoneAppInstance);
                }
            }
        }

    }

    public void existFind(AppInstanceGroupBO appInstanceGroupBO) {
        AppInstanceGroupDO appInstanceMachineDO = new AppInstanceGroupDO();
        appInstanceMachineDO.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
        appInstanceMachineDO.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
        appInstanceMachineDO.setInstanceGroupName(appInstanceGroupBO.getInstanceGroupName());
        List<AppInstanceGroupDO> findList = appInstanceGroupDao.existFind(appInstanceMachineDO);
        if(JudgeUtils.isNotEmpty(findList)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.GROUP_NAME_EXISTS);
        }
    }

    @Override
    public List<AppInstanceGroupBO> getAppInstanceGroupList(AppInstanceGroupBO appInstanceGroupBO) {
        return BeanConvertUtil.convertList(appInstanceGroupDao.find(appInstanceGroupBO), AppInstanceGroupBO.class);
    }

    @Override
    public AppInstanceGroupBO getAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO) {
        AppInstanceGroupDO appInstanceGroupDO =  new AppInstanceGroupDO();
        appInstanceGroupDO.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
        appInstanceGroupDO.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
        List<AppInstanceGroupDO> appInstanceGroupDOS = appInstanceGroupDao.find(appInstanceGroupDO);
        if(JudgeUtils.isEmpty(appInstanceGroupDOS)){
            BusinessException.throwBusinessException(MsgEnum.DB_SELECT_FAILED);
        }

        AppInstanceZoneDO appInstanceZone = new AppInstanceZoneDO();
        appInstanceZone.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
        appInstanceZone.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
        appInstanceZone.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<AppInstanceZoneDO> appInstanceZones = appInstanceZoneDao.find(appInstanceZone);

        AppInstanceGroupBO appInstanceGroupDetailBO = new AppInstanceGroupBO();
        appInstanceGroupDetailBO.setInstanceGroupId(appInstanceGroupDOS.get(0).getInstanceGroupId());
        appInstanceGroupDetailBO.setInstanceGroupName(appInstanceGroupDOS.get(0).getInstanceGroupName());
        appInstanceGroupDetailBO.setInstanceGroupCn(appInstanceGroupDOS.get(0).getInstanceGroupCn());
        appInstanceGroupDetailBO.setInstanceGroupDesc(appInstanceGroupDOS.get(0).getInstanceGroupDesc());
        appInstanceGroupDetailBO.setAdvancedSwitch(appInstanceGroupDOS.get(0).getAdvancedSwitch());
        Map<Long, AppInstanceZoneBO> zoneMap = new HashMap<>();
        if(appInstanceZones != null) {
            for (AppInstanceZoneDO instanceZone : appInstanceZones) {
                AppInstanceZoneBO zone = BeanConvertUtil.convert(instanceZone, AppInstanceZoneBO.class);
                zoneMap.put(zone.getZoneId(), zone);
                List<AppInstanceZoneBO> zoneAppInstances = appInstanceGroupDetailBO.getZoneAppInstances();
                if (zoneAppInstances == null) {
                    zoneAppInstances = new ArrayList<>();
                    appInstanceGroupDetailBO.setZoneAppInstances(zoneAppInstances);
                }
                zoneAppInstances.add(zone);
            }
        }

        AppInstanceMachineDO appInstanceMachine = new AppInstanceMachineDO();
        appInstanceMachine.setWorkspaceId(appInstanceGroupBO.getWorkspaceId());
        appInstanceMachine.setInstanceGroupId(appInstanceGroupBO.getInstanceGroupId());
        appInstanceMachine.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<AppInstanceMachineDO> appInstanceMachines = appInstanceMachineDao.find(appInstanceMachine);
        if(appInstanceMachines != null) {
            for (AppInstanceMachineDO instanceMachine : appInstanceMachines) {
                AppInstanceZoneBO appInstanceZoneBO = zoneMap.get(instanceMachine.getZoneId());
                if(appInstanceZoneBO != null) {
                    List<AppInstanceMachineBO> servers = appInstanceZoneBO.getServers();
                    if(servers == null) {
                        servers = new ArrayList<>();
                        appInstanceZoneBO.setServers(servers);
                    }
                    servers.add(BeanConvertUtil.convert(instanceMachine, AppInstanceMachineBO.class));
                }
            }
        }

        return appInstanceGroupDetailBO;
    }

    @Override
    public PageInfo<AppInstanceMachineBO> getInstanceGroupMachineList(int pageNum, int pageSize, AppInstanceMachineBO appInstanceMachineBO) {

        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> appInstanceGroupDao.getInstanceGroupMachineList(appInstanceMachineBO));
    }

    @Override
    public List<AppInstanceMachineBO> getInstanceGroupAllList(String workspaceId) {
        AppInstanceGroupDO entity = new AppInstanceGroupDO();
        entity.setWorkspaceId(workspaceId);
        entity.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<AppInstanceGroupDO> appInstanceGroupDOS = appInstanceGroupDao.find(entity);
        if(JudgeUtils.isEmpty(appInstanceGroupDOS)){
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(appInstanceGroupDOS,AppInstanceMachineBO.class);
    }

    public List<AppInstanceGroupBO> generateAppInstanceGroups(String workspaceId) {
        AppInstanceGroupDO appInstanceGroup = new AppInstanceGroupDO();
        appInstanceGroup.setWorkspaceId(workspaceId);
        appInstanceGroup.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<AppInstanceGroupDO> appInstanceGroupDOs = appInstanceGroupDao.find(appInstanceGroup);

        AppInstanceZoneDO appInstanceZone = new AppInstanceZoneDO();
        appInstanceZone.setWorkspaceId(workspaceId);
        appInstanceZone.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<AppInstanceZoneBO> appInstanceZones = appInstanceZoneDao.findExt(appInstanceZone);
        Map<String, Map<String, AppInstanceZoneBO>> groupZoneMap = new HashMap<>();
        for (AppInstanceZoneBO instanceZone : appInstanceZones) {
            Map<String, AppInstanceZoneBO> zoneMap = groupZoneMap.get(instanceZone.getInstanceGroupId().intValue()+"");
            if(zoneMap == null) {
                zoneMap = new HashMap<>();
                groupZoneMap.put(instanceZone.getInstanceGroupId().intValue()+"", zoneMap);
            }
            zoneMap.put(instanceZone.getZoneId().intValue()+"", BeanConvertUtil.convert(instanceZone, AppInstanceZoneBO.class));
        }

        AppInstanceMachineDO appInstanceMachine = new AppInstanceMachineDO();
        appInstanceMachine.setWorkspaceId(workspaceId);
        appInstanceMachine.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<AppInstanceMachineBO> appInstanceMachines = appInstanceMachineDao.findExt(appInstanceMachine);
        for (AppInstanceMachineBO instanceMachine : appInstanceMachines) {
            Map<String, AppInstanceZoneBO> zoneBOMap = groupZoneMap.get(instanceMachine.getInstanceGroupId().intValue()+"");
            if(zoneBOMap != null) {
                AppInstanceZoneBO appInstanceZoneBO = zoneBOMap.get(instanceMachine.getZoneId().intValue()+"");
                if(appInstanceZoneBO != null) {
                    List<AppInstanceMachineBO> servers = appInstanceZoneBO.getServers();
                    if(servers == null) {
                        servers = new ArrayList<>();
                        appInstanceZoneBO.setServers(servers);
                    }
                    servers.add(BeanConvertUtil.convert(instanceMachine, AppInstanceMachineBO.class));
                }
            }
        }

        List<AppInstanceGroupBO> appInstanceGroups = new ArrayList<>();
        for (AppInstanceGroupDO instanceGroupDO : appInstanceGroupDOs) {
            AppInstanceGroupBO groupBO = BeanConvertUtil.convert(instanceGroupDO, AppInstanceGroupBO.class);
            Map<String, AppInstanceZoneBO> zoneBOMap = groupZoneMap.get(instanceGroupDO.getInstanceGroupId().intValue()+"");
            if(zoneBOMap != null) {
                Collection<AppInstanceZoneBO> values = zoneBOMap.values();
                groupBO.setZoneAppInstances(new ArrayList<>(values));
            }
            appInstanceGroups.add(groupBO);
        }

        return appInstanceGroups;
    }
}
