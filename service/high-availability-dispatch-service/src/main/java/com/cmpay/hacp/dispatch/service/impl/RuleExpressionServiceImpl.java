package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.RuleExpressionBO;
import com.cmpay.hacp.dispatch.dao.IRuleExpressionExtDao;
import com.cmpay.hacp.dispatch.entity.RuleExpressionDO;
import com.cmpay.hacp.dispatch.service.DispatchZoneBaseService;
import com.cmpay.hacp.dispatch.service.RuleExpressionService;
import com.cmpay.hacp.utils.TenantSecurityUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RuleExpressionServiceImpl implements RuleExpressionService, DispatchZoneBaseService {

    @Autowired
    private IRuleExpressionExtDao ruleExpressionDao;
    @Override
    public void addRuleExpression(RuleExpressionBO ruleExpressionBO) {
        ruleExpressionBO.convertZoneWeightListToJson();
        ruleExpressionDao.insert(ruleExpressionBO);
    }

    @Override
    public void addRuleExpressions(int ruleId, List<RuleExpressionBO> ruleExpressionBOs) {
        if(ruleExpressionBOs != null) {
            for (RuleExpressionBO ruleExpressionBO : ruleExpressionBOs) {
                TenantSecurityUtils.copyTenantSecurity(ruleExpressionBO);
                ruleExpressionBO.setDispatchRuleId(ruleId);
                addRuleExpression(ruleExpressionBO);
            }
        }
    }

    @Override
    public void updateRuleExpression(RuleExpressionBO ruleExpressionBO) {
        ruleExpressionBO.convertZoneWeightListToJson();
        ruleExpressionDao.update(ruleExpressionBO);
    }

    @Override
    public void updateRuleExpressions(List<RuleExpressionBO> ruleExpressionBOs) {
        if(ruleExpressionBOs != null) {
            for (RuleExpressionBO ruleExpressionBO : ruleExpressionBOs) {
                TenantSecurityUtils.copyOperator(ruleExpressionBO);
                updateRuleExpression(ruleExpressionBO);
            }
        }
    }

    @Override
    public void deleteRuleExpression(RuleExpressionBO ruleExpressionBO) {
        if(JudgeUtils.isNull(ruleExpressionBO.getRuleExpressionId())){
            List<RuleExpressionDO> ruleExpressionDOS = ruleExpressionDao.find(ruleExpressionBO);
            ruleExpressionDOS.forEach(f->ruleExpressionDao.delete(f.getRuleExpressionId()));
            return;
        }
        ruleExpressionDao.delete(ruleExpressionBO.getRuleExpressionId());
    }

    @Override
    public List<RuleExpressionBO> getRuleExpressionList(RuleExpressionBO ruleExpressionBO) {

        return BeanConvertUtil.convertList(ruleExpressionDao.likeFind(ruleExpressionBO), RuleExpressionBO.class);
    }

    @Override
    public void deleteByRuleId(Integer dispatchRuleId) {
        ruleExpressionDao.deleteByRuleId(dispatchRuleId);
    }

    @Override
    public Boolean existUseZoneByZoneId(Long zoneId) {
        RuleExpressionDO entity = new RuleExpressionDO();
        entity.setZoneWeights("\"type\": \""+zoneId+"\",");
        List<RuleExpressionDO> ruleExpressionDOS = ruleExpressionDao.likeFind(entity);
        return JudgeUtils.isNotEmpty(ruleExpressionDOS);
    }
}
