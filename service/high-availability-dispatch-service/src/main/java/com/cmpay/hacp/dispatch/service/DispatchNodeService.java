package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.DashboardDispatchBO;
import com.cmpay.hacp.dispatch.bo.DispatchNodeBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

public interface DispatchNodeService {

    void addDispatchNode(DispatchNodeBO dispatchNodeBO);
    void updateDispatchNode(DispatchNodeBO dispatchNodeBO);
    void deleteDispatchNode(DispatchNodeBO dispatchNodeBO);
    List<DispatchNodeBO> getDispatchNodeList(String workspaceId);

    DispatchNodeBO randGetNode(DispatchNodeBO dispatchNode);

    PageInfo<DispatchNodeBO> getDispatchNodePage(int pageNum, int pageSize, DispatchNodeBO dispatchNodeBO);

    List<DashboardDispatchBO> getZoneDispatchList(String workspaceId);

    List<String> getRunningVersionList(String workspaceId);
}