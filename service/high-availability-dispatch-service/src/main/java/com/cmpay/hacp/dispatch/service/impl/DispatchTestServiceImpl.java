package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.DispatchNodeBO;
import com.cmpay.hacp.dispatch.bo.DispatchTestBO;
import com.cmpay.hacp.dispatch.client.AgentNodeFeignClient;
import com.cmpay.hacp.dispatch.client.FeignClientCache;
import com.cmpay.hacp.dispatch.dao.IDispatchTestExtDao;
import com.cmpay.hacp.dispatch.service.AppServiceService;
import com.cmpay.hacp.dispatch.service.DispatchNodeService;
import com.cmpay.hacp.dispatch.service.DispatchTestService;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hafr.agent.domain.ApiMeta;
import com.cmpay.hafr.agent.dto.TraceRequest;
import com.cmpay.hafr.agent.dto.TraceResult;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class DispatchTestServiceImpl implements DispatchTestService {

    @Autowired
    private IDispatchTestExtDao dispatchTestDao;

    @Autowired
    private DispatchNodeService dispatchNodeService;

    @Autowired
    private AppServiceService appServiceService;

    @Override
    public void addDispatchTest(DispatchTestBO dispatchTestBO) {
        dispatchTestBO.convertToJson();
        dispatchTestDao.insert(dispatchTestBO);
        if (StringUtils.isBlank(dispatchTestBO.getDispatchTestName())){
            dispatchTestBO.setDispatchTestName("T"+dispatchTestBO.getDispatchTestId());
            dispatchTestDao.update(dispatchTestBO);
        }
    }

    @Override
    public void updateDispatchTest(DispatchTestBO dispatchNodeBO) {
        dispatchNodeBO.convertToJson();
        dispatchTestDao.update(dispatchNodeBO);
    }

    @Override
    public void deleteDispatchTest(DispatchTestBO dispatchNodeBO) {
        dispatchTestDao.delete(dispatchNodeBO.getDispatchTestId());
    }

    @Override
    public DispatchTestBO getDispatchTest(DispatchTestBO dispatchNodeBO) {
        return BeanConvertUtil.convert(dispatchTestDao.get(dispatchNodeBO.getDispatchTestId()), DispatchTestBO.class);
    }

    @Override
    public PageInfo<DispatchTestBO> getDispatchTestList(int pageNum, int pageSize, DispatchTestBO dispatchNodeBO) {
        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> BeanConvertUtil.convertList(dispatchTestDao.likeFind(dispatchNodeBO), DispatchTestBO.class));
    }

    @Override
    public DefaultRspDTO<TraceResult> call(DispatchTestBO dispatchTestId) {
        DispatchTestBO dispatchTestBO = BeanConvertUtil.convert(dispatchTestDao.get(dispatchTestId.getDispatchTestId()), DispatchTestBO.class);
        if(JudgeUtils.isNull(dispatchTestBO)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.NO_USEFUL_DISPATCH_NODE);
        }
        DispatchNodeBO dispatchNodeBO = new DispatchNodeBO();
        dispatchNodeBO.setWorkspaceId(dispatchTestBO.getWorkspaceId());
        dispatchNodeBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        dispatchNodeBO.setConfigStatus(true);
        DispatchNodeBO randDispatchNode = dispatchNodeService.randGetNode(dispatchNodeBO);
        if(JudgeUtils.isNull(randDispatchNode)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.NO_USEFUL_DISPATCH_NODE);
        }
        AgentNodeFeignClient nodeClient = FeignClientCache.getClient(AgentNodeFeignClient.class, randDispatchNode.getNodeCallUrl());
        DefaultDTO<TraceRequest> req = new DefaultDTO<>();
        dispatchTestBO.setAppServiceName(appServiceService.getAppService(dispatchTestBO.getAppServiceId()).getAppServiceName());
        TraceRequest traceRequest = getTraceRequest(dispatchTestBO);
        req.setBody(traceRequest);
        DefaultRspDTO<TraceResult> traceResultDefaultRspDTO = nodeClient.simulateTrace(req);
        return traceResultDefaultRspDTO;
    }

    private static TraceRequest getTraceRequest(DispatchTestBO dispatchTestBO) {
        TraceRequest traceRequest = new TraceRequest();
        ApiMeta apiMeta = new ApiMeta();
        apiMeta.setTenantId(dispatchTestBO.getOperatorId());
        apiMeta.setWorkspaceId(dispatchTestBO.getWorkspaceId());
        traceRequest.setApiMeta(apiMeta);
        traceRequest.setScheme(dispatchTestBO.getScheme());
        traceRequest.setAddress(dispatchTestBO.getAddress());
        traceRequest.setPort(dispatchTestBO.getPort());
        traceRequest.setHost(dispatchTestBO.getHost());
        if(JudgeUtils.isNotNull(dispatchTestBO.getAppServiceId())) {
            traceRequest.setServiceName(dispatchTestBO.getAppServiceName());
        }
        traceRequest.setMethod(dispatchTestBO.getMethod());
        traceRequest.setUrlPath(dispatchTestBO.getUrlpath());
        traceRequest.setRequestBody(dispatchTestBO.getRequestbody());
        traceRequest.setCookies(dispatchTestBO.getCookieMap());
        traceRequest.setRequestHeaders(dispatchTestBO.getRequestheaderMap());
        traceRequest.setQueryParams(dispatchTestBO.getQueryParamsMap());
        return traceRequest;
    }

    private String getNodeCallUrl(String ipPort){
        if(!StringUtils.startsWith(ipPort, "http")) {

            return "http://" + ipPort;
        }
        return ipPort;
    }
}
