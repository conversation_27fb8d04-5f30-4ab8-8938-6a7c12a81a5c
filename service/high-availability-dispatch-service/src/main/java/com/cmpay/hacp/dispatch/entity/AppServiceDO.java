/*
 * @ClassName AppServiceDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class AppServiceDO extends BaseDO {
    /**
     * @Fields appServiceId 业务服务ID
     */
    private Integer appServiceId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields appServiceCn 中文名
     */
    private String appServiceCn;
    /**
     * @Fields appServiceName 业务服务名称
     */
    private String appServiceName;
    /**
     * @Fields appServiceDesc 业务服务描述
     */
    private String appServiceDesc;
    /**
     * @Fields listen 监听端口
     */
    private String listen;
    /**
     * @Fields domainNames 域名列表
     */
    private String domainNames;
    /**
     * @Fields extraConfigs 扩展配置
     */
    private String extraConfigs;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getAppServiceId() {
        return appServiceId;
    }

    public void setAppServiceId(Integer appServiceId) {
        this.appServiceId = appServiceId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getAppServiceCn() {
        return appServiceCn;
    }

    public void setAppServiceCn(String appServiceCn) {
        this.appServiceCn = appServiceCn;
    }

    public String getAppServiceName() {
        return appServiceName;
    }

    public void setAppServiceName(String appServiceName) {
        this.appServiceName = appServiceName;
    }

    public String getAppServiceDesc() {
        return appServiceDesc;
    }

    public void setAppServiceDesc(String appServiceDesc) {
        this.appServiceDesc = appServiceDesc;
    }

    public String getListen() {
        return listen;
    }

    public void setListen(String listen) {
        this.listen = listen;
    }

    public String getDomainNames() {
        return domainNames;
    }

    public void setDomainNames(String domainNames) {
        this.domainNames = domainNames;
    }

    public String getExtraConfigs() {
        return extraConfigs;
    }

    public void setExtraConfigs(String extraConfigs) {
        this.extraConfigs = extraConfigs;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}