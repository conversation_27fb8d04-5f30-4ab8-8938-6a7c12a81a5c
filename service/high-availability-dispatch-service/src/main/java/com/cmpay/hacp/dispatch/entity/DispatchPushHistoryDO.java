/*
 * @ClassName DispatchPushHistoryDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class DispatchPushHistoryDO extends BaseDO {
    /**
     * @Fields pushHistoryId 推送历史ID
     */
    private Integer pushHistoryId;
    /**
     * @Fields dispatchConfigId 调度配置ID
     */
    private Integer dispatchConfigId;
    /**
     * @Fields pushTimes 推送次数
     */
    private Byte pushTimes;
    /**
     * @Fields dispatchNodeId 调度节点ID
     */
    private Integer dispatchNodeId;
    /**
     * @Fields dispatchNodeName 调度节点名称
     */
    private String dispatchNodeName;
    /**
     * @Fields zoneId 机房ID
     */
    private Long zoneId;
    /**
     * @Fields ipPort 代理机房IP端口
     */
    private String ipPort;
    /**
     * @Fields pushStartTime 推送开始时间
     */
    private LocalDateTime pushStartTime;
    /**
     * @Fields pushEndTime 推送结束时间
     */
    private LocalDateTime pushEndTime;
    /**
     * @Fields configDesc 配置说明
     */
    private String configDesc;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields pushStatus 0:推送异常  1:推送成功
     */
    private Byte pushStatus;
    /**
     * @Fields msgCd 推送agent返回消息码
     */
    private String msgCd;
    /**
     * @Fields msgInfo 推送agent返回消息信息
     */
    private String msgInfo;

    public Integer getPushHistoryId() {
        return pushHistoryId;
    }

    public void setPushHistoryId(Integer pushHistoryId) {
        this.pushHistoryId = pushHistoryId;
    }

    public Integer getDispatchConfigId() {
        return dispatchConfigId;
    }

    public void setDispatchConfigId(Integer dispatchConfigId) {
        this.dispatchConfigId = dispatchConfigId;
    }

    public Byte getPushTimes() {
        return pushTimes;
    }

    public void setPushTimes(Byte pushTimes) {
        this.pushTimes = pushTimes;
    }

    public Integer getDispatchNodeId() {
        return dispatchNodeId;
    }

    public void setDispatchNodeId(Integer dispatchNodeId) {
        this.dispatchNodeId = dispatchNodeId;
    }

    public String getDispatchNodeName() {
        return dispatchNodeName;
    }

    public void setDispatchNodeName(String dispatchNodeName) {
        this.dispatchNodeName = dispatchNodeName;
    }

    public Long getZoneId() {
        return zoneId;
    }

    public void setZoneId(Long zoneId) {
        this.zoneId = zoneId;
    }

    public String getIpPort() {
        return ipPort;
    }

    public void setIpPort(String ipPort) {
        this.ipPort = ipPort;
    }

    public LocalDateTime getPushStartTime() {
        return pushStartTime;
    }

    public void setPushStartTime(LocalDateTime pushStartTime) {
        this.pushStartTime = pushStartTime;
    }

    public LocalDateTime getPushEndTime() {
        return pushEndTime;
    }

    public void setPushEndTime(LocalDateTime pushEndTime) {
        this.pushEndTime = pushEndTime;
    }

    public String getConfigDesc() {
        return configDesc;
    }

    public void setConfigDesc(String configDesc) {
        this.configDesc = configDesc;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Byte getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(Byte pushStatus) {
        this.pushStatus = pushStatus;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public void setMsgCd(String msgCd) {
        this.msgCd = msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }
}