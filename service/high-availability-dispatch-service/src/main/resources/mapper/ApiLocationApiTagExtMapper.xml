<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IApiLocationApiTagExtDao" >

    <resultMap id="BaseResultMap1" type="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDO" >
        <id column="api_location_id" property="apiLocationId" jdbcType="VARCHAR" />
        <id column="api_tag_id" property="apiTagId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List1" >
        api_location_id, api_tag_id, workspace_id, status, operator_id, operator_name, create_time, update_time
    </sql>

    <delete id="deleteByApiLocationId" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDO" >
        delete from api_location_api_tag
        where api_location_id = #{apiLocationId,jdbcType=VARCHAR}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>
</mapper>