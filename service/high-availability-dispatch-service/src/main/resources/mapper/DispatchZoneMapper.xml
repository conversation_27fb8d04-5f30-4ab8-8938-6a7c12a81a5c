<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchZoneDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchZoneDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="zone_name" property="zoneName" jdbcType="VARCHAR" />
        <result column="zone_label" property="zoneLabel" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, workspace_id, zone_name, zone_label, operator_id, operator_name, create_time, 
        update_time, remarks
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_zone
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from dispatch_zone
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.DispatchZoneDO" >
        insert into dispatch_zone
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="zoneName != null" >
                zone_name,
            </if>
            <if test="zoneLabel != null" >
                zone_label,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="remarks != null" >
                remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="zoneName != null" >
                #{zoneName,jdbcType=VARCHAR},
            </if>
            <if test="zoneLabel != null" >
                #{zoneLabel,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null" >
                #{remarks,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchZoneDO" >
        update dispatch_zone
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="zoneName != null" >
                zone_name = #{zoneName,jdbcType=VARCHAR},
            </if>
            <if test="zoneLabel != null" >
                zone_label = #{zoneLabel,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null" >
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchZoneDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_zone
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="zoneName != null" >
                and zone_name = #{zoneName,jdbcType=VARCHAR}
            </if>
            <if test="zoneLabel != null" >
                and zone_label = #{zoneLabel,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null" >
                and remarks = #{remarks,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>