<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppInstanceMachineDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO" >
        <id column="instance_machine_id" property="instanceMachineId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="instance_group_id" property="instanceGroupId" jdbcType="INTEGER" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="weight" property="weight" jdbcType="SMALLINT" />
        <result column="is_backup" property="isBackup" jdbcType="SMALLINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        instance_machine_id, workspace_id, instance_group_id, zone_id, address, weight, is_backup, 
        status, operator_id, operator_name, create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from app_instance_machine
        where instance_machine_id = #{instanceMachineId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from app_instance_machine
        where instance_machine_id = #{instanceMachineId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO" >
        insert into app_instance_machine
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="instanceMachineId != null" >
                instance_machine_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="instanceGroupId != null" >
                instance_group_id,
            </if>
            <if test="zoneId != null" >
                zone_id,
            </if>
            <if test="address != null" >
                address,
            </if>
            <if test="weight != null" >
                weight,
            </if>
            <if test="isBackup != null" >
                is_backup,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="instanceMachineId != null" >
                #{instanceMachineId,jdbcType=INTEGER},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupId != null" >
                #{instanceGroupId,jdbcType=INTEGER},
            </if>
            <if test="zoneId != null" >
                #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="address != null" >
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="weight != null" >
                #{weight,jdbcType=SMALLINT},
            </if>
            <if test="isBackup != null" >
                #{isBackup,jdbcType=SMALLINT},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO" >
        update app_instance_machine
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupId != null" >
                instance_group_id = #{instanceGroupId,jdbcType=INTEGER},
            </if>
            <if test="zoneId != null" >
                zone_id = #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="address != null" >
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="weight != null" >
                weight = #{weight,jdbcType=SMALLINT},
            </if>
            <if test="isBackup != null" >
                is_backup = #{isBackup,jdbcType=SMALLINT},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where instance_machine_id = #{instanceMachineId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO" >
        select 
        <include refid="Base_Column_List" />
        from app_instance_machine
        <where >
            <if test="instanceMachineId != null" >
                and instance_machine_id = #{instanceMachineId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupId != null" >
                and instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=BIGINT}
            </if>
            <if test="address != null" >
                and address = #{address,jdbcType=VARCHAR}
            </if>
            <if test="weight != null" >
                and weight = #{weight,jdbcType=SMALLINT}
            </if>
            <if test="isBackup != null" >
                and is_backup = #{isBackup,jdbcType=SMALLINT}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>