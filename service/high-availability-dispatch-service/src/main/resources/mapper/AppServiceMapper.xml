<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppServiceDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.AppServiceDO" >
        <id column="app_service_id" property="appServiceId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="app_service_cn" property="appServiceCn" jdbcType="VARCHAR" />
        <result column="app_service_name" property="appServiceName" jdbcType="VARCHAR" />
        <result column="app_service_desc" property="appServiceDesc" jdbcType="VARCHAR" />
        <result column="listen" property="listen" jdbcType="VARCHAR" />
        <result column="domain_names" property="domainNames" jdbcType="VARCHAR" />
        <result column="extra_configs" property="extraConfigs" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        app_service_id, workspace_id, app_service_cn, app_service_name, app_service_desc, 
        listen, domain_names, extra_configs, status, operator_id, operator_name, create_time, 
        update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from app_service
        where app_service_id = #{appServiceId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from app_service
        where app_service_id = #{appServiceId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.AppServiceDO" useGeneratedKeys="true" keyProperty="appServiceId" >
        insert into app_service
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="appServiceCn != null" >
                app_service_cn,
            </if>
            <if test="appServiceName != null" >
                app_service_name,
            </if>
            <if test="appServiceDesc != null" >
                app_service_desc,
            </if>
            <if test="listen != null" >
                listen,
            </if>
            <if test="domainNames != null" >
                domain_names,
            </if>
            <if test="extraConfigs != null" >
                extra_configs,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="appServiceCn != null" >
                #{appServiceCn,jdbcType=VARCHAR},
            </if>
            <if test="appServiceName != null" >
                #{appServiceName,jdbcType=VARCHAR},
            </if>
            <if test="appServiceDesc != null" >
                #{appServiceDesc,jdbcType=VARCHAR},
            </if>
            <if test="listen != null" >
                #{listen,jdbcType=VARCHAR},
            </if>
            <if test="domainNames != null" >
                #{domainNames,jdbcType=VARCHAR},
            </if>
            <if test="extraConfigs != null" >
                #{extraConfigs,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.AppServiceDO" >
        update app_service
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="appServiceCn != null" >
                app_service_cn = #{appServiceCn,jdbcType=VARCHAR},
            </if>
            <if test="appServiceName != null" >
                app_service_name = #{appServiceName,jdbcType=VARCHAR},
            </if>
            <if test="appServiceDesc != null" >
                app_service_desc = #{appServiceDesc,jdbcType=VARCHAR},
            </if>
            <if test="listen != null" >
                listen = #{listen,jdbcType=VARCHAR},
            </if>
            <if test="domainNames != null" >
                domain_names = #{domainNames,jdbcType=VARCHAR},
            </if>
            <if test="extraConfigs != null" >
                extra_configs = #{extraConfigs,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where app_service_id = #{appServiceId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppServiceDO" >
        select 
        <include refid="Base_Column_List" />
        from app_service
        <where >
            <if test="appServiceId != null" >
                and app_service_id = #{appServiceId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="appServiceCn != null" >
                and app_service_cn = #{appServiceCn,jdbcType=VARCHAR}
            </if>
            <if test="appServiceName != null" >
                and app_service_name = #{appServiceName,jdbcType=VARCHAR}
            </if>
            <if test="appServiceDesc != null" >
                and app_service_desc = #{appServiceDesc,jdbcType=VARCHAR}
            </if>
            <if test="listen != null" >
                and listen = #{listen,jdbcType=VARCHAR}
            </if>
            <if test="domainNames != null" >
                and domain_names = #{domainNames,jdbcType=VARCHAR}
            </if>
            <if test="extraConfigs != null" >
                and extra_configs = #{extraConfigs,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>