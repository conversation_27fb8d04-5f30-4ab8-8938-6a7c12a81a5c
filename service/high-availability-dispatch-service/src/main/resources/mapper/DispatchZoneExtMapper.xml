<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchZoneExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchZoneDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <id column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <id column="zone_name" property="zoneName" jdbcType="VARCHAR" />
        <id column="zone_label" property="zoneLabel" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.dispatch.bo.DispatchZoneBO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <id column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <id column="zone_name" property="zoneName" jdbcType="VARCHAR" />
        <id column="zone_label" property="zoneLabel" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, workspace_id, zone_name, zone_label, operator_id, operator_name, create_time, update_time,
        remarks
    </sql>

    <sql id="Simple_Column_ListList" >
        id, zone_name, zone_label
    </sql>

    <update id="deleteTenantZone">
        delete from dispatch_zone
        where id = #{id,jdbcType=BIGINT}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </update>

    <select id="getDetailList" resultMap="BaseResultExtMap"  parameterType="com.cmpay.hacp.dispatch.entity.DispatchZoneDO">
        select
            <include refid="Base_Column_List" />
        from dispatch_zone
        <where >
            <if test="id != null " >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="workspaceId != null and workspaceId !=''" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="zoneName != null and zoneName !=''" >
                and zone_name like  concat('%',#{zoneName,jdbcType=VARCHAR},'%')
            </if>
            <if test="zoneLabel != null and zoneLabel !=''" >
                and zone_label like  concat('%',#{zoneLabel,jdbcType=VARCHAR},'%')
            </if>
            <if test="remarks != null" >
                and remarks = #{remarks,jdbcType=VARCHAR}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="existFind" resultMap="BaseResultExtMap" >
        select
        <include refid="Base_Column_List" />
        from dispatch_zone
        <where>
            workspace_id = #{workspaceId,jdbcType=VARCHAR}
            and zone_label = #{zoneLabel,jdbcType=VARCHAR}
            <if test="id != null " >
                and id != #{id,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="getSimpleList" resultMap="BaseResultExtMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchZoneDO">
        select
        <include refid="Simple_Column_ListList" />
        from dispatch_zone
        <where >
            <if test="id != null " >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="workspaceId != null and workspaceId !=''" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="zoneName != null and zoneName !=''" >
                and zone_name = #{zoneName,jdbcType=VARCHAR}
            </if>
            <if test="zoneLabel != null and zoneLabel !=''" >
                and zone_label = #{zoneLabel,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dispatch_zone
        <where >
            <if test="id != null " >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="workspaceId != null and workspaceId !=''" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchZoneDO" >
        update dispatch_zone
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="zoneName != null" >
                zone_name = #{zoneName,jdbcType=VARCHAR},
            </if>
            <if test="zoneLabel != null" >
                zone_label = #{zoneLabel,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null" >
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </update>
</mapper>