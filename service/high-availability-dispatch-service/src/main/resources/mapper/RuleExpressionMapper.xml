<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IRuleExpressionDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" >
        <id column="rule_expression_id" property="ruleExpressionId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_rule_id" property="dispatchRuleId" jdbcType="INTEGER" />
        <result column="dispatch_rule_type" property="dispatchRuleType" jdbcType="VARCHAR" />
        <result column="priority_level" property="priorityLevel" jdbcType="TINYINT" />
        <result column="param_extractor" property="paramExtractor" jdbcType="VARCHAR" />
        <result column="calc_operator" property="calcOperator" jdbcType="VARCHAR" />
        <result column="result_value" property="resultValue" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" extends="BaseResultMap" >
        <result column="zone_weights" property="zoneWeights" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        rule_expression_id, workspace_id, dispatch_rule_id, dispatch_rule_type, priority_level, 
        param_extractor, calc_operator, result_value, status, operator_id, operator_name, 
        create_time, update_time
    </sql>

    <sql id="Blob_Column_List" >
        zone_weights
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from rule_expression
        where rule_expression_id = #{ruleExpressionId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from rule_expression
        where rule_expression_id = #{ruleExpressionId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" >
        insert into rule_expression
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="ruleExpressionId != null" >
                rule_expression_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="dispatchRuleId != null" >
                dispatch_rule_id,
            </if>
            <if test="dispatchRuleType != null" >
                dispatch_rule_type,
            </if>
            <if test="priorityLevel != null" >
                priority_level,
            </if>
            <if test="paramExtractor != null" >
                param_extractor,
            </if>
            <if test="calcOperator != null" >
                calc_operator,
            </if>
            <if test="resultValue != null" >
                result_value,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="zoneWeights != null" >
                zone_weights,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="ruleExpressionId != null" >
                #{ruleExpressionId,jdbcType=INTEGER},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleId != null" >
                #{dispatchRuleId,jdbcType=INTEGER},
            </if>
            <if test="dispatchRuleType != null" >
                #{dispatchRuleType,jdbcType=VARCHAR},
            </if>
            <if test="priorityLevel != null" >
                #{priorityLevel,jdbcType=TINYINT},
            </if>
            <if test="paramExtractor != null" >
                #{paramExtractor,jdbcType=VARCHAR},
            </if>
            <if test="calcOperator != null" >
                #{calcOperator,jdbcType=VARCHAR},
            </if>
            <if test="resultValue != null" >
                #{resultValue,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="zoneWeights != null" >
                #{zoneWeights,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" >
        update rule_expression
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleId != null" >
                dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER},
            </if>
            <if test="dispatchRuleType != null" >
                dispatch_rule_type = #{dispatchRuleType,jdbcType=VARCHAR},
            </if>
            <if test="priorityLevel != null" >
                priority_level = #{priorityLevel,jdbcType=TINYINT},
            </if>
            <if test="paramExtractor != null" >
                param_extractor = #{paramExtractor,jdbcType=VARCHAR},
            </if>
            <if test="calcOperator != null" >
                calc_operator = #{calcOperator,jdbcType=VARCHAR},
            </if>
            <if test="resultValue != null" >
                result_value = #{resultValue,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="zoneWeights != null" >
                zone_weights = #{zoneWeights,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where rule_expression_id = #{ruleExpressionId,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" >
        update rule_expression
        set workspace_id = #{workspaceId,jdbcType=VARCHAR},
            dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER},
            dispatch_rule_type = #{dispatchRuleType,jdbcType=VARCHAR},
            priority_level = #{priorityLevel,jdbcType=TINYINT},
            param_extractor = #{paramExtractor,jdbcType=VARCHAR},
            calc_operator = #{calcOperator,jdbcType=VARCHAR},
            result_value = #{resultValue,jdbcType=VARCHAR},
            status = #{status,jdbcType=TINYINT},
            operator_id = #{operatorId,jdbcType=VARCHAR},
            operator_name = #{operatorName,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            zone_weights = #{zoneWeights,jdbcType=LONGVARCHAR}
        where rule_expression_id = #{ruleExpressionId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" >
        select 
        <include refid="Base_Column_List" />
        from rule_expression
        <where >
            <if test="ruleExpressionId != null" >
                and rule_expression_id = #{ruleExpressionId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleId != null" >
                and dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
            </if>
            <if test="dispatchRuleType != null" >
                and dispatch_rule_type = #{dispatchRuleType,jdbcType=VARCHAR}
            </if>
            <if test="priorityLevel != null" >
                and priority_level = #{priorityLevel,jdbcType=TINYINT}
            </if>
            <if test="paramExtractor != null" >
                and param_extractor = #{paramExtractor,jdbcType=VARCHAR}
            </if>
            <if test="calcOperator != null" >
                and calc_operator = #{calcOperator,jdbcType=VARCHAR}
            </if>
            <if test="resultValue != null" >
                and result_value = #{resultValue,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="zoneWeights != null" >
                and zone_weights = #{zoneWeights,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>