<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppInstanceZoneExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.bo.AppInstanceZoneBO" >
        <id column="instance_zone_id" property="instanceZoneId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="instance_group_id" property="instanceGroupId" jdbcType="INTEGER" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="zone_label" property="zoneLabel" jdbcType="VARCHAR" />
        <result column="external_router_address" property="externalRouterAddress" jdbcType="VARCHAR" />
        <result column="check_switch" property="checkSwitch" jdbcType="TINYINT" />
        <result column="check_interval" property="checkInterval" jdbcType="SMALLINT" />
        <result column="check_fall" property="checkFall" jdbcType="SMALLINT" />
        <result column="check_rise" property="checkRise" jdbcType="SMALLINT" />
        <result column="check_timeout" property="checkTimeout" jdbcType="SMALLINT" />
        <result column="check_port" property="checkPort" jdbcType="SMALLINT" />
        <result column="check_http_send" property="checkHttpSend" jdbcType="VARCHAR" />
        <result column="check_http_expect_alive" property="checkHttpExpectAlive" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="findExt" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceZoneDO" >
        select
        instance_zone_id, a.workspace_id, instance_group_id, zone_id, external_router_address,
        check_switch, check_interval, check_fall, check_rise, check_timeout, check_port,
        check_http_send, check_http_expect_alive, status, a.operator_id, a.operator_name, a.create_time,
        a.update_time,zone_label
        from app_instance_zone a left join dispatch_zone z on a.zone_id=z.id
        <where >
            <if test="instanceZoneId != null" >
                and instance_zone_id = #{instanceZoneId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and a.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupId != null" >
                and instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=VARCHAR}
            </if>
            <if test="externalRouterAddress != null" >
                and external_router_address = #{externalRouterAddress,jdbcType=VARCHAR}
            </if>
            <if test="checkSwitch != null" >
                and check_switch = #{checkSwitch,jdbcType=TINYINT}
            </if>
            <if test="checkInterval != null" >
                and check_interval = #{checkInterval,jdbcType=SMALLINT}
            </if>
            <if test="checkFall != null" >
                and check_fall = #{checkFall,jdbcType=SMALLINT}
            </if>
            <if test="checkRise != null" >
                and check_rise = #{checkRise,jdbcType=SMALLINT}
            </if>
            <if test="checkTimeout != null" >
                and check_timeout = #{checkTimeout,jdbcType=SMALLINT}
            </if>
            <if test="checkPort != null" >
                and check_port = #{checkPort,jdbcType=SMALLINT}
            </if>
            <if test="checkHttpSend != null" >
                and check_http_send = #{checkHttpSend,jdbcType=VARCHAR}
            </if>
            <if test="checkHttpExpectAlive != null" >
                and check_http_expect_alive = #{checkHttpExpectAlive,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
        </where>
    </select>
</mapper>