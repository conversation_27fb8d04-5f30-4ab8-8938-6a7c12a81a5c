<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchConfigExtDao">

    <resultMap id="ApiLocationDispatchesMap" type="com.cmpay.hacp.dispatch.entity.DispatchDO" >
        <id column="api_location_id" property="apiLocationId" jdbcType="VARCHAR" />
        <result column="dispatch_name" property="dispatchName" jdbcType="VARCHAR" />
        <result column="api_rule_ids" property="apiRuleIds" jdbcType="VARCHAR" />
        <result column="is_emergency" property="isEmergency" jdbcType="TINYINT" />
        <result column="priority_level" property="priorityLevel" jdbcType="TINYINT" />
    </resultMap>

    <select id="findApiLocationDispatches" resultMap="ApiLocationDispatchesMap" parameterType="com.cmpay.hacp.dispatch.bo.DispatchConfigBO" >
        select
            distinct at.api_location_id, d.dispatch_name, d.api_rule_ids, is_emergency, priority_level
        from
        (select al.*, alat.api_tag_id from api_location al
            left join api_location_api_tag alat on al.api_location_id=alat.api_location_id
        where al.workspace_id = #{workspaceId,jdbcType=VARCHAR} and al.status=1 and alat.workspace_id = #{workspaceId,jdbcType=VARCHAR} and alat.status=1 ) at
            join dispatch d on at.api_location_id=d.api_location_id or at.api_tag_id=d.api_tag
        where at.workspace_id = #{workspaceId,jdbcType=VARCHAR} and at.status=1 and d.workspace_id = #{workspaceId,jdbcType=VARCHAR} and d.status=1
        order by api_location_id asc, is_emergency desc, priority_level asc;
    </select>

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.bo.DispatchConfigBO" >
        <id column="dispatch_config_id" property="dispatchConfigId" jdbcType="INTEGER" />
        <result column="dispatch_version" property="dispatchVersion" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="config_hex" property="configHex" jdbcType="VARCHAR" />
        <result column="is_current_version" property="isCurrentVersion" jdbcType="TINYINT" />
        <result column="config_desc" property="configDesc" jdbcType="VARCHAR" />
        <result column="push_times" property="pushTimes" jdbcType="TINYINT" />
        <result column="push_start_time" property="pushStartTime" jdbcType="TIMESTAMP" />
        <result column="push_end_time" property="pushEndTime" jdbcType="TIMESTAMP" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.bo.DispatchConfigBO" extends="BaseResultMap" >
        <result column="app_instance_groups" property="appInstanceGroups" jdbcType="LONGVARCHAR" />
        <result column="app_services" property="appServices" jdbcType="LONGVARCHAR" />
        <result column="app_api_locations" property="appApiLocations" jdbcType="LONGVARCHAR" />
        <result column="app_rules" property="appRules" jdbcType="LONGVARCHAR" />
        <result column="dispatches" property="dispatches" jdbcType="LONGVARCHAR" />
        <result column="api_tag_map" property="apiTagMap" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_config_id, dispatch_version, workspace_id, is_current_version,
        config_desc, config_hex, push_times, push_start_time, push_end_time,
        operator_id, operator_name, status, create_time, update_time
    </sql>


    <sql id="Blob_Column_List" >
        app_instance_groups, app_services, app_api_locations, app_rules, dispatches, api_tag_map
    </sql>

    <select id="likeFind" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" >
        select
        <include refid="Base_Column_List" />
        from dispatch_config
        <where >
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="dispatchVersion != null" >
                and dispatch_version like concat('%',#{dispatchVersion,jdbcType=VARCHAR},'%')
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="isCurrentVersion != null" >
                and is_current_version = #{isCurrentVersion,jdbcType=TINYINT}
            </if>
            <if test="configDesc != null" >
                and config_desc = #{configDesc,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="appInstanceGroups != null" >
                and app_instance_groups = #{appInstanceGroups,jdbcType=LONGVARCHAR}
            </if>
            <if test="appServices != null" >
                and app_services = #{appServices,jdbcType=LONGVARCHAR}
            </if>
            <if test="appApiLocations != null" >
                and app_api_locations = #{appApiLocations,jdbcType=LONGVARCHAR}
            </if>
            <if test="appRules != null" >
                and app_rules = #{appRules,jdbcType=LONGVARCHAR}
            </if>
            <if test="dispatches != null" >
                and dispatches = #{dispatches,jdbcType=LONGVARCHAR}
            </if>
            <if test="apiTagMap != null" >
                and api_tag_map = #{apiTagMap,jdbcType=LONGVARCHAR}
            </if>
        </where>
        order by is_current_version desc, dispatch_version desc
    </select>
    <select id="getRunningDispatchConfig" resultMap="ResultMapWithBLOBs">
            select
            <include refid="Base_Column_List" />
            ,
            <include refid="Blob_Column_List" />
            from dispatch_config
            <where>
            workspace_id = #{workspaceId,jdbcType=VARCHAR}
            <if test="versions!=null and versions.size()>0">
                and dispatch_version in
                <foreach collection="versions" item="version" open="(" close=")" separator=",">
                    #{version,jdbcType=VARCHAR}
                </foreach>
            </if>
            </where>
    </select>

    <update id="pushUpdate" parameterType="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" >
        update dispatch_config
        <set >
            <if test="dispatchVersion != null" >
                dispatch_version = #{dispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="isCurrentVersion != null" >
                is_current_version = #{isCurrentVersion,jdbcType=TINYINT},
            </if>
            <if test="pushTimes != null" >
                push_times = #{pushTimes,jdbcType=TINYINT},
            </if>
            <if test="pushStartTime != null" >
                push_start_time = #{pushStartTime,jdbcType=TIMESTAMP},
            </if>
            push_end_time = #{pushEndTime,jdbcType=TIMESTAMP},
            <if test="configDesc != null" >
                config_desc = #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="appInstanceGroups != null" >
                app_instance_groups = #{appInstanceGroups,jdbcType=LONGVARCHAR},
            </if>
            <if test="appServices != null" >
                app_services = #{appServices,jdbcType=LONGVARCHAR},
            </if>
            <if test="appApiLocations != null" >
                app_api_locations = #{appApiLocations,jdbcType=LONGVARCHAR},
            </if>
            <if test="appRules != null" >
                app_rules = #{appRules,jdbcType=LONGVARCHAR},
            </if>
            <if test="dispatches != null" >
                dispatches = #{dispatches,jdbcType=LONGVARCHAR},
            </if>
            <if test="apiTagMap != null" >
                api_tag_map = #{apiTagMap,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
    </update>
</mapper>
