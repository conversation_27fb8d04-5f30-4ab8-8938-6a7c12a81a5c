<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IApiLocationExtDao" >

    <resultMap id="ApiResultMap" type="com.cmpay.hacp.dispatch.bo.ApiLocationBO" >
        <id column="api_location_id" property="apiLocationId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="api_location_name" property="apiLocationName" jdbcType="VARCHAR" />
        <result column="api_location_cn" property="apiLocationCn" jdbcType="VARCHAR" />
        <result column="api_desc" property="apiDesc" jdbcType="VARCHAR" />
        <result column="app_service" property="appService" jdbcType="VARCHAR" />
        <result column="app_instance_group" property="appInstanceGroup" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ApiResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.bo.ApiLocationBO" extends="ApiResultMap" >
        <result column="uri_matches" property="uriMatches" jdbcType="LONGVARCHAR" />
        <result column="app_service_name" property="appServiceName" jdbcType="VARCHAR" />
        <result column="instance_group_name" property="instanceGroupName" jdbcType="VARCHAR" />
        <result column="app_service_cn" property="appServiceCn" jdbcType="VARCHAR" />
        <result column="instance_group_cn" property="instanceGroupCn" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Api_Column_List" >
        api_location_id, api_location_name,api_location_cn, api_desc, app_service, app_instance_group,
        a.workspace_id,a.status,a.operator_id, a.operator_name, a.create_time, a.update_time
    </sql>

    <sql id="Api_Blob_Column_List" >
        uri_matches,instance_group_name,app_service_name,instance_group_cn,app_service_cn
    </sql>

    <select id="likeFind" resultMap="ApiResultMapWithBLOBs" parameterType="com.cmpay.hacp.dispatch.bo.ApiLocationBO" >
        select 
        <include refid="Api_Column_List" />
        ,
        <include refid="Api_Blob_Column_List" />
        from api_location a left join app_instance_group g on  a.app_instance_group = g.instance_group_id
            left join app_service s on a.app_service = s.app_service_id
        <where >
            <if test="apiLocationId != null" >
                and api_location_id = #{apiLocationId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and a.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="apiLocationName != null" >
                and api_location_name like concat('%',#{apiLocationName,jdbcType=VARCHAR},'%')
            </if>
            <if test="apiLocationCn != null" >
                and api_location_cn like concat('%',#{apiLocationCn,jdbcType=VARCHAR},'%')
            </if>
            <if test="apiDesc != null" >
                and api_desc = #{apiDesc,jdbcType=VARCHAR}
            </if>
            <if test="appService != null" >
                and app_service = #{appService,jdbcType=VARCHAR}
            </if>
            <if test="appInstanceGroup != null" >
                and app_instance_group = #{appInstanceGroup,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and a.status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and a.operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and a.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and a.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
    <select id="existFind" resultType="com.cmpay.hacp.dispatch.entity.ApiLocationDO" >
        select
        <include refid="com.cmpay.hacp.dispatch.dao.IApiLocationDao.Base_Column_List" />
        from api_location
        <where>
            workspace_id = #{workspaceId,jdbcType=VARCHAR}
            and api_location_name = #{apiLocationName,jdbcType=VARCHAR}
            <if test="apiLocationId != null " >
                and api_location_id != #{apiLocationId,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>