package com.cmpay.hacp.tenant.service.impl;

import com.cmpay.hacp.tenant.bo.TenantWorkspaceUserBO;
import com.cmpay.hacp.tenant.dao.ITenantWorkspaceUserExtDao;
import com.cmpay.hacp.tenant.dao.ITenantWorkspaceUserRoleExtDao;
import com.cmpay.hacp.tenant.entity.TenantWorkspaceUserDO;
import com.cmpay.hacp.tenant.entity.TenantWorkspaceUserRoleDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.WorkspaceUserTypeEnum;
import com.cmpay.hacp.tenant.service.TenantWorkspaceRoleService;
import com.cmpay.hacp.tenant.service.HacpBaseService;
import com.cmpay.hacp.tenant.service.TenantWorkspaceUserService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 项目成员管理
 *
 * <AUTHOR>
 */
@Service
public class TenantWorkspaceUserServiceImpl implements TenantWorkspaceUserService {

    @Resource
    private ITenantWorkspaceUserExtDao tenantWorkspaceUserExtDao;

    @Resource
    private ITenantWorkspaceUserRoleExtDao tenantWorkspaceUserRoleExtDao;

    @Resource
    private TenantWorkspaceRoleService tenantWorkspaceRoleService;

    @Resource
    private HacpBaseService hacpBaseService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateAdminToAnyoneWorkspaceUserByWorkspaceId(String loginName, LocalDateTime localDateTime, String workspaceId) {
        TenantWorkspaceUserDO tenantWorkspaceUserDO = new TenantWorkspaceUserDO();
        tenantWorkspaceUserDO.setWorkspaceId(workspaceId);
        tenantWorkspaceUserDO.setUpdateUser(loginName);
        tenantWorkspaceUserDO.setUpdateTime(localDateTime);
        tenantWorkspaceUserExtDao.updateAdminToAnyoneWorkspaceUserByWorkspaceId(tenantWorkspaceUserDO);
        //todo 角色是否要变更
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateAdminWorkspaceUserByWorkspaceId(String loginName, LocalDateTime localDateTime, String workspaceId, String userId) {
        TenantWorkspaceUserDO tenantWorkspaceUserDO = new TenantWorkspaceUserDO();
        tenantWorkspaceUserDO.setWorkspaceId(workspaceId);
        tenantWorkspaceUserDO.setUserId(userId);
        tenantWorkspaceUserDO.setUpdateUser(loginName);
        tenantWorkspaceUserDO.setUpdateTime(localDateTime);
        tenantWorkspaceUserExtDao.updateAdminWorkspaceUserByWorkspaceId(tenantWorkspaceUserDO);
        //项目管理员角色与普通用户角色互斥，是项目管理员就没有其他角色
        this.deleteWorkspaceUserRoleByWorkspaceId(workspaceId, userId);
        TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
        tenantWorkspaceUser.setWorkspaceId(workspaceId);
        tenantWorkspaceUser.setUserId(userId);
        tenantWorkspaceUser.setWorkspaceUserType(WorkspaceUserTypeEnum.ADMIN.getCode());
        this.addWorkspaceUserRole(loginName, localDateTime, tenantWorkspaceUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceUserByWorkspaceId(String workspaceId) {
        //查询项目成员
        List<String> userIds = this.getWorkspaceUserIds(workspaceId);
        if (JudgeUtils.isNotEmpty(userIds)) {
            //删除项目成员
            tenantWorkspaceUserExtDao.deleteWorkspaceUserByWorkspaceId(workspaceId);
            userIds.stream().forEach(userId -> {
                //删除项目成员角色绑定关系
                this.deleteWorkspaceUserRoleByWorkspaceId(workspaceId, userId);
            });
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addWorkspaceUser(String loginName, TenantWorkspaceUserBO tenantWorkspaceUser) {
        LocalDateTime localDateTime = LocalDateTime.now();
        hacpBaseService.workspaceExist(tenantWorkspaceUser.getWorkspaceId());
        hacpBaseService.userExist(tenantWorkspaceUser.getUserId());
        if (JudgeUtils.equalsIgnoreCase(tenantWorkspaceUser.getWorkspaceUserType(), WorkspaceUserTypeEnum.ADMIN.getCode())) {
            //将项目管理员置为普通用户
            this.updateAdminToAnyoneWorkspaceUserByWorkspaceId(loginName, localDateTime, tenantWorkspaceUser.getWorkspaceId());
        }
        //新增项目成员
        TenantWorkspaceUserDO workspaceUserEntity = new TenantWorkspaceUserDO();
        workspaceUserEntity.setId(IdGenUtil.generateWorkspaceUserId());
        workspaceUserEntity.setWorkspaceId(tenantWorkspaceUser.getWorkspaceId());
        workspaceUserEntity.setUserId(tenantWorkspaceUser.getUserId());
        workspaceUserEntity.setWorkspaceUserType(tenantWorkspaceUser.getWorkspaceUserType());
        workspaceUserEntity.setCreateUser(loginName);
        workspaceUserEntity.setCreateTime(localDateTime);
        workspaceUserEntity.setUpdateUser(loginName);
        workspaceUserEntity.setUpdateTime(localDateTime);
        int insertWorkspaceUser = tenantWorkspaceUserExtDao.insert(workspaceUserEntity);
        if (insertWorkspaceUser < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        //删除项目成员角色
        this.deleteWorkspaceUserRoleByWorkspaceId(tenantWorkspaceUser.getWorkspaceId(), tenantWorkspaceUser.getUserId());
        //新增项目成员角色
        this.addWorkspaceUserRole(loginName, localDateTime, tenantWorkspaceUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateWorkspaceUser(String loginName, TenantWorkspaceUserBO tenantWorkspaceUser) {
        LocalDateTime localDateTime = LocalDateTime.now();
        //忽略前端传入的项目ID,不可改变项目成员与项目的绑定关系，需要根据ID主键查询项目ID
        TenantWorkspaceUserBO workspaceUserInfo = this.getWorkspaceUserInfo(tenantWorkspaceUser.getId());
        if (JudgeUtils.isNull(workspaceUserInfo)) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        hacpBaseService.workspaceExist(workspaceUserInfo.getWorkspaceId());
        hacpBaseService.userExist(workspaceUserInfo.getUserId());
        tenantWorkspaceUser.setWorkspaceId(workspaceUserInfo.getWorkspaceId());
        tenantWorkspaceUser.setUserId(workspaceUserInfo.getUserId());
        if (JudgeUtils.equalsIgnoreCase(tenantWorkspaceUser.getWorkspaceUserType(), WorkspaceUserTypeEnum.ADMIN.getCode())) {
            //将项目管理员置为普通用户
            this.updateAdminToAnyoneWorkspaceUserByWorkspaceId(loginName, localDateTime, tenantWorkspaceUser.getWorkspaceId());
        }
        //更新用户
        TenantWorkspaceUserDO workspaceUserEntity = new TenantWorkspaceUserDO();
        BeanUtils.copyProperties(workspaceUserEntity, tenantWorkspaceUser);
        workspaceUserEntity.setUpdateUser(loginName);
        workspaceUserEntity.setUpdateTime(localDateTime);
        int updateWorkspaceUser = tenantWorkspaceUserExtDao.update(workspaceUserEntity);
        if (updateWorkspaceUser < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        //删除项目成员角色
        this.deleteWorkspaceUserRoleByWorkspaceId(tenantWorkspaceUser.getWorkspaceId(), tenantWorkspaceUser.getUserId());
        //新增项目成员角色
        this.addWorkspaceUserRole(loginName, localDateTime, tenantWorkspaceUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceUserRoleByWorkspaceId(String workspaceId, String userId) {
        tenantWorkspaceUserRoleExtDao.deleteWorkspaceUserRoleByWorkspaceId(workspaceId, userId);
    }

    /**
     * 新增项目成员角色
     *
     * @param loginName           操作用户名
     * @param localDateTime       操作时间
     * @param tenantWorkspaceUser 项目成员角色详情
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addWorkspaceUserRole(String loginName, LocalDateTime localDateTime, TenantWorkspaceUserBO tenantWorkspaceUser) {
        hacpBaseService.workspaceExist(tenantWorkspaceUser.getWorkspaceId());
        //插入项目管理员角色
        if (JudgeUtils.equalsIgnoreCase(tenantWorkspaceUser.getWorkspaceUserType(), WorkspaceUserTypeEnum.ADMIN.getCode())) {
            List<String> workspaceRoleIds = tenantWorkspaceRoleService.getAdminWorkspaceRoleIds(tenantWorkspaceUser.getWorkspaceId());
            if (JudgeUtils.isNotEmpty(workspaceRoleIds)) {
                if (JudgeUtils.isNotEmpty(tenantWorkspaceUser.getWorkspaceRoleIds())) {
                    //去重合并
                    List<String> collect = Stream.of(workspaceRoleIds, tenantWorkspaceUser.getWorkspaceRoleIds())
                            .flatMap(Collection::stream)
                            .distinct()
                            .collect(Collectors.toList());
                    tenantWorkspaceUser.setWorkspaceRoleIds(collect);
                } else {
                    tenantWorkspaceUser.setWorkspaceRoleIds(workspaceRoleIds);
                }
            }
        }
        //插入项目成员角色
        if (JudgeUtils.isNotEmpty(tenantWorkspaceUser.getWorkspaceRoleIds())) {
            tenantWorkspaceUser.getWorkspaceRoleIds().stream().forEach(workspaceRoleId -> {
                TenantWorkspaceUserRoleDO workspaceUserRoleEntity = new TenantWorkspaceUserRoleDO();
                workspaceUserRoleEntity.setId(IdGenUtil.generateWorkspaceRoleId());
                workspaceUserRoleEntity.setWorkspaceRoleId(workspaceRoleId);
                workspaceUserRoleEntity.setUserId(tenantWorkspaceUser.getUserId());
                workspaceUserRoleEntity.setCreateUser(loginName);
                workspaceUserRoleEntity.setCreateTime(localDateTime);
                workspaceUserRoleEntity.setUpdateUser(loginName);
                workspaceUserRoleEntity.setUpdateTime(localDateTime);
                tenantWorkspaceUserRoleExtDao.insert(workspaceUserRoleEntity);
            });
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deletesWorkspaceUser(List<String> ids) {
        ids.stream().forEach(this::deleteWorkspaceUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceUser(String id) {
        TenantWorkspaceUserBO tenantWorkspaceUser = this.getWorkspaceUserInfo(id);
        if (JudgeUtils.isNotNull(tenantWorkspaceUser)) {
            if("1".equals(tenantWorkspaceUser.getWorkspaceUserType())){
                BusinessException.throwBusinessException(MsgEnum.DELETE_WORKPLACE_PERSON_IN_CHARGE_ERROR);
            }
            //删除项目成员
            tenantWorkspaceUserExtDao.delete(id);
            //删除项目成员拥有的角色
            this.deleteWorkspaceUserRoleByWorkspaceId(tenantWorkspaceUser.getWorkspaceId(), tenantWorkspaceUser.getUserId());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceUser(String workspaceId, String userId) {
        //删除项目成员
        tenantWorkspaceUserExtDao.deleteWorkspaceUser(workspaceId, userId);
        //删除项目成员拥有的角色
        this.deleteWorkspaceUserRoleByWorkspaceId(workspaceId, userId);

    }

    @Override
    public TenantWorkspaceUserBO getWorkspaceUserInfo(String id) {
        TenantWorkspaceUserDO workspaceUserEntity = tenantWorkspaceUserExtDao.get(id);
        if (JudgeUtils.isNull(workspaceUserEntity)) {
            return new TenantWorkspaceUserBO();
        }
        return this.getTenantWorkspaceUserInfo(workspaceUserEntity.getWorkspaceId(), workspaceUserEntity.getUserId());
    }

    @Override
    public TenantWorkspaceUserBO getTenantWorkspaceUserInfo(String workspaceId, String userId) {
        TenantWorkspaceUserBO tenantWorkspaceUser = tenantWorkspaceUserExtDao.getWorkspaceUserInfo(workspaceId, userId);
        if (JudgeUtils.isNotNull(tenantWorkspaceUser)) {
            List<String> workspaceRoleIds = tenantWorkspaceUserRoleExtDao.getUserWorkspaceRoleIds(workspaceId, userId);
            tenantWorkspaceUser.setWorkspaceRoleIds(workspaceRoleIds);
        }
        return tenantWorkspaceUser;
    }

    @Override
    public void existWorkspaceUser(String workspaceId, String userId) {
        TenantWorkspaceUserBO tenantWorkspaceUser = tenantWorkspaceUserExtDao.existWorkspaceUser(workspaceId, userId);
        if (JudgeUtils.isNull(tenantWorkspaceUser)) {
            BusinessException.throwBusinessException(MsgEnum.USER_IS_NOT_EXIST_WORKPLACE);
        }
    }

    @Override
    public List<TenantWorkspaceUserBO> getWorkspaceUsers(TenantWorkspaceUserBO tenantWorkspaceUser) {
        return tenantWorkspaceUserExtDao.getWorkspaceUsers(tenantWorkspaceUser);
    }

    @Override
    public List<String> getWorkspaceUserIds(String workspaceId) {
        return tenantWorkspaceUserExtDao.getWorkspaceUserIds(workspaceId);
    }

    @Override
    public PageInfo<TenantWorkspaceUserBO> getWorkspaceUsersByPage(int pageNum, int pageSize, TenantWorkspaceUserBO tenantWorkspaceUser) {
        return PageUtils.pageQueryWithCount(pageNum, pageSize, () -> {
            List<TenantWorkspaceUserBO> workspaceUsers = tenantWorkspaceUserExtDao.getWorkspaceUsers(tenantWorkspaceUser);
            if (JudgeUtils.isEmpty(workspaceUsers)) {
                return null;
            }
            workspaceUsers.stream().forEach(workspaceUser -> {
                workspaceUser.setWorkspaceRoles(tenantWorkspaceRoleService.getWorkspaceRolesByUserId(workspaceUser.getWorkspaceId(),
                        workspaceUser.getUserId()));
            });
            return workspaceUsers;
        });
    }
}
