package com.cmpay.hacp.tenant.dao;

import com.cmpay.hacp.tenant.bo.TenantWorkspaceUserBO;
import com.cmpay.hacp.tenant.entity.TenantWorkspaceUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ITenantWorkspaceUserExtDao extends ITenantWorkspaceUserDao {

    /**
     * 删除项目成员
     *
     * @param workspaceId 项目ID
     * @return 成功、失败
     */
    int deleteWorkspaceUserByWorkspaceId(@Param("workspaceId") String workspaceId);

    /**
     * 查询项目成员详情
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 项目成员详情
     */
    TenantWorkspaceUserBO getWorkspaceUserInfo(@Param("workspaceId") String workspaceId, @Param("userId") String userId);

    /**
     * 查询项目成员详情列表
     *
     * @param tenantWorkspaceUserBO 项目成员详情
     * @return 项目成员详情
     */
    List<TenantWorkspaceUserBO> getWorkspaceUsers(TenantWorkspaceUserBO tenantWorkspaceUserBO);

    /**
     * 查询项目成员ID列表
     *
     * @param workspaceId 项目ID
     * @return 项目成员ID列表
     */
    List<String> getWorkspaceUserIds(@Param("workspaceId") String workspaceId);

    /**
     * 将项目管理员置为普通用户
     *
     * @param tenantWorkspaceUserDO 项目详情
     * @return 成功数量
     */
    int updateAdminToAnyoneWorkspaceUserByWorkspaceId(TenantWorkspaceUserDO tenantWorkspaceUserDO);

    /**
     * 删除项目成员
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 成功数量
     */
    int deleteWorkspaceUser(@Param("workspaceId") String workspaceId, @Param("userId") String userId);

    /**
     * 修改项目管理员
     *
     * @param tenantWorkspaceUserDO 项目详情
     */
    int updateAdminWorkspaceUserByWorkspaceId(TenantWorkspaceUserDO tenantWorkspaceUserDO);

    /**
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return
     */
    TenantWorkspaceUserBO existWorkspaceUser(@Param("workspaceId") String workspaceId, @Param("userId") String userId);
}
