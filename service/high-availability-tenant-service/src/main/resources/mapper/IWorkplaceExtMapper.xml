<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.tenant.dao.IWorkspaceExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.WorkspaceDO">
        <id column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="workspace_name" property="workspaceName" jdbcType="VARCHAR"/>
        <result column="profile" property="profile" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ExtBaseResultMap" extends="BaseResultMap" type="com.cmpay.hacp.tenant.bo.TenantWorkspaceBO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="workspace_user_type" property="workspaceUserType" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="user_status" property="userStatus" jdbcType="VARCHAR"/>
        <result column="profile" property="profile" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        workspace_id
        , workspace_name, create_user, create_time, update_user, update_time,
        remarks, status
    </sql>

    <sql id="Deatil_Base_Column_List">
        SELECT t1.workspace_id,
               t1.workspace_name,
               t1.create_user,
               t1.create_time,
               t1.update_user,
               t1.update_time,
               t1.remarks,
               t1.status,
               t1.profile,
               t2.id,
               t3.tenant_id,
               t3.tenant_name,
               t4.workspace_user_type,
               t5.user_id,
               t5.user_name,
               t5.full_name,
               t5.email,
               t5.mobile,
               t5.status AS user_status
        FROM workspace t1
                 LEFT JOIN tenant_workspace t2 ON t1.workspace_id = t2.workspace_id
                 LEFT JOIN tenant t3 ON t2.tenant_id = t3.tenant_id
                 LEFT JOIN tenant_workspace_user t4 ON t1.workspace_id = t4.workspace_id
                 LEFT JOIN sys_user t5 ON t4.user_id = t5.user_id
    </sql>

    <select id="getDetailWorkspaceInfo" parameterType="java.lang.String" resultMap="ExtBaseResultMap">
        <include refid="Deatil_Base_Column_List"/>
        WHERE t1.workspace_id = #{workspaceId,jdbcType=VARCHAR}
        AND t4.workspace_user_type = '1'
    </select>


    <select id="getWorkspaceByWorkspaceName" resultMap="BaseResultMap">
        SELECT t2.workspace_id,
               t2.workspace_name,
               t2.create_user,
               t2.create_time,
               t2.update_user,
               t2.update_time,
               t2.remarks,
               t2.status
        FROM tenant_workspace t1
                 LEFT JOIN workspace t2 ON t1.workspace_id = t2.workspace_id
        WHERE t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
          AND t2.workspace_name = #{workspaceName,jdbcType=VARCHAR}
        order by t2.update_time desc
    </select>

    <select id="getIndexOwnWorkspaces" parameterType="java.lang.String" resultMap="ExtBaseResultMap">
        SELECT t3.workspace_id,
               t3.workspace_name,
               t3.create_user,
               t3.create_time,
               t3.update_user,
               t3.update_time,
               t3.remarks,
               t3.`status`,
               t3.profile,
               t6.user_id,
               t6.user_name,
               t6.full_name,
               t6.email,
               t6.mobile,
               t6.`status` AS user_status,
               t7.tenant_id,
               t7.tenant_name,
               t8.dept_id,
               t8.dept_name
        FROM workspace t3
                 LEFT JOIN tenant_workspace t4 ON t3.workspace_id = t4.workspace_id
                 LEFT JOIN tenant t7 ON t4.tenant_id = t7.tenant_id
                 LEFT JOIN tenant_user t5 ON t4.tenant_id = t5.tenant_id
                 LEFT JOIN sys_user t6 ON t5.user_id = t6.user_id
                 LEFT JOIN sys_dept t8 ON t7.dept_id = t8.dept_id
        WHERE t5.tenant_user_type = '1'
          and t3.workspace_id IN (SELECT workspace_id
                                  FROM tenant_workspace_user
                                  WHERE user_id = #{userId,jdbcType=VARCHAR}
                                  GROUP BY workspace_id
                                  UNION
                                  SELECT t1.workspace_id
                                  FROM tenant_workspace t1
                                           LEFT JOIN tenant_user t2 ON t1.tenant_id = t2.tenant_id
                                  WHERE t2.tenant_user_type = '1'
                                    and t2.user_id = #{userId,jdbcType=VARCHAR}
                                  GROUP BY t1.workspace_id)
        order by t3.update_time desc
    </select>

    <select id="getAllWorkspaces" resultType="java.lang.String">
        SELECT workspace_id
        FROM workspace
        order by update_time desc
    </select>

    <select id="getWorkspaceOwnList" parameterType="com.cmpay.hacp.tenant.bo.TenantWorkspaceBO"
            resultMap="ExtBaseResultMap">
        <include refid="Deatil_Base_Column_List"/>
        <where>
            t4.workspace_user_type = '1'
            <!--传入用户时，表示查询用户租户下拥有的项目 -->
            <if test="userId != null and userId != ''">
                and t1.workspace_id in (
                SELECT workspace_id
                FROM tenant_workspace_user
                WHERE user_id = #{userId,jdbcType=VARCHAR}
                GROUP BY workspace_id
                UNION
                SELECT a1.workspace_id
                FROM tenant_workspace a1
                LEFT JOIN tenant_user a2 ON a1.tenant_id = a2.tenant_id
                WHERE a2.tenant_user_type = '1'
                and a2.user_id = #{userId,jdbcType=VARCHAR}
                GROUP BY a1.workspace_id
                )
            </if>
            <if test="tenantId != null and tenantId != ''">
                and t3.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="tenantName != null and tenantName != ''">
                and t3.tenant_name like concat('%',#{tenantName,jdbcType=VARCHAR},'%')
            </if>
            <if test="workspaceId != null and workspaceId != ''">
                and t1.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceName != null and workspaceName != ''">
                and t1.workspace_name like concat('%',#{workspaceName,jdbcType=VARCHAR},'%')
            </if>
            <if test="createUser != null and createUser != ''">
                and t1.create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t1.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and t1.update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t1.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null and remarks != ''">
                and t1.remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and t1.status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
        order by t1.update_time desc
    </select>

    <select id="getWorkspaceAdminList" resultMap="ExtBaseResultMap">
        <include refid="Deatil_Base_Column_List"/>
        <where>
            t4.workspace_user_type = '1'
            <if test="userId != null and userId != ''">
                and t1.workspace_id in (SELECT a3.workspace_id
                FROM tenant_workspace_role a1
                LEFT JOIN tenant_workspace_user_role a2
                ON a1.workspace_role_id = a2.workspace_role_id
                LEFT JOIN tenant_workspace_user a3 ON a2.user_id = a3.user_id AND a1.workspace_id = a3.workspace_id
                WHERE a1.workspace_role_type = '1'
                AND a3.user_id = #{userId,jdbcType=VARCHAR}
                GROUP BY a3.workspace_id
                UNION
                SELECT b1.workspace_id
                FROM tenant_workspace b1
                LEFT JOIN tenant_user b2 ON b1.tenant_id = b2.tenant_id
                WHERE b2.tenant_user_type = '1'
                AND b2.user_id = #{userId,jdbcType=VARCHAR}
                GROUP BY b1.workspace_id)
            </if>
            <if test="tenantId != null and tenantId != ''">
                and t3.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="tenantName != null and tenantName != ''">
                and t3.tenant_name like concat('%',#{tenantName,jdbcType=VARCHAR},'%')
            </if>
            <if test="workspaceId != null and workspaceId != ''">
                and t1.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceName != null and workspaceName != ''">
                and t1.workspace_name like concat('%',#{workspaceName,jdbcType=VARCHAR},'%')
            </if>
            <if test="createUser != null and createUser != ''">
                and t1.create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t1.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and t1.update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t1.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null and remarks != ''">
                and t1.remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and t1.status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
        order by t1.update_time desc
    </select>
</mapper>
