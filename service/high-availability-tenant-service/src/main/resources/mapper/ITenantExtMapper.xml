<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.tenant.dao.ITenantExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.TenantDO">
        <id column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ExtBaseResultMap" extends="BaseResultMap" type="com.cmpay.hacp.tenant.bo.TenantBO">
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="tenant_user_type" property="tenantUserType" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="user_status" property="userStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        tenant_id, tenant_name, dept_id, create_user, create_time, update_user, update_time,
        remarks, status
    </sql>

    <sql id="Deatil_Base_Column_List">
        SELECT t1.tenant_id,
               t1.tenant_name,
               t1.dept_id,
               t4.dept_name,
               t1.create_user,
               t1.create_time,
               t1.update_user,
               t1.update_time,
               t1.remarks,
               t1.status,
               t2.tenant_user_type,
               t3.user_id,
               t3.user_name,
               t3.full_name,
               t3.email,
               t3.mobile,
               t3.status AS user_status
        FROM tenant t1
                 LEFT JOIN tenant_user t2 ON t1.tenant_id = t2.tenant_id AND t2.tenant_user_type = '1'
                 LEFT JOIN sys_user t3 ON t2.user_id = t3.user_id
                 LEFT JOIN sys_dept t4 on t1.dept_id = t4.dept_id
    </sql>

    <select id="getDetailTenants" resultMap="ExtBaseResultMap">
        <include refid="Deatil_Base_Column_List"/>
        <where>
            <if test="tenantId != null and tenantId != ''">
                and t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="tenantName != null and tenantName != ''">
                and t1.tenant_name = #{tenantName,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null and createUser != ''">
                and t1.create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t1.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and t1.update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t1.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null and remarks != ''">
                and t1.remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and t1.status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
        order by t1.update_time desc
    </select>

    <select id="getDetailTenantInfo" resultMap="ExtBaseResultMap" parameterType="java.lang.String">
        <include refid="Deatil_Base_Column_List"/>
        WHERE t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>

    <select id="getOwnTenantList" resultMap="ExtBaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tenant
        where tenant_id in
        (SELECT t2.tenant_id
        FROM tenant_workspace_user t1
        LEFT JOIN tenant_workspace t2 ON t1.workspace_id = t2.workspace_id
        where t1.user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY t2.tenant_id
        UNION
        SELECT t3.tenant_id
        FROM tenant_user t3
        LEFT JOIN tenant t4 ON t3.tenant_id = t4.tenant_id
        where t3.user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY t3.tenant_id)
        order by update_time
    </select>

    <select id="getAdminTenantList" resultMap="ExtBaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        tenant
        WHERE
        tenant_id IN (
        SELECT
        a0.tenant_id
        FROM
        tenant_workspace a0
        LEFT JOIN tenant_workspace_role a1 ON a0.workspace_id = a1.workspace_id
        LEFT JOIN tenant_workspace_user_role a2 ON a1.workspace_role_id = a2.workspace_role_id
        LEFT JOIN tenant_workspace_user a3 ON a2.user_id = a3.user_id AND a0.workspace_id = a3.workspace_id
        WHERE
        a1.workspace_role_type = '1'
        AND a3.user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY
        a0.tenant_id
        UNION
        SELECT
        t3.tenant_id
        FROM
        tenant_user t3
        LEFT JOIN tenant t4 ON t3.tenant_id = t4.tenant_id
        WHERE
        t3.user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY
        t3.tenant_id
        )
        ORDER BY
        update_time DESC
    </select>
</mapper>
