dependencies {
    api project(':common:high-availability-common')
    api project(':interface:high-availability-base-interface')
    api project(':service:high-availability-system-service')

    api('com.cmpay:lemon-framework-starter-context')
    api('com.cmpay:lemon-framework-starter-idgenerator')
    api('com.cmpay:lemon-framework-starter-datasource')
    api('com.cmpay:lemon-framework-starter-mybatis')
    api('com.cmpay:lemon-framework-starter-cache-jcache')
    api('com.cmpay:file-client-starter')
    api('com.cmpay:lemon-common')
    api('com.cmpay:alerting-starter')

    compileOnly 'org.apache.httpcomponents:httpclient'

}
