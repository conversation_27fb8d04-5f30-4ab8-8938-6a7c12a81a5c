package com.cmpay.hacp.extend.cmft.service.impl;

import com.cmft.api.utils.APIUtils;
import com.cmft.api.utils.dto.SM2KeyDTO;
import com.cmpay.hacp.extend.cmft.bo.CmftDispatchConfigBO;
import com.cmpay.hacp.extend.cmft.dao.ICmftDispatchConfigExtDao;
import com.cmpay.hacp.extend.cmft.entity.CmftDispatchConfigDO;
import com.cmpay.hacp.extend.cmft.service.CmftDispatchConfigService;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.StatusEnum;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class CmftDispatchConfigServiceImpl implements CmftDispatchConfigService {

    @Autowired
    private ICmftDispatchConfigExtDao cmftDispatchConfigDao;

    @Autowired
    private SystemCipherService systemCipherService;

    @Override
    public void saveOrUpdate(CmftDispatchConfigBO entity) {
        if(CommonConstant.ENCRYPTED_DISPLAY.equals(entity.getExternalPrivateKey())){
            entity.setExternalPrivateKey(null);
        }
        if(JudgeUtils.isNotNull(entity.getExternalPrivateKey())){
            entity.setExternalPrivateKey(systemCipherService.decryptData(entity.getUuid(), entity.getExternalPrivateKey()));
        }
        CmftDispatchConfigDO query = new CmftDispatchConfigDO();
        query.setWorkspaceId(entity.getWorkspaceId());
        query.setStatus(StatusEnum.ENABLE.getBooleanValue());
        List<CmftDispatchConfigDO> cmftDispatchConfigDOS = cmftDispatchConfigDao.find(query);
        if(JudgeUtils.isEmpty(cmftDispatchConfigDOS)){
            query = BeanConvertUtil.convert(entity, CmftDispatchConfigDO.class);
            query.setId(null);
            query.setUpdateTime(LocalDateTime.now());
            cmftDispatchConfigDao.insert(query);
            return;
        }
        query.setId(entity.getId());
        cmftDispatchConfigDOS = cmftDispatchConfigDao.find(query);
        if(JudgeUtils.isEmpty(cmftDispatchConfigDOS)){
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_MATCHING);
        }
        query = BeanConvertUtil.convert(entity, CmftDispatchConfigDO.class);
        query.setUpdateTime(LocalDateTime.now());
        cmftDispatchConfigDao.update(query);
    }

    @Override
    public CmftDispatchConfigBO getInfoByWorkspaceId(String workspaceId) {
        CmftDispatchConfigDO query = new CmftDispatchConfigDO();
        query.setWorkspaceId(workspaceId);
        query.setStatus(StatusEnum.ENABLE.getBooleanValue());
        List<CmftDispatchConfigDO> cmftDispatchConfigDOS = cmftDispatchConfigDao.find(query);
        if(JudgeUtils.isEmpty(cmftDispatchConfigDOS)){
            return new CmftDispatchConfigBO();
        }
        return BeanConvertUtil.convert(cmftDispatchConfigDOS.get(0),CmftDispatchConfigBO.class);
    }

    @Override
    public SM2KeyDTO getGenerate() {
        return APIUtils.createKey();
    }
}
