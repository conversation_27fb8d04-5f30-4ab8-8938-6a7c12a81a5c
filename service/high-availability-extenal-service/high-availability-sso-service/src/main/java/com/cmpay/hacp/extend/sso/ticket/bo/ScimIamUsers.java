package com.cmpay.hacp.extend.sso.ticket.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScimIamUsers {

    /**
     * scim的schema，返回字符串数组固定值"urn:ietf:params:scim:api:messages:2.0:ListResponse"。
     */
    private List<String> schemas;

    /**
     * 总记录数
     */
    private int totalResults;


    /**
     * person数组。首字母R大写
     */
    @JsonProperty(value = "Resources")
    private List<ScimIamUser> resources;

    /**
     * 开始指数
     */
    private String startIndex;
    /**
     * 页数
     */
    private String itemsPerPage;

}
