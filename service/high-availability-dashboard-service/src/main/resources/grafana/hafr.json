{
  "editable": false,
  "fiscalYearStartMonth": 0,
  "graphTooltip": 0,
  "links": [],
  "panels": [
    {
      "datasource": {
        "type": "prometheus",
        "uid": "<<datasource.uid>>"
      },
      "description": "PanelType:ZONE",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "fieldMinMax": false,
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              }
            ]
          }
        },
        "overrides": [<<overrides.zone>>]
      },
      "gridPos": {
        "h": 7,
        "w": 20,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "options": {
        "colorMode": "value",
        "graphMode": "area",
        "justifyMode": "auto",
        "orientation": "auto",
        "percentChangeColorMode": "standard",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showPercentChange": false,
        "text": {},
        "textMode": "auto",
        "wideLayout": true
      },
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "<<datasource.uid>>"
          },
          "disableTextWrap": false,
          "editorMode": "code",
          "expr": "sum by(routed_zone) (hafr_http_requests_total{selected_dispatch!=\"\", routed_zone!=\"\", workspace_id=\"$query_workspace\"})",
          "fullMetaSearch": false,
          "includeNullMetadata": true,
          "instant": false,
          "legendFormat": "机房：{{routed_zone}}",
          "range": true,
          "refId": "A",
          "useBackend": false
        }
      ],
      "title": "机房流量",
      "type": "stat"
    },
    {
      "datasource": {
        "type": "prometheus",
        "uid": "<<datasource.uid>>"
      },
      "description": "PanelType:ZONE_PERCENTAGE",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            }
          },
          "mappings": []
        },
        "overrides": [<<overrides.zone>>]
      },
      "gridPos": {
        "h": 7,
        "w": 4,
        "x": 20,
        "y": 0
      },
      "id": 4,
      "options": {
        "displayLabels": [
          "percent"
        ],
        "legend": {
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": true
        },
        "pieType": "donut",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "<<datasource.uid>>"
          },
          "editorMode": "code",
          "expr": "sum by(routed_zone) (hafr_http_requests_total{selected_dispatch!=\"\", routed_zone!=\"\", workspace_id=\"$query_workspace\"})",
          "instant": false,
          "legendFormat": "机房：{{routed_zone}}",
          "range": true,
          "refId": "A"
        }
      ],
      "title": "机房流量百分比",
      "type": "piechart"
    },
    {
      "datasource": {
        "type": "prometheus",
        "uid": "<<datasource.uid>>"
      },
      "description": "PanelType:DISPATCH",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": false,
            "axisCenteredZero": false,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "insertNulls": false,
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "auto",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              }
            ]
          }
        },
        "overrides": [<<overrides.dispatch>>]
      },
      "gridPos": {
        "h": 7,
        "w": 20,
        "x": 0,
        "y": 7
      },
      "id": 6,
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": true
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "<<datasource.uid>>"
          },
          "disableTextWrap": false,
          "editorMode": "code",
          "expr": "sum by(selected_dispatch, routed_zone) (increase(hafr_http_requests_total{selected_dispatch=~\"$query_dispatch\", routed_zone=~\"$query_zone\", workspace_id=\"$query_workspace\"}[$__rate_interval]))",
          "fullMetaSearch": false,
          "includeNullMetadata": true,
          "instant": false,
          "legendFormat": "机房{{routed_zone}}：{{selected_dispatch}}",
          "range": true,
          "refId": "A",
          "useBackend": false
        }
      ],
      "title": "分调度请求数据统计",
      "type": "timeseries"
    },
    {
      "datasource": {
        "type": "prometheus",
        "uid": "<<datasource.uid>>"
      },
      "description": "PanelType:DISPATCH_PERCENTAGE",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            }
          },
          "fieldMinMax": false,
          "mappings": []
        },
        "overrides": [<<overrides.dispatch>>]
      },
      "gridPos": {
        "h": 7,
        "w": 4,
        "x": 20,
        "y": 7
      },
      "id": 8,
      "options": {
        "displayLabels": [
          "percent"
        ],
        "legend": {
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": true
        },
        "pieType": "pie",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "<<datasource.uid>>"
          },
          "editorMode": "code",
          "expr": "sum by(selected_dispatch, routed_zone) (increase(hafr_http_requests_total{selected_dispatch=~\"$query_dispatch\", routed_zone=~\"$query_zone\", workspace_id=\"$query_workspace\"}[$__rate_interval]))",
          "format": "time_series",
          "instant": false,
          "interval": "",
          "legendFormat": "机房{{routed_zone}}：{{selected_dispatch}}",
          "range": true,
          "refId": "A"
        }
      ],
      "title": "分调度请求分机房百分比",
      "type": "piechart"
    },
    {
      "datasource": {
        "type": "prometheus",
        "uid": "<<datasource.uid>>"
      },
      "description": "PanelType:LOCATION",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": false,
            "axisCenteredZero": false,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "insertNulls": false,
            "lineInterpolation": "smooth",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "auto",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              }
            ]
          }
        },
        "overrides": [<<overrides.interface>>]
      },
      "gridPos": {
        "h": 7,
        "w": 24,
        "x": 0,
        "y": 14
      },
      "id": 10,
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": true
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "<<datasource.uid>>"
          },
          "disableTextWrap": false,
          "editorMode": "code",
          "expr": "sum by(routed_zone, location_name) (increase(hafr_http_requests_total{location_name=~\"$query_location\", routed_zone=~\"$query_zone\", workspace_id=\"$query_workspace\"}[$__rate_interval]))",
          "fullMetaSearch": false,
          "includeNullMetadata": true,
          "instant": false,
          "legendFormat": "机房{{routed_zone}}：{{location_name}}",
          "range": true,
          "refId": "A",
          "useBackend": false
        }
      ],
      "title": "分接口请求数据统计",
      "type": "timeseries"
    },
    {
      "datasource": {
        "type": "prometheus",
        "uid": "<<datasource.uid>>"
      },
      "description": "PanelType:AGENT",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": false,
            "axisCenteredZero": false,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "insertNulls": false,
            "lineInterpolation": "smooth",
            "lineStyle": {
              "fill": "solid"
            },
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "auto",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              }
            ]
          }
        },
        "overrides": [
          {
            "matcher": {
              "id": "byFrameRefID",
              "options": "A"
            },
            "properties": [
              {
                "id": "unit",
                "value": "percentunit"
              },
              {
                "id": "custom.thresholdsStyle",
                "value": {
                  "mode": "dashed"
                }
              },
              {
                "id": "thresholds",
                "value": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 0.6
                    }
                  ]
                }
              }
            ]
          },
          {
            "matcher": {
              "id": "byFrameRefID",
              "options": "B"
            },
            "properties": [
              {
                "id": "unit",
                "value": "bytes"
              },
              {
                "id": "custom.thresholdsStyle",
                "value": {
                  "mode": "dashed"
                }
              },
              {
                "id": "thresholds",
                "value": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 1073741824
                    }
                  ]
                }
              },
              {
                "id": "custom.lineStyle",
                "value": {
                  "dash": [
                    10,
                    10
                  ],
                  "fill": "dash"
                }
              }
            ]
          }
          <<overrides.instance>>
        ]
      },
      "gridPos": {
        "h": 7,
        "w": 24,
        "x": 0,
        "y": 21
      },
      "id": 12,
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": true
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "<<datasource.uid>>"
          },
          "disableTextWrap": false,
          "editorMode": "code",
          "expr": "hafr_agent_configs{workspace_id=\"$query_workspace\"}* on(instance) avg by(instance) (process_cpu_usage{instance=~\"$query_instance\"})",
          "fullMetaSearch": false,
          "includeNullMetadata": true,
          "instant": false,
          "legendFormat": "CPU: {{instance}}",
          "range": true,
          "refId": "A",
          "useBackend": false
        },
        {
          "datasource": {
            "type": "prometheus",
            "uid": "<<datasource.uid>>"
          },
          "disableTextWrap": false,
          "editorMode": "code",
          "exemplar": false,
          "expr": "hafr_agent_configs{workspace_id=\"$query_workspace\"}* on(instance) sum by(instance) (jvm_memory_used_bytes{instance=~\"$query_instance\"})",
          "fullMetaSearch": false,
          "hide": false,
          "includeNullMetadata": true,
          "instant": false,
          "legendFormat": "MEM: {{instance}}",
          "range": true,
          "refId": "B",
          "useBackend": false
        }
      ],
      "title": "调度节点信息统计",
      "type": "timeseries"
    }
  ],
  "tags": [],
  "templating": {
    "list": [
      {
        "datasource": {
          "type": "prometheus",
          "uid": "<<datasource.uid>>"
        },
        "definition": "label_values(hafr_http_requests_total,workspace_id)",
        "hide": 0,
        "includeAll": false,
        "label": "项目",
        "multi": false,
        "name": "query_workspace",
        "options": [],
        "query": {
          "qryType": 1,
          "query": "label_values(hafr_http_requests_total,workspace_id)",
          "refId": "PrometheusVariableQueryEditor-VariableQuery"
        },
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "type": "query"
      },
      {
        "current": {
          "selected": true,
          "text": [
            "All"
          ],
          "value": [
            "$__all"
          ]
        },
        "datasource": {
          "type": "prometheus",
          "uid": "<<datasource.uid>>"
        },
        "definition": "label_values(hafr_http_requests_total{workspace_id=\"$query_workspace\"},routed_zone)",
        "hide": 0,
        "includeAll": true,
        "label": "机房",
        "multi": true,
        "name": "query_zone",
        "options": [],
        "query": {
          "qryType": 1,
          "query": "label_values(hafr_http_requests_total{workspace_id=\"$query_workspace\"},routed_zone)",
          "refId": "PrometheusVariableQueryEditor-VariableQuery"
        },
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "type": "query"
      },
      {
        "current": {
          "selected": true,
          "text": [
            "All"
          ],
          "value": [
            "$__all"
          ]
        },
        "datasource": {
          "type": "prometheus",
          "uid": "<<datasource.uid>>"
        },
        "definition": "label_values(hafr_http_requests_total{workspace_id=\"$query_workspace\"},selected_dispatch)",
        "hide": 0,
        "includeAll": true,
        "label": "调度",
        "multi": true,
        "name": "query_dispatch",
        "options": [],
        "query": {
          "qryType": 1,
          "query": "label_values(hafr_http_requests_total{workspace_id=\"$query_workspace\"},selected_dispatch)",
          "refId": "PrometheusVariableQueryEditor-VariableQuery"
        },
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "type": "query"
      },
      {
        "current": {
          "selected": true,
          "text": [
            "All"
          ],
          "value": [
            "$__all"
          ]
        },
        "datasource": {
          "type": "prometheus",
          "uid": "<<datasource.uid>>"
        },
        "definition": "label_values(hafr_http_requests_total{workspace_id=\"$query_workspace\"},location_name)",
        "hide": 0,
        "includeAll": true,
        "label": "接口",
        "multi": true,
        "name": "query_location",
        "options": [],
        "query": {
          "qryType": 1,
          "query": "label_values(hafr_http_requests_total{workspace_id=\"$query_workspace\"},location_name)",
          "refId": "PrometheusVariableQueryEditor-VariableQuery"
        },
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "type": "query"
      },
      {
        "current": {
          "selected": true,
          "text": [
            "All"
          ],
          "value": [
            "$__all"
          ]
        },
        "datasource": {
          "type": "prometheus",
          "uid": "<<datasource.uid>>"
        },
        "definition": "label_values(process_cpu_usage,instance)",
        "hide": 0,
        "includeAll": true,
        "label": "节点",
        "multi": true,
        "name": "query_instance",
        "options": [],
        "query": {
          "qryType": 1,
          "query": "label_values(process_cpu_usage,instance)",
          "refId": "PrometheusVariableQueryEditor-VariableQuery"
        },
        "refresh": 1,
        "regex": "",
        "skipUrlSync": false,
        "sort": 1,
        "type": "query"
      }
    ]
  },
  "time": {
    "from": "now-1h",
    "to": "now"
  },
  "timepicker": {},
  "timezone": "browser",
  "title": "<<dashboard.name>>",
  "weekStart": ""
}
