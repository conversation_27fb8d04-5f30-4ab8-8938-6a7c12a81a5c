package com.cmpay.hacp.dashboard.service.impl;

import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.dashboard.bo.Summary;
import com.cmpay.hacp.dashboard.service.SummaryService;
import com.cmpay.hacp.dispatch.bo.ApiLocationBO;
import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.hacp.dispatch.bo.DispatchNodeBO;
import com.cmpay.hacp.dispatch.bo.DispatchZoneBO;
import com.cmpay.hacp.dispatch.entity.DispatchDO;
import com.cmpay.hacp.dispatch.service.ApiLocationService;
import com.cmpay.hacp.dispatch.service.DispatchNodeService;
import com.cmpay.hacp.dispatch.service.DispatchService;
import com.cmpay.hacp.dispatch.service.DispatchZoneService;
import com.cmpay.hacp.tenant.service.WorkspaceService;
import com.cmpay.lemon.framework.page.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SummaryServiceImpl implements SummaryService {
    private final WorkspaceService workspaceService;
    private final DispatchZoneService zoneService;
    private final DispatchNodeService nodeService;
    private final DispatchService dispatchService;
    private final ApiLocationService locationService;

    public SummaryServiceImpl(WorkspaceService workspaceService,
                              DispatchZoneService zoneService, DispatchNodeService nodeService,
                              DispatchService dispatchService, ApiLocationService locationService) {
        this.workspaceService = workspaceService;
        this.zoneService = zoneService;
        this.nodeService = nodeService;
        this.dispatchService = dispatchService;
        this.locationService = locationService;
    }

    @Override
    public Summary getSummary(String workspaceId) {
        Summary summary = new Summary();
        TenantWorkspaceBO workspaceInfo = workspaceService.getDetailWorkspaceInfo(workspaceId);
        if (workspaceInfo == null) {
            return null;
        }
        summary.setTenantId(workspaceInfo.getTenantId());
        summary.setTenantName(workspaceInfo.getTenantName());
        summary.setWorkspaceId(workspaceInfo.getWorkspaceId());
        summary.setWorkspaceName(workspaceInfo.getWorkspaceName());

        List<DispatchZoneBO> zoneList = zoneService.getSimpleList(workspaceId);
        if (zoneList != null) {
            List<Summary.ZoneInfo> collect = zoneList.stream().map(zone -> {
                Summary.ZoneInfo zoneInfo = new Summary.ZoneInfo();
                zoneInfo.setId(zone.getId());
                zoneInfo.setZoneName(zone.getZoneName());
                zoneInfo.setZoneLabel(zone.getZoneLabel());
                return zoneInfo;
            }).collect(Collectors.toList());
            List<DispatchNodeBO> nodeList = nodeService.getDispatchNodeList(workspaceId);
            if (nodeList != null) {
                nodeList.forEach(node -> collect.forEach(zoneInfo -> {
                    if (Objects.equals(node.getZoneId(), zoneInfo.getId())) {
                        Summary.NodeInfo nodeInfo = new Summary.NodeInfo();
                        nodeInfo.setDispatchNodeId(node.getDispatchNodeId());
                        nodeInfo.setDispatchNodeName(node.getDispatchNodeName());
                        nodeInfo.setRunningDispatchVersion(node.getRunningDispatchVersion());
                        zoneInfo.getNodeList().add(nodeInfo);
                    }
                }));
            }
            summary.setZoneList(collect);
        }

        DispatchBO condition = new DispatchBO();
        condition.setWorkspaceId(workspaceId);
        PageInfo<DispatchBO> dispatchPage = dispatchService.getDispatchList(1, Integer.MAX_VALUE, condition);
        List<DispatchBO> dispatchList = dispatchPage.getList();
        if (dispatchList != null) {
            List<Summary.DispatchInfo> collect = new ArrayList<>();
            for (DispatchDO dispatch : dispatchList) {
                Summary.DispatchInfo dispatchInfo = new Summary.DispatchInfo();
                dispatchInfo.setDispatchId(dispatch.getDispatchId());
                dispatchInfo.setDispatchName(dispatch.getDispatchName());
                dispatchInfo.setIsEmergency(dispatch.getIsEmergency());
                collect.add(dispatchInfo);
            }
            summary.setDispatchList(collect);
        }

        List<ApiLocationBO> locationList = locationService.getApiLocationList(workspaceId);
        if (locationList != null) {
            List<Summary.LocationInfo> collect = locationList.stream().map(location -> {
                Summary.LocationInfo locationInfo = new Summary.LocationInfo();
                locationInfo.setApiLocationId(location.getApiLocationId());
                locationInfo.setApiLocationName(location.getApiLocationName());
                return locationInfo;
            }).collect(Collectors.toList());
            summary.setLocationList(collect);
        }
        return summary;
    }
}
