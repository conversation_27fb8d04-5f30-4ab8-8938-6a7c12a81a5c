package com.cmpay.hacp.emergency.service.camunda.listener;

import com.cmpay.hacp.emergency.service.EmergencyProcessService;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("endEventListenerV1")
public class EndEventListener implements ExecutionListener {
    @Autowired
    private EmergencyProcessService emergencyProcessService;

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        emergencyProcessService.updateState(execution.getBusinessKey(),execution.getTenantId(), HistoricProcessInstance.STATE_COMPLETED);
    }
}
