package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.HacpCaseVariableBO;
import com.cmpay.hacp.emergency.bo.HacpEmergencyCaseBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.HacpProcessDetailBO;
import com.cmpay.hacp.emergency.bo.process.ParseProcessEntity;
import com.cmpay.lemon.framework.page.PageInfo;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @description: 案例服务
 * <AUTHOR>
 * @date 2024/5/16 11:09
 * @version 1.0
 */
public interface HacpCaseService {
    /**
     * 添加
     * @param caseBO BO
     */
    void addCase(HacpEmergencyCaseBO caseBO, MultipartFile multipartFile);

    /**
     * 查询列表
     * @param caseBO 查询条件
     * @param pageNum  当前页数
     * @param pageSize 每页条数
     * @return 信息
     */
    PageInfo<HacpEmergencyCaseBO> queryCaseList(HacpEmergencyCaseBO caseBO, int pageNum, int pageSize);

    /**
     * 获取案例详情
     * @param id
     * @param isDelete 是否查询删除
     * @return
     */
    HacpEmergencyCaseBO getCaseInfo(Long id, boolean isDelete);

    ParseProcessEntity parseProcessSourceByCaseId(Long id);
    List<HacpCaseVariableBO> getDynamicCaseVariables(Long id, boolean hideTaskPasswd);

    List<HacpCaseVariableBO> getAllCaseVariables(String id, boolean hideTaskPasswd);

    List<HacpCaseVariableBO> getDynamicCaseVariables(Map<String, HacpEmergencyTaskBO> tasks);
    /**
     * 删除
     * @param id 主键
     */
    void deleteCase(Long id);

    /**
     * 修改
     * @param caseBO BO
     */
    void updateCase(HacpEmergencyCaseBO caseBO, MultipartFile multipartFile);

    String startProcessInstance(Long caseId, String workspaceId, Map<String, HacpCaseVariableBO> variables, String uuid);

    String getCamundaProperty(String processDefinitionId, String nodeKey);

    String getNodeTaskId(String processDefinitionId, String nodeKey);

    <T extends BaseElement> Map<String, String> getCamundaTaskIds(String processDefinitionId, Class<T> type);

    HacpProcessDetailBO getCaseProcessDetail(String bpmnXml);

    Boolean existRuntimeCase(Long id, String workspaceId);
}
