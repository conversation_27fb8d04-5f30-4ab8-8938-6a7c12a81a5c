package com.cmpay.hacp.emergency.service.camunda.servicetask;

import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.process.ProcessExecuteLogEntity;
import com.cmpay.hacp.emergency.bo.task.HttpRequestBaseBO;
import com.cmpay.hacp.emergency.bo.task.InterfaceParamBO;
import com.cmpay.hacp.emergency.service.EmergencyProcessService;
import com.cmpay.hacp.emergency.service.HacpNodeRecodeService;
import com.cmpay.hacp.emergency.service.camunda.ServiceTaskTemplateDelegate;
import com.cmpay.hacp.emergency.service.factory.AbstractInterfaceStrategyFactory;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.service.HacpCaseService;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.emergency.utils.CommonUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.HttpCookie;
import java.util.Arrays;
import java.util.Map;



/**
 * <AUTHOR>
 * @version 1.0
 * @description: 接口调用
 * @date 2024/5/15 9:56
 */
@Service("doHttpApi")
@Slf4j
public class HttpInterfaceDelegateService  extends ServiceTaskTemplateDelegate {


    public HttpInterfaceDelegateService(HacpCaseService caseService,
            HacpNodeRecodeService nodeRecodeService,
            RuntimeService runtimeService,
            SystemCacheService cacheService,
            EmergencyProcessService emergencyProcessService) {
        super(caseService, nodeRecodeService, runtimeService, cacheService, emergencyProcessService);
    }

    @Override
    protected void processTask() throws IOException {
        ProcessExecuteLogEntity processExecuteLogEntity = logEntity.get();
        HacpEmergencyTaskBO hacpEmergencyTaskBO = taskInfoHolder.get();
        if (JudgeUtils.isNull(hacpEmergencyTaskBO)) {
            BusinessException.throwBusinessException(MsgEnum.TASK_NOT_EXIST);
        }

        // 获取调度ID 可能会是一个JSON格式，则需要进行类型转换
        String taskParam = hacpEmergencyTaskBO.getTaskParam();
        processExecuteLogEntity.appendBuffer("任务执行参数：" + taskParam);
        //将接口参数进行格式化
        InterfaceParamBO paramBO = JsonUtil.strToObject(hacpEmergencyTaskBO.getTaskParam(), InterfaceParamBO.class);
        paramBO.setUrlParam(CommonUtil.listToMap(taskParam, "urlParam"));
        paramBO.setCookies(CommonUtil.listToMap(taskParam, "cookies"));
        paramBO.setHeaders(CommonUtil.listToMap(taskParam, "headers"));
        if (JudgeUtils.isNotEmpty(paramBO.getUrlParam())) {
            if (!paramBO.getRequestUrl().endsWith("?")) {
                paramBO.setRequestUrl(paramBO.getRequestUrl() + "?");
            }
            paramBO.getUrlParam().forEach(x->{
                paramBO.setRequestUrl(paramBO.getRequestUrl() + x.get("name") + "=" + x.get("value") + "&");
            });
            if (paramBO.getRequestUrl().endsWith("&")) {
                paramBO.setRequestUrl(paramBO.getRequestUrl().substring(0, paramBO.getRequestUrl().length() - 1));
            }
        }
        AbstractInterfaceStrategyFactory factory = AbstractInterfaceStrategyFactory.newInstance(paramBO.getSignClass());
        HttpRequestBaseBO httpRequestBaseBO = new HttpRequestBaseBO();
        httpRequestBaseBO.setRequestUrl(paramBO.getRequestUrl());
        processExecuteLogEntity.appendBuffer("接口请求地址：" + httpRequestBaseBO.getRequestUrl());
        httpRequestBaseBO.setContent(paramBO.getBody());
        processExecuteLogEntity.appendBuffer("接口请求参数：" + paramBO.getBody());
        httpRequestBaseBO.setHeaders(CommonUtil.listToMap(paramBO.getHeaders()));
        processExecuteLogEntity.appendBuffer("接口请求头：" + httpRequestBaseBO.getHeaders());
        httpRequestBaseBO.setType(paramBO.getType());
        processExecuteLogEntity.appendBuffer("接口请求方式：" + httpRequestBaseBO.getType());
        Map<String, String> cookies = CommonUtil.listToMap(paramBO.getCookies());
        HttpCookie[] httpCookies = new HttpCookie[cookies.size()];
        int i = 0;
        for (Map.Entry<String, String> entry : cookies.entrySet()) {
            httpCookies[i++] = new HttpCookie(entry.getKey(), entry.getValue());
        }
        processExecuteLogEntity.appendBuffer("接口请求Cookies：" + Arrays.toString(httpCookies));
        httpRequestBaseBO.setCookies(httpCookies);

        processExecuteLogEntity.appendBuffer("开始调用接口");
        HttpResponse response = factory.request(httpRequestBaseBO);
        processExecuteLogEntity.appendBuffer("接口调用完成");
        processExecuteLogEntity.appendBuffer("接口响应信息：");
        processExecuteLogEntity.appendBuffer(response.toString());
        String body = response.body();
        recodeInfo.get().setResultLog(body);
        //检查响应结果
        if (JudgeUtils.isNotBlank(paramBO.getResponseCheckExpression())) {
            ExpressionParser parser = new SpelExpressionParser();
            //#body.get('msgCd')=='HAC00000'
            Expression exp = parser.parseExpression(paramBO.getResponseCheckExpression());
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariable("body", JSON.parseObject(body, Map.class));
            Object value = exp.getValue(context);
            boolean result = value != null && (boolean) value;
            if (!result) {
                traceEnabled = false;
                BusinessException.throwBusinessException(MsgEnum.API_REQUEST_FAILED);
            }
        }
        logEntity.set(processExecuteLogEntity);
    }
}
