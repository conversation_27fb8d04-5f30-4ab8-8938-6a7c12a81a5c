package com.cmpay.hacp.emergency.bo.process;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/20 10:51
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class ProcessInstanceExtDto {
    private String id;
    private String definitionId;
    private String businessKey;
    private String caseInstanceId;
    private boolean isActive;
    private boolean suspended;
    private String tenantId;
}
