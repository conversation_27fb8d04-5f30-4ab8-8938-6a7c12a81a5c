package com.cmpay.hacp.emergency.service.camunda.listener;

import com.cmpay.hacp.emergency.bo.HacpEmergencyNodeRecodeBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.emergency.service.EmergencyProcessService;
import com.cmpay.hacp.emergency.service.HacpNodeRecodeService;
import com.cmpay.hacp.enums.TaskLogStatusEnum;
import com.cmpay.hacp.message.service.MessageNoticeService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.task.Comment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component("userTaskDeleteListenerV1")
public class UserTaskDeleteListener implements TaskListener {
    @Autowired
    private HacpNodeRecodeService hacpNodeRecodeService;

    @Autowired
    private MessageNoticeService messageNoticeService;

    @Autowired
    private EmergencyProcessService emergencyProcessService;
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = BusinessException.class)
    public void notify(DelegateTask delegateTask) {
        if(((TaskEntity)delegateTask).getRevision()>1){
            return;
        }

        List<Comment> taskComments = delegateTask.getExecution()
                .getProcessEngineServices()
                .getTaskService()
                .getTaskComments(delegateTask.getId());
        String resultLog = JudgeUtils.isEmpty(taskComments) ? null:taskComments.get(0).getFullMessage();
        TaskLogStatusEnum status = TaskLogStatusEnum.REJECT;
        if(delegateTask.getDeleteReason().equals(EmergencyConstant.PROCESS_INSTANCE_FAIL_DELETE_COMMENT)){
            resultLog=delegateTask.getDeleteReason();
            status=TaskLogStatusEnum.FAIL;
        }
        if(delegateTask.getDeleteReason().equals(EmergencyConstant.STATE_INTERNALLY_TERMINATED)){
            resultLog=delegateTask.getDeleteReason();
            status=TaskLogStatusEnum.CANCEL;
        }
        long startTime = Long.parseLong(delegateTask.getVariable(EmergencyConstant.INITIATE_DURATION).toString());
        HacpEmergencyNodeRecodeBO recodeBO = new HacpEmergencyNodeRecodeBO();
        recodeBO.setBusinessKey(delegateTask.getExecution().getBusinessKey());
        recodeBO.setWorkspaceId(delegateTask.getTenantId());
        recodeBO.setActivityNodeId(delegateTask.getExecution().getCurrentActivityId());
        recodeBO.setId((Long)delegateTask.getVariable(EmergencyConstant.LOG_ID));
        hacpNodeRecodeService.updateStatus(recodeBO,resultLog,(int) (System.currentTimeMillis() - startTime), status);

        String extraId = delegateTask.getExecution().getBusinessKey() + CommonConstant.COLON + delegateTask.getId();
        messageNoticeService.readMessageExtraId(extraId,delegateTask.getAssignee(),delegateTask.getTenantId());
        emergencyProcessService.updateAuditUserId(delegateTask.getExecution().getBusinessKey(),delegateTask.getTenantId(),"");
        emergencyProcessService.updateState(delegateTask.getExecution().getBusinessKey(),delegateTask.getTenantId(),EmergencyConstant.EXTERNALLY_TERMINATED);
    }
}
