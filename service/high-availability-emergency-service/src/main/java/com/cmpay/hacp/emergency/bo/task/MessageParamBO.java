package com.cmpay.hacp.emergency.bo.task;

import com.cmpay.hacp.message.bo.ExtenalMessageCapable;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.message.enums.MessageTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/09/14 9:06
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageParamBO extends TaskParam implements ExtenalMessageCapable {

    private MessageTypeEnum messageType;

    private String messageContent;

    private String messageTitle;

    private List<UserInfo> messageUserInfos;

    private Map<String,String> extraParams;
    /**
     * 局部通知的，都需要传递该字段
     */
    private String tenantId;
    /**
     * 局部通知的，都需要传递该字段
     */
    private String workspaceId;

    private String extraId;

    @Override
    @JsonIgnore
    public List<String> getMessageUserIds() {
        return Optional.ofNullable(messageUserInfos).orElse(new ArrayList<>()).stream().map(UserInfo::getUserId).collect(Collectors.toList());
    }

    @Data
    public static class UserInfo{
        private String userId;

        private String userName;

        private String fullName;
    }


}
