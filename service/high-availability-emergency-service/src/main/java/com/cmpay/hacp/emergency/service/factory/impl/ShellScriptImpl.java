package com.cmpay.hacp.emergency.service.factory.impl;

import com.cmpay.hacp.emergency.bo.EmergencyHostArchiveBO;
import com.cmpay.hacp.emergency.bo.EmergencyHostBO;
import com.cmpay.hacp.emergency.bo.HacpCaseVariableBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.task.ShellScriptParamBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.emergency.dao.IHacpEmergencyTaskExtDao;
import com.cmpay.hacp.emergency.entity.HacpEmergencyTaskDO;
import com.cmpay.hacp.emergency.service.EmergencyHostArchiveService;
import com.cmpay.hacp.emergency.service.EmergencyHostService;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.emergency.service.factory.TaskStrategyType;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.QuickScriptCommandEnum;
import com.cmpay.hacp.enums.ShellLoginTypeEnum;
import com.cmpay.hacp.capable.TaskEncryptCapable;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.validation.CheckUtils;
import com.cmpay.hacp.emergency.utils.CommonUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.hacp.utils.crypto.SM4EncryptorUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/29 15:36
 * @version 1.0
 */
@Slf4j
@Service(value = ShellScriptImpl.BEAN_NAME)
public class ShellScriptImpl extends AbstractTaskStrategyFactory {
    @Autowired
    private SystemCipherService systemCipherService;
    @Autowired
    private SystemCacheService systemCacheService;
    @Autowired
    private EmergencyHostArchiveService emergencyHostArchiveService;
    @Autowired
    private EmergencyHostService emergencyHostService;
    @Autowired
    private IHacpEmergencyTaskExtDao taskDao;
    @Value("${spring.application.name}")
    private String applicationName;

    protected final static String BEAN_NAME = "shellScriptImpl";

    private static final String shellCode="4";

    public ShellScriptImpl() {
        super(Arrays.asList(
                new TaskStrategyType("4", "Shell脚本调用", BEAN_NAME),
                new TaskStrategyType("6", "系统重启", BEAN_NAME)
        ));
    }

    public static String getScript(String code,String defaultValue) {
        if(shellCode.equals(code)){
            return defaultValue;
        }
        return QuickScriptCommandEnum.getScript(code);
    }

    @Override
    public boolean checkTaskParam(String json) {
        //将JSON转换成应急调度参数，检查参数是否正确
        ShellScriptParamBO paramBO = toTaskParam(json);
        if (paramBO == null) {
            return false;
        }
        //检查脚本参数是否使用了动态参数，且是否一一对应
        if (JudgeUtils.isNull(getScript(taskType.getCode(), null)) && !CommonUtil.checkHasContainParam(paramBO.getScript(), paramBO.getScriptParam())) {
            return false;
        }
        if(paramBO.isDynamicConfig()){
            return true;
        }

        List<ShellScriptParamBO.ConnectParam> connectParam = paramBO.getConnectParam();
        switch (paramBO.getShellLoginType()){
            case APP:
                List<Integer> hostAppIds = paramBO.getHostAppIds();
                return JudgeUtils.isNotEmpty(hostAppIds);
            case TAG:
                List<Integer> hostTagIds = paramBO.getHostTagIds();
                return JudgeUtils.isNotEmpty(hostTagIds);
            case HOST:
                List<Integer> hostIds = paramBO.getHostIds();
                return JudgeUtils.isNotEmpty(hostIds);
            case USERNAME_PASSWORD:
                if(JudgeUtils.isEmpty(connectParam)){
                    return false;
                }
                for (ShellScriptParamBO.ConnectParam x : connectParam) {
                    boolean checked = !JudgeUtils.isBlankAny(x.getIpPort(), x.getUserName()) && CheckUtils.isInValidIpDomainPort(x.getIpPort());;
                    if (!checked) {
                        return false;
                    }
                }
        }
        return true;
    }

    @Override
    public ShellScriptParamBO toTaskParam(String json) {
        ShellScriptParamBO paramBO = JsonUtil.strToObject(json, ShellScriptParamBO.class);
        if (paramBO == null) {
            return null;
        }
        List<ShellScriptParamBO.ConnectParam> connectParam = paramBO.getConnectParam();
        paramBO.setConnectParam(connectParam);
        return paramBO;
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        //检查任务执行参数是否正确
        if (!checkTaskParam(taskInfo.getTaskParam())) {
            log.error("task param is error :{}",taskInfo.getTaskParam());
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
        }
        //设置设置服务任务执行调度
        if (node instanceof ServiceTask) {
            ((ServiceTask)node).setCamundaDelegateExpression("${doShellScript}");
            ((ServiceTask)node).setName(taskInfo.getTaskName());
        }
    }

    @Override
    public boolean isNeedEncrypt() {
        return true;
    }

    @Override
    public boolean isNotDynamicParam(String taskParamJson) {
        ShellScriptParamBO taskParam = JsonUtil.strToObject(taskParamJson, ShellScriptParamBO.class);
        assert taskParam != null;
        if (taskParam.isDynamicConfig()) {
            return false;
        }

        // 首先不是快捷脚本，重启
        // 其次未包含动态参数
        return JudgeUtils.isNotNull(getScript(taskType.getCode(),null)) ||
                (JudgeUtils.isEmpty(taskParam.getScriptParam()) || taskParam.getScriptParam().stream().anyMatch(x -> JudgeUtils.isBlank(x.getName())));
    }

    @Override
    public String toMaskField(String json) {
        ShellScriptParamBO taskParam = JsonUtil.strToObject(json, ShellScriptParamBO.class);

        assert taskParam != null;
        if(!ShellLoginTypeEnum.USERNAME_PASSWORD.equals(taskParam.getShellLoginType())){
            return json;
        }
        if(taskParam.isDynamicConfig()){
            return json;
        }

        List<ShellScriptParamBO.ConnectParam> connectParam = taskParam.getConnectParam();
        taskParam.setConnectParam(connectParam.stream().peek(m -> {
            if(JudgeUtils.isBlank(m.getPassword())){
                return;
            }
            m.setPassword(CommonConstant.ENCRYPTED_DISPLAY);
        }).collect(Collectors.toList()));
        return JsonUtil.objToStr(taskParam);
    }

    @Override
    public String toFieldEncrypt(TaskEncryptCapable taskBO,ExtState extState) {
        if (JudgeUtils.isNull(taskBO.getId())) {
            return encrypt(taskBO.getTaskParam(), taskBO.getUuid(),extState);
        }
        return mergePassword(taskBO, extState);
    }

    @Override
    public void toFieldDecrypt(HacpEmergencyTaskBO taskBO) {
        systemCipherService.otherModuleDecryptPassword(taskBO.getTaskParam());
    }

    @Override
    public void taskParamArchive(HacpCaseVariableBO taskBO,String workspaceId,String businessKey) {
        ShellScriptParamBO taskParam = this.toTaskParam(taskBO.getTaskParam());
        List<Integer> hostTagIds = taskParam.getHostTagIds();
        List<Integer> hostAppIds = taskParam.getHostAppIds();
        List<Integer> hostIds = taskParam.getHostIds();
        List<EmergencyHostBO> hosts=new ArrayList<>();
        if(JudgeUtils.isNotEmpty(hostTagIds)){
            hosts = emergencyHostService.findByTagIds(hostTagIds, workspaceId);
        }
        if (JudgeUtils.isNotEmpty(hostAppIds)){
            hosts = emergencyHostService.findByAppIds(hostAppIds, workspaceId);
        }
        if(JudgeUtils.isNotEmpty(hostIds)){
            hosts = emergencyHostService.findByIds(hostIds, workspaceId);
        }
        if(JudgeUtils.isNotEmpty(hosts)){
            List<EmergencyHostArchiveBO> emergencyHostArchiveBOS = BeanConvertUtil.convertList(hosts, EmergencyHostArchiveBO.class);
            Set<Long> hostIdSet=new HashSet<>();
            emergencyHostArchiveBOS=emergencyHostArchiveBOS.stream().filter(f->{
                if(hostIdSet.contains(f.getHostId())){
                    return false;
                }
                hostIdSet.add(f.getHostId());
                return true;
            }).collect(Collectors.toList());
            emergencyHostArchiveBOS.get(0).setBusinessKey(businessKey);
            emergencyHostArchiveBOS.get(0).setActivityId(taskBO.getActivityId());
            emergencyHostArchiveBOS.get(0).setWorkspaceId(workspaceId);
            emergencyHostArchiveService.addBatchHostArchive(emergencyHostArchiveBOS);
        }
    }

    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_SERVICE_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.SERVICE_TASK_ELEMENT;
    }

    private String encrypt(String json, String uuid,ExtState extState) {
        String sm4RandomSalt = null;
        try {
            // 可能没加密任何东西，但是传递了uuid过来
            sm4RandomSalt = systemCipherService.getSm4RandomSalt(uuid, uuid);
        } catch (BusinessException e) {
            if (e.getMsgCd().equals(MsgEnum.SM4_RANDOM_SALT_CACHE_NULL_ERROR.getMsgCd())) {
                return json;
            }
        }

        ShellScriptParamBO taskParam = JsonUtil.strToObject(json, ShellScriptParamBO.class);

        assert taskParam != null;
        if(!ShellLoginTypeEnum.USERNAME_PASSWORD.equals(taskParam.getShellLoginType())){
            return json;
        }
        if(taskParam.isDynamicConfig()&&JudgeUtils.isEmpty(taskParam.getConnectParam())){
            return json;
        }

        List<ShellScriptParamBO.ConnectParam> connectParam = taskParam.getConnectParam();
        int i = 0;
        for (ShellScriptParamBO.ConnectParam param : connectParam) {
            if (JudgeUtils.isNotNull(param.getId())) {
                param.setId(i++);
                continue;
            }
            param.setId(i++);
            if(JudgeUtils.isBlank(param.getPassword())){
                continue;
            }

            String password = null;
            try {
                String sm4Password = SM4EncryptorUtil.decryptEcb(sm4RandomSalt, param.getPassword());
                //前端sm2公钥加密,后台sm2私钥解密。
                password = SM2EncryptorUtil.decrypt(systemCipherService.getSm2PrivateKey(applicationName), sm4Password);
            } catch (Exception e) {
                BusinessException.throwBusinessException(MsgEnum.WRONG_USERNAME_OR_PASSWORD);
            }
            param.setPassword(systemCipherService.otherModuleEncryptPassword(password));
        }
        if(!ExtState.CASE_START.equals(extState)){
            systemCacheService.delete(CommonConstant.SM4_RANDOM_SALT + uuid + uuid);
        }

        return JsonUtil.objToStr(taskParam);
    }

    private String mergePassword(TaskEncryptCapable taskBO,ExtState extState) {
        HacpEmergencyTaskDO taskDO = taskDao.get(taskBO.getId());
        ShellScriptParamBO oldTaskParam = this.toTaskParam(taskDO.getTaskParam());
        ShellScriptParamBO newTaskParam = this.toTaskParam(taskBO.getTaskParam());

        switch(extState) {
            case CASE_START:
                if(isNotDynamicParam(taskDO.getTaskParam())){
                    return taskDO.getTaskParam();
                }
                if(ShellLoginTypeEnum.USERNAME_PASSWORD.equals(newTaskParam.getShellLoginType())){
                    return this.encrypt(taskBO.getTaskParam(), taskBO.getUuid(),extState);
                }
                return taskBO.getTaskParam();
            case TASK_UPDATE:
                if(!ShellLoginTypeEnum.USERNAME_PASSWORD.equals(newTaskParam.getShellLoginType())){
                    return taskBO.getTaskParam();
                }

                if(newTaskParam.isDynamicConfig()){
                    return taskBO.getTaskParam();
                }

                Map<Integer, String> passwordMap = new HashMap<>();
                if(ShellLoginTypeEnum.USERNAME_PASSWORD.equals(oldTaskParam.getShellLoginType())){
                    passwordMap = new HashMap<>(oldTaskParam.getConnectParam()
                            .stream()
                            .filter(f -> JudgeUtils.isNotBlank(f.getPassword()))
                            .filter(f -> JudgeUtils.isNotNull(f.getId()))
                            .collect(Collectors.toMap(ShellScriptParamBO.ConnectParam::getId, ShellScriptParamBO.ConnectParam::getPassword)));
                }
                return mergeConnectParamAndEncrypt(newTaskParam, passwordMap, taskBO.getUuid(),extState);
                default:
                    log.error("ExtState is error :{}",extState);
                    BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
                    break;
        }
        return null;
    }

    /**
     * 合并连接参数并加密
     *
     * @param shellScriptParamBO shell script param bo
     * @param passwordMap        密码映射
     * @param uuid               UUID
     * @return {@link String}
     */
    private String mergeConnectParamAndEncrypt(ShellScriptParamBO shellScriptParamBO, Map<Integer, String> passwordMap, String uuid,ExtState extState) {
        // 先取为null的，否则后面会改变原始数据
        List<ShellScriptParamBO.ConnectParam> connectNewParam = shellScriptParamBO.getConnectParam().stream()
                .filter(f -> JudgeUtils.isNull(f.getId()))
                .collect(Collectors.toList());

        List<ShellScriptParamBO.ConnectParam> connectParams = shellScriptParamBO.getConnectParam()
                .stream()
                .filter(f -> JudgeUtils.isNotNull(f.getId()))
                .peek(m -> {
                    if (CommonConstant.ENCRYPTED_DISPLAY.equals(m.getPassword()) && passwordMap.containsKey(m.getId())) {
                        m.setPassword(passwordMap.get(m.getId()));
                    } else {
                        m.setId(null);
                    }
                })
                .collect(Collectors.toList());
        connectParams.addAll(connectNewParam);
        shellScriptParamBO.setConnectParam(connectParams);
        return encrypt(JsonUtil.objToStr(shellScriptParamBO), uuid,extState);
    }
}
