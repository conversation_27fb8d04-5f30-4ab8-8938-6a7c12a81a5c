package com.cmpay.hacp.emergency.service.Impl;

import com.cmpay.hacp.emergency.bo.EmergencyEntityTagBO;
import com.cmpay.hacp.capable.EntityTagCapable;
import com.cmpay.hacp.emergency.dao.IEmergencyEntityTagExtDao;
import com.cmpay.hacp.emergency.entity.EmergencyEntityTagDO;
import com.cmpay.hacp.emergency.entity.EmergencyEntityTagDOKey;
import com.cmpay.hacp.emergency.service.EmergencyEntityTagService;
import com.cmpay.hacp.enums.EntityTypeEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/23 14:45
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
public class EmergencyEntityTagServiceImpl implements EmergencyEntityTagService {

    private final IEmergencyEntityTagExtDao employerHostTagExtDao;


    @Override
    public void add(EmergencyEntityTagBO bo) {
        employerHostTagExtDao.insert(bo);
    }

    @Override
    public void delete(EmergencyEntityTagBO bo) {
        EmergencyEntityTagDOKey param = BeanConvertUtil.convert(bo, EmergencyEntityTagDOKey.class);
        employerHostTagExtDao.deleteExt(param);
    }

    @Override
    public void deleteOrAdd(EntityTagCapable capable, EntityTypeEnum entityType) {
        if(JudgeUtils.isEmpty(capable.getTagIds())){
            return;
        }
        EmergencyEntityTagBO emergencyEntityTagBO = new EmergencyEntityTagBO();
        emergencyEntityTagBO.setEntityId(capable.getEntityId());
        emergencyEntityTagBO.setWorkspaceId(capable.getWorkspaceId());
        emergencyEntityTagBO.setEntityType(entityType);
        delete(emergencyEntityTagBO);
        emergencyEntityTagBO.setOperatorId(capable.getOperatorId());
        emergencyEntityTagBO.setOperatorName(capable.getOperatorName());
        for (Integer hostTagId : capable.getTagIds()){
            emergencyEntityTagBO.setTagId(hostTagId);
            add(emergencyEntityTagBO);
        }
    }

    @Override
    public EmergencyEntityTagBO getDetailInfo(EmergencyEntityTagBO bo) {
        EmergencyEntityTagDOKey query = new EmergencyEntityTagDOKey();
        query.setEntityId(bo.getEntityId());
        query.setEntityType(bo.getEntityType());
        query.setTagId(bo.getTagId());
        return BeanConvertUtil.convert(employerHostTagExtDao.get(query), EmergencyEntityTagBO.class);
    }

    @Override
    public List<EmergencyEntityTagBO> getList(EmergencyEntityTagBO bo) {
        List<EmergencyEntityTagDO> list=employerHostTagExtDao.find(bo);
        if(JudgeUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(list, EmergencyEntityTagBO.class);
    }

}
