package com.cmpay.hacp.emergency.bo.process;

import com.cmpay.hacp.emergency.bo.HacpExecuteTaskBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.camunda.bpm.engine.history.HistoricProcessInstance;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/20 10:51
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HistoricProcessInstanceExtDto {
    private String id;
    private String businessKey;
    private String processDefinitionId;
    private String processDefinitionKey;
    private String processDefinitionName;
    private Integer processDefinitionVersion;
    private Date startTime;
    private Date endTime;
    private Date removalTime;
    private Long durationInMillis;
    private String startUserId;
    private String startUserName;
    private String startActivityId;
    private String failActivityId;
    private String deleteReason;
    private String rootProcessInstanceId;
    private String superProcessInstanceId;
    private String processInstanceId;
    private String tenantId;
    private String state;
    private String executionState;
    private List<CurrentTask> currentTaskList;

    private List<HacpExecuteTaskBO> executeTaskList;

    private String caseName;

    private String caseId;

    private String caseDescribe;

    private String bpmnXml;

    private boolean isAssignee;

    private List<Integer> tagIds;

    /**
     * @Fields  总任务数
     */
    private Integer totalTaskNum = 0;
    /**
     * @Fields 未执行数
     */
    private Integer waitTaskNum = 0;
    /**
     * 已执行数量
     */
    private Integer completeTaskNum = 0;
    /**
     * 失败数
     */
    private Integer failedTaskNum = 0;

    @Getter
    @Setter
    @ToString
    public static class CurrentTask{
        private String currentTaskId;

        private String currentTaskName;

        private String assignee;

        private String currentActId;

        private String status;

        private String executeLog;

        private String activityType;

        private Date startTime;

        private Date endTime;

        private Long durationInMillis;

        private String comment;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            CurrentTask that = (CurrentTask) o;
            return Objects.equals(currentActId, that.currentActId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(currentActId);
        }
    }

    public static HistoricProcessInstanceExtDto fromHistoricProcessInstance(HistoricProcessInstance historicProcessInstance) {

        HistoricProcessInstanceExtDto dto = new HistoricProcessInstanceExtDto();

        dto.id = historicProcessInstance.getId();
        dto.businessKey = historicProcessInstance.getBusinessKey();
        dto.processDefinitionId = historicProcessInstance.getProcessDefinitionId();
        dto.processDefinitionKey = historicProcessInstance.getProcessDefinitionKey();
        dto.processDefinitionName = historicProcessInstance.getProcessDefinitionName();
        dto.processDefinitionVersion = historicProcessInstance.getProcessDefinitionVersion();
        dto.startTime = historicProcessInstance.getStartTime();
        dto.endTime = historicProcessInstance.getEndTime();
        dto.removalTime = historicProcessInstance.getRemovalTime();
        dto.durationInMillis = historicProcessInstance.getDurationInMillis();
        dto.startUserId = historicProcessInstance.getStartUserId();
        dto.startActivityId = historicProcessInstance.getStartActivityId();
        dto.deleteReason = historicProcessInstance.getDeleteReason();
        dto.rootProcessInstanceId = historicProcessInstance.getRootProcessInstanceId();
        dto.superProcessInstanceId = historicProcessInstance.getSuperProcessInstanceId();
        dto.tenantId = historicProcessInstance.getTenantId();
        dto.state = historicProcessInstance.getState();

        return dto;
    }

}
