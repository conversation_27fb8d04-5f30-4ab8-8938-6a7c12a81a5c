package com.cmpay.hacp.emergency.service.camunda.Impl;

import cn.hutool.core.collection.CollUtil;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.emergency.bo.*;
import com.cmpay.hacp.emergency.bo.process.*;
import com.cmpay.hacp.emergency.service.*;
import com.cmpay.hacp.emergency.service.camunda.ProcessCustomSqlService;
import com.cmpay.hacp.emergency.service.camunda.ProcessInstanceService;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.tenant.entity.ActRuExecutionDO;
import com.cmpay.hacp.tenant.entity.ActRuTaskDO;
import com.cmpay.hacp.enums.BpmHighlightStyleEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.TaskLogStatusEnum;
import com.cmpay.hacp.emergency.service.camunda.ProcessDefinitionService;
import com.cmpay.hacp.emergency.service.camunda.ProcessTaskService;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hacp.utils.date.DateUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.history.*;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.rest.dto.history.HistoricVariableInstanceDto;
import org.camunda.bpm.engine.rest.dto.task.TaskDto;
import org.camunda.bpm.engine.rest.exception.InvalidRequestException;
import org.camunda.bpm.engine.runtime.ActivityInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.RestartProcessInstanceBuilder;
import org.camunda.bpm.engine.runtime.VariableInstance;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.ws.rs.core.Response;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.cmpay.hacp.constant.EmergencyConstant.*;
import static org.camunda.bpm.engine.history.HistoricProcessInstance.STATE_ACTIVE;
import static org.camunda.bpm.engine.history.HistoricProcessInstance.STATE_INTERNALLY_TERMINATED;
import static org.camunda.bpm.engine.rest.dto.AbstractQueryDto.SORT_ORDER_DESC_VALUE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/15 9:56
 */
@Service
@Slf4j
public class ProcessInstanceServiceImpl implements ProcessInstanceService {

    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HacpTaskService hacpTaskService;
    @Autowired
    private ProcessTaskService processTaskService;
    @Autowired
    private ProcessCustomSqlService processCustomSqlService;
    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private HacpNodeRecodeService recodeService;
    @Autowired
    private SystemUserService systemUserService;
    @Autowired
    private SystemCacheService cacheService;
    @Autowired
    private HacpNodeRecodeService nodeRecodeService;
    @Autowired
    private HacpCaseService hacpCaseService;

    @Autowired
    private EmergencyProcessService emergencyProcessService;

    @Autowired
    private EmergencyVariableService emergencyVariableService;


    @Override
    @Async
    public ProcessInstance startProcessInstanceByDeployId(StartProcessBO startProcessBO) {
        // 获得流程定义
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery()
                .tenantIdIn(startProcessBO.getWorkspaceId())
                .deploymentId(startProcessBO.getDeploymentId())
                .singleResult();
        // 发起流程
        if (definition.isSuspended()) {
            BusinessException.throwBusinessException(MsgEnum.PROCESS_IS_SUSPENDED);
        }
        Map<String, Object> variables = startProcessBO.getVariables();
        if (CollUtil.isEmpty(variables)) {
            variables = new HashMap<>();
        }

        ArrayList<String> group = new ArrayList<>();
        variables.put(EmergencyConstant.PROCESS_INSTANCE_NAME, startProcessBO.getDeploymentName());
        variables.put(EmergencyConstant.INITIATOR + startProcessBO.getBusinessKey(), startProcessBO.getInitiator());
        // 创建流程实例
        identityService.setAuthentication(startProcessBO.getInitiator(), group, startProcessBO.getTenantIds());
//        authorizationService.createAuthorizationQuery().list();
//        Authorization newAuthorization = authorizationService.createNewAuthorization(8);
//        newAuthorization.setUserId(startProcessBO.getInitiator());
        //identityService.setAuthentication(newAuthorization);
        return runtimeService.startProcessInstanceById(definition.getId(), startProcessBO.getBusinessKey(), variables);
    }

    @Override
    public PageInfo<HistoricProcessInstanceExtDto> queryInstanceList(HistoricProcessInstanceQueryExtDto queryDto) {
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery().tenantIdIn(TenantUtils.getWorkspaceId());
        if (JudgeUtils.isNotNull(queryDto.getCaseTagId())) {
//            VariableQueryParameterDto variableQueryParameterDto = new VariableQueryParameterDto();
//            variableQueryParameterDto.setName(EmergencyConstant.CASE_TAG_ID);
//            variableQueryParameterDto.setValue("["+queryDto.getCaseTagId()+"]");
//            variableQueryParameterDto.setOperator(VariableQueryParameterDto.EQUALS_OPERATOR_NAME);
//            ArrayList<VariableQueryParameterDto> variableQueryParameter = new ArrayList<>();
//            variableQueryParameter.add(variableQueryParameterDto);
//            queryDto.setVariables(variableQueryParameter);
            Set<String> processIds = processCustomSqlService.queryHiVarinstTagId(queryDto.getCaseTagId()+"|");
            if (JudgeUtils.isEmpty(processIds)) {
                List<HistoricProcessInstanceExtDto> historicProcessInstanceDtoResults = new ArrayList<>();
                PageInfo<HistoricProcessInstanceExtDto> pageInfo = new PageInfo<>(historicProcessInstanceDtoResults);
                pageInfo.setTotal(0);
                return pageInfo;
            }
            query.processInstanceIds(processIds);
        }

        queryDto.applyFilters(query);
        if (!queryDto.sortOptionsValid()) {
            throw new InvalidRequestException(Response.Status.BAD_REQUEST,
                    "Only a single sorting parameter specified. sortBy and sortOrder required");
        }
        queryDto.setSortBy("startTime");
        queryDto.setSortOrder(SORT_ORDER_DESC_VALUE);
        queryDto.applySortingOptions(query);
        List<HistoricProcessInstance> matchingHistoricProcessInstances;
        if (queryDto.getPageNum() != null || queryDto.getPageSize() != null) {
            matchingHistoricProcessInstances = executePaginatedQuery(query, queryDto.getPageNum(), queryDto.getPageSize());
        } else {
            matchingHistoricProcessInstances = query.list();
        }

        List<HistoricProcessInstanceExtDto> historicProcessInstanceDtoResults = new ArrayList<>();
        for (HistoricProcessInstance historicProcessInstance : matchingHistoricProcessInstances) {
            HistoricProcessInstanceExtDto resultHistoricProcessInstanceDto = HistoricProcessInstanceExtDto.fromHistoricProcessInstance(
                    historicProcessInstance);
            //如果流程未结束，则获取正在执行的步骤
            if (JudgeUtils.equals(resultHistoricProcessInstanceDto.getState(), STATE_ACTIVE)) {
                List<VariableInstance> list = runtimeService.createVariableInstanceQuery()
                        .tenantIdIn(TenantUtils.getWorkspaceId())
                        .variableName(CASE_INFO + historicProcessInstance.getBusinessKey())
                        .list();
                if(JudgeUtils.isNotEmpty(list)){
                    HacpEmergencyCaseBO caseBO = JsonUtil.strToObject(list.get(0).getValue().toString(), HacpEmergencyCaseBO.class);
                    resultHistoricProcessInstanceDto.setTagIds(caseBO.getTagIds());
                    resultHistoricProcessInstanceDto.setCaseDescribe(caseBO.getCaseDescribe());
                }
                //判断是否为流程审批人
                resultHistoricProcessInstanceDto.setAssignee(isAssigneeTask(resultHistoricProcessInstanceDto.getBusinessKey(), SecurityUtils.getLoginUserId()));
                //判断是否是异常重试节点
//                HacpEmergencyNodeRecodeBO executeErrorRecode = recodeService.getExecuteLastRecode(
//                        historicProcessInstance.getRootProcessInstanceId());
//                if (JudgeUtils.isNotNull(executeErrorRecode)) {
//                    List<HistoricProcessInstanceExtDto.CurrentTask> currentTask = toCurrentTask(executeErrorRecode);
//                    resultHistoricProcessInstanceDto.setCurrentTaskList(currentTask);
//                    //异常情况判断是否为委托人
//                    resultHistoricProcessInstanceDto.setAssignee(isTaskOwner(resultHistoricProcessInstanceDto.getId(), SecurityUtils.getLoginUserId()));
//                } else {
                //获取当前执行步骤   2024.10.09--去掉当前步骤
//                    org.camunda.bpm.engine.task.Task task = taskService.createTaskQuery().processInstanceId(historicProcessInstance.getRootProcessInstanceId())
//                            .active().list().get(0);
//                    List<HistoricProcessInstanceExtDto.CurrentTask> currentTask = toCurrentTask(task);
//                    resultHistoricProcessInstanceDto.setCurrentTaskList(currentTask);
                //}
            } else {
                List<HistoricVariableInstance> list = historyService.createHistoricVariableInstanceQuery()
                        .tenantIdIn(TenantUtils.getWorkspaceId())
                        .variableName(CASE_INFO + historicProcessInstance.getBusinessKey())
                        .list();
                if(JudgeUtils.isNotEmpty(list)){
                    HacpEmergencyCaseBO caseBO = JsonUtil.strToObject(list.get(0).getValue().toString(), HacpEmergencyCaseBO.class);
                    resultHistoricProcessInstanceDto.setTagIds(caseBO.getTagIds());
                    resultHistoricProcessInstanceDto.setCaseDescribe(caseBO.getCaseDescribe());
                }
                if (JudgeUtils.equals(resultHistoricProcessInstanceDto.getState(), STATE_INTERNALLY_TERMINATED)) {
                      //2024.10.09--去掉当前步骤
//                    HistoricActivityInstance activityInstance = historyService.createHistoricActivityInstanceQuery()
//                            .processInstanceId(historicProcessInstance.getRootProcessInstanceId()).orderByHistoricActivityInstanceStartTime().desc().list().get(0);

//                    ArrayList<HistoricProcessInstanceExtDto.CurrentTask> currentTasks = new ArrayList<>();
//                    HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
//                    currentTask.setCurrentActId(activityInstance.getActivityId());
//                    currentTask.setCurrentTaskName(activityInstance.getActivityName());
//                    currentTask.setActivityType(activityInstance.getActivityType());
//                    currentTasks.add(currentTask);
//                    resultHistoricProcessInstanceDto.setCurrentTaskList(currentTasks);
                    //判断是否为委托人
                    resultHistoricProcessInstanceDto.setAssignee(isTaskOwner(resultHistoricProcessInstanceDto.getId(), SecurityUtils.getLoginUserId()));
                }
            }
            historicProcessInstanceDtoResults.add(resultHistoricProcessInstanceDto);
        }
        PageInfo<HistoricProcessInstanceExtDto> pageInfo = new PageInfo<>(historicProcessInstanceDtoResults);
        pageInfo.setTotal(query.count());
        return pageInfo;
    }

    private void processTasksAndApproves(HistoricProcessInstanceExtDto instanceExtDto) {
        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(instanceExtDto.getProcessDefinitionId());
        // 如果流程正在进行，获取当前已执行和执行过的的步骤（任务）
        getProcessTaskDetail(instanceExtDto);
        //过滤开始结束等节点得到任务节点
        List<HistoricProcessInstanceExtDto.CurrentTask> currentTaskList = instanceExtDto.getCurrentTaskList();
        ArrayList<HacpExecuteTaskBO> executeTasks = currentTaskList.stream().map(
                            x -> BeanUtils.copyPropertiesReturnDest(new HacpExecuteTaskBO(), x))
                    .filter(x -> JudgeUtils.equalsAny(x.getActivityType(), "userTask", "serviceTask")).collect(Collectors.toCollection(ArrayList::new));
        Map<String, HacpExecuteTaskBO>  executeTaskMap = executeTasks.stream()
                    .collect(Collectors.toMap(HacpExecuteTaskBO::getCurrentActId, v -> v));
        instanceExtDto.setExecuteTaskList(executeTasks);
        Collection<StartEvent> startEvents = bpmnModelInstance.getModelElementsByType(StartEvent.class);
        for (StartEvent startEvent : startEvents) {
            HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
            currentTask.setCurrentActId(startEvent.getId());
            currentTask.setCurrentTaskName(startEvent.getName());
            currentTask.setStatus(BpmHighlightStyleEnum.COMPLETE.getStatus());
            instanceExtDto.getCurrentTaskList().add(currentTask);
        }
        if(EmergencyConstant.STATE_COMPLETED.equals(instanceExtDto.getState())){
            Collection<EndEvent> endEvents = bpmnModelInstance.getModelElementsByType(EndEvent.class);
            for (EndEvent endEvent : endEvents) {
                HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
                currentTask.setCurrentActId(endEvent.getId());
                currentTask.setCurrentTaskName(endEvent.getName());
                currentTask.setStatus(BpmHighlightStyleEnum.COMPLETE.getStatus());
                instanceExtDto.getCurrentTaskList().add(currentTask);
            }
        }

        //将未执行的任务节点添加进去
        Collection<Task> modelElementsByType = bpmnModelInstance.getModelElementsByType(Task.class);
        boolean flag = true;
        for (Task task : modelElementsByType) {
            if (instanceExtDto.getExecuteTaskList().stream().anyMatch(x -> JudgeUtils.equals(x.getCurrentActId(), task.getId()))) {
                continue;
            }
            HacpExecuteTaskBO executeTaskBO = new HacpExecuteTaskBO();
            Object variable = cacheService.getValue(instanceExtDto.getBusinessKey() + EmergencyConstant.EXECUTE_TASK_STATE + task.getId());

            // 缓存失效后重新构造缓存
            if(JudgeUtils.isNull(variable)&&flag){
                nodeRecodeService.buildCache(instanceExtDto.getBusinessKey());
                variable = cacheService.getValue(instanceExtDto.getBusinessKey() + EmergencyConstant.EXECUTE_TASK_STATE + task.getId());
                flag=false;
            }
            //当历史数据在执行失败之后，失败节点回滚，然后服务重启之后缓存失效了，此时需要补充查失败的数据
            if (JudgeUtils.isNotNull(variable)) {
                String[] split = variable.toString().split("\\|");
                if (JudgeUtils.isNotEmpty(split) && JudgeUtils.equals(TaskLogStatusEnum.ONGOING.getCode(), split[0])) {
                    HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
                    currentTask.setCurrentActId(task.getId());
                    currentTask.setCurrentTaskName(task.getName());
                    currentTask.setStatus(BpmHighlightStyleEnum.ONGOING.getStatus());
                    instanceExtDto.getCurrentTaskList().add(currentTask);
                    executeTaskBO.setStatus(BpmHighlightStyleEnum.ONGOING.getStatus());
                    executeTaskBO.setCurrentTaskName(task.getName());
                    executeTaskBO.setStartTime(DateUtil.timeStrToDate(split[1]));
                    instanceExtDto.setExecutionState("0");
                } else {
                    executeTaskBO.setStatus(TaskLogStatusEnum.getEnumByCode(split[0]).getStyleCode().getStatus());
                }
            } else {
                executeTaskBO.setStatus(BpmHighlightStyleEnum.NO_MARKER.getStatus());
            }

            if(executeTaskBO.getStatus().equals(BpmHighlightStyleEnum.ONGOING.getStatus())&&!executeTaskMap.containsKey(executeTaskBO.getCurrentActId())){
                instanceExtDto.getCurrentTaskList().add(BeanConvertUtil.convert(executeTaskBO, HistoricProcessInstanceExtDto.CurrentTask.class));
            }

            executeTaskBO.setCurrentActId(task.getId());
            instanceExtDto.getExecuteTaskList().add(executeTaskBO);
        }
        instanceExtDto.setTotalTaskNum(bpmnModelInstance.getModelElementsByType(Task.class).size());
    }

    private List<HistoricProcessInstanceExtDto.CurrentTask> toCurrentTask(HacpEmergencyNodeRecodeBO recode) {
        ArrayList<HistoricProcessInstanceExtDto.CurrentTask> currentTasks = new ArrayList<>();
        HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
        currentTask.setCurrentActId(recode.getActivityNodeId());
        currentTask.setCurrentTaskId(recode.getTaskId().toString());
        currentTask.setCurrentTaskName(recode.getTaskName());
        currentTask.setStatus(TaskLogStatusEnum.getEnumByCode(recode.getExecuteResult()).getStyleCode().getStatus());
        //currentTask.setExecuteLog(recode.getResultLog());
        currentTask.setActivityType("serviceTask");
        currentTask.setStartTime(Date.from(recode.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
        currentTask.setEndTime(Date.from(recode.getCreateTime().plus(recode.getDuration(), ChronoUnit.MILLIS).atZone(ZoneId.systemDefault()).toInstant()));
        currentTask.setDurationInMillis(Long.parseLong(String.valueOf(recode.getDuration())));
        currentTasks.add(currentTask);
        return currentTasks;
    }

    private List<HistoricProcessInstanceExtDto.CurrentTask> toCurrentTask(org.camunda.bpm.engine.task.Task task) {
        ArrayList<HistoricProcessInstanceExtDto.CurrentTask> currentTasks = new ArrayList<>();
        HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
        currentTask.setCurrentActId(task.getId());
        currentTask.setCurrentTaskId("");
        currentTask.setCurrentTaskName(task.getName());
        currentTask.setStatus(BpmHighlightStyleEnum.TODO.getStatus());
        currentTask.setExecuteLog("");
        currentTask.setActivityType("userTask");
        currentTasks.add(currentTask);
        return currentTasks;
    }


    private List<HistoricProcessInstance> executePaginatedQuery(HistoricProcessInstanceQuery query,
                                                                Integer pageNum,
                                                                Integer pageSize) {
        if (pageNum == null) {
            pageNum = 0;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        return query.listPage((pageNum - 1) * pageSize, pageSize);
    }

    @Override
    public HistoricProcessInstanceExtDto getProcessInstanceById(String businessKey) {
        EmergencyProcessBO emergencyProcessBO = emergencyProcessService.getDetailInfoByBusKey(businessKey, TenantUtils.getWorkspaceId());

        if (JudgeUtils.isNull(emergencyProcessBO)) {
            BusinessException.throwBusinessException(MsgEnum.PROCESS_IS_NOT_EXIST);
        }
        HistoricProcessInstanceExtDto instanceExtDto = new HistoricProcessInstanceExtDto();
        instanceExtDto.setCaseDescribe(emergencyProcessBO.getCaseDesc());
        instanceExtDto.setCaseName(emergencyProcessBO.getCaseTitle());
        instanceExtDto.setProcessDefinitionName(emergencyProcessBO.getCaseTitle());
        instanceExtDto.setTagIds(emergencyProcessBO.getTagIds());
        instanceExtDto.setState(emergencyProcessBO.getState());
        instanceExtDto.setBusinessKey(emergencyProcessBO.getBusinessKey());
        instanceExtDto.setStartTime(DateUtil.localDateTimeToDateTime(emergencyProcessBO.getCreateTime()));
        instanceExtDto.setStartUserId(systemUserService.getUserInfo(emergencyProcessBO.getStartUser()).getFullName());
        instanceExtDto.setProcessDefinitionId(emergencyProcessBO.getProcessDefId());
        //获取流程定义xml
        instanceExtDto.setBpmnXml(processDefinitionService.getDefinitionXml(emergencyProcessBO.getProcessDefId())
                .getBpmn20Xml());

        //如果状态为正在进行中，则获取当前进行中的任务节点
        processTasksAndApproves(instanceExtDto);

        if (JudgeUtils.isNotNull(emergencyProcessBO.getEndTime())) {
            instanceExtDto.setEndTime(DateUtil.localDateTimeToDateTime(emergencyProcessBO.getEndTime()));
        }
        return instanceExtDto;
    }

    @Override
    public ProcessInstanceExtDto getRuntimeProcessInstanceById(String businessKey) {
        List<ActRuExecutionDO> ruExecutions = processCustomSqlService.getRuExecutions(businessKey);
        if (JudgeUtils.isEmpty(ruExecutions)) {
            return null;
        }
        ActRuExecutionDO actRuExecutionDO = ruExecutions.get(0);
        ProcessInstanceExtDto processInstanceDto = new ProcessInstanceExtDto();
        processInstanceDto.setId(actRuExecutionDO.getId());
        processInstanceDto.setBusinessKey(actRuExecutionDO.getBusinessKey());
        processInstanceDto.setActive(actRuExecutionDO.getIsActive() == 1);
        processInstanceDto.setTenantId(actRuExecutionDO.getTenantId());
        return processInstanceDto;
    }

    @Override
    public void setRuntimeVariable(HistoricProcessInstanceExtDto processInstanceExtDto) {
        HashMap<String, HacpEmergencyTaskBO> hashMap = new HashMap<>();
        String workspaceId =  TenantUtils.getWorkspaceIdNotNull();
        processInstanceExtDto.getExecuteTaskList().forEach(x->{
            //获取任务动态参数信息
            String taskId = hacpCaseService.getNodeTaskId(processInstanceExtDto.getProcessDefinitionId(), x.getCurrentActId());
            HacpEmergencyTaskBO taskInfo = hacpTaskService.getTaskInfo(Long.parseLong(taskId), false, false);
            EmergencyVariableBO variable = emergencyVariableService.getDetailInfo(processInstanceExtDto.getBusinessKey(),workspaceId,EmergencyConstant.TASK_VARIABLE + x.getCurrentActId());
            AbstractTaskStrategyFactory factory = AbstractTaskStrategyFactory.newInstance(taskInfo.getTaskType());
            if (JudgeUtils.isNotNull(variable)) {
                HacpCaseVariableBO caseVariableBO = JsonUtil.strToObject(variable.getValueJson(), HacpCaseVariableBO.class);
                assert caseVariableBO != null;
                taskInfo.setTaskParam(factory.toMaskField(caseVariableBO.getTaskParam()));
                taskInfo.setTaskName(caseVariableBO.getTaskName());
                taskInfo.setTaskDescribe(caseVariableBO.getTaskDescribe());
                taskInfo.setTaskType(caseVariableBO.getTaskType());
                taskInfo.setTagIds(caseVariableBO.getTagIds());
                taskInfo.setTagNames(caseVariableBO.getTagNames());
            } else {
                taskInfo.setTaskParam(factory.toMaskField(taskInfo.getTaskParam()));
            }
            String status = x.getStatus();
            BeanUtils.copyProperties(x, taskInfo);
            x.setStatus(status);
            x.setActivityType(factory.getActivityType());
            hashMap.put(x.getCurrentActId(), x);
        });
        //排序
        List<HacpEmergencyTaskBO> sort = this.sort(processInstanceExtDto.getProcessDefinitionId(), hashMap);
        Optional<HistoricProcessInstanceExtDto.CurrentTask> first = processInstanceExtDto.getCurrentTaskList()
                .stream()
                .filter(f -> BpmHighlightStyleEnum.ONGOING.getStatus().equals(f.getStatus()))
                .findFirst();

        processInstanceExtDto.getExecuteTaskList().clear();
        for (int i = 0; i < sort.size(); i++) {
            HacpExecuteTaskBO task = (HacpExecuteTaskBO) sort.get(i);
            processInstanceExtDto.getExecuteTaskList().add(task);
            if(i==sort.size()-1){
                continue;
            }
            HacpEmergencyTaskBO hacpEmergencyTaskBO = sort.get(i + 1);
            if(task.getStatus().equals(BpmHighlightStyleEnum.COMPLETE.getStatus())&&hacpEmergencyTaskBO.getStatus().equals(BpmHighlightStyleEnum.NO_MARKER.getStatus())){
                hacpEmergencyTaskBO.setStatus(BpmHighlightStyleEnum.ONGOING.getStatus());
            }
            if(task.getStatus().equals(BpmHighlightStyleEnum.CANCEL.getStatus())&&hacpEmergencyTaskBO.getStatus().equals(BpmHighlightStyleEnum.ONGOING.getStatus())){
                task.setStatus(BpmHighlightStyleEnum.FAIL_SKIP.getStatus());
            }
        }

        if(!first.isPresent()){
            return;
        }

        for (HacpEmergencyTaskBO task: sort) {
            if(CamundaUtil.CAMUNDA_USER_TASK.equals(((HacpExecuteTaskBO) task).getActivityType())){
                continue;
            }
            HistoricProcessInstanceExtDto.CurrentTask  currentTask = BeanConvertUtil.convert((HacpExecuteTaskBO)task, HistoricProcessInstanceExtDto.CurrentTask.class );
            processInstanceExtDto.getCurrentTaskList().add(currentTask);
            if(first.get().getCurrentActId().equals(((HacpExecuteTaskBO)task).getCurrentActId())){
                break;
            }
        }
    }

    /**
     * 通过SQL方式查询
     *
     * @param businessKey
     * @param actId
     * @return
     */
    private ArrayList<HistoricProcessInstanceExtDto.CurrentTask> getCurrentTask2(String businessKey, String actId) {
        List<ActRuExecutionDO> list = processCustomSqlService.getRuExecutions(businessKey);
        //判断结果数量为几个 如果多个表示存在并行网关多人会签等情况
        if (JudgeUtils.isEmpty(list)) {
            return null;
        }
        ArrayList<HistoricProcessInstanceExtDto.CurrentTask> currentTasks = new ArrayList<>();
        list.forEach(x -> {
            ActRuTaskDO actRuTaskDO = processCustomSqlService.getRuTasks(x.getId(), actId, null).get(0);
            // 同一个执行流程可能有多个任务 暂时不考虑多个的情况 待验证 todo
            HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
            currentTask.setCurrentActId(actRuTaskDO.getTaskDefKey());
            currentTask.setCurrentTaskId(actRuTaskDO.getId());
            currentTask.setCurrentTaskName(actRuTaskDO.getName());
            currentTasks.add(currentTask);
        });
        return currentTasks;
    }

    private void getProcessTaskDetail(HistoricProcessInstanceExtDto instanceExtDto) {
        instanceExtDto.setCurrentTaskList(new ArrayList<>());
        List<HacpEmergencyNodeRecodeBO> allRecode = nodeRecodeService.getAllRecodeParam(instanceExtDto.getBusinessKey());
        ArrayList<HistoricProcessInstanceExtDto.CurrentTask> currentTasks = new ArrayList<>();
        if (JudgeUtils.isEmpty(allRecode)) {
            return;
        }

        final boolean[] flag = {true};

        allRecode.forEach(f->{
            HistoricProcessInstanceExtDto.CurrentTask currentTask = new HistoricProcessInstanceExtDto.CurrentTask();
            currentTask.setCurrentTaskName(f.getTaskName());
            currentTask.setCurrentActId(f.getActivityNodeId());
            // currentTask.setCurrentTaskId(f.getId());

            currentTask.setStatus(TaskLogStatusEnum.getEnumByCode(f.getExecuteResult()).getStyleCode().getStatus());
            currentTask.setStartTime(DateUtil.localDateTimeToDateTime(f.getCreateTime()));
            Optional.ofNullable(f.getEndTime()).ifPresent(p->currentTask.setEndTime(DateUtil.localDateTimeToDateTime(p)));
            Optional.ofNullable(f.getDuration()).ifPresent(p->currentTask.setDurationInMillis(Long.valueOf(p)));
            AbstractTaskStrategyFactory abstractTaskStrategyFactory = AbstractTaskStrategyFactory.newInstance(f.getTaskType());
            String activityType = abstractTaskStrategyFactory.getActivityType();
            Object variable = cacheService.getValue(instanceExtDto.getBusinessKey() + EXECUTE_TASK_STATE + f.getActivityNodeId());
            // 缓存失效后重新构造缓存
            if (JudgeUtils.isNull(variable) && flag[0]) {
                nodeRecodeService.buildCache(instanceExtDto.getBusinessKey());
                variable = cacheService.getValue(instanceExtDto.getBusinessKey() + EXECUTE_TASK_STATE + f.getActivityNodeId());
                flag[0] = false;
            }
            if (JudgeUtils.isNotNull(variable)) {
                String[] split = variable.toString().split("\\|");
                currentTask.setStatus(TaskLogStatusEnum.getStatus(split[0], currentTask.getStatus()));
                currentTask.setStartTime(JudgeUtils.equalsAny(split[1],
                        CommonConstant.LEVER,
                        null,
                        "null") ? null : DateUtil.timeStrToDate(split[1]));
                currentTask.setEndTime(JudgeUtils.equalsAny(split[2],
                        CommonConstant.LEVER,
                        null,
                        "null") ? null : DateUtil.timeStrToDate(split[2]));
                currentTask.setDurationInMillis(JudgeUtils.equalsAny(split[3],
                        CommonConstant.LEVER,
                        null,
                        "null") ? null : Long.valueOf(split[3]));
            }
            switch (activityType){
                case CamundaUtil.CAMUNDA_USER_TASK:
                    HacpEmergencyTaskBO taskParam = (HacpEmergencyTaskBO)abstractTaskStrategyFactory.toTaskParam(f.getTaskParam());
                    // JsonUtil.strToObject(f.getTaskParam(),)
                    currentTask.setAssignee(JudgeUtils.isNotNull(taskParam.getTaskOperator())
                            ? systemUserService.getUserInfo(taskParam.getTaskOperator()).getFullName()
                            : taskParam.getTaskOperator());
                    currentTask.setComment(f.getResultLog());
                    currentTask.setActivityType(CamundaUtil.CAMUNDA_USER_TASK);
                    break;
                case CamundaUtil.SERVICE_TASK_ELEMENT:
                    currentTask.setActivityType(CamundaUtil.SERVICE_TASK_ELEMENT);
                    if (JudgeUtils.equals(currentTask.getStatus(), BpmHighlightStyleEnum.CANCEL.getStatus())) {
                        instanceExtDto.setFailActivityId(f.getActivityNodeId());
                        instanceExtDto.setState(STATE_INTERNALLY_TERMINATED);
                    }
                    if (JudgeUtils.equals(currentTask.getStatus(), BpmHighlightStyleEnum.CANCEL.getStatus())) {
                        instanceExtDto.setFailActivityId(f.getActivityNodeId());
                        instanceExtDto.setState(STATE_INTERNALLY_TERMINATED);
                    }
                    break;
                    default:
                        currentTask.setExecuteLog("");
            }
            currentTasks.add(currentTask);
        });

        instanceExtDto.setCurrentTaskList(currentTasks);
    }

    public boolean isAssigneeTask(String businessKey, String assignee) {
        List<org.camunda.bpm.engine.task.Task> list = taskService.createTaskQuery()
                .processInstanceBusinessKey(businessKey)
                .active()
                .taskAssignee(assignee)
                .list();
        // 同意或者拒绝，都会使修订版本+1，所以这里判断是否为第一次执行
        return !list.isEmpty() && ((TaskEntity)list.get(0)).getRevision() < 2;
    }

    private boolean isTaskOwner(String processInstanceId, String assignee) {
        return !taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .active().taskOwner(assignee).list().isEmpty();
    }

    @Override
    public void rejectTask(String processInstanceId) {
        //当前实例
        ActivityInstance activity = runtimeService.getActivityInstance(processInstanceId);
        HistoricActivityInstance lastNode = getLastNode(processInstanceId, activity.getActivityId());
        if (JudgeUtils.isNull(lastNode)) {
            return;
        }
        assert lastNode != null;
        runtimeService.createProcessInstanceModification(processInstanceId)
                //关闭相关任务
                .cancelActivityInstance(activity.getId())
                .setAnnotation("驳回")
                //启动目标活动节点
                .startBeforeActivity(lastNode.getActivityId())
                .setVariable("assignee", lastNode.getAssignee())
                .execute();
    }

    @Override
    public void externallyDeleteProcess(String businessKey,String taskId, String comment) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceBusinessKey(businessKey)
                .tenantIdIn(TenantUtils.getWorkspaceIdNotNull())
                .singleResult();
        runtimeService.restartProcessInstances(processInstance.getProcessInstanceId());
        //当前实例
        runtimeService.deleteProcessInstance(processInstance.getProcessInstanceId(), comment, false, true);
    }

    @Override
    public void setVariable(String executionId, String variableName, Object value) {
        //当前实例
        runtimeService.setVariable(executionId, variableName, value);
    }

    @Override
    public void setVariables(String executionId, Map<String, ?> variables) {
        runtimeService.setVariables(executionId, variables);
    }

    @Override
    public HistoricVariableInstanceDto getVariable(String businessKey, String variableName) {
        List<HistoricVariableInstance> list = historyService
                .createHistoricVariableInstanceQuery()
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .variableName(variableName + businessKey).list();
        if (JudgeUtils.isEmpty(list)) {
            return null;
        }
        return HistoricVariableInstanceDto.fromHistoricVariableInstance(list.get(0));
    }

    @Override
    public HistoricVariableInstanceDto getVariableByProcessInstanceId(String processInstanceId, String variableName) {
        HistoricVariableInstance matchingHistoricVariableInstances = historyService
                .createHistoricVariableInstanceQuery()
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .processInstanceId(processInstanceId)
                .variableName(variableName).singleResult();
        if (JudgeUtils.isNull(matchingHistoricVariableInstances)) {
            return null;
        }
        return HistoricVariableInstanceDto.fromHistoricVariableInstance(matchingHistoricVariableInstances);
    }

    @Override
    public void computeNodeStatus(HistoricProcessInstanceExtDto instanceExtDto) {
        //计算已完成次数
        long complete = instanceExtDto.getCurrentTaskList().stream().filter(x -> JudgeUtils.equalsAny(x.getActivityType(), "userTask", "serviceTask")
                && JudgeUtils.equals(x.getStatus(), BpmHighlightStyleEnum.COMPLETE.getStatus()))
                .map(HistoricProcessInstanceExtDto.CurrentTask::getCurrentActId).distinct().count();

        long fail = instanceExtDto.getCurrentTaskList().stream().filter(x -> JudgeUtils.equalsAny(x.getActivityType(), "userTask", "serviceTask")
                        && JudgeUtils.equalsAny(x.getStatus(), BpmHighlightStyleEnum.CANCEL.getStatus()))
                .map(HistoricProcessInstanceExtDto.CurrentTask::getCurrentActId).distinct().count();

        long failSkip = instanceExtDto.getCurrentTaskList().stream().filter(x -> JudgeUtils.equalsAny(x.getActivityType(), "userTask", "serviceTask")
                        && JudgeUtils.equalsAny(x.getStatus(), BpmHighlightStyleEnum.FAIL_SKIP.getStatus()))
                .map(HistoricProcessInstanceExtDto.CurrentTask::getCurrentActId).distinct().count();

        instanceExtDto.setCompleteTaskNum( Integer.parseInt(String.valueOf(complete)));
        instanceExtDto.setFailedTaskNum( Integer.parseInt(String.valueOf(fail))+Integer.parseInt(String.valueOf(failSkip)));
        int waitTaskNum = instanceExtDto.getTotalTaskNum() - instanceExtDto.getCompleteTaskNum() - instanceExtDto.getFailedTaskNum();

        if (JudgeUtils.equals(instanceExtDto.getState(), "ACTIVE")) {
            instanceExtDto.setWaitTaskNum(waitTaskNum > 0 ? waitTaskNum - 1 : 0);
        } else {
            instanceExtDto.setWaitTaskNum(waitTaskNum);
        }
    }


    private HistoricActivityInstance getLastNode(String processInstanceId, String currentActivityId) {
        // 新建一个有序不重复集合
        //获取所有已办用户任务节点
        ArrayList<HistoricActivityInstance> arrayList = new ArrayList<>(historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .activityType("userTask")
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list());
        if (JudgeUtils.isEmpty(arrayList)) {
            return null;
        }
        //去重
        List<HistoricActivityInstance> distinct = arrayList.stream().distinct().collect(Collectors.toList());
        // 遍历到当前的任务节点后,取上一次遍历的值
        HistoricActivityInstance backTask = null;
        for (HistoricActivityInstance nowTask : distinct) {
            if (JudgeUtils.equals(nowTask.getActivityId(), currentActivityId)) {
                return backTask;
            }
            backTask = nowTask;
        }
        // 如果backTask为最后的元素 则表示当前节点未在历史任务列表中 取历史任务当中的最后一个节点
        return distinct.get(distinct.size() - 1);
    }

    @Override
    public List<HacpEmergencyTaskBO> sort(String processDefinitionId, Map<String, HacpEmergencyTaskBO> taskIds) {
        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(
                processDefinitionId);
        ArrayList<HashMap<String, HacpEmergencyTaskBO>> bpmNodeList = CamundaUtil.getBpmNodeList(bpmnModelInstance, taskIds);
        ArrayList<HacpEmergencyTaskBO> taskBOS = new ArrayList<>();
        bpmNodeList.forEach(x -> x.forEach((k, v) -> taskBOS.add(v)));
        return taskBOS;
    }

    @Override
    public void deleteProcessInstance(String businessKey) {
        if(JudgeUtils.isNull(businessKey)){
            BusinessException.throwBusinessException(MsgEnum.BUSINESS_KEY_IS_NOT_NULL);
        }

        List<HistoricProcessInstance> deleteHistory = historyService
                .createHistoricProcessInstanceQuery()
                .finished()
                .processInstanceBusinessKey(businessKey)
                .tenantIdIn(TenantUtils.getWorkspaceIdNotNull())
                .list();
        historyService.deleteHistoricProcessInstances(deleteHistory.stream().map(HistoricProcessInstance::getId).collect(Collectors.toList()));
    }

    @Override
    public List<org.camunda.bpm.engine.task.Task> queryTaskAssignee() {
        return taskService.createTaskQuery()
                        .taskAssignee(SecurityUtils.getLoginUserId())
                        .tenantIdIn(TenantUtils.getWorkspaceId())
                        .orderByTaskCreateTime().desc().list();
    }

    @Override
    public TaskExecuteLogPage getTaskLog(String businessKey,String activityNodeId, int pageNum) {
        try {
            Object variable = cacheService.getValue(businessKey + EmergencyConstant.EXECUTE_TASK_STATE + activityNodeId);
            if(JudgeUtils.isNotNull(variable)&&variable.toString().split("\\|")[0].equals(TaskLogStatusEnum.ONGOING.getCode())){
                //进行中
                TaskExecuteLogPage runtimeExecuteTaskLog = recodeService.getRuntimeExecuteTaskLog(businessKey,activityNodeId, pageNum);
                runtimeExecuteTaskLog.setTaskStatus(TaskLogStatusEnum.ONGOING.getCode());
                return runtimeExecuteTaskLog;
            }
            TaskExecuteLogPage historyExecuteTaskLog = recodeService.getHistoryExecuteTaskLog(businessKey, activityNodeId,pageNum);
            historyExecuteTaskLog.setTaskStatus("2");
            return historyExecuteTaskLog;

        } catch (ProcessEngineException e) {
            TaskExecuteLogPage historyExecuteTaskLog = recodeService.getHistoryExecuteTaskLog(businessKey, activityNodeId,pageNum);
            historyExecuteTaskLog.setTaskStatus("2");
            return historyExecuteTaskLog;
        }
    }

    @Override
    public void restartProcessInstance(String businessKey,String workspaceId) {
        // 判断该流程是否可以重启
        HistoricProcessInstance historicProcessInstance = historyService
                .createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey(businessKey)
                .tenantIdIn(workspaceId)
                .singleResult();
        if (JudgeUtils.isNull(historicProcessInstance)){
            BusinessException.throwBusinessException(MsgEnum.PROCESS_IS_NOT_EXIST);
        }
        if (JudgeUtils.notEquals(historicProcessInstance.getState(), STATE_INTERNALLY_TERMINATED)) {
            BusinessException.throwBusinessException(MsgEnum.PROCESS_RESTART_FAILED);
        }
        //判断是否中断节点为用户任务
        HistoricActivityInstance activityInstance = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(historicProcessInstance.getRootProcessInstanceId())
                .tenantIdIn(workspaceId)
                .canceled().singleResult();
        RestartProcessInstanceBuilder processInstanceBuilder = runtimeService.restartProcessInstances(historicProcessInstance.getProcessDefinitionId())
                .processInstanceIds(historicProcessInstance.getRootProcessInstanceId())
                .initialSetOfVariables();
        Date startTime = historicProcessInstance.getStartTime();
        //如果在用户任务执行等待了
        if (JudgeUtils.equals(activityInstance.getActivityType(), CamundaUtil.CAMUNDA_USER_TASK)
                && JudgeUtils.notEquals(historicProcessInstance.getDeleteReason(), PROCESS_INSTANCE_FAIL_DELETE_COMMENT) ) {
            processInstanceBuilder.startBeforeActivity(activityInstance.getActivityId()).execute();
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceBusinessKey(historicProcessInstance.getBusinessKey())
                    .tenantIdIn(workspaceId)
                    .singleResult();
            processCustomSqlService.updateHiProinstStartTime(processInstance.getId(), startTime);
            historyService.deleteHistoricProcessInstance(historicProcessInstance.getRootProcessInstanceId());
            return;
        } else {
            processInstanceBuilder.startBeforeActivity("StartEvent_1").execute();
            historyService.deleteHistoricProcessInstance(historicProcessInstance.getRootProcessInstanceId());
        }


        //判断当前流程是否已结束--可能还是执行失败
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceBusinessKey(historicProcessInstance.getBusinessKey())
                .tenantIdIn(workspaceId)
                .singleResult();


        String processInstanceId1 = Optional.ofNullable(processInstance).map(ProcessInstance::getId).orElse(null);

        if (JudgeUtils.isNull(processInstance)) {
            HistoricProcessInstance historicProcessInstance1 = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceBusinessKey(historicProcessInstance.getBusinessKey())
                    .tenantIdIn(workspaceId)
                    .orderByProcessInstanceStartTime().desc().list().get(0);
            //重新返回流程实例id
            processInstanceId1 = historicProcessInstance1.getRootProcessInstanceId();
        }
        processCustomSqlService.updateHiProinstStartTime(processInstanceId1, startTime);
        processCustomSqlService.updateHiProinstUserId(processInstanceId1,historicProcessInstance.getStartUserId());
        nodeRecodeService.updateProcessInstanceId(historicProcessInstance.getRootProcessInstanceId(), processInstanceId1);
    }

    @Override
    public HistoricTaskInstance getHistoryFinishedTaskById(String processInstanceId, String taskId) {
        return historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .taskId(taskId).finished().singleResult();
    }


    @Override
    public String completeTask(String businessKey, String result, String comment, Map<String, String> variables,boolean async) {
        String loginUserId = SecurityUtils.getLoginUserId();
        if(!isAssigneeTask(businessKey,loginUserId)){
            BusinessException.throwBusinessException(MsgEnum.DON_T_DUPLICATE_APPROVALS);
        }

        //获取当前登录用户所执行实例的任务节点
        List<TaskDto> currentTasks = processTaskService.getCurrentTasksById(businessKey);
        if (JudgeUtils.isEmpty(currentTasks) || currentTasks.size() != 1) {
            BusinessException.throwBusinessException(MsgEnum.TASK_NOT_EXIST_OR_FINISHED);
        }
        TaskDto taskDto = currentTasks.get(0);

        if (JudgeUtils.isNotBlank(taskDto.getAssignee())
                && !JudgeUtils.equalsAny(loginUserId, taskDto.getAssignee(), taskDto.getOwner())) {
            BusinessException.throwBusinessException(MsgEnum.TASK_OPERATOR_NOT_YOU);
        }

        if (JudgeUtils.equals(result, APPROVE_REJECT)) {
            //拒绝，直接结束整个流程
            this.externallyDeleteProcess(businessKey, taskDto.getId(),JudgeUtils.isBlank(comment) ? NOT_APPROVE_DEFAULT_COMMENT:comment);
            return businessKey;
        }
        if (JudgeUtils.notEquals(result, APPROVE_PASS)) {
            //审批意见错误
            BusinessException.throwBusinessException(MsgEnum.PARAM_PARSE_ERROR);
        }
        if (JudgeUtils.isNotEmpty(variables)) {
            log.error("{}",taskDto);
            this.setVariables(taskDto.getExecutionId(), variables);
        }
        log.error("{}",taskDto);
        //设置委托任务负责人为发起人
        String startUserLogin = this.getVariable(businessKey, EmergencyConstant.INITIATOR).getValue().toString();
        //完成任务
        if(JudgeUtils.isBlank(comment)){
            comment = APPROVE_DEFAULT_COMMENT;
        }
        processTaskService.setOwner(taskDto.getId(), startUserLogin);
        if(async){
            processTaskService.completeTaskAsync(taskDto, comment);
        }else{
            processTaskService.completeTask(taskDto, comment);
        }
        return businessKey;
    }

    @Override
    public String retryTask(String businessKey,String activityId,Boolean skip) {
        if(JudgeUtils.isTrue(skip,false)){
            nodeRecodeService.updateSkipNode(businessKey,activityId);
        }
        String workspaceId = TenantUtils.getWorkspaceIdNotNull();
        emergencyProcessService.updateState(businessKey,workspaceId,EmergencyConstant.ACTIVE);
        //查询流程是否存在
        ProcessInstanceExtDto runtimeProcess = this.getRuntimeProcessInstanceById(businessKey);
        if (JudgeUtils.isNull(runtimeProcess) || !runtimeProcess.isActive()) {
            // 重启实例
            restartProcessInstance(businessKey, workspaceId);
            List<HacpEmergencyNodeRecodeBO> allRecode = nodeRecodeService.getAllRecode(businessKey);
            List<HacpEmergencyNodeRecodeBO> list = allRecode.stream()
                    .filter(m -> AbstractTaskStrategyFactory.isRetryAutoComplete(m.getTaskType()))
                    .collect(Collectors.toList());
            // 前面用同步，最后一个用异步
            for (int i = 0; i < list.size(); i++) {
                HacpEmergencyNodeRecodeBO hacpEmergencyNodeRecodeBO = list.get(i);
                if(TaskLogStatusEnum.COMPLETE.getCode().equals(hacpEmergencyNodeRecodeBO.getExecuteResult())) {
                    this.completeTask(businessKey, APPROVE_PASS, hacpEmergencyNodeRecodeBO.getResultLog(), null, i == list.size() - 1);
                }
            }
        }
        return businessKey;
    }

}
