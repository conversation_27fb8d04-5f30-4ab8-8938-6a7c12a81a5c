/*
 * @ClassName EmergencyEntityTagDOKey
 * @Description 
 * @version 1.0
 * @Date 2024-09-10 14:21:40
 */
package com.cmpay.hacp.emergency.entity;

import com.cmpay.hacp.enums.EntityTypeEnum;

public class EmergencyEntityTagDOKey {
    /**
     * @Fields entityId 主机ID
     */
    private Long entityId;
    /**
     * @Fields tagId 主机tag ID
     */
    private Integer tagId;
    /**
     * @Fields entityType 
     */
    private EntityTypeEnum entityType;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public Integer getTagId() {
        return tagId;
    }

    public void setTagId(Integer tagId) {
        this.tagId = tagId;
    }

    public EntityTypeEnum getEntityType() {
        return entityType;
    }

    public void setEntityType(EntityTypeEnum entityType) {
        this.entityType = entityType;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }
}