/*
 * @ClassName HacpEmergencyCaseDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-16 10:53:43
 */
package com.cmpay.hacp.emergency.bo;

import com.cmpay.hacp.capable.EntityTagCapable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/16 11:11
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpEmergencyCaseBO implements EntityTagCapable {
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields tenantId 租户编号
     */
    private String tenantId;

    private String workspaceId;
    /**
     * @Fields caseName 任务名称
     */
    private String caseName;
    /**
     * @Fields caseDescribe 任务描述
     */
    private String caseDescribe;
    /**
     * @Fields caseDeployId 部署id
     */
    private String caseDeployId;
    /**
     * @Fields operator 操作人
     */
    private String operator;
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
    /**
     * @Fields status 状态
     */
    private String status;
    /**
     * @Fields 流程定义xml
     */
    private String processDefinitionXml;

    private List<Integer> tagIds;

    private Integer tagId;

    private String entityType;

    private String tagName;

    @Override
    @JsonIgnore
    public String getOperatorId() {
        return this.getOperator();
    }

    @Override
    @JsonIgnore
    public Long getEntityId() {
        return this.getId();
    }


}