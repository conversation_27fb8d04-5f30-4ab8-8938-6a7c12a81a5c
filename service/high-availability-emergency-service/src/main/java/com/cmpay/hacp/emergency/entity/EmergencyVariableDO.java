/*
 * @ClassName EmergencyVariableDO
 * @Description 
 * @version 1.0
 * @Date 2024-12-25 17:53:09
 */
package com.cmpay.hacp.emergency.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class EmergencyVariableDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Integer id;
    /**
     * @Fields businessKey 绑定的业务id
     */
    private String businessKey;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields variableName 参数名
     */
    private String variableName;
    /**
     * @Fields valueJson 参数json值
     */
    private String valueJson;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getVariableName() {
        return variableName;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getValueJson() {
        return valueJson;
    }

    public void setValueJson(String valueJson) {
        this.valueJson = valueJson;
    }
}