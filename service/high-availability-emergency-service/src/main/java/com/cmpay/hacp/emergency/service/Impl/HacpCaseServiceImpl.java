package com.cmpay.hacp.emergency.service.Impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import com.cmpay.hacp.emergency.bo.*;
import com.cmpay.hacp.emergency.bo.process.ParseProcessEntity;
import com.cmpay.hacp.emergency.bo.process.ParseTaskTypeEntity;
import com.cmpay.hacp.emergency.bo.process.StartProcessBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.enums.ListenerTypeEnum;
import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.emergency.enums.RedisKeyTypeEnum;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.emergency.dao.IHacpEmergencyCaseExtDao;
import com.cmpay.hacp.emergency.entity.HacpEmergencyCaseDO;
import com.cmpay.hacp.emergency.service.*;
import com.cmpay.hacp.emergency.service.camunda.ProcessDefinitionService;
import com.cmpay.hacp.emergency.service.camunda.ProcessInstanceService;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.enums.EntityTypeEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.StatusEnum;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.tenant.service.TenantWorkspaceService;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.rest.dto.repository.ProcessDefinitionDiagramDto;
import org.camunda.bpm.engine.rest.dto.repository.ProcessDefinitionDto;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaExecutionListener;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaTaskListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cmpay.hacp.emergency.utils.CamundaUtil.getBpmNodeList;
import static com.cmpay.hacp.emergency.utils.CamundaUtil.setCamundaInstanceType;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/14 15:21
 */
@Service
public class HacpCaseServiceImpl implements HacpCaseService {
    private static final Logger logger = LoggerFactory.getLogger(HacpCaseServiceImpl.class);
    @Autowired
    private IHacpEmergencyCaseExtDao caseDao;
    @Autowired
    private ProcessDefinitionService definitionService;
    @Autowired
    @Lazy
    private ProcessInstanceService instanceService;
    @Autowired
    private TenantWorkspaceService tenantWorkspaceService;
    @Autowired
    private HacpTaskService taskService;
    @Autowired
    private EmergencyEntityTagService emergencyEntityTagService;
    @Autowired
    private  SystemCacheService cacheService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private EmergencyProcessService emergencyProcessService;
    @Autowired
    private EmergencyVariableService emergencyVariableService;

    private final ThreadLocal<List<Integer>> TAG_IDS_HOLDER = new ThreadLocal<>();

    @Override
    public void addCase(HacpEmergencyCaseBO caseBO, MultipartFile multipartFile) {
        TenantWorkspaceBO workspaceInfo = tenantWorkspaceService.getWorkspaceInfo(caseBO.getWorkspaceId());
        if (JudgeUtils.isNull(workspaceInfo)) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_EXIST);
        }

        HacpEmergencyCaseDO caseDO = new HacpEmergencyCaseDO();
        caseBO.setOperator(SecurityUtils.getLoginUserId());
        caseBO.setOperatorName(SecurityUtils.getLoginName());
        BeanUtils.copyProperties(caseDO, caseBO);
        caseDO.setStatus(StatusEnum.ENABLE.getValue());
        caseDO.setTenantId(workspaceInfo.getTenantId());
        caseDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        caseDO.setUpdateTime(DateTimeUtils.getCurrentLocalDateTime());
        //如果不为空则部署流程
        if (JudgeUtils.isNotNull(multipartFile)) {
            //String deploymentId = deploymentBpmn(multipartFile, caseBO, workspaceInfo.getTenantId());
            String deploymentId = deploymentBpmnXml(multipartFile, caseBO, caseBO.getWorkspaceId());
            caseDO.setCaseDeployId(deploymentId);
        }
        if (caseDao.insert(caseDO) != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }

        caseBO.setId(caseDO.getId());
        addCaseTag(caseBO);
    }

    private String deploymentBpmn(MultipartFile multipartFile, HacpEmergencyCaseBO caseBO, String tenantId) {
        BpmnModelInstance modelInstance = definitionService.fileToBpmnInstance(multipartFile, caseBO.getCaseName());
        validateProcessName(modelInstance, caseBO.getCaseName());
        Map<String, List<CamundaProperty>> serviceCamundaProperty = this.getCamundaProperty(modelInstance, ServiceTask.class);
        Map<String, List<CamundaProperty>> userCamundaProperty = this.getCamundaProperty(modelInstance, UserTask.class);
        //流程部署
        Deployment deployment = definitionService.deployDefinition(caseBO.getCaseName(),
                modelInstance,
                tenantId,
                multipartFile.getOriginalFilename());
        if (JudgeUtils.isBlank(deployment.getId())) {
            logger.error("预案部署失败");
            BusinessException.throwBusinessException(MsgEnum.PROCESS_DEPLOY_FAILED);
        }

        //将节点附加属性缓存 流程定义id
        ProcessDefinitionDto processDefinition = definitionService.getProcessDefinition(deployment.getId());
        camundaPropertyToRedis(processDefinition.getId(), serviceCamundaProperty);
        camundaPropertyToRedis(processDefinition.getId(), userCamundaProperty);
        return deployment.getId();
    }

    private void validateProcessName(BpmnModelInstance modelInstance, String definitionName) {
        Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
        if (processes.size() != 1) {
            logger.error("缺少流程元素【Process】");
            BusinessException.throwBusinessException(MsgEnum.CASE_PROCESS_CHECK_FAILED);
        }
        for (Process process : processes) {
            process.setName(definitionName);
            //将流程设置为可用
            if (!process.isExecutable()) {
                process.setExecutable(true);
            }
        }
        Collection<StartEvent> starts = modelInstance.getModelElementsByType(StartEvent.class);
        if (starts.size() != 1) {
            logger.error("缺少流程元素【StartEvent】");
            BusinessException.throwBusinessException(MsgEnum.CASE_PROCESS_NOT_FOUND_START_NODE);
        }
        for (StartEvent startEvent : starts) {
            startEvent.setName("开始");
        }

        Collection<EndEvent> ends = modelInstance.getModelElementsByType(EndEvent.class);
        if (ends.size() != 1) {
            logger.error("缺少流程元素【EndEvent】");
            BusinessException.throwBusinessException(MsgEnum.CASE_PROCESS_NOT_FOUND_END_NODE);
        }
        for (EndEvent endEvent : ends) {
            endEvent.setName("结束");
        }

    }

    private String deploymentBpmnXml(MultipartFile multipartFile, HacpEmergencyCaseBO caseBO, String tenantId) {
        BpmnModelInstance modelInstance = definitionService.fileToBpmnInstance(multipartFile, caseBO.getCaseName());
        validateProcessName(modelInstance, caseBO.getCaseName());
        ParseProcessEntity parseProcessEntity = parseProcessSource(modelInstance);
        //获取新的工作流XML
        String newProcessXml = setCamundaInstanceType(modelInstance, parseProcessEntity.getNodeIds());
        ByteArrayInputStream utf8Stream = IoUtil.toUtf8Stream(newProcessXml);
        BpmnModelInstance newModelInstance = Bpmn.readModelFromStream(utf8Stream);
        Collection<Task> tasks = newModelInstance.getModelElementsByType(Task.class);
        for (Task task : tasks) {
            HacpEmergencyTaskBO taskBO = parseProcessEntity.getTaskIds().get(task.getId());
            AbstractTaskStrategyFactory instance = AbstractTaskStrategyFactory.newInstance(taskBO.getTaskType());
            //处理节点属性
            instance.bpmnTaskFieldHandle(task, taskBO, newModelInstance);
        }
        Collection<EndEvent> endEvents = newModelInstance.getModelElementsByType(EndEvent.class);
        addEndListener(endEvents,newModelInstance);
        Deployment deployment = definitionService.deployDefinition(caseBO.getCaseName(),
                newModelInstance,
                tenantId,
                multipartFile.getOriginalFilename());
        //将节点附加属性缓存 流程定义id
        String processDefinitionId = definitionService.getProcessDefinition(deployment.getId()).getId();
        parseProcessEntity.getTaskIds().forEach((k, v) -> {
            String key = RedisKeyTypeEnum.buildRedisKey(
                    RedisKeyTypeEnum.PROCESS_DEFINITION_PROPERTIES_TASK_ID, processDefinitionId, k);
            Optional.ofNullable(v.getId()).ifPresent(p -> cacheService.setValue(key, String.valueOf(p), 1, TimeUnit.DAYS));
        });
        return deployment.getId();
    }

    /**
     * 添加结束监听器
     * @param endEvents
     * @param newModelInstance
     */
    private static void addEndListener(Collection<? extends Event> endEvents,BpmnModelInstance newModelInstance) {
        for (Event endEvent : endEvents) {
            //设置任务监听器
            ExtensionElements extensionElements = endEvent.getExtensionElements();
            if (extensionElements == null) {
                extensionElements = newModelInstance.newInstance(ExtensionElements.class);
                addListener(endEvent, extensionElements);
                return;
            }

            List<CamundaExecutionListener> listeners =extensionElements.getElementsQuery()
                    .filterByType(CamundaExecutionListener.class)
                    .list()
                    .stream()
                    .filter(f -> JudgeUtils.equalsAny(f.getCamundaEvent(),
                            ListenerTypeEnum.TASK_END_V1.getEvent()))
                    .collect(Collectors.toList());
            if (listeners.isEmpty()) {
                addListener(endEvent, extensionElements);
                return;
            }

            for (CamundaExecutionListener listener : listeners) {
                if (ListenerTypeEnum.EVENT_MAP.containsKey(listener.getCamundaEvent())) {
                    listener.removeAttribute("class");
                    listener.setCamundaDelegateExpression(ListenerTypeEnum.EVENT_MAP.get(listener.getCamundaEvent()).getExpression());
                    extensionElements.addChildElement(listener);
                }
            }
        }
    }

    private static void addListener(Event endEvent, ExtensionElements extensionElements) {
        CamundaExecutionListener camundaTaskListener = extensionElements.addExtensionElement(CamundaExecutionListener.class);
        camundaTaskListener.setCamundaEvent(ListenerTypeEnum.TASK_END_V1.getEvent());
        camundaTaskListener.setCamundaDelegateExpression(ListenerTypeEnum.TASK_END_V1.getExpression());
        endEvent.setExtensionElements(extensionElements);
    }

    private ParseProcessEntity parseProcessSource(BpmnModelInstance bpmnModelInstance) {
        Collection<Task> tasks = bpmnModelInstance.getModelElementsByType(Task.class);
        HashMap<String, ParseTaskTypeEntity> nodeTypeMap = new HashMap<>();
        HashMap<String, HacpEmergencyTaskBO> nodeTaskIdMap = new HashMap<>();
        ParseProcessEntity parseProcessEntity = new ParseProcessEntity();

        TAG_IDS_HOLDER.set(Optional.ofNullable(TAG_IDS_HOLDER.get()).orElse(new ArrayList<>()));
        for (Task task : tasks) {
            String taskId = CamundaUtil.getTaskId(task);
            HacpEmergencyTaskBO taskInfo = taskService.getTaskInfo(Long.parseLong(taskId), false, true);
            if (JudgeUtils.isNull(taskInfo)) {
                BusinessException.throwBusinessException(MsgEnum.TASK_NOT_EXIST);
            }
            TAG_IDS_HOLDER.get().addAll(Optional.ofNullable(taskInfo.getTagIds()).orElse(new ArrayList<>()));
            nodeTaskIdMap.put(task.getId(), taskInfo);
            AbstractTaskStrategyFactory abstractTaskStrategyFactory = AbstractTaskStrategyFactory.newInstance(taskInfo.getTaskType());
            nodeTypeMap.put(task.getId(), new ParseTaskTypeEntity(CamundaUtil.getNodeName(task), abstractTaskStrategyFactory.getBpmTaskType()));
        }
        parseProcessEntity.setTaskIds(nodeTaskIdMap);
        parseProcessEntity.setNodeIds(nodeTypeMap);
        return parseProcessEntity;
    }

    @Override
    public ParseProcessEntity parseProcessSourceByCaseId(Long id) {
        HacpEmergencyCaseBO caseInfo = getCaseInfo(id, false);
        return parseProcessSource(definitionService.getBpmnInstance(definitionService.getProcessDefinition(caseInfo.getCaseDeployId()).getId()));
    }

    public void camundaPropertyToRedis(String processDefinitionId, Map<String, List<CamundaProperty>> camundaProperty) {
        camundaProperty.forEach((k, v) -> {
            String key = RedisKeyTypeEnum.buildRedisKey(
                    RedisKeyTypeEnum.PROCESS_DEFINITION_PROPERTIES_TASK_ID, processDefinitionId, k);
            CamundaProperty taskId = v.stream().filter(x -> JudgeUtils.equals(x.getCamundaName(), EmergencyConstant.TASK_ID))
                    .collect(Collectors.toList()).get(0);
            if (JudgeUtils.isNull(taskId)) {
                return;
            }
            cacheService.setValue(key, taskId.getCamundaValue(), 1, TimeUnit.DAYS);
        });
    }

    @Override
    public String getCamundaProperty(String processDefinitionId, String nodeKey) {
//        String key = RedisKeyTypeEnum.buildRedisKey(RedisKeyTypeEnum.PROCESS_DEFINITION_PROPERTIES_TASK_ID, processDefinitionId, nodeKey);
//        String value = (String) cacheService.getValue(key);
//        if (JudgeUtils.isNotBlank(value)) {
//            return value;
//        }
//        //根据流程定义ID重新解析bpmn资源
//        Map<String, List<CamundaProperty>> userCamundaProperty = getCamundaProperty(processDefinitionId, UserTask.class);
//        Map<String, List<CamundaProperty>> serviceCamundaProperty = getCamundaProperty(processDefinitionId, ServiceTask.class);
//
//        camundaPropertyToRedis(processDefinitionId, serviceCamundaProperty);
//        camundaPropertyToRedis(processDefinitionId, userCamundaProperty);
//        return userCamundaProperty.containsKey(nodeKey) ?
//                userCamundaProperty.get(nodeKey).stream().filter(
//                                x -> JudgeUtils.equals(x.getCamundaName(), EmergencyConstant.TASK_ID)).collect(Collectors.toList())
//                        .get(0).getCamundaValue()
//                : serviceCamundaProperty.get(nodeKey).stream().filter(
//                        x -> JudgeUtils.equals(x.getCamundaName(), EmergencyConstant.TASK_ID)).collect(Collectors.toList())
//                .get(0).getCamundaValue();
        return getNodeTaskId(processDefinitionId, nodeKey);
    }

    @Override
    public String getNodeTaskId(String processDefinitionId, String nodeKey) {
        String key = RedisKeyTypeEnum.buildRedisKey(RedisKeyTypeEnum.PROCESS_DEFINITION_PROPERTIES_TASK_ID, processDefinitionId, nodeKey);
        String value = (String) cacheService.getValue(key);
        if (JudgeUtils.isNotBlank(value)) {
            return value;
        }
        BpmnModelInstance bpmnInstance = definitionService.getBpmnInstance(processDefinitionId);
        ParseProcessEntity parseProcessEntity = parseProcessSource(bpmnInstance);
        String taskId = String.valueOf(parseProcessEntity.getTaskIds().get(nodeKey).getId());
        cacheService.setValue(key, taskId, 1, TimeUnit.DAYS);
        return taskId;
    }

    @Override
    public <T extends BaseElement> Map<String, String> getCamundaTaskIds(String processDefinitionId, Class<T> type) {
//        Map<String, List<CamundaProperty>> camundaProperty = getCamundaProperty(processDefinitionId, type);
//        HashMap<String, String> hashMap = new HashMap<>();
//        camundaProperty.forEach((k, v) -> {
//            CamundaProperty taskId = v.stream().filter(x -> JudgeUtils.equals(x.getCamundaName(), EmergencyConstant.TASK_ID))
//                    .collect(Collectors.toList()).get(0);
//            if (JudgeUtils.isNull(taskId)) {
//                return;
//            }
//            hashMap.put(k, taskId.getCamundaValue());
//        });
        BpmnModelInstance bpmnInstance = definitionService.getBpmnInstance(processDefinitionId);
        Collection<T> tasks = bpmnInstance.getModelElementsByType(type);
        HashMap<String, String> nodeTaskIdMap = new HashMap<>();
        for (T task : tasks) {
            String taskId = CamundaUtil.getTaskId(task);
            nodeTaskIdMap.put(task.getId(), taskId);
        }
        return nodeTaskIdMap;
    }

    @Override
    public HacpProcessDetailBO getCaseProcessDetail(String bpmnXml) {
        ByteArrayInputStream utf8Stream = IoUtil.toUtf8Stream(bpmnXml);
        BpmnModelInstance modelInstance = Bpmn.readModelFromStream(utf8Stream);
        ParseProcessEntity parseProcessEntity = parseProcessSource(modelInstance);
        HacpProcessDetailBO processDetailBO = new HacpProcessDetailBO();
        processDetailBO.setTotalTaskNum(parseProcessEntity.getNodeIds().size());
        processDetailBO.setWaitTaskNum(processDetailBO.getTotalTaskNum());
        processDetailBO.setProcessStatus("WAIT");
        processDetailBO.setVariableList(getDynamicCaseVariables(parseProcessEntity.getTaskIds()));
        processDetailBO.setConfirmTaskNum(processDetailBO.getVariableList().size());
        processDetailBO.setActiveTaskNum(0);
        processDetailBO.setCompleteTaskNum(0);
        processDetailBO.setFailedTaskNum(0);
        ArrayList<HashMap<String, HacpEmergencyTaskBO>> bpmNodeList = getBpmNodeList(modelInstance, parseProcessEntity.getTaskIds());
        processDetailBO.getVariableList().forEach(x -> {
            bpmNodeList.forEach(y -> {
                if (y.containsKey(x.getActivityId())) {
                    y.get(x.getActivityId()).setTaskParamStatus("CONFIRM_PARAM");
                }
            });
        });
        processDetailBO.setTaskIds(bpmNodeList);
        return processDetailBO;
    }

    @Override
    public Boolean existRuntimeCase(Long id, String workspaceId) {
        HacpEmergencyCaseDO entity = new HacpEmergencyCaseDO();
        entity.setId(id);
        entity.setWorkspaceId(workspaceId);
        entity.setStatus(StatusEnum.ENABLE.getValue());
        List<HacpEmergencyCaseDO> hacpEmergencyCaseDOS = caseDao.find(entity);
        if(JudgeUtils.isEmpty(hacpEmergencyCaseDOS)){
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_EXIST);
        }
        String caseDeployId = hacpEmergencyCaseDOS.get(0).getCaseDeployId();
        List<ProcessInstance> list = runtimeService.createProcessInstanceQuery().deploymentId(caseDeployId).active().list();
        return JudgeUtils.isNotEmpty(list);
    }

    private <T extends BaseElement> Map<String, List<CamundaProperty>> getCamundaProperty(String processDefinitionId, Class<T> type) {
        BpmnModelInstance bpmnInstance = definitionService.getBpmnInstance(processDefinitionId);
        return getCamundaProperty(bpmnInstance, type);
    }

    private <T extends BaseElement> Map<String, List<CamundaProperty>> getCamundaProperty(BpmnModelInstance bpmnModelInstance,
                                                                                          Class<T> type) {
        Collection<T> elements = bpmnModelInstance.getModelElementsByType(type);
        HashMap<String, List<CamundaProperty>> hashMap = new HashMap<>();
        TAG_IDS_HOLDER.set(Optional.ofNullable(TAG_IDS_HOLDER.get()).orElse(new ArrayList<>()));
        for (T node : elements) {
            ExtensionElements extensionElements = node.getExtensionElements();
            if (extensionElements == null) {
                hashMap.put(node.getId(), null);
                continue;
            }
            Collection<CamundaProperty> properties = extensionElements.getElementsQuery()
                    .filterByType(CamundaProperties.class)
                    .singleResult()
                    .getCamundaProperties();
            properties.forEach(x -> {
                if (JudgeUtils.equals(x.getCamundaName(), EmergencyConstant.TASK_ID)) {
                    HacpEmergencyTaskBO taskInfo = taskService.getTaskInfo(Long.parseLong(x.getCamundaValue()), false, false);
                    if (JudgeUtils.isNull(taskInfo)) {
                        logger.error("taskInfoHolder is null");
                        BusinessException.throwBusinessException(MsgEnum.TASK_IS_NULL_ERROR);
                    }
                    AbstractTaskStrategyFactory instance = AbstractTaskStrategyFactory.newInstance(taskInfo.getTaskType());
                    //处理节点属性
                    instance.bpmnTaskFieldHandle(node, taskInfo, bpmnModelInstance);

                    TAG_IDS_HOLDER.get().addAll(Optional.ofNullable(taskInfo.getTagIds()).orElse(new ArrayList<>()));

                }
            });
            hashMap.put(node.getId(), new ArrayList<>(properties));
        }
        return hashMap;
    }

    @Override
    public PageInfo<HacpEmergencyCaseBO> queryCaseList(HacpEmergencyCaseBO caseBO, int pageNum, int pageSize) {
        caseBO.setStatus(StatusEnum.ENABLE.getValue());
        caseBO.setEntityType(EntityTypeEnum.CASE.getValue());
        PageInfo<HacpEmergencyCaseBO> pageInfo = PageUtils.pageQueryWithCount(pageNum,
                pageSize,
                () -> caseDao.findExt(caseBO));
        PageInfo<HacpEmergencyCaseBO> boPageInfo = new PageInfo<>(pageInfo.getList());
        boPageInfo.setHasNextPage(pageInfo.isHasNextPage());
        boPageInfo.setTotal(pageInfo.getTotal());
        boPageInfo.setPages(pageInfo.getPages());
        return boPageInfo;
    }

    @Override
    public HacpEmergencyCaseBO getCaseInfo(Long id, boolean isDelete) {
        if (JudgeUtils.isNull(id)) {
            BusinessException.throwBusinessException(MsgEnum.ID_NOT_NULL);
        }
        HacpEmergencyCaseDO caseDO = caseDao.get(id);
        if (JudgeUtils.isNull(caseDO)) {
            logger.error("case is null");
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_EXIST);
        }
        if (!isDelete && JudgeUtils.equals(caseDO.getStatus(), StatusEnum.DISABLE.getValue())) {
            logger.error("case status is 1  -----> value: {}",caseDO.getStatus());
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_EXIST);
        }
        if (JudgeUtils.notEquals(caseDO.getWorkspaceId(), TenantUtils.getWorkspaceIdNotNull())) {
            logger.error("case workspace id is {}  -----> use workspace id value: {}",caseDO.getWorkspaceId(),TenantUtils.getWorkspaceIdNotNull());
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_MATCHING);
        }
        HacpEmergencyCaseBO hacpEmergencyCaseBO = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyCaseBO(), caseDO);
        EmergencyEntityTagBO bo = new EmergencyEntityTagBO();
        bo.setEntityType(EntityTypeEnum.CASE);
        bo.setWorkspaceId(caseDO.getWorkspaceId());
        bo.setEntityId(caseDO.getId());
        List<EmergencyEntityTagBO> list = emergencyEntityTagService.getList(bo);
        if (JudgeUtils.isNotEmpty(list)) {
            hacpEmergencyCaseBO.setTagIds(list.stream().map(EmergencyEntityTagBO::getTagId).collect(Collectors.toList()));
        }
        return hacpEmergencyCaseBO;
    }

    @Override
    public List<HacpCaseVariableBO> getDynamicCaseVariables(Long id, boolean hideTaskPasswd) {
        return getCaseVariables(id, hideTaskPasswd, false);
    }

    @Override
    public List<HacpCaseVariableBO> getAllCaseVariables(String deployId, boolean hideTaskPasswd) {
        ProcessDefinitionDto processDefinition = definitionService.getProcessDefinition(deployId);
        Map<String, String> camundaTaskIds = getCamundaTaskIds(processDefinition.getId(), Task.class);
        ArrayList<HacpCaseVariableBO> list = new ArrayList<>();
        camundaTaskIds.forEach((k, v) -> {
            HacpEmergencyTaskBO taskInfo = taskService.getTaskInfo(Long.parseLong(v), false, hideTaskPasswd);
            HacpCaseVariableBO variableBO = new HacpCaseVariableBO();
            variableBO.setActivityId(k);
            variableBO.setTaskName(taskInfo.getTaskName());
            variableBO.setTaskType(taskInfo.getTaskType());
            variableBO.setTaskId(taskInfo.getId().toString());
            variableBO.setTaskParam(taskInfo.getTaskParam());
            variableBO.setTaskDescribe(taskInfo.getTaskDescribe());
            variableBO.setTagIds(taskInfo.getTagIds());
            variableBO.setTagNames(taskInfo.getTagNames());
            list.add(variableBO);
        });
        return list;
    }

    private ArrayList<HacpCaseVariableBO> getCaseVariables(Long id, boolean hideTaskPasswd, boolean all) {
        HacpEmergencyCaseBO caseInfo = getCaseInfo(id, false);
        if (JudgeUtils.isBlank(caseInfo.getCaseDeployId())) {
            logger.error("case deploy id is null ---->   {}",caseInfo);
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_DEPLOY_BPMN);
        }
        ProcessDefinitionDto processDefinition = definitionService.getProcessDefinition(caseInfo.getCaseDeployId());
        Map<String, String> camundaTaskIds = getCamundaTaskIds(processDefinition.getId(), ServiceTask.class);
        ArrayList<HacpCaseVariableBO> list = new ArrayList<>();
        camundaTaskIds.forEach((k, v) -> {
            HacpEmergencyTaskBO taskInfo = taskService.getTaskInfo(Long.parseLong(v), false, hideTaskPasswd);
            HacpCaseVariableBO variableBO = convertCaseVariable(k, taskInfo, all);
            if (JudgeUtils.isNotNull(variableBO)) {
                list.add(variableBO);
            }
        });
        return list;
    }

    @Override
    public List<HacpCaseVariableBO> getDynamicCaseVariables(Map<String, HacpEmergencyTaskBO> tasks) {
        ArrayList<HacpCaseVariableBO> list = new ArrayList<>();
        tasks.forEach((k, v) -> {
            HacpCaseVariableBO variableBO = convertCaseVariable(k, v, false);
            if (JudgeUtils.isNotNull(variableBO)) {
                list.add(variableBO);
            }
        });
        return list;
    }

    /**
     * @param k
     * @param v
     * @param all 只需要动态参数为false，需要固定与动态的全量为true
     * @return
     */
    private HacpCaseVariableBO convertCaseVariable(String k, HacpEmergencyTaskBO v, boolean all) {
        AbstractTaskStrategyFactory factory = AbstractTaskStrategyFactory.newInstance(v.getTaskType());
        if (!all && factory.isNotDynamicParam(v.getTaskParam())) {
            return null;
        }
        HacpCaseVariableBO variableBO = new HacpCaseVariableBO();
        variableBO.setActivityId(k);
        variableBO.setTaskName(v.getTaskName());
        variableBO.setTaskType(v.getTaskType());
        variableBO.setTaskId(v.getId().toString());
        variableBO.setTaskParam(v.getTaskParam());
        variableBO.setTaskDescribe(v.getTaskDescribe());
        return variableBO;
    }

    @Override
    public void deleteCase(Long id) {
        //查询是否存在
        HacpEmergencyCaseBO caseInfo = getCaseInfo(id, false);
        if (JudgeUtils.isNull(caseInfo) || JudgeUtils.isNull(caseInfo.getId())) {
            logger.error("case id is null ---->   {}",caseInfo);
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_EXIST);
        }
        HacpEmergencyCaseDO caseDO = new HacpEmergencyCaseDO();
        caseDO.setStatus(StatusEnum.DISABLE.getValue());
        caseDO.setId(id);
        caseDO.setWorkspaceId(caseInfo.getWorkspaceId());
        caseDao.update(caseDO);
        definitionService.deleteDefinition(String.valueOf(id));
    }

    @Override
    public void updateCase(HacpEmergencyCaseBO caseBO, MultipartFile multipartFile) {
        TenantWorkspaceBO workspaceInfo = tenantWorkspaceService.getWorkspaceInfo(caseBO.getWorkspaceId());
        if (JudgeUtils.isNull(workspaceInfo)) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_EXIST);
        }
        HacpEmergencyCaseDO caseDO = new HacpEmergencyCaseDO();
        caseBO.setOperator(SecurityUtils.getLoginUserId());
        caseBO.setOperatorName(SecurityUtils.getLoginName());
        BeanUtils.copyProperties(caseDO, caseBO);
        if (JudgeUtils.isNotNull(multipartFile)) {
            //String deploymentId = deploymentBpmn(multipartFile, caseBO, workspaceInfo.getTenantId());
            String deploymentId = deploymentBpmnXml(multipartFile, caseBO, caseBO.getWorkspaceId());
            caseDO.setCaseDeployId(deploymentId);
        }
        caseDO.setUpdateTime(LocalDateTime.now());
        caseDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        caseDO.setWorkspaceId(TenantUtils.getWorkspaceIdNotNull());
        if (caseDao.update(caseDO) != 1) {
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_EXIST);
        }
        // 更新时不将任务的标签再次更新至案例中
        TAG_IDS_HOLDER.remove();
        addCaseTag(caseBO);
    }

    private void addCaseTag(HacpEmergencyCaseBO caseBO) {
        List<Integer> tagIds = Optional.ofNullable(caseBO.getTagIds()).orElse(new ArrayList<>());
        tagIds.addAll(Optional.ofNullable(TAG_IDS_HOLDER.get()).orElse(new ArrayList<>()));
        caseBO.setTagIds(tagIds.stream().distinct().collect(Collectors.toList()));
        emergencyEntityTagService.deleteOrAdd(caseBO,EntityTypeEnum.CASE);
    }

    @Override
    public String startProcessInstance(Long caseId, String workspaceId, Map<String, HacpCaseVariableBO> variables, String uuid) {
        String loginUserId = SecurityUtils.getLoginUserId();
        TenantWorkspaceBO workspaceInfo = tenantWorkspaceService.getWorkspaceInfo(workspaceId);
        if (JudgeUtils.isNull(workspaceInfo)) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_EXIST);
        }
        HacpEmergencyCaseBO caseInfo = getCaseInfo(caseId, false);
        if (JudgeUtils.isNull(caseInfo)) {
            logger.error("case is null ---->   {}",caseInfo);
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_EXIST);
        }
        if (JudgeUtils.isBlank(caseInfo.getCaseDeployId())) {
            logger.error("case deploy id null ---->   {}",caseInfo);
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_DEPLOY_BPMN);
        }
        ProcessDefinitionDiagramDto processDefinitionDiagramDto
                = definitionService.getDefinitionXmlByDeployId(caseInfo.getCaseDeployId());
        if (JudgeUtils.isNull(processDefinitionDiagramDto)) {
            logger.error("case xml is null {}", caseInfo);
            BusinessException.throwBusinessException(MsgEnum.CASE_NOT_DEPLOY_BPMN);
        }

        // 处理任务的参数
        List<HacpCaseVariableBO> oldTasksParam = this.getAllCaseVariables(caseInfo.getCaseDeployId(), false);
        if (JudgeUtils.isNotEmpty(variables)) {
            oldTasksParam.forEach(f -> {
                if (!variables.containsKey(f.getActivityId())) {
                    // 如果不包含该任务，则将任务参数作为副本添加到参数中
                    return;
                }
                HacpCaseVariableBO hacpCaseVariableBO = variables.get(f.getActivityId());
                AbstractTaskStrategyFactory factory = AbstractTaskStrategyFactory.newInstance(hacpCaseVariableBO.getTaskType());
                factory.checkTaskParam(hacpCaseVariableBO.getTaskParam());
                hacpCaseVariableBO.setUuid(uuid);
                hacpCaseVariableBO.setId(Long.valueOf(hacpCaseVariableBO.getTaskId()));
                f.setTaskParam(factory.toFieldEncrypt(hacpCaseVariableBO, AbstractTaskStrategyFactory.ExtState.CASE_START));
            });
        }

        ArrayList<String> workspaceIds = new ArrayList<>();
        workspaceIds.add(workspaceId);
        // 启动流程需要设置哪些流程变量
        StartProcessBO startProcessBO = new StartProcessBO();
        // 赋值业务id
        startProcessBO.setBusinessKey(UUID.fastUUID().toString());

        startProcessBO.setTenantIds(workspaceIds);
        startProcessBO.setInitiator(loginUserId);
        HashMap<String, Object> processVariables = new HashMap<>();
        processVariables.put(EmergencyConstant.CASE_ID, caseInfo.getId());
        caseInfo.setProcessDefinitionXml(null);

        processVariables.put(EmergencyConstant.CASE_TAG_ID, combineTagIds(Optional.ofNullable(caseInfo.getTagIds()).orElse(new ArrayList<>())));
        processVariables.put(EmergencyConstant.START_USER_NAME, SecurityUtils.getLoginName());
        processVariables.put(EmergencyConstant.CASE_INFO+startProcessBO.getBusinessKey(), JsonUtil.objToStr(caseInfo));

        List<EmergencyVariableBO> variableList=new ArrayList<>();
        // 将变量节点变量设置进流程变量中
        if (JudgeUtils.isNotEmpty(oldTasksParam)) {
            oldTasksParam.forEach(task -> {
                String key = EmergencyConstant.TASK_VARIABLE + task.getActivityId();
                String value = JsonUtil.objToStr(task);
                processVariables.put(key, value);
                AbstractTaskStrategyFactory factory = AbstractTaskStrategyFactory.newInstance(task.getTaskType());
                factory.taskParamArchive(task,workspaceId,startProcessBO.getBusinessKey());
                EmergencyVariableBO emergencyVariableBO = new EmergencyVariableBO();
                emergencyVariableBO.setBusinessKey(startProcessBO.getBusinessKey());
                emergencyVariableBO.setWorkspaceId(workspaceId);
                emergencyVariableBO.setVariableName(key);
                emergencyVariableBO.setValueJson(value);
                variableList.add(emergencyVariableBO);
            });
        }

        startProcessBO.setVariables(processVariables);
        processVariables.put("businessTenantId", workspaceInfo.getTenantId());
        startProcessBO.setDeploymentName(caseInfo.getCaseName());
        startProcessBO.setDeploymentId(caseInfo.getCaseDeployId());
        startProcessBO.setWorkspaceId(TenantUtils.getWorkspaceIdNotNull());
        instanceService.startProcessInstanceByDeployId(startProcessBO);

        EmergencyProcessBO bo = new EmergencyProcessBO();
        TenantSecurityUtils.copyTenantSecurity(bo);
        bo.setBusinessKey(startProcessBO.getBusinessKey());
        bo.setTagIds(caseInfo.getTagIds());
        bo.setCaseDeployId(caseInfo.getCaseDeployId());
        bo.setProcessDefId(processDefinitionDiagramDto.getId());
        bo.setCaseTitle(caseInfo.getCaseName());
        bo.setCaseDesc(caseInfo.getCaseDescribe());
        bo.setStartUser(startProcessBO.getInitiator());
        bo.setState(HistoricProcessInstance.STATE_ACTIVE);
        emergencyProcessService.add(bo);
        emergencyVariableService.addBatch(variableList);

        return startProcessBO.getBusinessKey();
    }

    private String combineTagIds(List<Integer> tagIds){
        StringBuilder stringBuilder = new StringBuilder();
        for (Integer tagId : tagIds) {
            stringBuilder.append(tagId).append("|");
        }
        return stringBuilder.toString();
    }
}
