package com.cmpay.hacp.emergency.bo;

import com.cmpay.hacp.capable.EntityTagCapable;
import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.emergency.entity.EmergencyHostDO;
import com.cmpay.hacp.utils.validation.CheckUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper =true)
public class EmergencyHostBO extends EmergencyHostDO implements TenantCapable, EntityTagCapable {

    private String uuid;

    private String hostTag;
    private Integer hostTagId;

    private String hostApp;
    /**
     * @Fields hostTagId 标签
     */
    private List<Integer> hostTagIds;

    private List<Integer> hostAppIds;

    /**
     * @Fields isSuccessful 是否成功(1-成功，0-失败)
     */
    private Short isSuccessful;

    private String batchNumber;

    /**
     * @Fields remark 备注
     */
    private String remark;

    private String entityType;


    public void checkParam() {
        CheckUtils.isInValidIpDomainPort(this.getHostAddress()+ CommonConstant.COLON + this.getHostPort());
    }

    @Override
    public List<Integer> getTagIds() {
        return this.getHostTagIds();
    }

    @Override
    public Long getEntityId() {
        return this.getHostId();
    }
}
