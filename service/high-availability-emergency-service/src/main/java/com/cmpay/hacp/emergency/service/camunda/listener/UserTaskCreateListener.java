package com.cmpay.hacp.emergency.service.camunda.listener;

import com.cmpay.hacp.emergency.bo.HacpCaseVariableBO;
import com.cmpay.hacp.emergency.bo.HacpEmergencyNodeRecodeBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.message.bo.InteriorMessageBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.emergency.service.EmergencyProcessService;
import com.cmpay.hacp.emergency.service.HacpNodeRecodeService;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.message.enums.MessageChildTypeEnum;
import com.cmpay.hacp.enums.TaskLogStatusEnum;
import com.cmpay.hacp.message.service.MessageSendService;
import com.cmpay.hacp.message.utils.InteriorMessageUtils;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;

/**
 * 用户任务创建前触发
 * @description:
 * <AUTHOR>
 * @date 2024/10/8 14:04
 * @version 1.0
 */
@Slf4j
@Component("userTaskCreateListenerV1")
public class UserTaskCreateListener implements TaskListener {

    @Autowired
    @Qualifier("interiorMessageServiceImpl")
    private MessageSendService messageSendService;
    @Autowired
    private HacpNodeRecodeService hacpNodeRecodeService;
    @Autowired
    private EmergencyProcessService emergencyProcessService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = BusinessException.class)
    public void notify(DelegateTask delegateTask) {
        HacpEmergencyNodeRecodeBO lastRecode = hacpNodeRecodeService.getLastRecodeAndLog(delegateTask.getExecution()
                .getBusinessKey(), delegateTask.getExecution().getCurrentActivityId());
        if(JudgeUtils.isNotNull(lastRecode)){
            return;
        }

        String variable =(String) delegateTask.getVariable(EmergencyConstant.TASK_VARIABLE + delegateTask.getTaskDefinitionKey());
        HacpCaseVariableBO hacpCaseVariableBO = JsonUtil.strToObject(variable, HacpCaseVariableBO.class);
        HacpEmergencyTaskBO taskParam = (HacpEmergencyTaskBO) AbstractTaskStrategyFactory.newInstance(hacpCaseVariableBO.getTaskType()).toTaskParam(hacpCaseVariableBO.getTaskParam());
        ArrayList<String> list = new ArrayList<>();
        list.add(taskParam.getTaskOperator());
        InteriorMessageBO messageParamBO = InteriorMessageUtils.buildSystemMessage("预案审批",
                "您有一条应急预案待审批：\"" + delegateTask.getVariable(EmergencyConstant.PROCESS_INSTANCE_NAME)+"\"",
                MessageChildTypeEnum.EMERGENCY_APPROVAL,
                list,
                delegateTask.getTenantId(),
                null);
        String extraId = delegateTask.getExecution().getBusinessKey()+ CommonConstant.COLON + delegateTask.getId();
        messageParamBO.setExtraId(extraId);
        try {
            messageSendService.sendMessage(messageParamBO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 将任务处理人也变更成缓存内的,否则会是未修改过
        delegateTask.setAssignee(taskParam.getTaskOperator());
        delegateTask.setVariable(EmergencyConstant.INITIATE_DURATION, System.currentTimeMillis());

        Long caseId = Long.parseLong(delegateTask.getVariable(EmergencyConstant.CASE_ID).toString());
        HacpEmergencyNodeRecodeBO recodeBO = new HacpEmergencyNodeRecodeBO();
        recodeBO.setActivityNodeId(delegateTask.getExecution().getCurrentActivityId());
        recodeBO.setBusinessKey(delegateTask.getExecution().getBusinessKey());
        recodeBO.setWorkspaceId(delegateTask.getTenantId());
        recodeBO.setTaskId(Long.valueOf(hacpCaseVariableBO.getTaskId()));
        recodeBO.setTaskName(hacpCaseVariableBO.getTaskName());
        recodeBO.setTaskDescribe(hacpCaseVariableBO.getTaskDescribe());
        recodeBO.setTaskType(hacpCaseVariableBO.getTaskType());
        recodeBO.setProcessId(delegateTask.getProcessInstanceId());
        recodeBO.setCaseId(caseId);
        recodeBO.setTaskParam(hacpCaseVariableBO.getTaskParam());
        recodeBO.setExecuteResult(TaskLogStatusEnum.ONGOING.getCode());
        recodeBO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        Long id = hacpNodeRecodeService.add(recodeBO);

        delegateTask.setVariable(EmergencyConstant.LOG_ID, id);
        emergencyProcessService.updateAuditUserId(delegateTask.getExecution().getBusinessKey(),delegateTask.getTenantId(),delegateTask.getAssignee());
    }
}