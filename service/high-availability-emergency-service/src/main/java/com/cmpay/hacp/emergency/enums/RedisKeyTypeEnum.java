package com.cmpay.hacp.emergency.enums;

import lombok.Getter;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/6/12 9:22
 * @version 1.0
 */
@Getter
public enum RedisKeyTypeEnum {

    /**
     * 流程定义属性 任务 ID
     */
    PROCESS_DEFINITION_PROPERTIES_TASK_ID("process-definition-properties-task-id"),

    ;

    private final String key;

    RedisKeyTypeEnum(String key) {
        this.key = key;
    }

    public static RedisKeyTypeEnum getRedisKeyTypeEnum(String key) {
        for (RedisKeyTypeEnum value : RedisKeyTypeEnum.values()) {
            if (value.getKey().equals(key)) {
                return value;
            }
        }
        return null;
    }
    public static String buildRedisKey(RedisKeyTypeEnum redisKeyTypeEnum, String... keys) {
        StringBuilder builder = new StringBuilder();
        builder.append(redisKeyTypeEnum.getKey());
        for (String key : keys) {
            builder.append(":").append(key);
        }
        return builder.toString();
    }
}
