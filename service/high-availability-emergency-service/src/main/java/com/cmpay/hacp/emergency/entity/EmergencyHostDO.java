/*
 * @ClassName EmergencyHostDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-30 14:19:16
 */
package com.cmpay.hacp.emergency.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.hacp.enums.HostOSEnum;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class EmergencyHostDO extends BaseDO {
    /**
     * @Fields hostId 
     */
    private Long hostId;
    /**
     * @Fields hostDesc 机器描述
     */
    private String hostDesc;
    /**
     * @Fields hostAddress 机器地址
     */
    private String hostAddress;
    /**
     * @Fields hostPort port
     */
    private Integer hostPort;
    /**
     * @Fields hostUsername 账号
     */
    private String hostUsername;
    /**
     * @Fields hostPassword 密码
     */
    private String hostPassword;
    /**
     * @Fields secretKey 密钥
     */
    private String secretKey;
    /**
     * @Fields hostAppId 应用id
     */
    private Integer hostAppId;
    /**
     * @Fields hostOS 主机系统
     */
    private HostOSEnum hostOS;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Long getHostId() {
        return hostId;
    }

    public void setHostId(Long hostId) {
        this.hostId = hostId;
    }

    public String getHostDesc() {
        return hostDesc;
    }

    public void setHostDesc(String hostDesc) {
        this.hostDesc = hostDesc;
    }

    public String getHostAddress() {
        return hostAddress;
    }

    public void setHostAddress(String hostAddress) {
        this.hostAddress = hostAddress;
    }

    public Integer getHostPort() {
        return hostPort;
    }

    public void setHostPort(Integer hostPort) {
        this.hostPort = hostPort;
    }

    public String getHostUsername() {
        return hostUsername;
    }

    public void setHostUsername(String hostUsername) {
        this.hostUsername = hostUsername;
    }

    public String getHostPassword() {
        return hostPassword;
    }

    public void setHostPassword(String hostPassword) {
        this.hostPassword = hostPassword;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public Integer getHostAppId() {
        return hostAppId;
    }

    public void setHostAppId(Integer hostAppId) {
        this.hostAppId = hostAppId;
    }

    public HostOSEnum getHostOS() {
        return hostOS;
    }

    public void setHostOS(HostOSEnum hostOS) {
        this.hostOS = hostOS;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}