package com.cmpay.hacp.emergency.service.factory.impl;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.emergency.enums.ListenerTypeEnum;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.emergency.service.factory.TaskStrategyType;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaTaskListener;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/6/4 9:44
 * @version 1.0
 */
@Slf4j
@Service(value = ManualOperationImpl.BEAN_NAME)
public class ManualOperationImpl extends AbstractTaskStrategyFactory {

    protected final static String BEAN_NAME = "manualOperationImpl";
    public ManualOperationImpl() {
        super(Collections.singletonList(new TaskStrategyType("1", "手动审批", BEAN_NAME)));
        addRetryAutoComplete("1");
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        if (JudgeUtils.isBlank(taskInfo.getTaskOperator())) {
            if (JudgeUtils.isNotBlank(taskInfo.getTaskParam())) {
                taskInfo.setTaskOperator(JsonUtil.strToObject(taskInfo.getTaskParam(), HacpEmergencyTaskBO.class).getTaskOperator());
            } else {
                BusinessException.throwBusinessException(MsgEnum.TASK_OPERATOR_IS_NULL);
            }
        }

        //设置设置任务审批人
        if (node instanceof UserTask) {
            ((UserTask)node).setCamundaAssignee(taskInfo.getTaskOperator());
            ((UserTask)node).setName(taskInfo.getTaskName());
            //设置任务监听器
            ExtensionElements extensionElements = node.getExtensionElements();
            if (extensionElements == null) {
                extensionElements = modelInstance.newInstance(ExtensionElements.class);
                //node.setExtensionElements(newInstance);
                addListener(node, extensionElements);
                return;
            }
            List<CamundaTaskListener> listeners =  extensionElements.getElementsQuery()
                    .filterByType(CamundaTaskListener.class)
                    .list()
                    .stream()
                    .filter(f -> JudgeUtils.equalsAny(f.getCamundaEvent(),
                            ListenerTypeEnum.USER_TASK_CREATE_V1.getEvent(),
                            ListenerTypeEnum.USER_TASK_COMPLETE_V1.getEvent(),
                            ListenerTypeEnum.USER_TASK_DELETE_V1.getEvent()))
                    .collect(Collectors.toList());
            if (listeners.isEmpty()) {
                addListener(node, extensionElements);
                return;
            }
            for (CamundaTaskListener listener : listeners) {
                if (ListenerTypeEnum.EVENT_MAP.containsKey(listener.getCamundaEvent())) {
                    listener.removeAttribute("class");
                    listener.setCamundaDelegateExpression(ListenerTypeEnum.EVENT_MAP.get(listener.getCamundaEvent()).getExpression());
                    extensionElements.addChildElement(listener);
                }
            }
        }
    }

    private static <T extends BaseElement> void addListener(T node, ExtensionElements extensionElements) {
        CamundaTaskListener camundaTaskListener = extensionElements.addExtensionElement(CamundaTaskListener.class);
        camundaTaskListener.setCamundaEvent(ListenerTypeEnum.USER_TASK_CREATE_V1.getEvent());
        camundaTaskListener.setCamundaDelegateExpression(ListenerTypeEnum.USER_TASK_CREATE_V1.getExpression());
        node.setExtensionElements(extensionElements);
        CamundaTaskListener completeTaskListener = extensionElements.addExtensionElement(CamundaTaskListener.class);
        completeTaskListener.setCamundaEvent(ListenerTypeEnum.USER_TASK_COMPLETE_V1.getEvent());
        completeTaskListener.setCamundaDelegateExpression(ListenerTypeEnum.USER_TASK_COMPLETE_V1.getExpression());
        node.setExtensionElements(extensionElements);
        CamundaTaskListener deleteTaskListener = extensionElements.addExtensionElement(CamundaTaskListener.class);
        deleteTaskListener.setCamundaEvent(ListenerTypeEnum.USER_TASK_DELETE_V1.getEvent());
        deleteTaskListener.setCamundaDelegateExpression(ListenerTypeEnum.USER_TASK_DELETE_V1.getExpression());
        node.setExtensionElements(extensionElements);
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_USER_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.CAMUNDA_USER_TASK;
    }

    @Override
    public boolean checkTaskParam(String json) {
        HacpEmergencyTaskBO taskBO = (HacpEmergencyTaskBO)toTaskParam(json);
        if (taskBO == null) {
            return false;
        }
        return JudgeUtils.isNotBlank(taskBO.getTaskOperator()) ;
    }

    @Override
    public TaskParam toTaskParam(String json) {
        return JsonUtil.strToObject(json, HacpEmergencyTaskBO.class);
    }
}
