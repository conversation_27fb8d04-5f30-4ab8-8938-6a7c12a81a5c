package com.cmpay.hacp.emergency.service.camunda;

import org.camunda.bpm.engine.rest.dto.task.CommentDto;
import org.camunda.bpm.engine.rest.dto.task.TaskDto;

import java.util.List;

/**
 * @description: 流程启动
 * <AUTHOR>
 * @date 2024/5/14 17:59
 * @version 1.0
 */
public interface ProcessTaskService {

    List<TaskDto> getCurrentTasksById(String businessKey);

    void completeTaskAsync(TaskDto task, String comment);

    void completeTask(TaskDto task, String comment);

    void setAssignee(String taskId, String userId);

    void setOwner(String taskId, String userId);

    Object getTaskVariable(String taskId, String variableName);

    void setTaskVariable(String taskId, String variableName, String variableValue);

    List<TaskDto> queryWaitingTasks(String userId, int firstResult, int maxResults);

    TaskDto getTaskInfo(String taskId);

    CommentDto getCommentDto(String taskId);

    List<TaskDto> getActiveTaskByExecutionId(String executionId);
}
