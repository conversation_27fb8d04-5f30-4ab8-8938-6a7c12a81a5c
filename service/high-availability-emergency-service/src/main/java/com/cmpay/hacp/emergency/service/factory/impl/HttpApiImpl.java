package com.cmpay.hacp.emergency.service.factory.impl;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.task.InterfaceParamBO;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.emergency.service.factory.TaskStrategyType;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/29 15:36
 * @version 1.0
 */
@Slf4j
@Service(value = HttpApiImpl.BEAN_NAME)
public class HttpApiImpl extends AbstractTaskStrategyFactory {

    protected final static String BEAN_NAME = "httpApiImpl";

    public HttpApiImpl() {
        super(Collections.singletonList(new TaskStrategyType("2", "接口调用", BEAN_NAME)));
    }

    //依赖应急调度服务

    @Override
    public boolean checkTaskParam(String json) {
        //将JSON转换成应急调度参数，检查参数是否正确
        InterfaceParamBO taskInitParamBO = (InterfaceParamBO)toTaskParam(json);
        if (taskInitParamBO == null) {
            return false;
        }
        return !JudgeUtils.isBlankAny(taskInitParamBO.getRequestUrl()
                , taskInitParamBO.getType());
    }

    @Override
    public TaskParam toTaskParam(String json) {
        //todo  详细字段转换待补充
        return JsonUtil.strToObject(json, InterfaceParamBO.class);
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        //检查任务执行参数是否正确
        if (!checkTaskParam(taskInfo.getTaskParam())) {
            log.error("task param is error : {}", taskInfo.getTaskParam());
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
        }
        //设置设置服务任务执行调度
        if (node instanceof ServiceTask) {
            ((ServiceTask)node).setCamundaDelegateExpression("${doHttpApi}");
            ((ServiceTask)node).setName(taskInfo.getTaskName());
        }
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }



    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_SERVICE_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.SERVICE_TASK_ELEMENT;
    }
}
