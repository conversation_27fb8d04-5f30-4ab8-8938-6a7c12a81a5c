package com.cmpay.hacp.emergency.service.camunda;

import com.cmpay.hacp.emergency.bo.HacpCaseVariableBO;
import com.cmpay.hacp.emergency.bo.HacpEmergencyNodeRecodeBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.process.ProcessExecuteLogEntity;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.emergency.service.HacpNodeRecodeService;
import com.cmpay.hacp.enums.TaskLogStatusEnum;
import com.cmpay.hacp.emergency.service.EmergencyProcessService;
import com.cmpay.hacp.emergency.service.HacpCaseService;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 创建bpm系统服务时，需要继承该类，不再继承{@link org.camunda.bpm.engine.delegate.JavaDelegate}
 *
 * <AUTHOR>
 * @create 2024/09/11 14:20
 * @since 1.0.0
 */
@Slf4j
public abstract class ServiceTaskTemplateDelegate implements JavaDelegate {

    protected ThreadLocal<HacpEmergencyNodeRecodeBO> recodeInfo=ThreadLocal.withInitial(HacpEmergencyNodeRecodeBO::new);
    protected final HacpCaseService caseService;
    protected final HacpNodeRecodeService nodeRecodeService;

    private final RuntimeService runtimeService;

    protected ThreadLocal<DelegateExecution> execution=new ThreadLocal<>();

    protected ThreadLocal<HacpEmergencyTaskBO> taskInfoHolder =new ThreadLocal<>();

    protected ThreadLocal<ProcessExecuteLogEntity> logEntity;

    protected final SystemCacheService cacheService;

    protected Boolean traceEnabled = true;

    protected final EmergencyProcessService emergencyProcessService;


    /**
     * 上下文传递使用
     */
    protected ThreadLocal<Map<String, String>> contextHolder=ThreadLocal.withInitial(HashMap::new);

    public ServiceTaskTemplateDelegate(HacpCaseService caseService,
                                       HacpNodeRecodeService nodeRecodeService,
                                       RuntimeService runtimeService,
                                       SystemCacheService cacheService,
            EmergencyProcessService emergencyProcessService) {
        this.caseService = caseService;
        this.nodeRecodeService = nodeRecodeService;
        this.runtimeService = runtimeService;
        this.cacheService = cacheService;
        this.emergencyProcessService = emergencyProcessService;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void execute(DelegateExecution execution) {
        this.execution.set(execution);
        if(skipTheNode()) return;
        long startTime = System.currentTimeMillis();
        beforeInit();
        init();
        beforeProcessTask();
        try {
            logEntity.get().appendBuffer("-----------------任务：" + taskInfoHolder.get().getTaskName() + "，开始执行：" + LocalDateTime.now() + "--------------");
            processTask();
            recodeInfo.get().setExecuteResult(TaskLogStatusEnum.COMPLETE.getCode());
        } catch (Exception e) {
            exception(e);
        } finally {
            try {
                logEntity.get().appendBuffer("-----------------任务：" + taskInfoHolder.get().getTaskName() + "，执行结束：" + LocalDateTime.now() + "--------------");
                recodeInfo.get().setDuration((int) (System.currentTimeMillis() - startTime));
                finallyHandle();
            }catch (Exception e){
                log.error("log error:",e);
            }
        }
        afterFinallyHandle();
    }

    private void init() {
        initTaskInfo();
        initRecodeInfo();
        initLogEntity();
    }

    private void initLogEntity() {
        String key = EmergencyConstant.RUNTIME_LOG_PREFIX_KEY + execution.get().getBusinessKey() + ":" + execution.get().getCurrentActivityId();
        logEntity = ThreadLocal.withInitial(()->new ProcessExecuteLogEntity(cacheService.getQueue(key), cacheService.getList(key)));
    }

    /**
     * 跳过节点
     *
     * @return boolean
     */
    private boolean skipTheNode() {
        //        boolean exist = cacheService.setIfAbsent(EmergencyConstant.EXECUTE_TASK_LOCK + execution.getProcessInstanceId() + execution.getCurrentActivityId()
//                , LocalDateTime.now(), 30, TimeUnit.MINUTES);
//        if (!exist) {
//            logEntity.appendBuffer("当前节点正在执行中....");
//            return;
//        }
        DelegateExecution delegateExecution = execution.get();
        HacpEmergencyNodeRecodeBO lastRecode = nodeRecodeService.getLastRecode(delegateExecution.getBusinessKey(),
                delegateExecution.getCurrentActivityId());
        if(JudgeUtils.isNull(lastRecode)){
            return false;
        }
        if(JudgeUtils.equalsAny(lastRecode.getExecuteResult(), CamundaUtil.EXECUTE_SUCCESS, TaskLogStatusEnum.SKIP.getCode())){
            log.info("跳过节点：{}", delegateExecution.getCurrentActivityId());
            return true;
        }
        return false;
    }

    protected void beforeInit() {
        DelegateExecution delegateExecution = execution.get();
        String taskId = caseService.getCamundaProperty(delegateExecution.getProcessDefinitionId(), delegateExecution.getCurrentActivityId());
        contextHolder.get().put(EmergencyConstant.TASK_ID, taskId);
        String key = delegateExecution.getBusinessKey() + EmergencyConstant.EXECUTE_TASK_STATE + delegateExecution.getCurrentActivityId();
        Object value = cacheService.getValue(key);
        if (JudgeUtils.isNotNull(value) && JudgeUtils.equals(value, CamundaUtil.EXECUTE_SUCCESS)) {
            return;
        }
        // 登记日志时修改
        cacheService.setValue(key, TaskLogStatusEnum.ONGOING.getCode()+"|" + new Date().getTime() +"|" +null+"|" +null, 30, TimeUnit.MINUTES);
    }

    protected void initRecodeInfo() {
        HacpEmergencyNodeRecodeBO hacpEmergencyNodeRecodeBO = recodeInfo.get();
        DelegateExecution delegateExecution = execution.get();
        HacpEmergencyTaskBO hacpEmergencyTaskBO = taskInfoHolder.get();

        hacpEmergencyNodeRecodeBO.setActivityNodeId(delegateExecution.getCurrentActivityId());
        hacpEmergencyNodeRecodeBO.setWorkspaceId(delegateExecution.getTenantId());
        hacpEmergencyNodeRecodeBO.setBusinessKey(delegateExecution.getBusinessKey());
        hacpEmergencyNodeRecodeBO.setProcessId(delegateExecution.getProcessInstanceId());
        hacpEmergencyNodeRecodeBO.setCaseId(Long.valueOf(delegateExecution.getVariable(EmergencyConstant.CASE_ID).toString()));
        hacpEmergencyNodeRecodeBO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        hacpEmergencyNodeRecodeBO.setTaskId(Long.valueOf(contextHolder.get().get(EmergencyConstant.TASK_ID)));
        hacpEmergencyNodeRecodeBO.setTaskType(hacpEmergencyTaskBO.getTaskType());
        hacpEmergencyNodeRecodeBO.setTaskDescribe(hacpEmergencyTaskBO.getTaskDescribe());
        hacpEmergencyNodeRecodeBO.setTaskName(hacpEmergencyTaskBO.getTaskName());
        hacpEmergencyNodeRecodeBO.setTaskParam(hacpEmergencyTaskBO.getTaskParam());
        hacpEmergencyNodeRecodeBO.setExecuteResult(TaskLogStatusEnum.ONGOING.getCode());
        hacpEmergencyNodeRecodeBO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        hacpEmergencyNodeRecodeBO.setId(nodeRecodeService.add(hacpEmergencyNodeRecodeBO));
        recodeInfo.set(hacpEmergencyNodeRecodeBO);
    }

    /**
     * 初始化任务信息taskInfo
     * <br/>
     * taskId从{@link ServiceTaskTemplateDelegate#contextHolder}中获取,key为{@link EmergencyConstant#TASK_ID}
     * <br/>
     *
     */
    protected void initTaskInfo() {
        String variable = (String) runtimeService.getVariable(execution.get().getId(),
                EmergencyConstant.TASK_VARIABLE + execution.get().getCurrentActivityId());
        HacpCaseVariableBO caseVariableBO = JsonUtil.strToObject(variable, HacpCaseVariableBO.class);
        taskInfoHolder.set(BeanConvertUtil.convert(caseVariableBO, HacpEmergencyTaskBO.class));
        assert taskInfoHolder.get() != null;
        taskInfoHolder.get().setId(Long.valueOf(contextHolder.get().get(EmergencyConstant.TASK_ID)));
    }

    protected void beforeProcessTask() {
    }


    /**
     * 异常任务使用
     * <br/>
     * 非try中的任务建议放至{@link ServiceTaskTemplateDelegate#beforeProcessTask()}}
     *
     * @throws Exception 例外
     */
    protected abstract void processTask() throws Exception;

    protected void exception(Exception ex) {
        recodeInfo.get().setExecuteResult(TaskLogStatusEnum.FAIL.getCode());
        logEntity.get().appendBuffer("执行异常：", ProcessExecuteLogEntity.LogLevel.ERROR);
        logEntity.get().appendBuffer(ex.getMessage(), ProcessExecuteLogEntity.LogLevel.ERROR);
        if(traceEnabled){
            logEntity.get().appendTrace(ex);
        }
        log.error("task error :",ex);
        HacpEmergencyNodeRecodeBO hacpEmergencyNodeRecodeBO = recodeInfo.get();
        runtimeService.deleteProcessInstance(execution.get().getProcessInstanceId(), EmergencyConstant.PROCESS_INSTANCE_FAIL_DELETE_COMMENT,true);
        emergencyProcessService.updateState(hacpEmergencyNodeRecodeBO.getBusinessKey(),hacpEmergencyNodeRecodeBO.getWorkspaceId(),EmergencyConstant.STATE_INTERNALLY_TERMINATED);
    }

    protected void finallyHandle() {
        ProcessExecuteLogEntity processExecuteLogEntity = logEntity.get();
        String resultLog = processExecuteLogEntity.toString().substring(0, Math.min(processExecuteLogEntity.toString().length(), 51200));
        HacpEmergencyNodeRecodeBO hacpEmergencyNodeRecodeBO = recodeInfo.get();
        nodeRecodeService.updateStatus(hacpEmergencyNodeRecodeBO, resultLog, hacpEmergencyNodeRecodeBO.getDuration(), TaskLogStatusEnum.getEnumByCode(
                hacpEmergencyNodeRecodeBO.getExecuteResult()));
        //清空队列---任务执行完成之后查数据库日志记录，不查询队列
        //logEntity.getLogQueue().clear();
//        cacheService.delete(EmergencyConstant.EXECUTE_TASK_LOCK + execution.getProcessInstanceId() + execution.getCurrentActivityId());
    }

    /**
     * finally执行后，预留的方法，不会在finally中执行
     */
    protected void afterFinallyHandle() {
    }
}
