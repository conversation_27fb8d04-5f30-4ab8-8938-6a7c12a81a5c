package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.HacpEmergencyNodeRecodeBO;
import com.cmpay.hacp.emergency.bo.process.TaskExecuteLogPage;
import com.cmpay.hacp.enums.TaskLogStatusEnum;

import java.util.List;

/**
 * @description: 任务服务
 * <AUTHOR>
 * @date 2024/5/14 15:18
 * @version 1.0
 */
public interface HacpNodeRecodeService {
    /**
     * 添加任务
     * @param recodeBO 任务BO
     */
    Long add(HacpEmergencyNodeRecodeBO recodeBO);

    void updateStatus(HacpEmergencyNodeRecodeBO recodeBO,String resultLog,Integer duration,TaskLogStatusEnum executeResult);

    HacpEmergencyNodeRecodeBO getLastRecode(String businessKey, String activityId);

    HacpEmergencyNodeRecodeBO getLastRecodeAndLog(String businessKey, String activityNodeId);

    HacpEmergencyNodeRecodeBO getExecuteLastRecode(String businessKey);

    TaskExecuteLogPage getRuntimeExecuteTaskLog(String businessKey, String activityNodeId, int pageNum);

    List<String> getRuntimeExecuteTaskLog(String businessKey, String activityNodeId);

    TaskExecuteLogPage getHistoryExecuteTaskLog(String businessKey, String activityNodeId,int pageNum);


    List<HacpEmergencyNodeRecodeBO> getAllRecode(String businessKey);
    List<HacpEmergencyNodeRecodeBO> getAllRecodeParam(String businessKey);

    List<HacpEmergencyNodeRecodeBO> getCompleteRecode(String businessKey);

    void updateProcessInstanceId(String oldVal, String newVal);

    void updateSkipNode(String businessKey, String activityId);

    void buildCache(String businessKey);

    void updateNodeStatus(String businessKey, String currentActivityId, String resultLoge, String status);

    void deleteExt(HacpEmergencyNodeRecodeBO recodeBO);
}
