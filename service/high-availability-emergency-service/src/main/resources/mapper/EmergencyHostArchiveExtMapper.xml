<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyHostArchiveExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyHostArchiveDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="host_id" property="hostId" jdbcType="BIGINT" />
        <result column="activity_id" property="activityId" jdbcType="VARCHAR" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="host_desc" property="hostDesc" jdbcType="VARCHAR" />
        <result column="host_address" property="hostAddress" jdbcType="VARCHAR" />
        <result column="host_port" property="hostPort" jdbcType="INTEGER" />
        <result column="host_username" property="hostUsername" jdbcType="VARCHAR" />
        <result column="host_password" property="hostPassword" jdbcType="VARCHAR" />
        <result column="secret_key" property="secretKey" jdbcType="VARCHAR" />
        <result column="host_os" property="hostOS" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, host_id, activity_id, business_key, workspace_id, host_desc, host_address, host_port,
        host_username, host_password, secret_key, host_os
    </sql>


    <insert id="batchInsert" >
        insert into emergency_host_archive
            (
                host_id,
                activity_id,
                business_key,
                workspace_id,
                host_desc,
                host_address,
                host_port,
                host_username,
                host_password,
                secret_key,
                host_os
            )
            values
            <foreach collection="list" item="item" separator=",">
                (
                    #{item.hostId,jdbcType=INTEGER},
                    #{activityId,jdbcType=VARCHAR},
                    #{businessKey,jdbcType=VARCHAR},
                    #{workspaceId,jdbcType=VARCHAR},
                    #{item.hostDesc,jdbcType=VARCHAR},
                    #{item.hostAddress,jdbcType=VARCHAR},
                    #{item.hostPort,jdbcType=INTEGER},
                    #{item.hostUsername,jdbcType=VARCHAR},
                    #{item.hostPassword,jdbcType=VARCHAR},
                    #{item.secretKey,jdbcType=VARCHAR},
                    #{item.hostOS,jdbcType=VARCHAR}
                )
            </foreach>
    </insert>


    <select id="findExt" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.EmergencyHostArchiveDO" >
        select
        host_id,
        id, activity_id, business_key, workspace_id, host_desc, host_address, host_port,
        host_username, host_password, secret_key, host_os
        from emergency_host_archive
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="hostId != null" >
                and host_id = #{hostId,jdbcType=BIGINT}
            </if>
            <if test="activityId != null" >
                and activity_id = #{activityId,jdbcType=VARCHAR}
            </if>
            <if test="businessKey != null" >
                and business_key = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="hostDesc != null" >
                and host_desc = #{hostDesc,jdbcType=VARCHAR}
            </if>
            <if test="hostAddress != null" >
                and host_address = #{hostAddress,jdbcType=VARCHAR}
            </if>
            <if test="hostPort != null" >
                and host_port = #{hostPort,jdbcType=INTEGER}
            </if>
            <if test="hostUsername != null" >
                and host_username = #{hostUsername,jdbcType=VARCHAR}
            </if>
            <if test="hostPassword != null" >
                and host_password = #{hostPassword,jdbcType=VARCHAR}
            </if>
            <if test="secretKey != null" >
                and secret_key = #{secretKey,jdbcType=VARCHAR}
            </if>
            <if test="hostOS != null" >
                and host_os = #{hostOS,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>