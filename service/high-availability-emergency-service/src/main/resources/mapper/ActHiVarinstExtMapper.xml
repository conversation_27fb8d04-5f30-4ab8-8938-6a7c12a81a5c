<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IActHiVarinstDao" >
    <select id="queryByTagId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
            PROC_INST_ID_
        from act_hi_varinst
        where NAME_ = 'caseTagId'
        and text_ like CONCAT('%',#{tagId,jdbcType=VARCHAR},'%')
    </select>
</mapper>