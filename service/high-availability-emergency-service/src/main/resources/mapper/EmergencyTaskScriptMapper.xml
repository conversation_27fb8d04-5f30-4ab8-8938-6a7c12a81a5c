<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyTaskScriptDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyTaskScriptDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="script_name" property="scriptName" jdbcType="VARCHAR" />
        <result column="script_describe" property="scriptDescribe" jdbcType="VARCHAR" />
        <result column="script_content" property="scriptContent" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, script_name, script_describe, script_content, operator_id, operator_name,
        create_time, update_time, status, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from emergency_task_script
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from emergency_task_script
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.emergency.entity.EmergencyTaskScriptDO" >
        insert into emergency_task_script
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="scriptName != null" >
                script_name,
            </if>
            <if test="scriptDescribe != null" >
                script_describe,
            </if>
            <if test="scriptContent != null" >
                script_content,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="scriptName != null" >
                #{scriptName,jdbcType=VARCHAR},
            </if>
            <if test="scriptDescribe != null" >
                #{scriptDescribe,jdbcType=VARCHAR},
            </if>
            <if test="scriptContent != null" >
                #{scriptContent,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.emergency.entity.EmergencyTaskScriptDO" >
        update emergency_task_script
        <set >
            <if test="scriptName != null" >
                script_name = #{scriptName,jdbcType=VARCHAR},
            </if>
            <if test="scriptDescribe != null" >
                script_describe = #{scriptDescribe,jdbcType=VARCHAR},
            </if>
            <if test="scriptContent != null" >
                script_content = #{scriptContent,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.EmergencyTaskScriptDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_task_script
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="scriptName != null" >
                and script_name = #{scriptName,jdbcType=VARCHAR}
            </if>
            <if test="scriptDescribe != null" >
                and script_describe = #{scriptDescribe,jdbcType=VARCHAR}
            </if>
            <if test="scriptContent != null" >
                and script_content = #{scriptContent,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>