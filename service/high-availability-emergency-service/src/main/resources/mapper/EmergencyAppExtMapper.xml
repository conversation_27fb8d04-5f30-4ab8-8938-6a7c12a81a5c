<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyAppExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyAppDO" >
        <id column="host_app_id" property="hostAppId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="host_app" property="hostApp" jdbcType="VARCHAR" />
        <result column="host_app_name" property="hostAppName" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        host_app_id, workspace_id, host_app, host_app_name, status, operator_id, operator_name, 
        create_time, update_time
    </sql>
    <select id="getDetailInfo" resultMap="BaseResultMap" resultType="com.cmpay.hacp.emergency.entity.EmergencyAppDO">
        select
        <include refid="Base_Column_List" />
        from emergency_app
        where host_app_id = #{hostAppId,jdbcType=INTEGER}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteExt" parameterType="com.cmpay.hacp.emergency.entity.EmergencyAppDO">
        delete from emergency_app
        where host_app_id = #{hostAppId,jdbcType=INTEGER}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

</mapper>