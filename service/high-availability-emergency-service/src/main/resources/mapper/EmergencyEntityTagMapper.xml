<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IEmergencyEntityTagDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.EmergencyEntityTagDO" >
        <id column="entity_id" property="entityId" jdbcType="BIGINT" />
        <id column="tag_id" property="tagId" jdbcType="INTEGER" />
        <id column="entity_type" property="entityType" jdbcType="VARCHAR" />
        <id column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        entity_id, tag_id, entity_type, workspace_id, status, operator_id, operator_name, 
        create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.EmergencyEntityTagDOKey" >
        select 
        <include refid="Base_Column_List" />
        from emergency_entity_tag
        where entity_id = #{entityId,jdbcType=BIGINT}
          and tag_id = #{tagId,jdbcType=INTEGER}
          and entity_type = #{entityType,jdbcType=VARCHAR}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="com.cmpay.hacp.entity.EmergencyEntityTagDOKey" >
        delete from emergency_entity_tag
        where entity_id = #{entityId,jdbcType=BIGINT}
          and tag_id = #{tagId,jdbcType=INTEGER}
          and entity_type = #{entityType,jdbcType=VARCHAR}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.EmergencyEntityTagDO" >
        insert into emergency_entity_tag
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="entityId != null" >
                entity_id,
            </if>
            <if test="tagId != null" >
                tag_id,
            </if>
            <if test="entityType != null" >
                entity_type,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="entityId != null" >
                #{entityId,jdbcType=BIGINT},
            </if>
            <if test="tagId != null" >
                #{tagId,jdbcType=INTEGER},
            </if>
            <if test="entityType != null" >
                #{entityType,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.EmergencyEntityTagDO" >
        update emergency_entity_tag
        <set >
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where entity_id = #{entityId,jdbcType=BIGINT}
          and tag_id = #{tagId,jdbcType=INTEGER}
          and entity_type = #{entityType,jdbcType=VARCHAR}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.EmergencyEntityTagDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_entity_tag
        <where >
            <if test="entityId != null" >
                and entity_id = #{entityId,jdbcType=BIGINT}
            </if>
            <if test="tagId != null" >
                and tag_id = #{tagId,jdbcType=INTEGER}
            </if>
            <if test="entityType != null" >
                and entity_type = #{entityType,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>