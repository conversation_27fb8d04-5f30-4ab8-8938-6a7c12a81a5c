<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IActHiProinstDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.ActHiProinstDO" >
        <id column="ID_" property="id" jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="BUSINESS_KEY_" property="businessKey" jdbcType="VARCHAR" />
        <result column="PROC_DEF_KEY_" property="procDefKey" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="START_TIME_" property="startTime" jdbcType="TIMESTAMP" />
        <result column="END_TIME_" property="endTime" jdbcType="TIMESTAMP" />
        <result column="REMOVAL_TIME_" property="removalTime" jdbcType="TIMESTAMP" />
        <result column="DURATION_" property="duration" jdbcType="BIGINT" />
        <result column="START_USER_ID_" property="startUserId" jdbcType="VARCHAR" />
        <result column="START_ACT_ID_" property="startActId" jdbcType="VARCHAR" />
        <result column="END_ACT_ID_" property="endActId" jdbcType="VARCHAR" />
        <result column="SUPER_PROCESS_INSTANCE_ID_" property="superProcessInstanceId" jdbcType="VARCHAR" />
        <result column="ROOT_PROC_INST_ID_" property="rootProcInstId" jdbcType="VARCHAR" />
        <result column="SUPER_CASE_INSTANCE_ID_" property="superCaseInstanceId" jdbcType="VARCHAR" />
        <result column="CASE_INST_ID_" property="caseInstId" jdbcType="VARCHAR" />
        <result column="DELETE_REASON_" property="deleteReason" jdbcType="VARCHAR" />
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR" />
        <result column="STATE_" property="state" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID_, PROC_INST_ID_, BUSINESS_KEY_, PROC_DEF_KEY_, PROC_DEF_ID_, START_TIME_, END_TIME_, 
        REMOVAL_TIME_, DURATION_, START_USER_ID_, START_ACT_ID_, END_ACT_ID_, SUPER_PROCESS_INSTANCE_ID_, 
        ROOT_PROC_INST_ID_, SUPER_CASE_INSTANCE_ID_, CASE_INST_ID_, DELETE_REASON_, TENANT_ID_, 
        STATE_
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from ACT_HI_PROCINST
        where ID_ = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from ACT_HI_PROCINST
        where ID_ = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.ActHiProinstDO" >
        insert into ACT_HI_PROCINST
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID_,
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_,
            </if>
            <if test="businessKey != null" >
                BUSINESS_KEY_,
            </if>
            <if test="procDefKey != null" >
                PROC_DEF_KEY_,
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_,
            </if>
            <if test="startTime != null" >
                START_TIME_,
            </if>
            <if test="endTime != null" >
                END_TIME_,
            </if>
            <if test="removalTime != null" >
                REMOVAL_TIME_,
            </if>
            <if test="duration != null" >
                DURATION_,
            </if>
            <if test="startUserId != null" >
                START_USER_ID_,
            </if>
            <if test="startActId != null" >
                START_ACT_ID_,
            </if>
            <if test="endActId != null" >
                END_ACT_ID_,
            </if>
            <if test="superProcessInstanceId != null" >
                SUPER_PROCESS_INSTANCE_ID_,
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_,
            </if>
            <if test="superCaseInstanceId != null" >
                SUPER_CASE_INSTANCE_ID_,
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_,
            </if>
            <if test="deleteReason != null" >
                DELETE_REASON_,
            </if>
            <if test="tenantId != null" >
                TENANT_ID_,
            </if>
            <if test="state != null" >
                STATE_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefKey != null" >
                #{procDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="removalTime != null" >
                #{removalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                #{duration,jdbcType=BIGINT},
            </if>
            <if test="startUserId != null" >
                #{startUserId,jdbcType=VARCHAR},
            </if>
            <if test="startActId != null" >
                #{startActId,jdbcType=VARCHAR},
            </if>
            <if test="endActId != null" >
                #{endActId,jdbcType=VARCHAR},
            </if>
            <if test="superProcessInstanceId != null" >
                #{superProcessInstanceId,jdbcType=VARCHAR},
            </if>
            <if test="rootProcInstId != null" >
                #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="superCaseInstanceId != null" >
                #{superCaseInstanceId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="deleteReason != null" >
                #{deleteReason,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="state != null" >
                #{state,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.ActHiProinstDO" >
        update ACT_HI_PROCINST
        <set >
            <if test="procInstId != null" >
                PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                BUSINESS_KEY_ = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefKey != null" >
                PROC_DEF_KEY_ = #{procDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                START_TIME_ = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null" >
                END_TIME_ = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="removalTime != null" >
                REMOVAL_TIME_ = #{removalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                DURATION_ = #{duration,jdbcType=BIGINT},
            </if>
            <if test="startUserId != null" >
                START_USER_ID_ = #{startUserId,jdbcType=VARCHAR},
            </if>
            <if test="startActId != null" >
                START_ACT_ID_ = #{startActId,jdbcType=VARCHAR},
            </if>
            <if test="endActId != null" >
                END_ACT_ID_ = #{endActId,jdbcType=VARCHAR},
            </if>
            <if test="superProcessInstanceId != null" >
                SUPER_PROCESS_INSTANCE_ID_ = #{superProcessInstanceId,jdbcType=VARCHAR},
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="superCaseInstanceId != null" >
                SUPER_CASE_INSTANCE_ID_ = #{superCaseInstanceId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="deleteReason != null" >
                DELETE_REASON_ = #{deleteReason,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null" >
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="state != null" >
                STATE_ = #{state,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.ActHiProinstDO" >
        select 
        <include refid="Base_Column_List" />
        from ACT_HI_PROCINST
        <where >
            <if test="id != null" >
                and ID_ = #{id,jdbcType=VARCHAR}
            </if>
            <if test="procInstId != null" >
                and PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR}
            </if>
            <if test="businessKey != null" >
                and BUSINESS_KEY_ = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="procDefKey != null" >
                and PROC_DEF_KEY_ = #{procDefKey,jdbcType=VARCHAR}
            </if>
            <if test="procDefId != null" >
                and PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null" >
                and START_TIME_ = #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null" >
                and END_TIME_ = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="removalTime != null" >
                and REMOVAL_TIME_ = #{removalTime,jdbcType=TIMESTAMP}
            </if>
            <if test="duration != null" >
                and DURATION_ = #{duration,jdbcType=BIGINT}
            </if>
            <if test="startUserId != null" >
                and START_USER_ID_ = #{startUserId,jdbcType=VARCHAR}
            </if>
            <if test="startActId != null" >
                and START_ACT_ID_ = #{startActId,jdbcType=VARCHAR}
            </if>
            <if test="endActId != null" >
                and END_ACT_ID_ = #{endActId,jdbcType=VARCHAR}
            </if>
            <if test="superProcessInstanceId != null" >
                and SUPER_PROCESS_INSTANCE_ID_ = #{superProcessInstanceId,jdbcType=VARCHAR}
            </if>
            <if test="rootProcInstId != null" >
                and ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR}
            </if>
            <if test="superCaseInstanceId != null" >
                and SUPER_CASE_INSTANCE_ID_ = #{superCaseInstanceId,jdbcType=VARCHAR}
            </if>
            <if test="caseInstId != null" >
                and CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR}
            </if>
            <if test="deleteReason != null" >
                and DELETE_REASON_ = #{deleteReason,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null" >
                and TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="state != null" >
                and STATE_ = #{state,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>