<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyProcessDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyProcessDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="case_title" property="caseTitle" jdbcType="VARCHAR" />
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR" />
        <result column="case_deploy_id" property="caseDeployId" jdbcType="VARCHAR" />
        <result column="case_desc" property="caseDesc" jdbcType="VARCHAR" />
        <result column="audit_user_id" property="auditUserId" jdbcType="VARCHAR" />
        <result column="start_user" property="startUser" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="state" property="state" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, business_key, workspace_id, case_title, process_def_id, case_deploy_id, case_desc, 
        audit_user_id, start_user, end_time, operator_id, operator_name, create_time, update_time, 
        status, state
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from emergency_process
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from emergency_process
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.emergency.entity.EmergencyProcessDO" useGeneratedKeys="true" keyProperty="id" >
        insert into emergency_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="businessKey != null" >
                business_key,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="caseTitle != null" >
                case_title,
            </if>
            <if test="processDefId != null" >
                process_def_id,
            </if>
            <if test="caseDeployId != null" >
                case_deploy_id,
            </if>
            <if test="caseDesc != null" >
                case_desc,
            </if>
            <if test="auditUserId != null" >
                audit_user_id,
            </if>
            <if test="startUser != null" >
                start_user,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="state != null" >
                state,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="businessKey != null" >
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="caseTitle != null" >
                #{caseTitle,jdbcType=VARCHAR},
            </if>
            <if test="processDefId != null" >
                #{processDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseDeployId != null" >
                #{caseDeployId,jdbcType=VARCHAR},
            </if>
            <if test="caseDesc != null" >
                #{caseDesc,jdbcType=VARCHAR},
            </if>
            <if test="auditUserId != null" >
                #{auditUserId,jdbcType=VARCHAR},
            </if>
            <if test="startUser != null" >
                #{startUser,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="state != null" >
                #{state,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.emergency.entity.EmergencyProcessDO" >
        update emergency_process
        <set >
            <if test="businessKey != null" >
                business_key = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="caseTitle != null" >
                case_title = #{caseTitle,jdbcType=VARCHAR},
            </if>
            <if test="processDefId != null" >
                process_def_id = #{processDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseDeployId != null" >
                case_deploy_id = #{caseDeployId,jdbcType=VARCHAR},
            </if>
            <if test="caseDesc != null" >
                case_desc = #{caseDesc,jdbcType=VARCHAR},
            </if>
            <if test="auditUserId != null" >
                audit_user_id = #{auditUserId,jdbcType=VARCHAR},
            </if>
            <if test="startUser != null" >
                start_user = #{startUser,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="state != null" >
                state = #{state,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.EmergencyProcessDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_process
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="businessKey != null" >
                and business_key = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="caseTitle != null" >
                and case_title = #{caseTitle,jdbcType=VARCHAR}
            </if>
            <if test="processDefId != null" >
                and process_def_id = #{processDefId,jdbcType=VARCHAR}
            </if>
            <if test="caseDeployId != null" >
                and case_deploy_id = #{caseDeployId,jdbcType=VARCHAR}
            </if>
            <if test="caseDesc != null" >
                and case_desc = #{caseDesc,jdbcType=VARCHAR}
            </if>
            <if test="auditUserId != null" >
                and audit_user_id = #{auditUserId,jdbcType=VARCHAR}
            </if>
            <if test="startUser != null" >
                and start_user = #{startUser,jdbcType=VARCHAR}
            </if>
            <if test="endTime != null" >
                and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="state != null" >
                and state = #{state,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>