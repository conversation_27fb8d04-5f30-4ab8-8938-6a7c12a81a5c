<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyTagDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyTagDO" >
        <id column="tag_id" property="tagId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="tag" property="tag" jdbcType="VARCHAR" />
        <result column="tag_name" property="tagName" jdbcType="VARCHAR" />
        <result column="entity_type" property="entityType" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        tag_id, workspace_id, tag, tag_name, entity_type, status, operator_id, operator_name,
        create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from emergency_tag
        where tag_id = #{tagId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from emergency_tag
        where tag_id = #{tagId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.emergency.entity.EmergencyTagDO" useGeneratedKeys="true" keyProperty="tagId" >
        insert into emergency_tag
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="tag != null" >
                tag,
            </if>
            <if test="tagName != null" >
                tag_name,
            </if>
            <if test="entityType != null" >
                entity_type,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="tag != null" >
                #{tag,jdbcType=VARCHAR},
            </if>
            <if test="tagName != null" >
                #{tagName,jdbcType=VARCHAR},
            </if>
            <if test="entityType != null" >
                #{entityType,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.emergency.entity.EmergencyTagDO" >
        update emergency_tag
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="tag != null" >
                tag = #{tag,jdbcType=VARCHAR},
            </if>
            <if test="tagName != null" >
                tag_name = #{tagName,jdbcType=VARCHAR},
            </if>
            <if test="entityType != null" >
                entity_type = #{entityType,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where tag_id = #{tagId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.EmergencyTagDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_tag
        <where >
            <if test="tagId != null" >
                and tag_id = #{tagId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="tag != null" >
                and tag = #{tag,jdbcType=VARCHAR}
            </if>
            <if test="tagName != null" >
                and tag_name = #{tagName,jdbcType=VARCHAR}
            </if>
            <if test="entityType != null" >
                and entity_type = #{entityType,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>