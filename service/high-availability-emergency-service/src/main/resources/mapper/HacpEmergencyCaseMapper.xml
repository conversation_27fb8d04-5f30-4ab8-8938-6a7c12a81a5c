<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IHacpEmergencyCaseDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.HacpEmergencyCaseDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="case_name" property="caseName" jdbcType="VARCHAR" />
        <result column="case_describe" property="caseDescribe" jdbcType="VARCHAR" />
        <result column="case_deploy_id" property="caseDeployId" jdbcType="VARCHAR" />
        <result column="operator" property="operator" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tenant_id, workspace_id, case_name, case_describe, case_deploy_id, operator, 
        operator_name, create_time, update_time, status, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from hacp_emergency_case
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from hacp_emergency_case
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.HacpEmergencyCaseDO"  useGeneratedKeys="true" keyProperty="id" >
        insert into hacp_emergency_case
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="tenantId != null" >
                tenant_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="caseName != null" >
                case_name,
            </if>
            <if test="caseDescribe != null" >
                case_describe,
            </if>
            <if test="caseDeployId != null" >
                case_deploy_id,
            </if>
            <if test="operator != null" >
                operator,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="caseName != null" >
                #{caseName,jdbcType=VARCHAR},
            </if>
            <if test="caseDescribe != null" >
                #{caseDescribe,jdbcType=VARCHAR},
            </if>
            <if test="caseDeployId != null" >
                #{caseDeployId,jdbcType=VARCHAR},
            </if>
            <if test="operator != null" >
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.HacpEmergencyCaseDO" >
        update hacp_emergency_case
        <set >
            <if test="tenantId != null" >
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="caseName != null" >
                case_name = #{caseName,jdbcType=VARCHAR},
            </if>
            <if test="caseDescribe != null" >
                case_describe = #{caseDescribe,jdbcType=VARCHAR},
            </if>
            <if test="caseDeployId != null" >
                case_deploy_id = #{caseDeployId,jdbcType=VARCHAR},
            </if>
            <if test="operator != null" >
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.HacpEmergencyCaseDO" >
        select 
        <include refid="Base_Column_List" />
        from hacp_emergency_case
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="caseName != null" >
                and case_name = #{caseName,jdbcType=VARCHAR}
            </if>
            <if test="caseDescribe != null" >
                and case_describe = #{caseDescribe,jdbcType=VARCHAR}
            </if>
            <if test="caseDeployId != null" >
                and case_deploy_id = #{caseDeployId,jdbcType=VARCHAR}
            </if>
            <if test="operator != null" >
                and operator = #{operator,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
        order by tm_smp desc
    </select>
</mapper>