<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IActRuTaskDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.ActRuTaskDO" >
        <id column="ID_" property="id" jdbcType="VARCHAR" />
        <result column="REV_" property="rev" jdbcType="INTEGER" />
        <result column="EXECUTION_ID_" property="executionId" jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="CASE_EXECUTION_ID_" property="caseExecutionId" jdbcType="VARCHAR" />
        <result column="CASE_INST_ID_" property="caseInstId" jdbcType="VARCHAR" />
        <result column="CASE_DEF_ID_" property="caseDefId" jdbcType="VARCHAR" />
        <result column="NAME_" property="name" jdbcType="VARCHAR" />
        <result column="PARENT_TASK_ID_" property="parentTaskId" jdbcType="VARCHAR" />
        <result column="DESCRIPTION_" property="description" jdbcType="VARCHAR" />
        <result column="TASK_DEF_KEY_" property="taskDefKey" jdbcType="VARCHAR" />
        <result column="OWNER_" property="owner" jdbcType="VARCHAR" />
        <result column="ASSIGNEE_" property="assignee" jdbcType="VARCHAR" />
        <result column="DELEGATION_" property="delegation" jdbcType="VARCHAR" />
        <result column="PRIORITY_" property="priority" jdbcType="INTEGER" />
        <result column="CREATE_TIME_" property="createTime" jdbcType="TIMESTAMP" />
        <result column="LAST_UPDATED_" property="lastUpdated" jdbcType="TIMESTAMP" />
        <result column="DUE_DATE_" property="dueDate" jdbcType="TIMESTAMP" />
        <result column="FOLLOW_UP_DATE_" property="followUpDate" jdbcType="TIMESTAMP" />
        <result column="SUSPENSION_STATE_" property="suspensionState" jdbcType="INTEGER" />
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID_, REV_, EXECUTION_ID_, PROC_INST_ID_, PROC_DEF_ID_, CASE_EXECUTION_ID_, CASE_INST_ID_, 
        CASE_DEF_ID_, NAME_, PARENT_TASK_ID_, DESCRIPTION_, TASK_DEF_KEY_, OWNER_, ASSIGNEE_, 
        DELEGATION_, PRIORITY_, CREATE_TIME_, LAST_UPDATED_, DUE_DATE_, FOLLOW_UP_DATE_, 
        SUSPENSION_STATE_, TENANT_ID_
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from ACT_RU_TASK
        where ID_ = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from ACT_RU_TASK
        where ID_ = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.tenant.entity.ActRuTaskDO" >
        insert into ACT_RU_TASK
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID_,
            </if>
            <if test="rev != null" >
                REV_,
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_,
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_,
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_,
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_,
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_,
            </if>
            <if test="caseDefId != null" >
                CASE_DEF_ID_,
            </if>
            <if test="name != null" >
                NAME_,
            </if>
            <if test="parentTaskId != null" >
                PARENT_TASK_ID_,
            </if>
            <if test="description != null" >
                DESCRIPTION_,
            </if>
            <if test="taskDefKey != null" >
                TASK_DEF_KEY_,
            </if>
            <if test="owner != null" >
                OWNER_,
            </if>
            <if test="assignee != null" >
                ASSIGNEE_,
            </if>
            <if test="delegation != null" >
                DELEGATION_,
            </if>
            <if test="priority != null" >
                PRIORITY_,
            </if>
            <if test="createTime != null" >
                CREATE_TIME_,
            </if>
            <if test="lastUpdated != null" >
                LAST_UPDATED_,
            </if>
            <if test="dueDate != null" >
                DUE_DATE_,
            </if>
            <if test="followUpDate != null" >
                FOLLOW_UP_DATE_,
            </if>
            <if test="suspensionState != null" >
                SUSPENSION_STATE_,
            </if>
            <if test="tenantId != null" >
                TENANT_ID_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="rev != null" >
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="executionId != null" >
                #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseDefId != null" >
                #{caseDefId,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentTaskId != null" >
                #{parentTaskId,jdbcType=VARCHAR},
            </if>
            <if test="description != null" >
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="taskDefKey != null" >
                #{taskDefKey,jdbcType=VARCHAR},
            </if>
            <if test="owner != null" >
                #{owner,jdbcType=VARCHAR},
            </if>
            <if test="assignee != null" >
                #{assignee,jdbcType=VARCHAR},
            </if>
            <if test="delegation != null" >
                #{delegation,jdbcType=VARCHAR},
            </if>
            <if test="priority != null" >
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdated != null" >
                #{lastUpdated,jdbcType=TIMESTAMP},
            </if>
            <if test="dueDate != null" >
                #{dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="followUpDate != null" >
                #{followUpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="suspensionState != null" >
                #{suspensionState,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.tenant.entity.ActRuTaskDO" >
        update ACT_RU_TASK
        <set >
            <if test="rev != null" >
                REV_ = #{rev,jdbcType=INTEGER},
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseDefId != null" >
                CASE_DEF_ID_ = #{caseDefId,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                NAME_ = #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentTaskId != null" >
                PARENT_TASK_ID_ = #{parentTaskId,jdbcType=VARCHAR},
            </if>
            <if test="description != null" >
                DESCRIPTION_ = #{description,jdbcType=VARCHAR},
            </if>
            <if test="taskDefKey != null" >
                TASK_DEF_KEY_ = #{taskDefKey,jdbcType=VARCHAR},
            </if>
            <if test="owner != null" >
                OWNER_ = #{owner,jdbcType=VARCHAR},
            </if>
            <if test="assignee != null" >
                ASSIGNEE_ = #{assignee,jdbcType=VARCHAR},
            </if>
            <if test="delegation != null" >
                DELEGATION_ = #{delegation,jdbcType=VARCHAR},
            </if>
            <if test="priority != null" >
                PRIORITY_ = #{priority,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                CREATE_TIME_ = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdated != null" >
                LAST_UPDATED_ = #{lastUpdated,jdbcType=TIMESTAMP},
            </if>
            <if test="dueDate != null" >
                DUE_DATE_ = #{dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="followUpDate != null" >
                FOLLOW_UP_DATE_ = #{followUpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="suspensionState != null" >
                SUSPENSION_STATE_ = #{suspensionState,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null" >
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.tenant.entity.ActRuTaskDO" >
        select 
        <include refid="Base_Column_List" />
        from ACT_RU_TASK
        <where >
            <if test="id != null" >
                and ID_ = #{id,jdbcType=VARCHAR}
            </if>
            <if test="rev != null" >
                and REV_ = #{rev,jdbcType=INTEGER}
            </if>
            <if test="executionId != null" >
                and EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR}
            </if>
            <if test="procInstId != null" >
                and PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR}
            </if>
            <if test="procDefId != null" >
                and PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR}
            </if>
            <if test="caseExecutionId != null" >
                and CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR}
            </if>
            <if test="caseInstId != null" >
                and CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR}
            </if>
            <if test="caseDefId != null" >
                and CASE_DEF_ID_ = #{caseDefId,jdbcType=VARCHAR}
            </if>
            <if test="name != null" >
                and NAME_ = #{name,jdbcType=VARCHAR}
            </if>
            <if test="parentTaskId != null" >
                and PARENT_TASK_ID_ = #{parentTaskId,jdbcType=VARCHAR}
            </if>
            <if test="description != null" >
                and DESCRIPTION_ = #{description,jdbcType=VARCHAR}
            </if>
            <if test="taskDefKey != null" >
                and TASK_DEF_KEY_ = #{taskDefKey,jdbcType=VARCHAR}
            </if>
            <if test="owner != null" >
                and OWNER_ = #{owner,jdbcType=VARCHAR}
            </if>
            <if test="assignee != null" >
                and ASSIGNEE_ = #{assignee,jdbcType=VARCHAR}
            </if>
            <if test="delegation != null" >
                and DELEGATION_ = #{delegation,jdbcType=VARCHAR}
            </if>
            <if test="priority != null" >
                and PRIORITY_ = #{priority,jdbcType=INTEGER}
            </if>
            <if test="createTime != null" >
                and CREATE_TIME_ = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="lastUpdated != null" >
                and LAST_UPDATED_ = #{lastUpdated,jdbcType=TIMESTAMP}
            </if>
            <if test="dueDate != null" >
                and DUE_DATE_ = #{dueDate,jdbcType=TIMESTAMP}
            </if>
            <if test="followUpDate != null" >
                and FOLLOW_UP_DATE_ = #{followUpDate,jdbcType=TIMESTAMP}
            </if>
            <if test="suspensionState != null" >
                and SUSPENSION_STATE_ = #{suspensionState,jdbcType=INTEGER}
            </if>
            <if test="tenantId != null" >
                and TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>