<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IEmergencyVariableDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.EmergencyVariableDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="variable_name" property="variableName" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.entity.EmergencyVariableDO" extends="BaseResultMap" >
        <result column="value_json" property="valueJson" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, business_key, workspace_id, variable_name
    </sql>

    <sql id="Blob_Column_List" >
        value_json
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from emergency_variable
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from emergency_variable
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.EmergencyVariableDO" useGeneratedKeys="true" keyProperty="id" >
        insert into emergency_variable
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="businessKey != null" >
                business_key,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="variableName != null" >
                variable_name,
            </if>
            <if test="valueJson != null" >
                value_json,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="businessKey != null" >
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="variableName != null" >
                #{variableName,jdbcType=VARCHAR},
            </if>
            <if test="valueJson != null" >
                #{valueJson,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.EmergencyVariableDO" >
        update emergency_variable
        <set >
            <if test="businessKey != null" >
                business_key = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="variableName != null" >
                variable_name = #{variableName,jdbcType=VARCHAR},
            </if>
            <if test="valueJson != null" >
                value_json = #{valueJson,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.entity.EmergencyVariableDO" >
        update emergency_variable
        set business_key = #{businessKey,jdbcType=VARCHAR},
            workspace_id = #{workspaceId,jdbcType=VARCHAR},
            variable_name = #{variableName,jdbcType=VARCHAR},
            value_json = #{valueJson,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.EmergencyVariableDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_variable
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="businessKey != null" >
                and business_key = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="variableName != null" >
                and variable_name = #{variableName,jdbcType=VARCHAR}
            </if>
            <if test="valueJson != null" >
                and value_json = #{valueJson,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>