<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IEmergencyTagExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.EmergencyTagDO" >
        <id column="tag_id" property="tagId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="tagDO" property="tagDO" jdbcType="VARCHAR" />
        <result column="tag_name" property="tagName" jdbcType="VARCHAR" />
        <result column="entity_type" property="entityType" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>


    <sql id="Base_Column_List" >
        tag_id, workspace_id, tagDO, tag_name, entity_type, status, operator_id, operator_name,
        create_time, update_time
    </sql>
    <delete id="deleteExt" parameterType="com.cmpay.hacp.entity.EmergencyTagDO">
        delete from emergency_tag
        where tag_id = #{tagId,jdbcType=INTEGER}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>
    <select id="getDetailInfo" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.EmergencyTagDO" >
        select
        <include refid="Base_Column_List" />
        from emergency_tag
        where tag_id = #{tagId,jdbcType=INTEGER}
        and status = #{status,jdbcType=TINYINT}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <select id="getTagInfoByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
            from emergency_tag
        where
        tag_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getTagNames" resultType="java.lang.String">

    </select>

</mapper>