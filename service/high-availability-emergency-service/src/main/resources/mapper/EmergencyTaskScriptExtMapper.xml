<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IEmergencyTaskScriptExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.EmergencyTaskScriptDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="script_name" property="scriptName" jdbcType="VARCHAR" />
        <result column="script_describe" property="scriptDescribe" jdbcType="VARCHAR" />
        <result column="script_content" property="scriptContent" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, script_name, script_describe, script_content, operator_id, operator_name,
        create_time, update_time, status, tm_smp
    </sql>

    <select id="findExt" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.EmergencyTaskScriptDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_task_script
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="scriptName != null" >
                and script_name like concat('%',#{scriptName,jdbcType=VARCHAR},'%')
            </if>
            <if test="scriptDescribe != null" >
                and script_describe = #{scriptDescribe,jdbcType=VARCHAR}
            </if>
            <if test="scriptContent != null" >
                and script_content = #{scriptContent,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>