<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.ISysDynamicLogDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.SysDynamicLogDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="request_id" property="requestId" jdbcType="VARCHAR" />
        <result column="operator" property="operator" jdbcType="VARCHAR" />
        <result column="operation_action" property="operationAction" jdbcType="VARCHAR" />
        <result column="execution_target" property="executionTarget" jdbcType="VARCHAR" />
        <result column="operator_ip" property="operatorIp" jdbcType="VARCHAR" />
        <result column="operator_time" property="operatorTime" jdbcType="TIMESTAMP" />
        <result column="operator_status" property="operatorStatus" jdbcType="VARCHAR" />
        <result column="data_size" property="dataSize" jdbcType="BIGINT" />
        <result column="data_size_type" property="dataSizeType" jdbcType="VARCHAR" />
        <result column="application_name" property="applicationName" jdbcType="VARCHAR" />
        <result column="data_it" property="dataIt" jdbcType="VARCHAR" />
        <result column="data_path" property="dataPath" jdbcType="VARCHAR" />
        <result column="interface_record" property="interfaceRecord" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="duration" property="duration" jdbcType="BIGINT" />
        <result column="type" property="type" jdbcType="CHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.entity.SysDynamicLogDO" extends="BaseResultMap" >
        <result column="params" property="params" jdbcType="LONGVARCHAR" />
        <result column="exception" property="exception" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, request_id, operator, operation_action, execution_target, operator_ip, operator_time, 
        operator_status, data_size, data_size_type, application_name, data_it, data_path, 
        interface_record, end_time, duration, type
    </sql>

    <sql id="Blob_Column_List" >
        params, exception
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from sys_dynamic_log
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from sys_dynamic_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.SysDynamicLogDO" >
        insert into sys_dynamic_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="requestId != null" >
                request_id,
            </if>
            <if test="operator != null" >
                operator,
            </if>
            <if test="operationAction != null" >
                operation_action,
            </if>
            <if test="executionTarget != null" >
                execution_target,
            </if>
            <if test="operatorIp != null" >
                operator_ip,
            </if>
            <if test="operatorTime != null" >
                operator_time,
            </if>
            <if test="operatorStatus != null" >
                operator_status,
            </if>
            <if test="dataSize != null" >
                data_size,
            </if>
            <if test="dataSizeType != null" >
                data_size_type,
            </if>
            <if test="applicationName != null" >
                application_name,
            </if>
            <if test="dataIt != null" >
                data_it,
            </if>
            <if test="dataPath != null" >
                data_path,
            </if>
            <if test="interfaceRecord != null" >
                interface_record,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="duration != null" >
                duration,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="params != null" >
                params,
            </if>
            <if test="exception != null" >
                exception,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="requestId != null" >
                #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="operator != null" >
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operationAction != null" >
                #{operationAction,jdbcType=VARCHAR},
            </if>
            <if test="executionTarget != null" >
                #{executionTarget,jdbcType=VARCHAR},
            </if>
            <if test="operatorIp != null" >
                #{operatorIp,jdbcType=VARCHAR},
            </if>
            <if test="operatorTime != null" >
                #{operatorTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorStatus != null" >
                #{operatorStatus,jdbcType=VARCHAR},
            </if>
            <if test="dataSize != null" >
                #{dataSize,jdbcType=BIGINT},
            </if>
            <if test="dataSizeType != null" >
                #{dataSizeType,jdbcType=VARCHAR},
            </if>
            <if test="applicationName != null" >
                #{applicationName,jdbcType=VARCHAR},
            </if>
            <if test="dataIt != null" >
                #{dataIt,jdbcType=VARCHAR},
            </if>
            <if test="dataPath != null" >
                #{dataPath,jdbcType=VARCHAR},
            </if>
            <if test="interfaceRecord != null" >
                #{interfaceRecord,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                #{duration,jdbcType=BIGINT},
            </if>
            <if test="type != null" >
                #{type,jdbcType=CHAR},
            </if>
            <if test="params != null" >
                #{params,jdbcType=LONGVARCHAR},
            </if>
            <if test="exception != null" >
                #{exception,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.SysDynamicLogDO" >
        update sys_dynamic_log
        <set >
            <if test="requestId != null" >
                request_id = #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="operator != null" >
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operationAction != null" >
                operation_action = #{operationAction,jdbcType=VARCHAR},
            </if>
            <if test="executionTarget != null" >
                execution_target = #{executionTarget,jdbcType=VARCHAR},
            </if>
            <if test="operatorIp != null" >
                operator_ip = #{operatorIp,jdbcType=VARCHAR},
            </if>
            <if test="operatorTime != null" >
                operator_time = #{operatorTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorStatus != null" >
                operator_status = #{operatorStatus,jdbcType=VARCHAR},
            </if>
            <if test="dataSize != null" >
                data_size = #{dataSize,jdbcType=BIGINT},
            </if>
            <if test="dataSizeType != null" >
                data_size_type = #{dataSizeType,jdbcType=VARCHAR},
            </if>
            <if test="applicationName != null" >
                application_name = #{applicationName,jdbcType=VARCHAR},
            </if>
            <if test="dataIt != null" >
                data_it = #{dataIt,jdbcType=VARCHAR},
            </if>
            <if test="dataPath != null" >
                data_path = #{dataPath,jdbcType=VARCHAR},
            </if>
            <if test="interfaceRecord != null" >
                interface_record = #{interfaceRecord,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                duration = #{duration,jdbcType=BIGINT},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=CHAR},
            </if>
            <if test="params != null" >
                params = #{params,jdbcType=LONGVARCHAR},
            </if>
            <if test="exception != null" >
                exception = #{exception,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.entity.SysDynamicLogDO" >
        update sys_dynamic_log
        set request_id = #{requestId,jdbcType=VARCHAR},
            operator = #{operator,jdbcType=VARCHAR},
            operation_action = #{operationAction,jdbcType=VARCHAR},
            execution_target = #{executionTarget,jdbcType=VARCHAR},
            operator_ip = #{operatorIp,jdbcType=VARCHAR},
            operator_time = #{operatorTime,jdbcType=TIMESTAMP},
            operator_status = #{operatorStatus,jdbcType=VARCHAR},
            data_size = #{dataSize,jdbcType=BIGINT},
            data_size_type = #{dataSizeType,jdbcType=VARCHAR},
            application_name = #{applicationName,jdbcType=VARCHAR},
            data_it = #{dataIt,jdbcType=VARCHAR},
            data_path = #{dataPath,jdbcType=VARCHAR},
            interface_record = #{interfaceRecord,jdbcType=VARCHAR},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            duration = #{duration,jdbcType=BIGINT},
            type = #{type,jdbcType=CHAR},
            params = #{params,jdbcType=LONGVARCHAR},
            exception = #{exception,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.SysDynamicLogDO" >
        select 
        <include refid="Base_Column_List" />
        from sys_dynamic_log
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="requestId != null" >
                and request_id = #{requestId,jdbcType=VARCHAR}
            </if>
            <if test="operator != null" >
                and operator = #{operator,jdbcType=VARCHAR}
            </if>
            <if test="operationAction != null" >
                and operation_action = #{operationAction,jdbcType=VARCHAR}
            </if>
            <if test="executionTarget != null" >
                and execution_target = #{executionTarget,jdbcType=VARCHAR}
            </if>
            <if test="operatorIp != null" >
                and operator_ip = #{operatorIp,jdbcType=VARCHAR}
            </if>
            <if test="operatorTime != null" >
                and operator_time = #{operatorTime,jdbcType=TIMESTAMP}
            </if>
            <if test="operatorStatus != null" >
                and operator_status = #{operatorStatus,jdbcType=VARCHAR}
            </if>
            <if test="dataSize != null" >
                and data_size = #{dataSize,jdbcType=BIGINT}
            </if>
            <if test="dataSizeType != null" >
                and data_size_type = #{dataSizeType,jdbcType=VARCHAR}
            </if>
            <if test="applicationName != null" >
                and application_name = #{applicationName,jdbcType=VARCHAR}
            </if>
            <if test="dataIt != null" >
                and data_it = #{dataIt,jdbcType=VARCHAR}
            </if>
            <if test="dataPath != null" >
                and data_path = #{dataPath,jdbcType=VARCHAR}
            </if>
            <if test="interfaceRecord != null" >
                and interface_record = #{interfaceRecord,jdbcType=VARCHAR}
            </if>
            <if test="endTime != null" >
                and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="duration != null" >
                and duration = #{duration,jdbcType=BIGINT}
            </if>
            <if test="type != null" >
                and type = #{type,jdbcType=CHAR}
            </if>
            <if test="params != null" >
                and params = #{params,jdbcType=LONGVARCHAR}
            </if>
            <if test="exception != null" >
                and exception = #{exception,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>