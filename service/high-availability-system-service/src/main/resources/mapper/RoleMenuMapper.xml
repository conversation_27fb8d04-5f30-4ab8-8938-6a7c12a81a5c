<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IRoleMenuDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.RoleMenuDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="menu_id" property="menuId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        role_id,
        menu_id
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_role_menu
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_role_menu
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.RoleMenuDO">
        insert into sys_role_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="roleId != null">
                role_id,
            </if>
            <if test="menuId != null">
                menu_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="menuId != null">
                #{menuId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.RoleMenuDO">
        update sys_role_menu
        <set>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="menuId != null">
                menu_id = #{menuId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.RoleMenuDO">
        select
        <include refid="Base_Column_List"/>
        from sys_role_menu
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="roleId != null">
                and role_id = #{roleId,jdbcType=BIGINT}
            </if>
            <if test="menuId != null">
                and menu_id = #{menuId,jdbcType=BIGINT}
            </if>
        </where>
    </select>
</mapper>
