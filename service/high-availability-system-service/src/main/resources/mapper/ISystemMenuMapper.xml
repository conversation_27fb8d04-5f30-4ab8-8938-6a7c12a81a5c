<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.ISystemMenuDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.SystemMenuDO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="perms" property="perms" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="BIGINT"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="meta" property="meta" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="redirect" property="redirect" jdbcType="VARCHAR"/>
        <result column="en_name" property="enName" jdbcType="VARCHAR"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="hide_title" property="hideTitle" jdbcType="BOOLEAN"/>
        <result column="hidden" property="hidden" jdbcType="BOOLEAN"/>
        <result column="hide_children" property="hideChildren" jdbcType="BOOLEAN"/>
        <result column="keepalive" property="keepalive" jdbcType="BOOLEAN"/>
        <result column="hide_page_title_bar" property="hidePageTitleBar" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        menu_id, parent_id, name, app_id, url, perms, type, icon, order_num, create_user_id,
        create_time, modify_time, meta, component, redirect, en_name,
        parent_name,
        hide_title,
        hidden,
        hide_children,
        keepalive,
        hide_page_title_bar
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        where menu_id = #{menuId,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        delete
        from sys_menu
        where menu_id = #{menuId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.SystemMenuDO">
        insert into sys_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuId != null">
                menu_id,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="perms != null">
                perms,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="icon != null">
                icon,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="meta != null">
                meta,
            </if>
            <if test="component != null">
                component,
            </if>
            <if test="redirect != null">
                redirect,
            </if>
            <if test="enName != null">
                en_name,
            </if>
            <if test="parentName != null">
                parent_name,
            </if>
            <if test="hideTitle != null">
                hide_title,
            </if>
            <if test="hidden != null">
                hidden,
            </if>
            <if test="hideChildren != null">
                hide_children,
            </if>
            <if test="keepalive != null">
                keepalive,
            </if>
            <if test="hidePageTitleBar != null">
                hide_page_title_bar
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuId != null">
                #{menuId,jdbcType=BIGINT},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="perms != null">
                #{perms,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="icon != null">
                #{icon,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                #{orderNum,jdbcType=BIGINT},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="meta != null">
                #{meta,jdbcType=VARCHAR},
            </if>
            <if test="component != null">
                #{component,jdbcType=VARCHAR},
            </if>
            <if test="redirect != null">
                #{redirect,jdbcType=VARCHAR},
            </if>
            <if test="enName != null">
                #{enName,jdbcType=VARCHAR},
            </if>
            <if test="parentName != null">
                #{parentName,jdbcType=VARCHAR},
            </if>
            <if test="hideTitle != null">
                #{hideTitle,jdbcType=BOOLEAN},
            </if>
            <if test="hidden != null">
                #{hidden,jdbcType=BOOLEAN},
            </if>
            <if test="hideChildren != null">
                #{hideChildren,jdbcType=BOOLEAN},
            </if>
            <if test="keepalive != null">
                #{keepalive,jdbcType=BOOLEAN},
            </if>
            <if test="hidePageTitleBar != null">
                #{hidePageTitleBar,jdbcType=BOOLEAN}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.SystemMenuDO">
        update sys_menu
        <set>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="perms != null">
                perms = #{perms,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="icon != null">
                icon = #{icon,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum,jdbcType=BIGINT},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="meta != null">
                meta = #{meta,jdbcType=VARCHAR},
            </if>
            <if test="component != null">
                component = #{component,jdbcType=VARCHAR},
            </if>
            <if test="redirect != null">
                redirect = #{redirect,jdbcType=VARCHAR},
            </if>
            <if test="enName != null">
                en_name = #{enName,jdbcType=VARCHAR},
            </if>
            <if test="parentName != null">
                parent_name = #{parentName,jdbcType=VARCHAR},
            </if>
            <if test="hideTitle != null">
                hide_title = #{hideTitle,jdbcType=BOOLEAN},
            </if>
            <if test="hidden != null">
                hidden = #{hidden,jdbcType=BOOLEAN},
            </if>
            <if test="hideChildren != null">
                hide_children = #{hideChildren,jdbcType=BOOLEAN},
            </if>
            <if test="keepalive != null">
                keepalive = #{keepalive,jdbcType=BOOLEAN},
            </if>
            <if test="hidePageTitleBar != null">
                hide_page_title_bar = #{hidePageTitleBar,jdbcType=BOOLEAN}
            </if>
        </set>
        where menu_id = #{menuId,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.SystemMenuDO">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        <where>
            <if test="menuId != null">
                and menu_id = #{menuId,jdbcType=BIGINT}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId,jdbcType=BIGINT}
            </if>
            <if test="name != null">
                and name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id = #{appId,jdbcType=VARCHAR}
            </if>
            <if test="url != null">
                and url = #{url,jdbcType=VARCHAR}
            </if>
            <if test="perms != null">
                and perms = #{perms,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="icon != null">
                and icon = #{icon,jdbcType=VARCHAR}
            </if>
            <if test="orderNum != null">
                and order_num = #{orderNum,jdbcType=BIGINT}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="meta != null">
                and meta = #{meta,jdbcType=VARCHAR}
            </if>
            <if test="component != null">
                and component = #{component,jdbcType=VARCHAR}
            </if>
            <if test="redirect != null">
                and redirect = #{redirect,jdbcType=VARCHAR}
            </if>
            <if test="enName != null">
                and en_name = #{enName,jdbcType=VARCHAR}
            </if>
            <if test="parentName != null">
                and parent_name = #{parentName,jdbcType=VARCHAR}
            </if>
            <if test="hideTitle != null">
                and hide_title = #{hideTitle,jdbcType=BOOLEAN}
            </if>
            <if test="hidden != null">
                and hidden = #{hidden,jdbcType=BOOLEAN}
            </if>
            <if test="hideChildren != null">
                and hide_children = #{hideChildren,jdbcType=BOOLEAN}
            </if>
            <if test="keepalive != null">
                and keepalive = #{keepalive,jdbcType=BOOLEAN}
            </if>
            <if test="hidePageTitleBar != null">
                and hide_page_title_bar = #{hidePageTitleBar,jdbcType=BOOLEAN}
            </if>
        </where>
    </select>
</mapper>