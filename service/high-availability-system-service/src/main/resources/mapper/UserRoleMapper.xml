<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IUserRoleDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.UserRoleDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        user_id,
        role_id
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_user_role
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_user_role
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.UserRoleDO">
        insert into sys_user_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="roleId != null">
                role_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.UserRoleDO">
        update sys_user_role
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.UserRoleDO">
        select
        <include refid="Base_Column_List"/>
        from sys_user_role
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="roleId != null">
                and role_id = #{roleId,jdbcType=BIGINT}
            </if>
        </where>
    </select>
</mapper>
