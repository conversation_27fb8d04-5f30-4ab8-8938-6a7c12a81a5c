<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IRoleMenuExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.RoleMenuDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="menu_id" property="menuId" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.entity.MenuDO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="perms" property="perms" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="BIGINT"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="meta" property="meta" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="redirect" property="redirect" jdbcType="VARCHAR"/>
        <result column="en_name" property="enName" jdbcType="VARCHAR"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="hide_title" property="hideTitle" jdbcType="BOOLEAN"/>
        <result column="hidden" property="hidden" jdbcType="BOOLEAN"/>
        <result column="hide_children" property="hideChildren" jdbcType="BOOLEAN"/>
        <result column="keepalive" property="keepalive" jdbcType="BOOLEAN"/>
        <result column="hide_page_title_bar" property="hidePageTitleBar" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        role_id,
        menu_id
    </sql>


    <insert id="batchInsertRoleMenus">
        insert into sys_role_menu (id, role_id, menu_id) values
        <foreach item="menuId" collection="menuIds" separator=",">
            (CONCAT(CONCAT(#{roleId}, '-'), #{menuId}), #{roleId}, #{menuId})
        </foreach>
    </insert>

    <delete id="deleteByRoleId" parameterType="java.lang.Number">
        delete
        from sys_role_menu
        where role_id = #{roleId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByMenuId" parameterType="java.lang.Number">
        delete
        from sys_role_menu
        where menu_id = #{menuId,jdbcType=BIGINT}
    </delete>

    <select id="getMenusByRoleId" parameterType="java.lang.Number" resultMap="BaseResultExtMap">
        select m.menu_id,
               m.parent_id,
               m.name,
               m.app_id,
               m.url,
               m.perms,
               m.type,
               m.icon,
               m.order_num,
               m.create_user_id,
               m.create_time,
               m.modify_time,
               m.meta,
               m.component,
               m.redirect,
               m.en_name,
               m.parent_name,
               m.hide_title,
               m.hidden,
               m.hide_children,
               m.keepalive,
               m.hide_page_title_bar
        from sys_menu as m
                 left join sys_role_menu as r on r.role_id = #{roleId,jdbcType=BIGINT}
        where r.menu_id = m.menu_id
    </select>

    <select id="getRolesByMenuId" parameterType="java.lang.Number" resultType="com.cmpay.hacp.entity.RoleDO">
        select t.role_id,
               t.role_name,
               t.remark,
               t.status,
               t.dept_id,
               t.create_user_id,
               t.create_time,
               t.modify_time,
               t.owner_app_id
        from sys_role as t
                 left join sys_role_menu as r on r.menu_id = #{menuId,jdbcType=BIGINT}
        where r.role_id = t.role_id
    </select>
</mapper>
