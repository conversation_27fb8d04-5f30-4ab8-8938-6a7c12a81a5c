<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ILoginHistoryLogDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.LoginHistoryLogDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="login_ip" property="loginIp" jdbcType="VARCHAR"/>
        <result column="login_terminal" property="loginTerminal" jdbcType="VARCHAR"/>
        <result column="login_from" property="loginFrom" jdbcType="VARCHAR"/>
        <result column="login_date" property="loginDate" jdbcType="DATE"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="is_use" property="isUse" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , user_id, user_name, name, mobile, login_ip, login_terminal, login_from, login_date,
        login_time, request_id, is_use
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_login_history_log
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_login_history_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.LoginHistoryLogDO">
        insert into sys_login_history_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="loginIp != null">
                login_ip,
            </if>
            <if test="loginTerminal != null">
                login_terminal,
            </if>
            <if test="loginFrom != null">
                login_from,
            </if>
            <if test="loginDate != null">
                login_date,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="requestId != null">
                request_id,
            </if>
            <if test="isUse != null">
                is_use,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="loginIp != null">
                #{loginIp,jdbcType=VARCHAR},
            </if>
            <if test="loginTerminal != null">
                #{loginTerminal,jdbcType=VARCHAR},
            </if>
            <if test="loginFrom != null">
                #{loginFrom,jdbcType=VARCHAR},
            </if>
            <if test="loginDate != null">
                #{loginDate,jdbcType=DATE},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="requestId != null">
                #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="isUse != null">
                #{isUse,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.LoginHistoryLogDO">
        update sys_login_history_log
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="loginIp != null">
                login_ip = #{loginIp,jdbcType=VARCHAR},
            </if>
            <if test="loginTerminal != null">
                login_terminal = #{loginTerminal,jdbcType=VARCHAR},
            </if>
            <if test="loginFrom != null">
                login_from = #{loginFrom,jdbcType=VARCHAR},
            </if>
            <if test="loginDate != null">
                login_date = #{loginDate,jdbcType=DATE},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="requestId != null">
                request_id = #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="isUse != null">
                is_use = #{isUse,jdbcType=SMALLINT},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.LoginHistoryLogDO">
        select
        <include refid="Base_Column_List"/>
        from sys_login_history_log
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null">
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="name != null">
                and name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="mobile != null">
                and mobile = #{mobile,jdbcType=VARCHAR}
            </if>
            <if test="loginIp != null">
                and login_ip = #{loginIp,jdbcType=VARCHAR}
            </if>
            <if test="loginTerminal != null">
                and login_terminal = #{loginTerminal,jdbcType=VARCHAR}
            </if>
            <if test="loginFrom != null">
                and login_from = #{loginFrom,jdbcType=VARCHAR}
            </if>
            <if test="loginDate != null">
                and login_date = #{loginDate,jdbcType=DATE}
            </if>
            <if test="loginTime != null">
                and login_time = #{loginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="requestId != null">
                and request_id = #{requestId,jdbcType=VARCHAR}
            </if>
            <if test="isUse != null">
                and is_use = #{isUse,jdbcType=SMALLINT}
            </if>
        </where>
    </select>
</mapper>
