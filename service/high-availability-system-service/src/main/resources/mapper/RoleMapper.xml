<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IRoleDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.RoleDO">
        <id column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="owner_app_id" property="ownerAppId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_id,
        role_name,
        remark,
        status,
        dept_id,
        create_user_id,
        create_time,
        modify_time,
        owner_app_id
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_role
        where role_id = #{roleId,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        delete
        from sys_role
        where role_id = #{roleId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.RoleDO">
        insert into sys_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="ownerAppId != null">
                owner_app_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ownerAppId != null">
                #{ownerAppId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.RoleDO">
        update sys_role
        <set>
            <if test="roleName != null">
                role_name = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ownerAppId != null">
                owner_app_id = #{ownerAppId,jdbcType=VARCHAR},
            </if>
        </set>
        where role_id = #{roleId,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.RoleDO">
        select
        <include refid="Base_Column_List"/>
        from sys_role
        <where>
            <if test="roleId != null">
                and role_id = #{roleId,jdbcType=BIGINT}
            </if>
            <if test="roleName != null">
                and role_name = #{roleName,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId,jdbcType=VARCHAR}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="ownerAppId != null">
                and owner_app_id = #{ownerAppId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
