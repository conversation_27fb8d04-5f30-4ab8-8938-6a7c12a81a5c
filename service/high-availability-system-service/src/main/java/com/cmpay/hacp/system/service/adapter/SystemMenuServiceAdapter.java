package com.cmpay.hacp.system.service.adapter;

import com.cmpay.hacp.bo.menu.*;
import com.cmpay.hacp.system.bo.system.DictBO;
import com.cmpay.hacp.system.dao.IMenuExtDao;
import com.cmpay.hacp.system.dao.IRoleMenuExtDao;
import com.cmpay.hacp.system.entity.MenuDO;
import com.cmpay.hacp.enums.MenuEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemDictionaryService;
import com.cmpay.hacp.system.service.SystemMenuService;
import com.cmpay.hacp.system.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.system.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.system.utils.MenuUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SystemMenuServiceAdapter implements SystemMenuService {

    @Resource
    public IMenuExtDao menuExtDao;

    @Resource
    public IRoleMenuExtDao roleMenuExtDao;

    @Resource
    public MenuUtils menuUtils;

    private static final String MENU_TYPE = "MENU_TYPE";

    @Autowired
    private SystemDictionaryService systemDictionaryService;

    @Override
    public List<DictBO> type() {
        DictBO dictBO = new DictBO();
        dictBO.setType(MENU_TYPE);
        return systemDictionaryService.queryDictInfos(dictBO);
    }

    /**
     * 校验菜单操作表单数据合法性
     *
     * @param menuBO
     */
    @Override
    public void verifyFormData(MenuBO menuBO) {
        if (JudgeUtils.isBlank(menuBO.getName())) {
            BusinessException.throwBusinessException(MsgEnum.MENU_NAME_CANNOT_NULL);
        }
        if (menuBO.getParentId() == null) {
            BusinessException.throwBusinessException(MsgEnum.PARENT_MENU_CANNOT_NULL);
        }
        //上级菜单类型
        String parentType = MenuEnum.CATALOG.getValue();
        if (menuBO.getParentId() != 0) {
            menuBO = this.getMenuById(menuBO.getParentId());
            parentType = menuBO.getType();
        }
        //目录、菜单
        //todo 上级菜单的验证逻辑

        //按钮
        if (JudgeUtils.equalsIgnoreCase(menuBO.getType(), MenuEnum.BUTTON.getValue()) && !JudgeUtils.equalsIgnoreCase(parentType,
                MenuEnum.MENU.getValue())) {
            BusinessException.throwBusinessException(MsgEnum.PARENT_MENU_MUSTBE_MENU);
        }
    }


    @Override
    public PermMenuTreeMetaBO getUserMenus(String userId) {
        PermMenuTreeMetaBO menuTree = new PermMenuTreeMetaBO();
        //查询系统菜单
        List<MenuBO> menus;
        menus = menuExtDao.queryUserMenus(userId);
        if (JudgeUtils.isEmpty(menus)) {
            return menuTree;
        }
        //菜单
        List<MenuTreeMetaBO> menuList = new ArrayList<>();
        //按钮
        List<MenuActionMetaBO> buttonList = new ArrayList<>();
        for (MenuBO menuDO : menus) {

            if (JudgeUtils.equalsIgnoreCase(menuDO.getType(), MenuEnum.BUTTON.getValue())) {
                //按钮
                buttonList.add(menuUtils.getMenuActionRspMetaDTO(menuDO));
            } else {
                //菜单
                menuList.add(menuUtils.getMenuTreeRspMetaDTO(menuDO));
            }
        }
        if (JudgeUtils.isNotEmpty(menuList)) {
            //转为树形菜单
            menuTree.setMenuTreeList(menuUtils.getMenuTreeMetas(menuList));
        }
        menuTree.setMenuActionList(buttonList);
        return menuTree;
    }


    /**
     * 获取应用菜单
     *
     * @return
     */
    @Override
    public PermMenuTreeBO getAppMenus() {
        List<MenuDO> menus = menuExtDao.queryAppMenus(new MenuDO());
        PermMenuTreeBO permMenuTreeRspDTO = new PermMenuTreeBO();
        if (JudgeUtils.isNotEmpty(menus)) {
            permMenuTreeRspDTO.setMenuTreeList(menuUtils.getTreeMenus(menus));
        }
        return permMenuTreeRspDTO;
    }

    /**
     * 查询所有菜单
     *
     * @param pageNum
     * @param pageSize
     * @param userId
     * @return
     */
    @Override
    public PageInfo<MenuBO> page(int pageNum, int pageSize, String userId) {
        List<MenuBO> menus = new ArrayList<>();
        PageInfo<MenuBO> pageInfo = new PageInfo<>(menus);
        if (pageNum == 0 || pageSize == 0) {
            pageInfo = new PageInfo<>(BeanConvertUtil.convertList(menuExtDao.queryUserMenus(userId), MenuBO.class));
        } else {
            pageInfo = PageUtils.pageQueryWithCount(pageNum,
                    pageSize,
                    () -> BeanConvertUtil.convertList(menuExtDao.queryUserMenus(userId), MenuBO.class));
        }
        return pageInfo;

    }


    /**
     * 根据Id查找菜单信息
     *
     * @param menuId
     * @return
     */
    @Override
    public MenuBO getMenuById(Long menuId) {
        MenuDO menuDO = menuExtDao.get(menuId);
        MenuBO menuBO = new MenuBO();
        if (JudgeUtils.isNotNull(menuDO)) {
            BeanUtils.copyProperties(menuBO, menuDO);
        }
        return menuBO;
    }

    /**
     * 新增菜单
     *
     * @param menuBO
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void add(String operatorId, MenuBO menuBO) {
        this.verifyFormData(menuBO);
        MenuDO menuDO = new MenuDO();
        BeanUtils.copyProperties(menuDO, menuBO);
        menuDO.setMeta(JudgeUtils.isNotBlank(menuBO.getMeta()) ? menuBO.getMeta() : "{}");
        int insert = menuExtDao.insert(menuDO);
        if (insert < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
    }

    /**
     * 更新
     *
     * @param menuBO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void update(String operatorId, MenuBO menuBO) {
        this.verifyFormData(menuBO);
        MenuDO menuDO = new MenuDO();
        BeanUtils.copyProperties(menuDO, menuBO);
        menuDO.setMeta(JudgeUtils.isNotBlank(menuBO.getMeta()) ? menuBO.getMeta() : "{}");
        int update = menuExtDao.update(menuDO);
        if (update < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
    }

    /**
     * 删除
     *
     * @param menuId
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void delete(Long menuId) {
        MenuDO menuDO = new MenuDO();
        menuDO.setParentId(menuId);
        List<MenuDO> menuDOS = menuExtDao.find(menuDO);
        if (JudgeUtils.isNotEmpty(menuDOS)) {
            BusinessException.throwBusinessException(MsgEnum.DELETE_SUBMENU_OR_BUTTON_FIRST);
        }
        menuExtDao.delete(menuId);
        roleMenuExtDao.deleteByMenuId(menuId);
    }

    /**
     * 批量删除菜单ID
     *
     * @param menuIds
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void delete(List<Long> menuIds) {
        menuIds.stream().forEach(this::delete);
    }


}
