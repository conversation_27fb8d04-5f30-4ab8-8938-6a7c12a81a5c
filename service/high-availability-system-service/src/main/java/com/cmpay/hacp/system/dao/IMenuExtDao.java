/*
 * @ClassName IMenuDao
 * @Description
 * @version 1.0
 * @Date 2021-08-09 10:09:54
 */
package com.cmpay.hacp.system.dao;

import com.cmpay.hacp.system.entity.MenuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IMenuExtDao extends IMenuDao {
    /**
     * 查询用户菜单列表
     *
     * @param userId
     * @return
     */
    List<MenuDO> queryUserMenus(@Param("userId") String userId);

    /**
     * 查询应用菜单，包括按钮
     *
     * @param menuDO
     * @return
     */
    List<MenuDO> queryAppMenus(@Param("menuDO") MenuDO menuDO);
}