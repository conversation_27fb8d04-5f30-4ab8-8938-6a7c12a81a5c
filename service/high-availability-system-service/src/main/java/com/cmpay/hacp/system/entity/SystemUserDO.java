/*
 * @ClassName SystemUserDO
 * @Description
 * @version 1.0
 * @Date 2023-08-08 16:10:59
 */
package com.cmpay.hacp.system.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class SystemUserDO extends BaseDO {
    /**
     * @Fields userId 用户编号
     */
    private String userId;
    /**
     * @Fields userName 用户名
     */
    private String userName;
    /**
     * @Fields fullName 姓名
     */
    private String fullName;
    /**
     * @Fields password 密码
     */
    private String password;
    /**
     * @Fields salt 盐
     */
    private String salt;
    /**
     * @Fields deptId 部门编号
     */
    private String deptId;
    /**
     * @Fields dutyId 岗位编号
     */
    private Long dutyId;
    /**
     * @Fields email 邮箱
     */
    private String email;
    /**
     * @Fields mobile 手机号
     */
    private String mobile;
    /**
     * @Fields weixin 微信号
     */
    private String weixin;
    /**
     * @Fields status DISABLE:禁用  ENABLE:启用
     */
    private String status;
    /**
     * @Fields createUserId 创建者id
     */
    private String createUserId;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;
    /**
     * @Fields lastLoginTime 用户上次登陆时间
     */
    private LocalDateTime lastLoginTime;
    /**
     * @Fields hasRole 是否拥有角色 Y:是 N:否
     */
    private String hasRole;
    /**
     * @Fields cstUserId 用户中心id
     */
    private String cstUserId;
    /**
     * @Fields appId 应用id
     */
    private String appId;
    /**
     * @Fields pwdModifyTime 密码上次修改时间
     */
    private LocalDateTime pwdModifyTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public Long getDutyId() {
        return dutyId;
    }

    public void setDutyId(Long dutyId) {
        this.dutyId = dutyId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getWeixin() {
        return weixin;
    }

    public void setWeixin(String weixin) {
        this.weixin = weixin;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getHasRole() {
        return hasRole;
    }

    public void setHasRole(String hasRole) {
        this.hasRole = hasRole;
    }

    public String getCstUserId() {
        return cstUserId;
    }

    public void setCstUserId(String cstUserId) {
        this.cstUserId = cstUserId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public LocalDateTime getPwdModifyTime() {
        return pwdModifyTime;
    }

    public void setPwdModifyTime(LocalDateTime pwdModifyTime) {
        this.pwdModifyTime = pwdModifyTime;
    }
}