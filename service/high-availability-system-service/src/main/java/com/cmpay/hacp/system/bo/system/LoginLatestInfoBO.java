
package com.cmpay.hacp.system.bo.system;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class LoginLatestInfoBO {
    /**
     * @Fields id 主键
     */
    private String id;
    /**
     * @Fields pid 关联主键
     */
    private String pid;
    /**
     * @Fields userId 用户ID
     */
    private String userId;
    /**
     * @Fields latestDate 最近一次登录日期
     */
    private LocalDate latestDate;
    /**
     * @Fields latestTime 最近一次登录时间
     */
    private LocalDateTime latestTime;
    /**
     * @Fields firstDate 第一次登录日期
     */
    private LocalDate firstDate;
    /**
     * @Fields firstTime 第一登录时间
     */
    private LocalDateTime firstTime;
    /**
     * @Fields isUse 是否可用(0-否,1-是)
     */
    private Short isUse;

}
