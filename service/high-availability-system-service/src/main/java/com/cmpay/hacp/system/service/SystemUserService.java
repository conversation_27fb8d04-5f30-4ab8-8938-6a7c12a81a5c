package com.cmpay.hacp.system.service;


import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.system.bo.system.UserInfoBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

/**
 * 系统用户服务
 *
 * <AUTHOR> tnw
 */
public interface SystemUserService {

    /**
     * 分页查询用户列表
     *
     * @param pageSize
     * @param pageNum
     * @param userInfoBO
     * @return
     */
    PageInfo<UserBO> getUsersByPage(Integer pageSize, Integer pageNum, UserInfoBO userInfoBO);

    /**
     * 查询用户信息
     *
     * @param userId
     * @return
     */
    UserBO getUserInfo(String userId);

    List<UserBO> getUsersByUserId(List<String> userIds);

    /**
     * 用户删除
     *
     * @param userId
     */
    void delete(String userId);

    /**
     * 根据手机号删除用户
     *
     * @param mobile
     */
    void deleteUserByMobile(String mobile);

    /**
     * 用户角色删除
     *
     * @param userId
     */
    void deleteUserRole(String userId);

    /**
     * 批量删除用户
     *
     * @param userIds
     */
    void deleteBatch(List<String> userIds);


    /**
     * 用户密码更新
     *
     * @param userId
     * @param oldPassword
     * @param newPassword
     */
    void updatePassword(String userId, String oldPassword, String newPassword);

    /**
     * 查询所有用户
     *
     * @param operatorId
     * @return
     */
    List<UserBO> getAllUsers(String operatorId);

    /**
     * 查询已授权用户
     *
     * @param userBO
     * @return
     */
    List<UserBO> getUsersByCondition(UserBO userBO);

    /**
     * 根据角色查询用户
     *
     * @param roleId
     * @return
     */
    List<UserBO> getUsersByRoleId(Long roleId);

    /**
     * 用户新增
     *
     * @param operatorId
     * @param userBO
     * @param roleIdList
     */
    void add(String operatorId, UserBO userBO, List<Long> roleIdList);

    /**
     * 批量用户新增
     *
     * @param userBO
     */
    void batchAdd(UserBO userBO);

    /**
     * 批量用户更新
     *
     * @param userBO
     */
    void batchUpdate(UserBO userBO);

    /**
     * 批量用户更新
     *
     * @param userBO
     */
    void updateUserByMobile(UserBO userBO);

    /**
     * 用户修改
     *
     * @param operatorId
     * @param userBO
     * @param roleIdList
     */
    void update(String operatorId, UserBO userBO, List<Long> roleIdList);

    /**
     * 检查用户名是否可用
     *
     * @param userName
     */
    void checkUserName(String userName);

    /**
     * 检查手机号码是否可用
     *
     * @param mobile
     */
    void checkMobile(String mobile);

    /**
     * 查询用户
     *
     * @param userBO
     * @return
     */
    UserBO getUserInfo(UserBO userBO);

    /**
     * 加密密码
     *
     * @param password
     * @return
     */
    String encryptPassword(String password);

    /**
     * 解密密码
     * 使用权限中心集成方案时，因拿不到私钥，无法解密，为自有功能实现的方法
     *
     * @param password
     * @return
     */
    String decryptPassword(String password);

    /**
     * 查询所有用户的电话号码并去重
     *
     * @return
     */
    List<UserBO> getAllUserMobiles();

    /**
     * 批量禁用用户
     *
     * @param userIds 用户ID
     */
    void disableBatch(List<String> userIds);

    /**
     * 禁用用户
     *
     * @param userId 用户ID
     */
    void disable(String userId);

    /**
     * 批量启用用户
     *
     * @param userIds 用户ID
     */
    void enableBatch(List<String> userIds);

    /**
     * 启用用户
     *
     * @param userId 用户ID
     */
    void enable(String userId);

    void updateDateTime(UserBO userInfo);
}
