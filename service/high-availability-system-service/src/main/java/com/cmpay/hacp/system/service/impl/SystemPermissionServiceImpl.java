package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.system.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.system.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.system.service.SystemMenuService;
import com.cmpay.hacp.system.service.SystemPermissionService;
import com.cmpay.hacp.system.service.adapter.SystemPermissionServiceAdapter;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.cache.jcache.JCacheCacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 系统权限服务实现
 *
 * <AUTHOR>
 * @create 2024/05/11 15:06:16
 * @since 1.0.0
 */
@Service
@Primary
public class SystemPermissionServiceImpl extends SystemPermissionServiceAdapter {

    @Autowired
    private SystemMenuService systemMenuService;

    /**
     * 获取用户权限,菜单并缓存
     *
     * @param userId
     * @return
     */
    @Override
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_MENU", key = "'MENU:'+ #applicationName+':'+#userId", unless = "#result == null")
    public PermMenuTreeMetaBO queryUserPermissions(String userId, String applicationName) {
        return systemMenuService.getUserMenus(userId);
    }

}
