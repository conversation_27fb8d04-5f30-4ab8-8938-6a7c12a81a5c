package com.cmpay.hacp.system.log.service.impl;

import com.cmpay.hacp.system.dao.ISysDynamicLogExtDao;
import com.cmpay.hacp.system.entity.SysDynamicLogDO;
import com.cmpay.hacp.system.log.bo.SysDynamicLogBO;
import com.cmpay.hacp.system.log.service.SystemDynamicLogService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SystemDynamicLogServiceImpl implements SystemDynamicLogService {

    @Resource
    private ISysDynamicLogExtDao sysDynamicLogExtDao;

    @Override
    @Async
    public void setDynamicRspDataSize(Long dataSize, String dataSizeType, String requestId, String interfaceRecord) {
        sysDynamicLogExtDao.updateDynamicRspDataSize(dataSize, dataSizeType, requestId, interfaceRecord);
    }


    @Override
    @Async
    public void addDynamicLog(SysDynamicLogBO sysDynamicLogBO) {
        SysDynamicLogDO dynamicLogDO = new SysDynamicLogDO();
        BeanUtils.copyProperties(dynamicLogDO, sysDynamicLogBO);
        if (JudgeUtils.isNotBlank(sysDynamicLogBO.getLogId())) {
            dynamicLogDO.setRequestId(sysDynamicLogBO.getLogId());
        }
        dynamicLogDO.setId(IdGenUtil.generatorLogId());
        sysDynamicLogExtDao.insert(dynamicLogDO);
    }

    @Override
    public List<SysDynamicLogBO> getDynamicListByRequestId(String requestId) {
        return sysDynamicLogExtDao.getDynamicListByRequestId(requestId);
    }
}
