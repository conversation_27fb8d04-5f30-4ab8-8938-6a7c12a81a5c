
package com.cmpay.hacp.system.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

/**
 * 系统部门DO
 *
 * @Author: ky_liu
 */
@DataObject
public class SystemDepartmentDO extends BaseDO {
    /**
     * @Fields deptId 部门id 主键
     */
    private String deptId;

    /**
     * @Fields parentsId 上级部门id，一级部门为0
     */
    private String parentId;

    /**
     * @Fields deptName 部门名称
     */
    private String deptName;

    /**
     * @Fields orderNum 排序
     */
    private Integer orderNum;

    /**
     * @Fields status DISABLE:禁用  ENABLE:启用
     */
    private String status;

    /**
     * @Fields createUserId 创建者id
     */
    private String createUserId;

    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;

    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}
