package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.bo.menu.MenuBO;
import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.hacp.system.dao.IRoleExtDao;
import com.cmpay.hacp.system.dao.IRoleMenuExtDao;
import com.cmpay.hacp.system.dao.IUserRoleExtDao;
import com.cmpay.hacp.system.entity.MenuDO;
import com.cmpay.hacp.system.entity.RoleDO;
import com.cmpay.hacp.system.entity.UserRoleDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemDepartmentService;
import com.cmpay.hacp.system.service.SystemDictionaryService;
import com.cmpay.hacp.system.service.SystemRoleService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SystemRoleServiceImpl implements SystemRoleService {

    @Autowired
    private IRoleExtDao roleExtDao;

    @Autowired
    private IUserRoleExtDao userRoleExtDao;

    @Autowired
    private IRoleMenuExtDao roleMenuExtDao;

    @Autowired
    private SystemDictionaryService systemDictionaryService;

    @Autowired
    private SystemDepartmentService systemDepartmentService;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public List<RoleBO> getAllRoles(String userId) {
        //不是系统管理员，只能查询本部门及子部门的角色
        RoleDO roleDO = new RoleDO();
        List<String> deptIds = new ArrayList<>();
        if (!systemDictionaryService.isAdmin(userId, applicationName)) {
            deptIds = systemDepartmentService.getDeptIds(userId);
        }
        List<RoleBO> roleList = roleExtDao.getRoles(roleDO, deptIds);
        return roleList;
    }


    @Override
    public RoleBO getRole(Long roleId) {
        RoleBO roleBO = new RoleBO();
        RoleDO roleDO = roleExtDao.get(roleId);
        if (JudgeUtils.isNotNull(roleDO)) {
            BeanUtils.copyProperties(roleBO, roleDO);
        }
        return roleBO;
    }


    @Override
    public List<MenuBO> getMenuIdsByRoleId(Long roleId) {
        List<MenuBO> menuBOS = new ArrayList<>();
        List<MenuDO> roleMenuDOS = roleMenuExtDao.getMenusByRoleId(roleId);
        if (JudgeUtils.isNotEmpty(roleMenuDOS)) {
            menuBOS = BeanConvertUtil.convertList(roleMenuDOS, MenuBO.class);
        }
        return menuBOS;
    }

    @Override
    public List<Long> getRolesByUserId(String userId) {
        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setUserId(userId);
        List<UserRoleDO> userRoleDOS = userRoleExtDao.find(userRoleDO);
        return userRoleDOS.stream().map(role -> role.getRoleId()).collect(Collectors.toList());
    }


    @Override
    public PageInfo<RoleBO> getRolesPage(Integer pageSize, Integer pageNum, String operatorId, RoleBO roleBO) {
        List<RoleBO> roleBOS = new ArrayList<>();
        PageInfo<RoleBO> pageInfo = new PageInfo<>(roleBOS);
        List<String> deptIds = new ArrayList<>();
        //不是系统管理员，只能查询本部门及子部门的角色
        if (!systemDictionaryService.isAdmin(operatorId, applicationName)) {
            deptIds = systemDepartmentService.getDeptIds(operatorId);
        }
        RoleDO roleDO = new RoleDO();
        BeanUtils.copyProperties(roleDO, roleBO);
        if (pageNum == 0 || pageSize == 0) {
            pageInfo = new PageInfo<RoleBO>(BeanConvertUtil.convertList(roleExtDao.getRoles(roleDO, deptIds), RoleBO.class));
        } else {
            List<String> finalDeptIds = deptIds;
            pageInfo = PageUtils.pageQueryWithCount(pageNum,
                    pageSize,
                    () -> BeanConvertUtil.convertList(roleExtDao.getRoles(roleDO, finalDeptIds), RoleBO.class));
        }
        return pageInfo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public Long add(String operatorId, RoleBO roleBO, List<Long> menuIds) {
        RoleDO roleDO = new RoleDO();
        BeanUtils.copyProperties(roleDO, roleBO);
        long update = roleExtDao.insertKey(roleDO);
        if (update < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
        this.addRoleMenu(operatorId, roleDO.getRoleId(), menuIds);
        return roleBO.getRoleId();
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void addRoleMenu(String operatorId, Long roleId, List<Long> menuIds) {
        //删除角色下所有菜单
        roleMenuExtDao.deleteByRoleId(roleId);
        int insertRoleMenus = this.batchInsertRoleMenus(roleId, menuIds);
        if (insertRoleMenus < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public Long update(String operatorId, RoleBO roleBO, List<Long> menuIds) {
        RoleDO roleDO = new RoleDO();
        BeanUtils.copyProperties(roleDO, roleBO);
        int update = roleExtDao.update(roleDO);
        if (update < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
        this.addRoleMenu(operatorId, roleBO.getRoleId(), menuIds);
        return roleBO.getRoleId();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public int batchInsertRoleMenus(Long roleId, List<Long> menuIds) {
        return roleMenuExtDao.batchInsertRoleMenus(roleId, menuIds);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void delete(String operatorId, Long roleId) {
        int delete = roleExtDao.delete(roleId);
        if (delete < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_DELETE_FAILED);
        }
        //删除用户角色关联数据
        userRoleExtDao.deleteByRoleId(roleId);
        //删除角色菜单关联数据
        roleMenuExtDao.deleteByRoleId(roleId);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void deleteBatch(String operatorId, List<Long> roleIds) {
        roleIds.stream().forEach(roleId -> {
            this.delete(operatorId, roleId);
        });
    }

    /**
     * 根据role id 列表获取所有的menus
     *
     * @param roleIds
     * @return
     */
    @Override
    public List<MenuBO> getMenuIdsByRoleIds(List<Long> roleIds) {
        List<MenuBO> menuvos = new ArrayList<>();
        roleIds.stream().forEach(roleId -> {
            menuvos.addAll(this.getMenuIdsByRoleId(roleId));
        });
        return menuvos;
    }

}
