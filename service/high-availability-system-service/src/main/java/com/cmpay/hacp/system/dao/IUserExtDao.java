package com.cmpay.hacp.system.dao;

import com.cmpay.hacp.system.bo.system.SystemUserBO;
import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.system.entity.UserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface IUserExtDao extends IUserDao {
    /**
     * 查询用户列表
     *
     * @param userDO
     * @return
     */
    List<UserBO> queryUsers(@Param("userDO") UserDO userDO, @Param("deptIds") List<String> deptIds);


    /**
     * 查询用户列表
     *
     * @param mobile
     * @return
     */
    List<UserBO> queryUsersByMobile(@Param("mobile") String mobile);

    /**
     * 更新用戶
     *
     * @param userDO
     * @return
     */
    int updateUserByMobile(UserDO userDO);

    /**
     * 查询所有用户手机号码
     *
     * @return
     */
    List<UserBO> getAllUserMobiles();

    /**
     * 禁用用户
     *
     * @param userId
     * @param modifyTime
     * @return
     */
    int disable(@Param("userId") String userId, @Param("modifyTime") LocalDateTime modifyTime);

    /**
     * 启用用户
     *
     * @param userId
     * @param modifyTime
     * @return
     */
    int enable(@Param("userId") String userId, @Param("modifyTime") LocalDateTime modifyTime);

    /**
     * 查询系统用户列表
     *
     * @param systemUser 系统用户详情
     * @return 系统用户列表
     */
    List<SystemUserBO> getSystemUsers(SystemUserBO systemUser);

    /**
     * 更新为当前系统用户，可以登录系统
     *
     * @param systemUser 系统用户详情
     * @return 系统用户列表
     */
    int changeOwnSystemUserHasRole(SystemUserBO systemUser);

    /**
     * 查询用户ID
     *
     * @param systemUser
     * @return 用户ID列表
     */
    List<String> findSystemUserId(SystemUserBO systemUser);


    List<UserBO> getUsersByUserId(List<String> userIds);
}
