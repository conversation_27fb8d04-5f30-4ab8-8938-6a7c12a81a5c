package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.constant.CaptchaConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemCaptchaService;
import com.cmpay.lemon.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

/**
 * 验证图片验证码
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SystemCaptchaServiceImpl implements SystemCaptchaService {

    /**
     * 随机数
     */
    private final char[] CHARS = "23456789abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ".toCharArray();

    @Autowired
    private SystemCacheService systemCacheService;
    /**
     * 图片验证码的失效时间
     */
    @Value("${hacp.login-config.captcha-valid-seconds:60}")
    private int captchaValidSeconds;

    @Override
    public boolean checkCaptcha(String captchaReqId, String captchaCode) {
        //1.参数检查
        if (StringUtils.isEmpty(captchaReqId) || StringUtils.isEmpty(captchaCode)) {
            BusinessException.throwBusinessException(MsgEnum.WRONG_IMAGE_CODE.getMsgCd(), MsgEnum.WRONG_IMAGE_CODE.getMsgInfo());
        }
        //2.从hazelcast获取保存的图片验证码
        String oldCaptchaCode = (String) systemCacheService.getValue(captchaReqId);
        if (StringUtils.isEmpty(oldCaptchaCode)) {
            BusinessException.throwBusinessException(MsgEnum.EXPIRED_IMAGE_CODE.getMsgCd(), MsgEnum.EXPIRED_IMAGE_CODE.getMsgInfo());
        }
        //3.判断输入的验证码是否正确(忽略大小写)，成功或失败后清除redis缓存
        if (!oldCaptchaCode.equalsIgnoreCase(captchaCode)) {
            log.info("验证失败，Redis 清除图片验证码码 {}={}", captchaReqId, captchaCode);
            systemCacheService.delete(captchaReqId);
            BusinessException.throwBusinessException(MsgEnum.WRONG_IMAGE_CODE.getMsgCd(), MsgEnum.WRONG_IMAGE_CODE.getMsgInfo());
        }
        systemCacheService.delete(captchaReqId);
        return true;

    }


    @Override
    public void captcha(HttpServletRequest request, HttpServletResponse resp, String captchaReqId) {
        //1.获取请求信息
        if (StringUtils.isEmpty(captchaReqId)) {
            BusinessException.throwBusinessException(MsgEnum.INCOMPLETE_PARAM.getMsgCd(), MsgEnum.INCOMPLETE_PARAM.getMsgInfo());
        }

        //2.构建图片验证码
        BufferedImage bufferedImage = new BufferedImage(CaptchaConstant.WIDTH, CaptchaConstant.HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics2d = bufferedImage.createGraphics();
        graphics2d.setColor(new Color(255, 255, 255));
        graphics2d.fillRect(0, 0, CaptchaConstant.WIDTH, CaptchaConstant.HEIGHT);
        try {
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < CaptchaConstant.NUMS; i++) {
                sb.append(CHARS[random.nextInt(CHARS.length)]);
            }
            String code = sb.toString();
            for (int i = 0; i < CaptchaConstant.NUMS; i++) {
                graphics2d.setColor(getRandColor(random, 1, 150));
                graphics2d.setFont(new Font("STIX", Font.PLAIN, random.nextInt(8) + 25));
                graphics2d.drawString(String.valueOf(code.charAt(i)),
                        CaptchaConstant.WIDTH / CaptchaConstant.NUMS * i,
                        CaptchaConstant.HEIGHT - random.nextInt(6) - 7);
            }
            for (int i = 0; i < 50; i++) {
                int xs = random.nextInt(CaptchaConstant.WIDTH);
                int ys = random.nextInt(CaptchaConstant.HEIGHT);
                int xe = xs + random.nextInt(CaptchaConstant.WIDTH / 8);
                int ye = ys + random.nextInt(CaptchaConstant.HEIGHT / 8);
                graphics2d.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));
                graphics2d.drawLine(xs, ys, xe, ye);
            }
            graphics2d.dispose();
            //3.将生成的图片验证码存入缓存当中
            systemCacheService.setValue(captchaReqId, code, captchaValidSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgEnum.ERROR_CREATE_IMAGE_CODE.getMsgCd(),
                    MsgEnum.ERROR_CREATE_IMAGE_CODE.getMsgInfo());
        }

        //4.返回图片流信息
        OutputStream outputStream = null;
        try {
            resp.setContentType("image/jpeg");
            resp.setHeader("Pragma", "No-cache");
            resp.setHeader("Cache-Control", "no-cache");
            resp.setDateHeader("Expire", 0);
            outputStream = resp.getOutputStream();
            ImageIO.write(bufferedImage, "jpg", outputStream);
            outputStream.close();
            outputStream.flush();
        } catch (IOException e) {
            BusinessException.throwBusinessException(MsgEnum.ERROR_CREATE_IMAGE_CODE.getMsgCd(),
                    MsgEnum.ERROR_CREATE_IMAGE_CODE.getMsgInfo());
        }

    }

    /**
     * 取其某一范围的color
     *
     * @param fc int 范围参数1
     * @param bc int 范围参数2
     * @return Color
     */
    private Color getRandColor(SecureRandom random, int fc, int bc) {
        // 取其随机颜色
        if (fc > 255) {
            fc = 255;
        }
        if (bc > 255) {
            bc = 255;
        }
        int r = fc + random.nextInt(bc - fc);
        int g = fc + random.nextInt(bc - fc);
        int b = fc + random.nextInt(bc - fc);
        return new Color(r, g, b);
    }
}
