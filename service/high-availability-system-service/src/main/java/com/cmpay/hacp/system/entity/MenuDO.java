/*
 * @ClassName MenuDO
 * @Description
 * @version 1.0
 * @Date 2021-09-02 15:24:38
 */
package com.cmpay.hacp.system.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class MenuDO extends BaseDO {
    /**
     * @Fields menuId 菜单id
     */
    private Long menuId;
    /**
     * @Fields parentId 父菜单id，一级菜单为0
     */
    private Long parentId;
    /**
     * @Fields name 菜单名称
     */
    private String name;
    /**
     * @Fields appId 应用英文简称
     */
    private String appId;
    /**
     * @Fields url 菜单url
     */
    private String url;
    /**
     * @Fields perms 授权(多个用逗号分隔，如：user:list,user:create)
     */
    private String perms;
    /**
     * @Fields type 类型   D：目录   M：菜单   B：按钮
     */
    private String type;
    /**
     * @Fields icon 菜单图标
     */
    private String icon;
    /**
     * @Fields orderNum 排序
     */
    private Long orderNum;
    /**
     * @Fields createUserId 创建者id
     */
    private String createUserId;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;
    /**
     * @Fields meta meta数据
     */
    private String meta;
    /**
     * @Fields component 组件名
     */
    private String component;
    /**
     * @Fields redirect 路由重定向跳转地址
     */
    private String redirect;
    /**
     * @Fields alias 对应的英文名
     */
    private String enName;

    /**
     * 父菜单名称
     */
    private String parentName;

    /**
     * 隐藏标题
     */
    private Boolean hideTitle;
    /**
     * 显示
     */
    private Boolean hidden;
    /**
     * 隐藏子菜单
     */
    private Boolean hideChildren;

    private Boolean keepalive;

    private Boolean hidePageTitleBar;

    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPerms() {
        return perms;
    }

    public void setPerms(String perms) {
        this.perms = perms;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getRedirect() {
        return redirect;
    }

    public void setRedirect(String redirect) {
        this.redirect = redirect;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public Boolean getHideTitle() {
        return hideTitle;
    }

    public void setHideTitle(Boolean hideTitle) {
        this.hideTitle = hideTitle;
    }

    public Boolean getKeepalive() {
        return keepalive;
    }

    public void setKeepalive(Boolean keepalive) {
        this.keepalive = keepalive;
    }

    public Boolean getHidePageTitleBar() {
        return hidePageTitleBar;
    }

    public void setHidePageTitleBar(Boolean hidePageTitleBar) {
        this.hidePageTitleBar = hidePageTitleBar;
    }

    public Boolean getHidden() {
        return hidden;
    }

    public void setHidden(Boolean hidden) {
        this.hidden = hidden;
    }

    public Boolean getHideChildren() {
        return hideChildren;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setHideChildren(Boolean hideChildren) {


        this.hideChildren = hideChildren;
    }
}