package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.log.service.SystemDynamicLogService;
import com.cmpay.hacp.system.log.service.impl.SystemDynamicLogServiceImpl;
import com.cmpay.hacp.system.properties.HacpWebAdminProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 日志记录Bean
 *
 * @author: lihuiquan
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({HacpWebAdminProperties.class})
public class HacpWebAdminManagementLogAutoConfiguration {

    /**
     * 运管管理系统
     *
     * @return
     */
    @Bean
    @ConditionalOnProperty(prefix = "hacp.web.admin.log", name = "management", havingValue = "true", matchIfMissing = false)
    @ConditionalOnClass({SystemDynamicLogService.class, SystemDynamicLogServiceImpl.class})
    public SystemDynamicLogService systemDynamicLogServiceImpl() {
        log.info("init SystemDynamicLogServiceImpl");
        return new SystemDynamicLogServiceImpl();
    }


}
