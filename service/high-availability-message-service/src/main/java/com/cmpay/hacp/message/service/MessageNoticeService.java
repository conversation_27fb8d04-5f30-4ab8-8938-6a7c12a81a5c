package com.cmpay.hacp.message.service;

import com.cmpay.hacp.message.bo.MessageContentExtBO;
import com.cmpay.hacp.message.bo.MessageContentUserBO;
import com.cmpay.lemon.framework.page.PageInfo;

/**
 * <AUTHOR>
 * @create 2024/09/29 9:35
 * @since 1.0.0
 */

public interface MessageNoticeService {
    int getNumberOfUnreadMessages(String userId);

    PageInfo<MessageContentExtBO> queryMessage(int pageNum, int pageSize, MessageContentUserBO messageContentExtBO);

    void readMessage(Long messageId ,String loginUserId);

    void readMessageExtraId(String extraId,String loginUserId,String workspaceId);

}
