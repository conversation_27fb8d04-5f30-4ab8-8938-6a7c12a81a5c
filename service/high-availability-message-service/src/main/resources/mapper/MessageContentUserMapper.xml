<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IMessageContentUserDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.MessageContentUserDO" >
        <id column="message_id" property="messageId" jdbcType="BIGINT" />
        <result column="message_content_id" property="messageContentId" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="BIT" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        message_id, message_content_id, user_id, status, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from message_content_user
        where message_id = #{messageId,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from message_content_user
        where message_id = #{messageId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.MessageContentUserDO" >
        insert into message_content_user
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="messageId != null" >
                message_id,
            </if>
            <if test="messageContentId != null" >
                message_content_id,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="status != null" >
                `status`,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="messageId != null" >
                #{messageId,jdbcType=BIGINT},
            </if>
            <if test="messageContentId != null" >
                #{messageContentId,jdbcType=INTEGER},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=BIT},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.MessageContentUserDO" >
        update message_content_user
        <set >
            <if test="messageContentId != null" >
                message_content_id = #{messageContentId,jdbcType=INTEGER},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                `status` = #{status,jdbcType=BIT},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where message_id = #{messageId,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.MessageContentUserDO" >
        select 
        <include refid="Base_Column_List" />
        from message_content_user
        <where >
            <if test="messageId != null" >
                and message_id = #{messageId,jdbcType=BIGINT}
            </if>
            <if test="messageContentId != null" >
                and message_content_id = #{messageContentId,jdbcType=INTEGER}
            </if>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and `status` = #{status,jdbcType=BIT}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>