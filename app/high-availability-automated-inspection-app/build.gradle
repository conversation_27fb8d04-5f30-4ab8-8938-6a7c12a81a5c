apply plugin: 'org.springframework.boot'

dependencies {
    implementation project(":rest:high-availability-inspection-rest")

    implementation 'com.cmpay:lemon-framework-starter-actuator-prometheus'
    implementation 'com.cmpay:lemon-framework-starter-actuator-security'
    implementation('com.cmpay:cmpay-tracing-starter')
}

springBoot {
    mainClass = 'com.cmpay.ha.inspection.HighAvailabilityAutomatedInspectionApplication'
}