<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="LMN"/>
    <springProperty scope="context" name="rootLevel" source="lemon.logger.level" defaultValue="INFO"/>
    <springProperty scope="context" name="sqlLevel" source="lemon.sql.level" defaultValue="DEBUG"/>
    <springProperty scope="context" name="maxFileSize" source="lemon.logger.maxFileSize" defaultValue="20MB"/>
    <springProperty scope="context" name="totalSizeCap" source="lemon.logger.totalSizeCap" defaultValue="10GB"/>
    <springProperty scope="context" name="maxHistory" source="lemon.logger.maxHistory" defaultValue="0"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/lemon-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/lemon-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/error-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/error-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="FILE-CLIENT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/client-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/client-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE-ACCESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/access-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/access-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE-STREAM-CONSUMER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/stream-consumer-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/stream-consumer-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE-STREAM-PRODUCER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/stream-producer-${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/stream-producer-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.cmpay.lemon.framework.springcloud.fegin" level="INFO" additivity="true">
        <appender-ref ref="FILE-CLIENT"/>
    </logger>

    <logger name="com.netflix.loadbalancer.LoadBalancerContext" level="DEBUG" additivity="true">
        <appender-ref ref="FILE-CLIENT"/>
    </logger>

    <logger name="webRequestAccessLogger" level="INFO" additivity="false">
        <appender-ref ref="FILE-ACCESS"/>
    </logger>

    <logger name="org.springframework.amqp.rabbit.listener.BlockingQueueConsumer" level="INFO" additivity="true">
        <appender-ref ref="FILE-STREAM-CONSUMER"/>
    </logger>

    <logger name="com.cmpay.lemon.framework.stream.consumer.DefaultInputConsumer" level="INFO" additivity="true">
        <appender-ref ref="FILE-STREAM-CONSUMER"/>
    </logger>

    <logger name="com.cmpay.lemon.framework.stream.logging.SimpleOutputLogger" level="INFO" additivity="true">
        <appender-ref ref="FILE-STREAM-PRODUCER"/>
    </logger>

    <!-- print sql -->
    <logger name="com.cmpay.monitor.dao" level="${sqlLevel}" additivity="true">
    </logger>

    <root level="${rootLevel}">
        <springProfile name="ci, sit, uat, str, pre, prd">
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR-FILE"/>
        </springProfile>
        <springProfile name="dev,sit">
            <appender-ref ref="STDOUT"/>
        </springProfile>
    </root>

</configuration>