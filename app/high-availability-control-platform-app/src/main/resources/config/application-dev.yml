
lemon:
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: *******************************************,*************:6446,*************:6446/hacp_sit_database?autoReconnect=true&serverTimezone=Asia/Shanghai
      username: hacpadm
      password: 'F6mK#w3u'

  security:
    authorize-requests:
      permit-all:
        - /v1/sys/encrypt-password
        - /v1/sys/captcha/getCaptcha
        - /v1/sys/cipher/rsa/publickey
        - /v1/sys/cipher/sm2/publickey
        - /v1/sys/cipher/keys
        - /v1/tenant/sys/log/add
        - /v1/tenant/workspace/application/sync
        - /v1/tenant/workspace/application/sync-all
        - /webjars/**
        - /swagger-resources
        - /swagger-resources/**
        - /v2/api-docs
        - /doc.html

hacp:
  admin:
    email-server:
      host: smtp.qiye.163.com
      port: 465
      user: <EMAIL>
      secret: xxx
  push:
    watch:
      initialDelay: 10000
      delay: 20000
  web:
    admin:
      cache-type: hazelcast
      log:
        management: true
#      aesKey: c74ff85723086efb96c05bb0dde4e928
#      Sensitive:
#        SM4Key: ec1039b2869428e76df8e41c60372101
    dashboard:
      grafana:
        api-url: 'http://***********:3000/api'
        dashboard-url-http: 'http://***********:3000/'
        dashboard-url-https: 'https://***********:3443/'
  emergence:
    kubesphere:
      properties:
        url: 'http://************:31407'
        client-id: 'kubesphere'
        client-secret: 'kubesphere'
        username: 'lihuiquangy'
        password: 'LIhuiquan@2024'
        grant-type: 'password'
  management:
    tenant-session-filter:
      permitUrls:
        - /v1/tenant/**
        - /v1/sys/**
        - /v1/message/notice/**

logging:
  level:
    #debug级别可以打印执行Sql
    org.camunda: debug

