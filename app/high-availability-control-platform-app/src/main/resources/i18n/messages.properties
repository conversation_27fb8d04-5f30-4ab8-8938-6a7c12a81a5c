DPZN0001 = 正在使用的版本不能再发布
DPZN0002 = 版本不存在
DPZN0003 = 发布备注不能为空
DPZN0004 = 推送过的版本不能被删除
DPZN0005 = 重复发布,已经发布的版本
DPZN0006 = 工作空间为空
DPZN0007 = 用户ID不能为空
DPZN0008 = 开启和关闭的调度不能都为空
DPZN0009 = 用户名称不能为空
DPZN0010 = 开启/关闭的调度不存在, 或者当前状态已经正确
DPZN1003 = 调度描述不能为空
DPZN1004 = 没有同步的调度节点
DPZN1005 = API绑定了的业务节点
DPZN1006 = API绑定了的业务服务
DPZN1007 = 调度已经绑定了API
DPZN1008 = 调度已经绑定了规则
DPZN1009 = 调度没有绑定API或tag
DPZN1010 = 调度不能同时绑定API和tag
DPZN1011 = 测试案例找不到
DPZN1012 = 节点地址不合法
DPZN1013 = 规则名称不合法
DPZN1014 = 机房标识不合法

DPZN2004 = 跨机房节点地址不能为空
DPZN2005 = 机房正在使用,请先删除
DPZN2006 = 规则名称已存在
DPZN2007 = 标签正在使用，无法删除
DPZN2008 = 服务名称已存在
DPZN2009 = 节点组名称已存在
DPZN2010 = 调度名称已经存在
DPZN2011 = 接口名称已经存在
DPZN2012 = 机房标识已经存在


HAC00000 = 交易成功
HAC50000 = 交易失败
HAC50001 = JSON转换错误

HAC00002 = 租户名称不能为空
HAC00003 = 租户管理员不能为空
HAC00004 = 租户ID不能为空
HAC00005 = 已存在相同项目名称
HAC00006 = 项目ID不能为空
HAC00007 = 项目名称不能为空
HAC00008 = 项目成员不能为空
HAC00009 = 主键ID不能为空
HAC00010 = 项目成员角色不能为空
HAC00011 = 已存在相同项目角色名称
HAC00015 = 项目角色名称不能为空
HAC00016 = 项目角色类型不能为空
HAC00017 = 菜单ID集合不能为空
HAC00018 = 已存在相同项目角色编码
HAC00019 = 项目角色编码不能为空
HAC00020 = 项目角色描述不能为空
HAC00021 = 项目角色ID不能为空
HAC00022 = 用户ID不能为空
HAC00023 = 列表中存在被其他项目使用的应用，请重新提交
HAC00024 = 注册中心地址不能为空
HAC00025 = 项目环境不能为空
HAC00026 = 机房名称不能为空
HAC00027 = 机房标识不能为空
HAC00028 = 当前页不能为空
HAC00029 = 每页数量不能为空
HAC00030 = 公钥不能为空
HAC00031 = 私钥不能为空
HAC00032 = 同城双活公钥不能为空


HAC00100 = 数据更新失败
HAC00101 = 数据删除失败
HAC00102 = 数据新增失败
HAC00103 = 查询记录不存在
HAC00104 = 已存在相同数据
HAC00105 = 数据正在使用
HAC00200 = 菜单名称不能为空
HAC00201 = 上级菜单不能为空
HAC00204 = 上级菜单只能为菜单类型
HAC00206 = 请先删除子菜单或按钮

HAC20000 = 任务参数设置有误
HAC20001 = 案例不存在或已删除
HAC20002 = 该案例尚未部署案例执行流程或已被删除
HAC20003 = 流程面板只能绘制一个流程案例
HAC20004 = 案例流程图校验失败
HAC20005 = 应急任务不存在或配置有误
HAC20006 = 任务处理人不能为空
HAC20007 = 当前任务不存在或已完成，请刷新再试！
HAC20008 = 执行任务失败，原因：当前用户非任务执行人
HAC20009 = 接口请求失败
HAC20010 = 该记录所归属项目不匹配，请切换项目再试
HAC20011 = 脚本执行连接失败，请检查网络或者账号密码
HAC20012 = 远程服务器断开失败
HAC20013 = 脚本执行失败, 请核实日志
HAC20014 = 流程中缺少开始节点
HAC20015 = 流程中缺少结束节点
HAC20016 = 脚本参数配置有误
HAC20017 = 任务不能为空！
HAC20018 = 任务类型不匹配！
HAC20019 = 请勿重复审批！
HAC20020 = 流程所处阶段不符合！
HAC20021 = 任务选择数据过多，请进行任务拆分!


HAC20101 = 脚本描述超出限制！
HAC20102 = 脚本内容超出限制！
HAC20103 = 脚本登录类型错误！
HAC20104 = 业务主键不能为空！


HAC21000 = 流程部署失败, 请检查流程设计
HAC21001 = 该流程处于挂起状态
HAC21002 = 对不起，流程已完结或不存在，请刷新再试！
HAC21003 = 对不起，应急调度流程执行失败
HAC21004 = 对不起，接口请求流程执行失败
HAC21005 = 对不起，系统脚本流程执行失败
HAC21006 = 请求方式暂仅支持GET/POST
HAC21007 = 警告，非法篡改数据！
HAC21008 = 加密key不存在！
HAC21009 = 地址或端口错误！
HAC21010 = 当前项目尚未绑定容器信息，请绑定后再试！
HAC21011 = 对不起，该租户账号获取授权失败
HAC21012 = 对不起，容器重启失败，请查看失败信息
HAC21013 = 对不起，当前pod已删除或不存在
HAC21014 = 对不起，获取容器pod列表失败
HAC21015 = 任务ID不能为空
HAC21016 = 该流程实例状态不能重启
HAC21017 = 账号信息不一致，请重填密码！

HAC22000 = 账号不能为空
HAC22001 = 端口不能为空
HAC22002 = 地址不能为空
HAC22003 = 机器类型不能为空
HAC22004 = ID不能为空
HAC22005 = 应用名不能为空

HAC30000 = 系统用户不存在
HAC30001 = 项目不存在
HAC30002 = 租户不存在
HAC30003 = 该用户已是此项目成员，只能进行删除或修改操作
HAC30004 = 对不起，您没有权限操作该数据
HAC30005 = 获取项目应用数据异常
HAC30006 = 抱歉，您当前还未加入任何租户，请联系【租户或项目】管理员添加
HAC30007 = 当前未选择项目或已选择的项目已过期,请选择或重新选择项目
HAC30009 = 无法直接删除项目负责人，请修改负责人后再删除！
HAC30010 = 删除失败，请先清空配置！

HAC30100 = 邮件发送异常！ 
HAC30101 = 邮件发送失败，邮箱连接超时，请检查邮箱配置！ 
HAC30102 = 邮件发送失败，请检查发送者/目标邮箱地址是否有效！

HAC40000 = 消息传递类不符合

HAC70001 = 已存在相同租户名称
HAC70002 = 账户或密码错误
HAC70003 = session已失效，请重新登录
HAC70005 = 用户名或密码错误
HAC70006 = 数据解密失败!
HAC70007 = 缓存key不能为空！
HAC70008 = 图形验证码错误
HAC70009 = 验证码已过期
HAC70010 = 参数不完整
HAC70011 = 手机号已注册
HAC70013 = 用户名已存在
HAC70014 = 用户名不存在
HAC70022 = 输入的旧密码有误
HAC70026 = 用户未登录
HAC70030 = 图形验证码生成错误
HAC70035 = 用户输入的新旧密码相同
HAC70036 = SM4密钥过期
HAC70037 = 生成SM4密钥异常
HAC70042 = 生成AccessToken失败
HAC70049 = 无权限访问资源
HAC70057 = 该用户角色不具备获取菜单权限
HAC70074 = 登陆状态检查异常!
HAC70078 = 传入的参数异常,无法对参数进行解析！
HAC70079 = 必要参数不能为空！
HAC70083 = 校验JwtSessionToken未通过,请求使用设备不一致
HAC70084 = 客户端ID不能为空
HAC70085 = 客户端密钥不能为空
HAC70086 = 授权类型不能为空
HAC70087 = 请求地址不能为空

HAC90000 = 同城双活接口调用失败！
HAC90001 = 数据加密失败！
HAC90002 = 数据解密失败！
HAC90003 = 同城双活配置不存在！
HAC90004 = 要发布的ID不能为空！
HAC90005 = 发布状态核验结果失败！

HAC10000 = 查询面板ID为空
HAC10001 = 未查询到面板信息
HAC10002 = 查询项目ID为空
HAC10003 = 查询面板类型为空
HAC10010 = 面板模板不存在
HAC10011 = 面板模板为空
HAC10020 = 指标数据源不存在
HAC10021 = 未查询到Grafana面板

HFA00000 = 交易成功
HFA99999 = 交易失败
HFA00001 = API元数据不能为空
HFA00002 = 租户ID不能为空
HFA00003 = 项目ID不能为空
HFA00004 = 机房标识不合法
HFA00005 = 客户端ID不能为空
HFA00010 = 配置数据不能为空
HFA00011 = 配置版本不能为空
HFA00012 = 业务配置不能都为空
HFA00020 = 业务节点组列表不能为空
HFA00021 = 业务节点名不合法
HFA00022 = 各机房业务节点列表不能为空
HFA00023 = 业务节点机房标识不合法
HFA00024 = 业务节点服务器列表不能为空
HFA00025 = 业务节点服务器地址不合法
HFA00026 = 业务节点调度分流入口地址不合法
HFA00030 = 业务服务列表不能为空
HFA00031 = 业务服务名不合法
HFA00032 = 业务服务监听地址不能为空
HFA00033 = 业务服务域名列表不能为空
HFA00034 = 业务服务域名不能为空
HFA00035 = 业务服务监听地址不合法
HFA00036 = 业务服务监听端口已被系统使用
HFA00040 = 业务接口列表不能为空
HFA00041 = 业务接口名不合法
HFA00042 = 业务接口URI匹配规则列表不能为空
HFA00043 = 业务接口URI匹配类型不能为空
HFA00044 = 业务接口URI匹配值不能为空
HFA00045 = 业务接口归属业务服务名不能为空
HFA00046 = 业务接口路由业务节点名不能为空
HFA00053 = 业务接口调度列表不能为空
HFA00054 = 业务接口调度名不合法
HFA00055 = 业务接口调度流量规则列表不能为空
HFA00056 = 业务接口调度流量规则名不能为空
HFA00060 = 业务流量规则不能为空
HFA00063 = 业务流量规则列表不能为空
HFA00064 = 业务流量规则名不合法
HFA00065 = 业务流量规则分流数据源不能为空
HFA00066 = 业务流量规则分流表达式列表不能为空
HFA00067 = 业务流量规则分流表达式参数名不能为空
HFA00068 = 业务流量规则分流表达式计算操作符不能为空
HFA00069 = 业务流量规则分流表达式参数值不能为空
HFA00070 = 业务流量规则分流表达式分机房权重不能为空
HFA00071 = 业务流量规则分流表达式分机房权重的机房标识不能为空
HFA00072 = 业务流量规则分流表达式分机房权重的权重值不合法
HFA00080 = 业务接口配置的归属业务服务名不存在
HFA00081 = 业务接口配置的路由业务节点名不存在
HFA00082 = 业务接口配置的调度ID不存在
HFA00083 = 业务接口配置的流量规则名不存在
HFA00100 = 待更新项目的此版本详细配置已存在
HFA00101 = 项目配置不属于此租户
HFA00102 = 项目此版本详细配置不存在
HFA00103 = 项目配置不存在
HFA00104 = 项目配置状态已禁用
HFA00200 = 后端Nginx配置测试失败
HFA00201 = 后端Nginx重加载失败
HFA00203 = 后端Nginx更新回退后重加载失败
HFA00204 = 后端Nginx更新回退后启动检查失败
HFA00300 = API响应数据为空
HFA00310 = API请求方法不合法
# HFA080 0 0-HFA08999由hafr-agent-router使用
HFA08000 = API请求数据为空
HFA08001 = API请求数据不合法
HFA08002 = API不支持
HFA08003 = API元数据为空
HFA08004 = API元数据不合法
HFA08999 = 后端未初始化

# 系统错误
HFA09000 = 系统错误（序列化配置失败）
HFA09001 = 系统错误（反序列化配置失败）
HFA09010 = 系统错误（渲染后端配置文件失败）
HFA09011 = 系统错误（写后端配置文件失败）
HFA09012 = 系统错误（备份后端配置文件失败）
HFA09013 = 系统错误（回退后端配置文件失败）
HFA09020 = 系统错误（调用后端接口失败）

SYS00001 = 对不起，系统忙，请稍后再试！
SYS00002 = 访问数据库异常
SYS00003 = 签名异常
SYS00404 = 404 error
SYS00401 = 401 error
SYS01401 = session forced to expire
SYS02401 = invalid refresh token
SYS03401 = 认证失败
SYS00403 = 禁止操作
SYS00005 = task schedule exception
SYS00006 = server 404 error
SYS00007 = 服务不可用
SYS00100 = 不能获取分布式锁
SYS00101 = 累计操作异常
SYS10001 = bean validation exception
SYS20000 = client exception
SYS20001 = UnknownHostException
SYS20002 = timeout excepiton
SYS30001 = illegal parameter
SYS40001 = producer of rabbit exception
SYS40021 = consumer of rabbit exception
SYS99999 = no message_code found
SYS11111 = warning

SYS50000 = 系统异常

SYS50101 = Excel的sheet名称不匹配！
SYS50102 = Excel模板不匹配！
SYS50103 = 导入失败！
SYS50104 = 解析失败！

MON00000=交易成功
MON40000=交易失败
MON00001=登录数据解析失败
MON00002=账户或密码错误
MON00003=session已失效，请重新登录
MON00100=数据更新失败
MON00101=数据删除失败
MON00102=数据新增失败
MON00103=查询记录不存在
MON00200=菜单名称不能为空
MON00201=上级菜单不能为空
MON00203=上级菜单只能为菜单或者目录
MON00204=上级菜单只能为菜单类型
UPM00022=输入的旧密码有误
UPM00035=用户输入的新旧密码相同
MON00205=系统菜单不允许删除
MON00206=请先删除子菜单或按钮
MON50000=系统异常
00000=成功
UPM00004=ACCESSTOKEN无效
UPM00005=用户名或密码错误
UPM00008=图形验证码错误
UPM00009=参数不完整
UPM00011=手机号已注册
UPM00012=手机号未注册
UPM00013=用户名已存在
UPM00014=用户名不存在
UPM00026=用户未登录
UPM00030=图形验证码生成错误
UPM00037=SESSION_TOKEN不存在
UPM00042=生成AccessToken失败
UPM00049=无权限访问资源
UPM00057=该用户角色不具备获取菜单权限
UPM00058=异常码转换异常
UPM00066=生成SessionToken失败
UPM00067=解析JwtSessionToken失败
UPM00068=校验JwtSessionToken未通过,颁发SessionToken的应用不一致
UPM00070=获取应用系统的publickey失败
UPM00071=JwtSessionToken已过期，请重新登录
UPM00072=校验JwtSessionToken未通过,用户的所属应用编码不一致!
UPM00073=校验JwtSessionToken未通过,用户的用户名不一致!
UPM00074=登陆状态检查异常!
UPM00078=传入的参数异常,无法对参数进行解析！
UPM00082=校验JwtSessionToken未通过,IP地址不一致
UPM00083=校验JwtSessionToken未通过,请求使用设备不一致
UPM00097=token的用途非法
UPM00098=单点登陆的目标应用appId非法