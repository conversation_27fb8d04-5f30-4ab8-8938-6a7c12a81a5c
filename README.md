---
# ICS扩展工具
### 工程简介

* ~~`hiclientutilext`: **已过时**~~
* `hiehcacheext`: EHCache基础
* `hiesscatc`: 密管平台组件
* `hifil`: 文件服务器
* `hifilemng`: 文件扫描工具
* `hiflowctrl`: 流量控制
* `himodatc`: 应用扩展包
* `himodexpr`: 应用扩展表达式
* `himodhandler`: 应用扩展服务
* `himsession`: 会话组件
* `himsessionframework`: 会话框架
* `hiqueue`: RabbitMQ基础 mongo连接
* `hismsrule`: CGW短信规则配置
* `hisession`: 会话功能，**代码版本问题**
* `himsessionc`: 会话缓存功能，**待确定**
* `hirabqueue`: 内存队列，**待确定**
* `hiunit`: ICS本地测试工具，**未使用**
* `hizok`: zookeeper功能，**未使用**
* `himonitor`: 监控工具，**未使用**
* `himq`: 内存队列，**未使用**
* `hirest`: ICS调用spring RESTful API适配客户端服务

## 版本构建
### 工具
* gradle 2.14.1
* jdk 1.5 ~ 1.7

### 发布
> 代码更新时发布到maven库

```gradle uploadArchives```