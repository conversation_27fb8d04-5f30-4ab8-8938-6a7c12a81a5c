package com.cmpay.hacp.api;

/**
 * <AUTHOR>
 */
public interface SystemDictApi extends BaseApi {

    /**
     * 删除字典信息
     */
    String DELETE = BASE_SYSTEM + "/dict/delete";

    /**
     * 新增字典
     */
    String ADD = BASE_SYSTEM + "/dict/add";

    /**
     * 修改字典
     */
    String UPDATE = BASE_SYSTEM + "/dict/update";

    /**
     * 查询字典信息
     */
    String INFO = BASE_SYSTEM + "/dict/info/{id}";

    /**
     * 字典下拉框
     */
    String SELECT = BASE_SYSTEM + "/dict/select";

    /**
     * 字典下拉框
     */
    String SELECT_CHILDREN = BASE_SYSTEM + "/dict/select-children";

    /**
     * 获取字典列表
     */
    String PAGE = BASE_SYSTEM + "/dict/page";

    /**
     * 字典树形结构
     */
    String TREE_DICT = BASE_SYSTEM + "/dict/tree";


    /**
     * 查询是否是管理员
     */
    String IS_ADMIN = BASE_SYSTEM + "/dict/isAdmin";
}
