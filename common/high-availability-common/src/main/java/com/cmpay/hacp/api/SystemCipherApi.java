package com.cmpay.hacp.api;

/**
 * <AUTHOR>
 */
public interface SystemCipherApi extends BaseApi {

    /**
     * 获取rsa公钥
     */
    String RSA_PUBLICKEY = BASE_SYSTEM + "/cipher/rsa/publickey";

    /**
     * 获取SM2公钥
     */
    String SM2_PUBLICKEY = BASE_SYSTEM + "/cipher/sm2/publickey";

    /**
     * 获取SM4秘钥
     */
    String SM4_KEY = BASE_SYSTEM + "/cipher/sm4/key";

    /**
     * rsa私钥解密
     */
    String RSA_DECRYPT = BASE_SYSTEM + "/cipher/rsa/decrypt";

    /**
     * SM2私钥解密
     */
    String SM2_DECRYPT = BASE_SYSTEM + "/cipher/sm2/decrypt";

    /**
     * SM4秘钥解密
     */
    String SM4_DECRYPT = BASE_SYSTEM + "/cipher/sm4/decrypt";


    /**
     * sm4随机盐
     */
    String KEYS = BASE_SYSTEM + "/cipher/keys";
}
