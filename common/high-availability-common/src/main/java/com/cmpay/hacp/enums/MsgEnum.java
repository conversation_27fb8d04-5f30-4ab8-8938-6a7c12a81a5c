package com.cmpay.hacp.enums;

import com.cmpay.lemon.common.AlertCapable;
import com.cmpay.lemon.common.utils.StringUtils;


/**
 * 错误码枚举
 *
 * <AUTHOR>
 * @since 2022-03-07 14:59:53
 */
public enum MsgEnum implements AlertCapable {
    SUCCESS("HAC00000", "交易成功"),
    /**
     * 交易失败
     */
    FAIL("HAC50000", "交易失败"),
    JSON_CONVERTED_ERROR("HAC50001","JSON转换错误"),
    /*===================  参数校验相关   ======================*/
    /**
     * 租户名称不能为空
     */
    TENANT_NAME_NOT_NULL("HAC00002", "租户名称不能为空"),
    /**
     * 租户管理员不能为空
     */
    TENANT_ADMIN_USER_NOT_NULL("HAC00003", "租户管理员不能为空"),
    /**
     * 租户ID不能为空
     */
    TENANT_ID_NOT_NULL("HAC00004", "租户ID不能为空"),
    /**
     * 已存在相同项目名称
     */
    EXIST_WORKPLACE_NAME("HAC00005", "已存在相同项目名称"),
    /**
     * 项目ID不能为空
     */
    WORKPLACE_ID_NOT_NULL("HAC00006", "项目ID不能为空"),
    /**
     * 项目名称不能为空
     */
    WORKPLACE_NAME_NOT_NULL("HAC00007", "项目名称不能为空"),
    /**
     * 项目成员不能为空
     */
    WORKPLACE_USER_NOT_NULL("HAC00008", "项目成员不能为空"),
    /**
     * 主键ID不能为空
     */
    ID_NOT_NULL("HAC00009", "主键ID不能为空"),

    /**
     * 项目成员角色不能为空
     */
    WORKPLACE_ROLE_NOT_NULL("HAC00010", "项目成员角色不能为空"),
    /**
     * 已存在相同项目角色
     */
    EXIST_WORKPLACE_ROLE_NAME("HAC00011", "已存在相同项目角色"),
    /**
     * 项目角色名称不能为空
     */
    WORKPLACE_ROLE_NAME_NOT_NULL("HAC00015", "项目角色名称不能为空"),
    /**
     * 项目角色类型不能为空
     */
    WORKPLACE_ROLE_TYPE_NOT_NULL("HAC00016", "项目角色类型不能为空"),
    /**
     * 菜单ID集合不能为空
     */
    WORKPLACE_ROLE_MENU_ID_NOT_NULL("HAC00017", "菜单ID集合不能为空"),
    /**
     * 已存在相同项目角色编码
     */

    EXIST_WORKPLACE_ROLE_CODE("HAC00018", "已存在相同项目角色编码"),
    /**
     * 项目角色编码不能为空
     */
    WORKPLACE_ROLE_CODE_NOT_NULL("HAC00019", "项目角色编码不能为空"),
    /**
     * 项目角色描述不能为空
     */
    WORKPLACE_ROLE_DESCRIBE_NOT_NULL("HAC00020", "项目角色描述不能为空"),
    /**
     * 项目角色ID不能为空
     */
    WORKPLACE_ROLE_ID_NOT_NULL("HAC00021", "项目角色ID不能为空"),

    /**
     * 用户ID不能为空
     */
    USER_ID_NOT_NULL("HAC00022", "用户ID不能为空"),

    /**
     * 列表中存在被其他项目使用的应用，请重新提交
     */
    APPLICATION_EXIST_WORKPLACE("HAC00023", "列表中存在被其他项目使用的应用，请重新提交"),
    /**
     * 注册中心地址不能为空
     */
    WORKPLACE_URL_NOT_NULL("HAC00024", "注册中心地址不能为空"),
    /**
     * 项目环境不能为空
     */
    WORKPLACE_PROFILE_NOT_NULL("HAC00025", "项目环境不能为空"),

    ROOM_NAME_NOT_NULL("HAC00026", "机房名称不能为空"),
    ROOM_LABEL_NOT_NULL("HAC00027", "机房标识不能为空"),

    PAGE_NUM_NOT_NULL("HAC00028", "当前页不能为空"),
    PAGE_SIZE_NOT_NULL("HAC00029", "每页数量不能为空"),


    /**
     * 数据更新失败
     */
    DB_UPDATE_FAILED("HAC00100", "数据更新失败"),

    /**
     * 数据删除失败
     */
    DB_DELETE_FAILED("HAC00101", "数据删除失败"),

    /**
     * 数据新增失败
     */
    DB_INSERT_FAILED("HAC00102", "数据新增失败"),

    /**
     * 查询记录不存在
     */
    DB_SELECT_FAILED("HAC00103", "查询记录不存在"),
    /**
     * 已存在相同数据
     */
    DB_THE_SAME_DATA_EXISTS("HAC00104", "已存在相同数据"),
    DATA_IS_BEING_USE("HAC00105", "数据正在使用"),

    /**
     * 菜单名称不能为空
     */
    MENU_NAME_CANNOT_NULL("HAC00200", "菜单名称不能为空"),

    /**
     * 上级菜单不能为空
     */
    PARENT_MENU_CANNOT_NULL("HAC00201", "上级菜单不能为空"),

    /**
     * 上级菜单只能为菜单类型
     */
    PARENT_MENU_MUSTBE_MENU("HAC00204", "上级菜单只能为菜单类型"),

    /**
     * 请先删除子菜单或按钮
     */
    DELETE_SUBMENU_OR_BUTTON_FIRST("HAC00206", "请先删除子菜单或按钮"),

    /*===================    应急案例相关       =============================*/

    TASK_PARAM_IS_ERROR("HAC20000", "任务参数设置有误"),
    CASE_NOT_EXIST("HAC20001", "案例不存在或已删除"),
    CASE_NOT_DEPLOY_BPMN("HAC20002", "该案例尚未部署案例执行流程或已被删除"),
    CASE_IS_TOO_MANY("HAC20003", "流程面板只能绘制一个流程案例"),
    CASE_PROCESS_CHECK_FAILED("HAC20004", "案例流程图校验失败"),
    TASK_NOT_EXIST("HAC20005", "应急任务不存在或配置有误"),
    TASK_OPERATOR_IS_NULL("HAC20006", "任务处理人不能为空"),
    TASK_NOT_EXIST_OR_FINISHED("HAC20007", "当前任务不存在或已完成，请刷新再试！"),
    TASK_OPERATOR_NOT_YOU("HAC20008", "执行任务失败，原因：当前用户非任务执行人"),
    API_REQUEST_FAILED("HAC20009", "结果表达式匹配失败！"),
    WORKPLACE_NOT_MATCHING("HAC20010", "该记录所归属项目不匹配，请切换项目再试"),
    REMOTE_MACHINE_CONNECTION_FAIL("HAC20011", "脚本执行连接失败，请检查网络或者账号密码"),
    REMOTE_MACHINE_DISCONNECTION_FAIL("HAC20012", "远程服务器断开失败"),
    SCRIPT_EXECUTION_FAILED("HAC20013", "脚本执行失败, 请核实日志"),
    CASE_PROCESS_NOT_FOUND_START_NODE("HAC20014", "流程中缺少开始节点"),
    CASE_PROCESS_NOT_FOUND_END_NODE("HAC20015", "流程中缺少结束节点"),
    SCRIPT_PARAM_ERROR("HAC20016", "对不起，脚本参数配置有误"),

    TASK_IS_NULL_ERROR("HAC20017", "任务不能为空！"),

    TASK_TYPE_IS_ERROR("HAC20018", "任务类型不匹配！"),

    DON_T_DUPLICATE_APPROVALS("HAC20019","请勿重复审批！"),

    THE_PROCESS_IS_AT_A_STAGE_THAT_DOES_NOT_MEET("HAC20020","流程所处阶段不符合"),

    TASK_PARAM_IS_TOO_LONG("HAC20021","任务选择数据过多，请进行任务拆分!"),


    SCRIPT_DESC_THE_LENGTH_EXCEEDS_THE_LIMIT("HAC20101", "脚本描述超出限制！"),
    SCRIPT_CONTENT_THE_LENGTH_EXCEEDS_THE_LIMIT("HAC20102", "脚本内容超出限制！"),
    BUSINESS_KEY_IS_NOT_NULL("HAC20104", "业务主键不能为空！"),
    SHELL_LOGIN_TYPE_ERROR("HAC20103", "脚本登录类型错误！"),

    PROCESS_DEPLOY_FAILED("HAC21000", "流程部署失败, 请检查流程设计"),

    PROCESS_IS_SUSPENDED("HAC21001", "该流程处于挂起状态"),

    PROCESS_IS_NOT_EXIST("HAC21002", "对不起，流程已完结或不存在，请刷新再试！"),

    PROCESS_DISPATCH_EXECUTE_FAILED("HAC21003", "对不起，应急调度流程执行失败"),

    PROCESS_API_EXECUTE_FAILED("HAC21004", "对不起，接口请求流程执行失败"),

    PROCESS_SCRIPT_EXECUTE_FAILED("HAC21005", "对不起，系统脚本流程执行失败"),
    API_METHOD_NOT_SUPPORT("HAC21006", "请求方式暂仅支持GET/POST"),

    ILLEGAL_TAMPERING_WITH_DATA("HAC21007","警告，非法篡改数据！"),

    ENCRYPT_KEY_IS_NOT_EXIST("HAC21008","加密key不存在！"),
    ADDRESS_PORT_IS_ERROR("HAC21009","地址或端口错误！"),
    KUBESPHERE_CONFIG_IS_NULL("HAC21010","当前项目尚未绑定容器信息，请绑定后再试！"),
    KUBESPHERE_PERMISSION_IS_ERROR("HAC21011","对不起，该租户账号获取授权失败"),
    POD_DELETE_FAIL("HAC21012","对不起，容器重启失败，请查看失败信息"),
    POD_NOT_EXIST("HAC21013","对不起，当前pod已删除或不存在"),
    GET_KUBESPHERE_FAIL("HAC21014","对不起，获取容器列表失败"),

    TASK_ID_IS_NOT_NULL("HAC21015","任务ID不能为空"),

    PROCESS_RESTART_FAILED("HAC21016","该流程实例状态不能重启"),

    KUBESPHERE_PASSWORD_IS_ERROR("HAC21017","账号信息不一致，请重填密码！"),

    /*===================    租户相关       =============================*/
    /**
     * 系统用户不存在
     */
    SYS_USER_NOT_EXIST("HAC30000", "系统用户不存在"),
    /**
     * 项目不存在
     */
    WORKPLACE_NOT_EXIST("HAC30001", "项目不存在"),

    /**
     * 租户不存在
     */
    TENANT_NOT_EXIST("HAC30002", "租户不存在"),

    /**
     * 该用户已是此项目成员，只能进行删除或修改操作
     */
    WORKPLACE_USER_EXIST("HAC30003", "该用户已是此项目成员，只能进行删除或修改操作"),

    /**
     * 对不起，您没有权限操作该数据
     */
    NOT_OPERATION_PERMISSION("HAC30004", "对不起，您没有权限操作该数据"),

    /**
     * 获取项目应用数据异常
     */
    QUERY_WORKPLACE_APPLICATION_EXCEPTION("HAC30005", "获取项目应用数据异常"),

    /**
     * 抱歉，您当前还未加入任何租户，请联系【租户或项目】管理员添加
     */
    USER_IS_NOT_EXIST_WORKPLACE("HAC30006", "抱歉，您当前还未加入任何租户，请联系【租户或项目】管理员添加"),

    /**
     * 当前未选择项目或已选择的项目已过期,请选择或重新选择项目
     */
    QUERY_WORKPLACE_EXPIRE("HAC30007", "当前未选择项目或已选择的项目已过期,请选择或重新选择项目"),

    /**
     * 已存在相同租户名称
     */
    EXIST_TENANT_NAME("HAC30008", "已存在相同租户名称"),

    /**
     * 已存在相同租户名称
     */
    DELETE_WORKPLACE_PERSON_IN_CHARGE_ERROR("HAC30009", "无法直接删除项目负责人，请修改负责人后再删除！"),

    /**
     * 删除失败，请先清空配置！
     */
    DELETE_WORKPLACE_CLEAR_CONFIG_ERROR("HAC30010", "删除失败，请先清空配置！"),

    EMAIL_MASSAGE_SEND_FAILED("HAC30100","邮件发送异常！"),
    EMAIL_MASSAGE_SEND_TIMEOUT_FAILED("HAC30101","邮件发送失败，邮箱连接超时，请检查邮箱配置！"),
    EMAIL_MASSAGE_SEND_FAILURE_FAILED("HAC30102","邮件发送失败，请检查发送者/目标邮箱地址是否有效！"),


    /*  =================               MSG 相关    ======================*/
    MESSAGE_CLASS_ERROR("HAC40000","消息传递类不符合"),


    /*===================   系统相关 ========================*/

    /**
     * 系统异常
     */
    SYSTEM_EXCEPTION("SYS50000", "系统异常"),

    EXCEL_SHEET_NAME_ERROR("SYS50101","Excel的sheet名称不匹配！"),
    EXCEL_TEMPLATE_ERROR("SYS50102","Excel模板不匹配！"),

    EXCEL_IMPORT_ERROR("SYS50103","导入失败！"),
    EXCEL_ANALYSIS_ERROR("SYS50104","解析失败！"),

    /**
     * 账户或密码错误
     */
    LOGIN_ACCOUNT_OR_PASSWORD_ERROR("HAC70002", "账户或密码错误"),

    /**
     * session已失效，请重新登录
     */
    LOGIN_SESSION_EXPIRE("HAC70003", "session已失效，请重新登录"),

    /**
     * 用户名或密码错误
     */
    WRONG_USERNAME_OR_PASSWORD("HAC70005", "用户名或密码错误"),
    SM4_AND_SM2_DECRYPTION_FAILED("HAC70006", "数据解密失败！"),

    /**
     * 图片验证码错误
     */
    WRONG_IMAGE_CODE("HAC70008", "图形验证码错误"),

    EXPIRED_IMAGE_CODE("HAC70009", "验证码已过期"),

    /**
     * 参数不完整
     */
    INCOMPLETE_PARAM("HAC70010", "参数不完整"),

    /**
     * 手机号已存在
     */
    MOBILE_ALREADY_EXISTS("HAC70011", "手机号已注册"),

    /**
     * 用户名已存在
     */
    USERNAME_ALREADY_EXISTS("HAC70013", "用户名已存在"),

    /**
     * 用户名不存在
     */
    USERNAME_NOT_EXISTS("HAC70014", "用户名不存在"),

    /**
     * 输入的原始密码有误
     */
    WRONG_ORIGIN_PASSWORD("HAC70022", "输入的旧密码有误"),
    /**
     * 用户未登录
     */
    USER_NOT_LOGIN_IN("HAC70026", "用户未登录"),
    /**
     * 图片验证码生成错误
     */
    ERROR_CREATE_IMAGE_CODE("HAC70030", "图形验证码生成错误"),

    /**
     * 用户输入的新旧密码相同
     */
    SAME_PASSWORD("HAC70035", "用户输入的新旧密码相同"),

    SM4_RANDOM_SALT_OVERDUE("HAC70036", "SM4密钥过期"),
    SM4_RANDOM_SALT_GENERATION_ERROR("HAC70037", "生成SM4密钥异常"),
    SM4_RANDOM_SALT_CACHE_NULL_ERROR("HAC70038", "SM4密钥缓存为空！"),


    /**
     * 生成AccessToken失败
     */
    FAILED_TO_GET_ACCESSTOKEN("HAC70042", "生成AccessToken失败"),

    /**
     * 无权限访问资源
     */
    MENU_NO_PERMISSION("HAC70049", "无权限访问资源"),

    /**
     * 该用户角色不具备获取菜单权限
     */
    USER_NOT_HAS_ROLE("HAC70057", "该用户角色不具备获取菜单权限"),

    /**
     * 登陆状态过滤器检查发生了异常
     */
    LOGIN_STATUS_CHECK_EXCEPTION("HAC70074", "登陆状态检查异常!"),

    /**
     * 传入的参数异常,无法对参数进行解析！
     */
    MAX_LENGTH_GO_BEYOND_ERROR("HAC70077", "参数长度超出！"),
    /**
     * 传入的参数异常,无法对参数进行解析！
     */
    PARAM_PARSE_ERROR("HAC70078", "传入的参数异常,无法对参数进行解析！"),
    PARAM_IS_NULL_ERROR("HAC70079", "必要参数不能为空！"),

    /**
     * 校验SessionToken未通过,请求使用设备不一致
     */
    FAILED_TO_CHECK_SESSIONTOKEN_CAUSE_EQUIPMENT("HAC70083", "校验JwtSessionToken未通过,请求使用设备不一致"),

    /*
    第三方接口使用 ，930000-949999
     */
    CMFT_DISPATCH_API_ERROR("HAC90000", "同城双活接口调用失败！" ),
    CMFT_DISPATCH_ENCRYPT_ERROR("HAC90001", "数据加密失败！" ),
    CMFT_DISPATCH_DECRYPT_ERROR("HAC90002", "数据解密失败！" ),
    CMFT_DISPATCH_CONFIG_NOT_EXIST("HAC90003", "同城双活配置不存在！"),
    CMFT_DISPATCH_IDS_NOT_NULL("HAC90004","正在发布的ID不能为空！"),
    RELEASE_VERIFY_ERROR("HAC90005","状态核验结果失败！请检查信息。"),
    ;


    private String msgCd;
    private String msgInfo;

    MsgEnum(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    @Override
    public String getMsgCd() {
        return msgCd;
    }

    @Override
    public String getMsgInfo() {
        return msgInfo;
    }

    public static AlertCapable getEnum(String msgCd) {
        for (MsgEnum msgEnum : MsgEnum.values()) {
            if (StringUtils.equals(msgEnum.getMsgCd(), msgCd)) {
                return msgEnum;
            }
        }
        return null;
    }
}
