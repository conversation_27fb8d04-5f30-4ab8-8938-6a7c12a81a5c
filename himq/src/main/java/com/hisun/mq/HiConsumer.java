package com.hisun.mq;

import com.hisun.mq.networking.request.MessagePushCommand;
import com.taobao.gecko.core.command.ResponseStatus;
import com.taobao.gecko.core.util.OpaqueGenerator;
import com.taobao.gecko.service.Connection;
import com.taobao.gecko.service.exception.NotifyRemotingException;

public class HiConsumer {

	private String name;
	
	private Connection conn;
	
	private String groupName;

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Connection getConn() {
		return conn;
	}

	public void setConn(Connection conn) {
		this.conn = conn;
	}
	
	public void send(Message message) {
		if(this.conn == null) {
			return ;
		}
		
		//--
		String data = message.getmsgBody();
		MessagePushCommand cmd = new MessagePushCommand(OpaqueGenerator.getNextOpaque());
		cmd.setData(data);
		cmd.setMsgId(message.getMsgId());
		
		try {
			this.conn.send(cmd);
			//this.conn.response(respCmd);
		} catch (NotifyRemotingException e) {
			e.printStackTrace();
		}
	}
	
}
