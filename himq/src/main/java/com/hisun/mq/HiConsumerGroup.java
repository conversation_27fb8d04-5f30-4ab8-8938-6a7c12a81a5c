package com.hisun.mq;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Random;

public class HiConsumerGroup {

	private String groupName;
	
	private List consumerList = new ArrayList();
	
	public void addConsumer(HiConsumer consumer) {
		this.consumerList.add(consumer);
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
	public HiConsumer getConsumerByRandom() {
		Random random = new Random();
		int r = random.nextInt();
		int c = this.consumerList.size();
		int i = r % c;
		int j = 0;
		for (Iterator iter = this.consumerList.iterator(); iter.hasNext();) {
			if (i == j) {
				HiConsumer consumer = (HiConsumer)iter.next();
				return consumer;
			}

			j++;
		}
		return null;
	}
}
