package com.hisun.mq;

public class Message {
	private String msgId; // 消息ID 全局唯一
	private String txDt; // 交易日期
	private String topic; // 主题名称
	private boolean isAck = false; // 消息是否反馈标志
	private boolean reTry = false; // 是否重发标志
	// private String nodeId;
	private String sip; // 消息源IP
	private char msgSts = 'U'; // U：初始化 P：处理过程中 F：失败 S:成功
	private int sndTms = 0; // 尝试次数
	private int maxTms = 0; // 最大尝试次数
	private int failTms = 0; // 失败发送次数
	private int startTm; // 开始处理时间
	private int endTm; // 处理结束时间
	private int regTm; // 登记队列时间
	private String msgBody; // 消息体

	public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

	public String getTopic() {
		return topic;
	}

	public void setTopic(String topic) {
		this.topic = topic;
	}

	public String getTxDt() {
		return txDt;
	}

	public void setTxDt(String txDt) {
		this.txDt = txDt;
	}

	public String getSip() {
		return sip;
	}

	public void setSip(String sip) {
		this.sip = sip;
	}

	public char getMsgSts() {
		return msgSts;
	}

	public void setMsgSts(char msgSts) {
		this.msgSts = msgSts;
	}

	public int getSndTms() {
		return sndTms;
	}

	public void setSndTms(int sndTms) {
		this.sndTms = sndTms;
	}

	public int getMaxTms() {
		return maxTms;
	}

	public void setMaxTms(int maxTms) {
		this.maxTms = maxTms;
	}

	public boolean isAck() {
		return isAck;
	}

	public void setAck(boolean isAck) {
		this.isAck = isAck;
	}
	
	public boolean reTry() {
		return this.reTry;
	}
	
	public void setRetry(boolean reTry) {
		this.reTry = reTry;
	}

	public String getmsgBody() {
		return msgBody;
	}

	public void setmsgBody(String msgBody) {
		this.msgBody = msgBody;
	}

	public int getFailTms() {
		return failTms;
	}

	public void setFailTms(int failTms) {
		this.failTms = failTms;
	}

	public int getStartTm() {
		return startTm;
	}

	public void setStartTm(int startTm) {
		this.startTm = startTm;
	}

	public int getEndTm() {
		return endTm;
	}

	public void setEndTm(int endTm) {
		this.endTm = endTm;
	}
	
	public String toString() {
		return "MSG_ID:"+ this.msgId +" MSG_BODY:["+this.msgBody+"]";
	}
}
