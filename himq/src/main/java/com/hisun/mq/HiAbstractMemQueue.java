package com.hisun.mq;

import java.util.concurrent.ConcurrentLinkedQueue;

import org.apache.commons.lang.StringUtils;

import com.hisun.mq.dao.IQueueDAO;
import com.hisun.mq.dao.QueueDAOImpl;
import com.hisun.mq.exception.DAOException;

public abstract class HiAbstractMemQueue extends HiAbstratcQueue {
	protected ConcurrentLinkedQueue <Message> messageQueue = new ConcurrentLinkedQueue<Message>();
	
	protected IQueueDAO queueDAO;
	
	private String tblnm;
	
	public HiAbstractMemQueue(){
		try {
			queueDAO = new QueueDAOImpl();
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public void setQueueDAO(IQueueDAO queueDAO) {
		this.queueDAO = queueDAO;
	}
	
	public String getTblnm() {
		return tblnm;
	}

	public void setTblnm(String tblnm) {
		this.tblnm = tblnm;
	}

	public void createTable(String s) {
		if(StringUtils.isEmpty(this.tblnm)) {
			this.tblnm = "MSGTQUEUE1";  //如果没有指定表名，为公共队列表名； 
		}
		
		queueDAO.setTblNam(this.tblnm);
		try {
			queueDAO.createTable(this.tblnm);
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
