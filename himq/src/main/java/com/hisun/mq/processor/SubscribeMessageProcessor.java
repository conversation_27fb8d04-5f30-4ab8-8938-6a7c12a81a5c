package com.hisun.mq.processor;

import java.util.concurrent.ThreadPoolExecutor;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.networking.request.MessagePushCommand;
import com.taobao.gecko.service.Connection;
import com.taobao.gecko.service.RequestProcessor;

public class SubscribeMessageProcessor implements RequestProcessor <MessagePushCommand> {

	static final Log log = LogFactory.getLog(SubscribeMessageProcessor.class);
	
	public SubscribeMessageProcessor() {
		
	}
	
	@Override
	public void handleRequest(MessagePushCommand request, Connection conn) {
		if(log.isInfoEnabled()) {
			log.info("handler subscribe message starting....");
		}
		
		String data = request.getData();
		log.info("data:["+data+"]");
	}

	@Override
	public ThreadPoolExecutor getExecutor() {
		return null;
	}

}
