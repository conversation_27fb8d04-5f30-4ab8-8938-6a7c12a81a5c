package com.hisun.mq.processor;

import java.util.concurrent.ThreadPoolExecutor;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.HiConsumer;
import com.hisun.mq.HiConsumerGroup;
import com.hisun.mq.HiQueueContainer;
import com.hisun.mq.HiSubscribeMemQueue;
import com.hisun.mq.HiTopic;
import com.hisun.mq.IQueue;
import com.hisun.mq.networking.request.ConsumerRegisterCommand;
import com.hisun.mq.networking.response.BooleanCommand;
import com.taobao.gecko.core.command.ResponseStatus;
import com.taobao.gecko.service.Connection;
import com.taobao.gecko.service.RequestProcessor;
import com.taobao.gecko.service.exception.NotifyRemotingException;

public class ConsumerRegisterProcessor implements RequestProcessor<ConsumerRegisterCommand> {

	static final Log log = LogFactory.getLog(ConsumerRegisterProcessor.class);
	
	public ConsumerRegisterProcessor() {
		
	}
	
	@Override
	public void handleRequest(ConsumerRegisterCommand request, Connection conn) {
		if(log.isDebugEnabled()) {
			log.debug("consumer register starting ");
		}
		
		//--
		String topicName = request.getTopic();
		if(StringUtils.isEmpty(topicName)) {
			BooleanCommand respCmd = new BooleanCommand(request.getOpaque(), ResponseStatus.ERROR, "请输入主题");
			try {
				conn.response(respCmd);
			} catch (NotifyRemotingException e) {
				log.error("",e);
			}
			return;
		}
		String queueName = request.getQueNam();
		String groupName = request.getGroupNam();
		HiQueueContainer container = HiQueueContainer.getInstance();
		IQueue queue = container.getQueueByName(queueName);
		
		HiSubscribeMemQueue que = (HiSubscribeMemQueue)queue;
		HiTopic topic = que.getTopic(topicName);
		if(topic == null) {
			topic = new HiTopic();
			topic.setTopicName(topicName);
			que.addTopic(topic);
		}
		
		HiConsumerGroup group = topic.getConsumer(groupName);
		if(group == null) {
			group = new HiConsumerGroup();
			group.setGroupName(groupName);
			topic.addConsumer(group);
		}
		
		HiConsumer consumer = new HiConsumer();
		consumer.setName("");
		consumer.setGroupName(groupName);
		consumer.setConn(conn);
		group.addConsumer(consumer);
		
		BooleanCommand respCmd = new BooleanCommand(request.getOpaque(), ResponseStatus.NO_ERROR, "注册成功");
		try {
			conn.response(respCmd);
		} catch (NotifyRemotingException e) {
			log.error("",e);
		}
	}

	@Override
	public ThreadPoolExecutor getExecutor() {
		return null;
	}

}
