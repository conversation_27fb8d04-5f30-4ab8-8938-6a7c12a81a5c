package com.hisun.mq.utils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Vector;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class HiThreadPool extends ThreadPoolExecutor {
	// 线程池Map
	static ConcurrentHashMap threadPoolMap = new ConcurrentHashMap();
	final ConcurrentLinkedQueue<HiWorker> workers = new ConcurrentLinkedQueue<HiWorker>();

	public static ConcurrentHashMap getThreadPoolMap() {
		return threadPoolMap;
	}
	
	public ConcurrentLinkedQueue<HiWorker> getWorkers() {
		return workers;
	}
	
	String name;
	public HiThreadPool(String name) {
		super(5, 50, 60L, TimeUnit.SECONDS, new SynchronousQueue(),
				hisunThreadFactory(name));
		this.name = name;
		threadPoolMap.put(name, this);
	}

	protected void beforeExecute(Thread t, Runnable r) {
		super.beforeExecute(t, r);
		workers.add(new HiWorker(t, r));

	}

	protected void afterExecute(Runnable r, Throwable t) {
		super.afterExecute(r, t);
		for (HiWorker worker : workers) {
			if (worker.task == r) {
				workers.remove(worker);
			}
		}
	}

	private HiThreadPool(String name, int min, int max, int queue) {
		super(min, max, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue(queue),
				hisunThreadFactory(name));
		this.name = name;
		threadPoolMap.put(name, this);
	}

	private HiThreadPool(String name, int min, int max) {
		super(min, max, 60L, TimeUnit.SECONDS, new SynchronousQueue(),
				hisunThreadFactory(name));
		this.name = name;
		threadPoolMap.put(name, this);
	}

	public static HiThreadPool createThreadPool(String name) {
		return new HiThreadPool(name);
	}

	public static HiThreadPool createThreadPool(String name, int minThreads,
			int maxThreads) {
		return new HiThreadPool(name, minThreads, maxThreads);
	}

	public static HiThreadPool createThreadPool(String name, int minThreads,
			int maxThreads, int queueSize) {
		return new HiThreadPool(name, minThreads, maxThreads, queueSize);
	}

	// 线程命名个性化
	public static ThreadFactory hisunThreadFactory(final String name) {
		SecurityManager s = System.getSecurityManager();
		final ThreadGroup group = (s != null) ? s.getThreadGroup() : Thread
				.currentThread().getThreadGroup();
		final AtomicInteger threadNumber = new AtomicInteger(1);
		final String namePrefix = "hisun-pool-" + name + "-thread-";

		return new ThreadFactory() {
			public Thread newThread(Runnable r) {
				Thread t = new Thread(group, r, namePrefix
						+ threadNumber.getAndIncrement(), 0);
				if (t.isDaemon())
					t.setDaemon(false);
				if (t.getPriority() != Thread.NORM_PRIORITY)
					t.setPriority(Thread.NORM_PRIORITY);
				return t;
			}
		};
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

}

class HiArrayBlockingQueue extends ArrayBlockingQueue {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6024891247208676281L;

	public HiArrayBlockingQueue() {
		super(1);
	}

	public boolean offer(Object e) {
		return false;
	}
}
