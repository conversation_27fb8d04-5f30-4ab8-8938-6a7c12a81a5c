package com.hisun.mq.utils;

import java.io.File;
import java.io.IOException;
import java.net.URL;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.ini4j.Ini;
import org.ini4j.InvalidFileFormatException;
import org.ini4j.Profile.Section;

public class MQServerConfig {
	
	private static final Log log = LogFactory.getLog(MQServerConfig.class);

	private int serverPort = 8579;
	
	private String dataSource;  //数据库
	
	private int getProcessThreadCount = 10 * Runtime.getRuntime().availableProcessors();

    private int putProcessThreadCount = 10 * Runtime.getRuntime().availableProcessors();
    
    private int maxMessageSize = 4 * 1000;

    private long lastModified ;
    
    private String queue_cfg;
    private String db_cfg;
	public int getServerPort() {
		return serverPort;
	}

	public void setServerPort(int serverPort) {
		this.serverPort = serverPort;
	}

	public String getDataSource() {
		return dataSource;
	}

	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}

	public int getGetProcessThreadCount() {
		return getProcessThreadCount;
	}

	public void setGetProcessThreadCount(int getProcessThreadCount) {
		this.getProcessThreadCount = getProcessThreadCount;
	}

	public int getPutProcessThreadCount() {
		return putProcessThreadCount;
	}

	public void setPutProcessThreadCount(int putProcessThreadCount) {
		this.putProcessThreadCount = putProcessThreadCount;
	}
	
	public int getMaxMessageSize() {
		return maxMessageSize;
	}

	public void setMaxMessageSize(int maxMessageSize) {
		this.maxMessageSize = maxMessageSize;
	}

	public String getQueue_cfg() {
		return queue_cfg;
	}

	public void setQueue_cfg(String queue_cfg) {
		this.queue_cfg = queue_cfg;
	}

	public String getDb_cfg() {
		return db_cfg;
	}

	public void setDb_cfg(String db_cfg) {
		this.db_cfg = db_cfg;
	}

	private int getInt(final Section section, final String key, final int defaultValue) {
        final String value = section.get(key);
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        else {
            return NumberUtils.toInt(value);
        }
    }


    private int getInt(final Section section, final String key) {
        final String value = section.get(key);
        if (StringUtils.isBlank(value)) {
            throw new NullPointerException("Blank value for " + key);
        }
        else {
        	return NumberUtils.toInt(value);
        }
    }
    
    private String getString(final Section section, final String key) {
    	return section.get(key);
    }
	
	public void load(URL url) {
		File file = new File(url.getPath());
		if(file.lastModified() != this.lastModified) {
			this.lastModified = file.lastModified();
			try {
				log.info("Load system config starting....");
				Ini conf = new Ini(file);
				Section section = conf.get("system");
				this.serverPort = this.getInt(section, "serverPort");
				this.maxMessageSize = this.getInt(section, "maxMessageSize");
				this.putProcessThreadCount = this.getInt(section, "putThreadSize");
				this.getProcessThreadCount = this.getInt(section, "getThreadSize");
				
				section = conf.get("queue");
				this.queue_cfg = this.getString(section, "queue_cfg");
				section = conf.get("database");
				this.db_cfg    = this.getString(section, "db_cfg");
				log.info("Load system config sucessfully");
			} catch (InvalidFileFormatException e) {
				log.error("Load Config Failure!", e);
			} catch (IOException e) {
				log.error("Load Config Failure!", e);
			}
		}
	}
	
	public String toString() {
		return "serverPort:["+this.serverPort+"] dataSource:["+this.dataSource+"] maxMessageSize:["+this.maxMessageSize+"]";
	}
}
