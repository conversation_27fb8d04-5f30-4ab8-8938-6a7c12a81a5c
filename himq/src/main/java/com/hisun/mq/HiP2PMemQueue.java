package com.hisun.mq;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.exception.DAOException;
import com.hisun.mq.exception.QueueException;


public class HiP2PMemQueue extends HiAbstractMemQueue {

	private Log log = LogFactory.getLog(HiP2PMemQueue.class);
	
	public HiP2PMemQueue() {
		if(log.isDebugEnabled()) {
			log.debug("create p2p mem queue");
		}
		
	}
	
	public HiP2PMemQueue(int messageSize) {
		this.setMaxQueuesize(messageSize);
	}
	
	@Override
	public Message poll() {
		Message message = messageQueue.poll();
		return message;
	}

	@Override
	public void push(Message message) throws QueueException{
		if(log.isInfoEnabled()) {
			log.info("start process push message to queue! message:["+message.getmsgBody()+"]");
		}
		//-- 计算message的大小，如果大小超过4K，则压缩
		//--TODO
		
		//-- 队列未满时写内存和数据库
		if(messageQueue.size() < this.getMaxQueuesize()) {
			//-- 同时写数据库
			try {
				queueDAO.add(message);
			} catch (DAOException e) {
				log.error("",e);
				throw new QueueException("",e);
			}
			
			//-- 如果这里失败如何处理？ 不处理，反正重新装载
			messageQueue.offer(message);
		}
		else  //-- 写数据库
		{
			try {
				queueDAO.add(message);
			} catch (DAOException e) {
				log.error("",e);
				throw new QueueException("",e);
			}
		}
	}

	@Override
	public Message peek() {
		Message message = messageQueue.peek();
		
		try {
			queueDAO.update(message);
		} catch (DAOException e) {
			log.error("更新失败",e);
		}
		
		return message;
	}
	
	public void update(Message message) throws DAOException {
		try {
			queueDAO.update(message);
		} catch (DAOException e) {
			log.error("更新失败",e);
			throw e;
		}
	}

	@Override
	public void clear() {
		messageQueue.clear();
	}
	
	private void writeDB(Message message){
		
	}
	
}
