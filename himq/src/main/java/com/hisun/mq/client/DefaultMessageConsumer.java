package com.hisun.mq.client;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.apache.commons.lang.StringUtils;

import com.hisun.mq.Message;
import com.hisun.mq.exception.NullException;
import com.hisun.mq.networking.request.ConsumerCommand;
import com.hisun.mq.networking.request.ConsumerRegisterCommand;
import com.hisun.mq.networking.request.MessagePushCommand;
import com.hisun.mq.networking.response.BooleanCommand;
import com.hisun.mq.networking.response.DataCommand;
import com.hisun.mq.processor.SubscribeMessageProcessor;
import com.taobao.gecko.core.command.RequestCommand;
import com.taobao.gecko.core.command.ResponseCommand;
import com.taobao.gecko.core.command.ResponseStatus;
import com.taobao.gecko.core.util.OpaqueGenerator;
import com.taobao.gecko.service.RequestProcessor;
import com.taobao.gecko.service.SingleRequestCallBackListener;
import com.taobao.gecko.service.exception.NotifyRemotingException;

public class DefaultMessageConsumer implements MessageConsumer{

	protected static final long DEFAULT_OP_TIMEOUT = 3000L;
	
	protected final RemotingClientWrapper remotingClient;
	
	private MessageListener listener;
	private FetchManager fetchManager;
	
	private String serverUrl;
	private String groupName;  // 群组
	private String queueName;  // 队列名称
	private String topic;      // 主题
	
	public DefaultMessageConsumer(RemotingClientWrapper remotingClient,String groupName, String serverUrl ) {
		this.remotingClient = remotingClient;
		this.serverUrl = serverUrl;
		this.groupName = groupName;
	}
	
	public void createQueue(String queueName) {
		this.queueName = queueName;
	}
	
	public void createTopic(String topic, SingleRequestCallBackListener listener) {
		this.topic = topic;
		
		//-- 注册topic
		//this.remotingClient.
		ConsumerRegisterCommand cmd = new ConsumerRegisterCommand(OpaqueGenerator.getNextOpaque());
		cmd.setGroupNam(this.groupName);
		cmd.setQueNam(this.queueName);
		cmd.setTopic(this.topic);
		
		try {
			//ResponseCommand resp = remotingClient.invokeServer(cmd, DEFAULT_OP_TIMEOUT, TimeUnit.SECONDS);
			remotingClient.sendToServer(cmd, listener, DEFAULT_OP_TIMEOUT, TimeUnit.SECONDS);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (TimeoutException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (NotifyRemotingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (NullException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public void setMessageListener(MessageListener listener) {
		this.listener = listener;
	}
	
	public MessageListener getMessageListener() {
		return this.listener;
	}
	
	@Override
	public <T extends RequestCommand> void registerProcessor(
			Class<T> commandClazz, RequestProcessor<T> processor) {
		this.remotingClient.registerProcessor(MessagePushCommand.class, new SubscribeMessageProcessor());
	}

	public void start(ConsumerConfig consumerConfig) {
		//-- 如果非主题，主动获取消息
		if(StringUtils.isEmpty(this.topic)) {
			this.fetchManager = new DefaultFetchManager(consumerConfig, this);
			this.fetchManager.resetFetchState();
			this.fetchManager.startFetchRunner();
		}
		else { // 如果是订阅者 注册processor
			//this.remotingClient
			
		}
	}
	
	@Override
	public Message fetch(long timeOut, TimeUnit unit) {
		//-- 获取数据
		ConsumerCommand cmd = new ConsumerCommand(OpaqueGenerator.getNextOpaque());
		cmd.setQueNam(this.queueName);
		
		try {
			ResponseCommand resp = remotingClient.invokeServer(cmd, DEFAULT_OP_TIMEOUT, unit);
			if (resp instanceof DataCommand) {
				 final DataCommand dataCmd = (DataCommand) resp;
				 if(dataCmd.getResponseStatus() == ResponseStatus.NO_ERROR) {
					 final byte[] data = dataCmd.getData();
		             Message message = new Message();
		             //message.setData(new String(data));
		             message.setmsgBody(new String(data));
		             return message;
				 }
				 else { //-- 没有记录
					 return null;
				 }
	            
			}
			else {
				BooleanCommand response = (BooleanCommand) resp;
				
				return null;
			}
			
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (TimeoutException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (NotifyRemotingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (NullException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
		return null;
	}

}
