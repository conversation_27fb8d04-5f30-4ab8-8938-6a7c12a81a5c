package com.hisun.mq.client;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.Message;
import com.hisun.mq.exception.NullException;
import com.hisun.mq.networking.request.PutCommand;
import com.hisun.mq.networking.response.BooleanCommand;
import com.taobao.gecko.core.command.ResponseCommand;
import com.taobao.gecko.core.command.ResponseStatus;
import com.taobao.gecko.core.util.OpaqueGenerator;
import com.taobao.gecko.service.exception.NotifyRemotingException;

public class DefaultMessageProducer implements MessageProducer {

	private static final Log log = LogFactory.getLog(DefaultMessageProducer.class);
	protected static final long DEFAULT_OP_TIMEOUT = 3000L;
	private static final int MAX_RETRY = 1;

	protected final RemotingClientWrapper remotingClient;

	private String groupNam;   // 组名
	private String topic;      //
	private String serverUrl;  //
	private String queueName;  //

	private boolean isAck = false;
	private boolean isRetry = false;
	private int retryCount = 0;
	private int maxCount = 5; // 默认重发5次

	public DefaultMessageProducer(RemotingClientWrapper remotingClient,
			String groupName, String serverUrl) {
		this.remotingClient = remotingClient;

		this.groupNam = groupName;
		this.serverUrl = serverUrl;
	}

	public void setAckMode(boolean isAck) {
		this.isAck = isAck;
	}

	public void setRetryMode(boolean isRetry) {
		this.isRetry = false;
	}

	public void setGroupName(String groupName) {
		this.groupNam = groupName;
	}

	@Override
	public void publish(String topic) {
		// TODO Auto-generated method stub

	}

	@Override
	public void createQueue(String queueName) throws NullException {
		if (StringUtils.isEmpty(queueName)) {
			throw new NullException("queNam is null");
		}

		this.queueName = queueName;

	}

	@Override
	public void createTopic(String topic) {
		this.topic = topic;
	}

	@Override
	public void sendMessage(Message message) throws InterruptedException,
			TimeoutException, NotifyRemotingException, NullException {
		this.sendMessage(message, this.DEFAULT_OP_TIMEOUT,
				TimeUnit.MILLISECONDS);
	}

	@Override
	public void sendMessage(Message message, long timeOut, TimeUnit unit)
			throws InterruptedException, TimeoutException, NotifyRemotingException, NullException {
		// TODO Auto-generated method stub
		if (message == null) {
			throw new NullException();
		}

		// --
		PutCommand cmd = new PutCommand(topic, OpaqueGenerator.getNextOpaque());
		cmd.setQueNam(this.queueName);
		cmd.setAck(this.isAck);
		//cmd.setData(message.getData());
		cmd.setData(message.getmsgBody());
		cmd.setMsgId(message.getMsgId());
		cmd.setReTry(this.isRetry);
		if(!StringUtils.isEmpty(this.topic)) {
			cmd.setTopic(this.topic);
		}

		ResponseCommand resp = remotingClient.invokeServer(cmd, 30,
				TimeUnit.SECONDS);
		if (resp.getResponseStatus() != ResponseStatus.NO_ERROR) {
			// -- 失败 判断重发
			// -- 如果重发，登记其他线程重发？
			// -- 登记日志
			if (this.isRetry) {
				//TODO
				
			}
		}
		else { //-- 失败
			BooleanCommand bcmd = (BooleanCommand)resp;
			log.info("bcmd resp message:["+bcmd.getResponseStatus()+"]");
		}
	}

	@Override
	public void shutdown() throws NotifyRemotingException {
		this.remotingClient.close();
	}

}
