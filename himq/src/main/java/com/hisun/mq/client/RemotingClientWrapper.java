/*
 * (C) 2007-2012 HisunTech Group Holding Limited.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * 
 */
package com.hisun.mq.client;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.exception.NullException;
import com.taobao.gecko.core.command.RequestCommand;
import com.taobao.gecko.core.command.ResponseCommand;
import com.taobao.gecko.service.RemotingClient;
import com.taobao.gecko.service.RemotingFactory;
import com.taobao.gecko.service.RequestProcessor;
import com.taobao.gecko.service.SingleRequestCallBackListener;
import com.taobao.gecko.service.config.ClientConfig;
import com.taobao.gecko.service.exception.NotifyRemotingException;

/**
 * RemotingClient包装，添加连接的建立和关闭的计数
 * 
 * <AUTHOR>
 * @Date 2012-11-05
 * 
 */
public class RemotingClientWrapper {
	private static Log log = LogFactory.getLog(RemotingClientWrapper.class);

	private final Map<String, RemotingClient> sessions = new HashMap<String, RemotingClient>();

	/**
	 * url: tcp://0.0.0.0:0000 tcp://0.0.0.0:0000
	 * 
	 * @param url
	 * @throws NotifyRemotingException
	 * @throws NullException
	 */
	public RemotingClientWrapper(String url, ClientConfig clientConfig)
			throws NotifyRemotingException, NullException {
		if (StringUtils.isEmpty(url)) {
			throw new NullException("url is null");
		}

		String urls[] = StringUtils.split(url, " ");
		if (urls == null) {
			throw new NullException("urls is null");
		}

		for (int i = 0; i < urls.length; i++) {
			String group = urls[i];
			if (log.isDebugEnabled()) {
				log.debug("index [" + i + "] url [" + group + "]");
			}
			RemotingClient remotingClient = RemotingFactory.connect(clientConfig);
			//remotingClient.registerProcessor(MessagePushCommand.class, );
			addRemotingClient(group, remotingClient);
		}
	}

	public void addRemotingClient(String group, RemotingClient remotingClient) {
		this.sessions.put(group, remotingClient);
	}

	public void removeRemotingClient(String group) {
		this.sessions.remove(group);
	}

	public void connect() {
		for (Iterator iter = this.sessions.keySet().iterator(); iter.hasNext();) {
			String group = (String) iter.next();
			RemotingClient remotingClient = (RemotingClient) this.sessions
					.get(group);

			try {
				remotingClient.connect(group);
			} catch (NotifyRemotingException e) {
				// 出错后继续
				log.error("", e);
			}
		}
	}

	public void awaitReadyInterrupt() {
		for (Iterator iter = this.sessions.keySet().iterator(); iter.hasNext();) {
			String group = (String) iter.next();
			RemotingClient remotingClient = (RemotingClient) this.sessions
					.get(group);

			try {
				remotingClient.awaitReadyInterrupt(group);
			} catch (NotifyRemotingException e) {
				log.error("", e);
			} catch (InterruptedException e) {
				log.error("", e);
			}
		}
	}

	public RemotingClientPair getRemotingClientByRand() {
		// -- 随机数
		Random random = new Random();
		int r = random.nextInt();
		int c = this.sessions.size();
		int i = r % c;
		int j = 0;
		for (Iterator iter = this.sessions.keySet().iterator(); iter.hasNext();) {
			if (i == j) {
				String group = (String) iter.next();
				RemotingClient remotingClient = this.sessions.get(group);
				return new RemotingClientPair(group, remotingClient);
			}

			j++;
		}

		return null;
	}

	public ResponseCommand invokeServer(RequestCommand cmd, long timeOut,
			TimeUnit unit) throws InterruptedException, TimeoutException,
			NotifyRemotingException, NullException {
		// 算法
		RemotingClientPair remotingClientPair = getRemotingClientByRand();

		if (remotingClientPair == null) {
			throw new NullException("server is null");
		}

		String group = remotingClientPair.getGroup();
		RemotingClient remotingClient = remotingClientPair.getRemotingClient();

		if (log.isDebugEnabled()) {
			log.debug("serverUrl:[" + group + "]");
		}
		if (remotingClient.isConnected(group)) {
			ResponseCommand rsp = remotingClient.invokeToGroup(group, cmd,
					timeOut, unit);
			return rsp;
		}

		return null;
	}

	public void sendToServer(RequestCommand cmd,
			SingleRequestCallBackListener listener, long timeOut, TimeUnit unit)
			throws InterruptedException, TimeoutException,
			NotifyRemotingException, NullException {
		// 算法
		RemotingClientPair remotingClientPair = getRemotingClientByRand();

		if (remotingClientPair == null) {
			throw new NullException("server is null");
		}

		String group = remotingClientPair.getGroup();
		RemotingClient remotingClient = remotingClientPair.getRemotingClient();

		if (log.isDebugEnabled()) {
			log.debug("serverUrl:[" + group + "]");
		}
		if (remotingClient.isConnected(group)) {
			remotingClient.sendToGroup(group, cmd, listener, timeOut, unit);
		}
	}

	public void close() throws NotifyRemotingException {
		for (Iterator iter = this.sessions.keySet().iterator(); iter.hasNext();) {
			String group = (String) iter.next();
			RemotingClient remotingClient = this.sessions.get(group);
			//remotingClient.close(group, false);
			remotingClient.stop();
		}
		sessions.clear();
		//sessions = null;
	}

	public void invokeAllServer(RequestCommand cmd)
			throws InterruptedException, TimeoutException,
			NotifyRemotingException {
		for (Iterator iter = this.sessions.keySet().iterator(); iter.hasNext();) {
			String group = (String) iter.next();
			RemotingClient remotingClient = this.sessions.get(group);
			remotingClient.invokeToGroup(group, cmd);
		}

	}
	
	public <T extends RequestCommand> void registerProcessor(final Class<T> commandClazz, final RequestProcessor<T> processor) {
		for (Iterator iter = this.sessions.keySet().iterator(); iter.hasNext();) {
			String group = (String) iter.next();
			RemotingClient remotingClient = this.sessions.get(group);
			remotingClient.registerProcessor(commandClazz, processor);
		}
	}

	class RemotingClientPair {
		private String group;
		private RemotingClient remotingClient;

		public RemotingClientPair(String group, RemotingClient remotingClient) {
			this.group = group;
			this.remotingClient = remotingClient;
		}

		public String getGroup() {
			return this.group;
		}

		public RemotingClient getRemotingClient() {
			return this.remotingClient;
		}
	}
}