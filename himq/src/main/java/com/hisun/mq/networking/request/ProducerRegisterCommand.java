package com.hisun.mq.networking.request;

import net.sf.json.JSONObject;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.networking.AbstractRequestCommand;
import com.taobao.gecko.core.buffer.IoBuffer;

public class ProducerRegisterCommand extends AbstractRequestCommand {

	static final Log log = LogFactory.getLog(ProducerRegisterCommand.class);
	
	private String queNam;
	
	private String topic;
	
	private String groupNam;
	
	public ProducerRegisterCommand(Integer opaque) {
		super(opaque);
		
		
	}

	@Override
	public IoBuffer encode() {
		
		JSONObject req = new JSONObject();
		req.put("op", "producer-register");
		req.put("quenam", this.queNam);
		req.put("topic", this.topic);
		req.put("groupnam", this.groupNam);
		req.put("opaque", this.getOpaque());
		
		IoBuffer buffer = IoBuffer.allocate((req.toString()+"\r\n").length());
		buffer.put(req.toString().getBytes());
		buffer.flip();
		
		return buffer;
	}

	public String getQueNam() {
		return queNam;
	}

	public void setQueNam(String queNam) {
		this.queNam = queNam;
	}

	public String getTopic() {
		return topic;
	}

	public void setTopic(String topic) {
		this.topic = topic;
	}

	public String getGroupNam() {
		return groupNam;
	}

	public void setGroupNam(String groupNam) {
		this.groupNam = groupNam;
	}
}
