package com.hisun.mq.networking;

import java.io.IOException;

import com.taobao.gecko.core.nio.TCPController;

public class HiTCPListenerImpl {

	private static int port = 8888;
	
	private int size = 1;
	
	private static int maxSize = 5;
	
	private static TCPController controller;
	
	private HiTCPListenerImpl () {
		
	}
	
	public static void getInstance() {
		if(controller != null) {
			return;
		}
		
		try {
			initialize();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	private static void initialize() throws IOException {
		controller = new TCPController();
		HiMessageHandler handler = new HiMessageHandler();
		controller.setHandler(handler);
		controller.bind(port);
		controller.setBacklog(1);
		controller.setReadThreadCount(maxSize);
	}
	
	private void loadConfig() {
		
	}
}
