package com.hisun.mq.networking;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.networking.request.ConsumerCommand;
import com.hisun.mq.networking.request.ConsumerRegisterCommand;
import com.hisun.mq.networking.request.MessagePushCommand;
import com.hisun.mq.networking.request.PutCommand;
import com.hisun.mq.networking.request.VersionCommand;
import com.hisun.mq.networking.response.BooleanCommand;
import com.hisun.mq.networking.response.DataCommand;
import com.hisun.mq.utils.ByteUtils;
import com.hisun.mq.utils.HiConstants;
import com.taobao.gecko.core.buffer.IoBuffer;
import com.taobao.gecko.core.command.ResponseStatus;
import com.taobao.gecko.core.core.CodecFactory;
import com.taobao.gecko.core.core.Session;
import com.taobao.gecko.core.util.ByteBufferMatcher;
import com.taobao.gecko.core.util.ShiftAndByteBufferMatcher;

public class MQCodecFactory implements CodecFactory {

	static final Log log = LogFactory.getLog(MQCodecFactory.class);
	
	@Override
	public Encoder getEncoder() {
		return new Encoder() {
			@Override
			public IoBuffer encode(Object message, Session session) {
				if(log.isDebugEnabled()) {
					log.debug("encoding ......");
				}
				return ((MetaEncodeCommand) message).encode();
			}
		};
	}
	
	static final ByteBufferMatcher LINE_MATCHER = new ShiftAndByteBufferMatcher(IoBuffer.wrap(MetaEncodeCommand.CRLF));

	@Override
	public Decoder getDecoder() {
		return new Decoder() {
			@Override
			public Object decode(IoBuffer buff, Session session) {
				if(log.isDebugEnabled()) {
					log.debug("decoder...");
				}
				
				if (buff == null || !buff.hasRemaining()) {
                    return null;
                }
                buff.mark();
                
                //-- 开始解码
                int index = LINE_MATCHER.matchFirst(buff);
                if(index >= 0) {
                	final byte[] bytes = new byte[index - buff.position()];
                	buff.get(bytes);
                	//-- 跳过 \r\n
                	buff.position(buff.position() + 2);
                	final String line = ByteUtils.getString(bytes);
                	if(log.isDebugEnabled()) {
                		log.debug("line=["+line+"]");
                	}
                	// 转JSON
                	JSONObject jo = JSONObject.fromObject(line);
                	String op = (String)jo.get(HiConstants.OPERATOR);
                	
                	if(StringUtils.equalsIgnoreCase(op, HiConstants.VERSION)) {
                		return decodeVersion(jo);
                	}
                	
                	else if(StringUtils.equals(op, HiConstants.PUT)) {
                		return decodePut(jo);
                	}
                	
                	else if(StringUtils.equals(op, HiConstants.RESULT)){
                		return decodeBoolean(jo);
                	}
                	
                	else if(StringUtils.equals(op, HiConstants.GET)) {  // decodeGet 
                		return decodeGet(jo);
                	}
                	
                	else if(StringUtils.equals(op, HiConstants.VALUE)) { // decodeData 返回消息解码
                		return decodeData(jo);
                	}
                	
                	else if(StringUtils.equalsIgnoreCase(op, HiConstants.REGISTER)) {  // decode 消费者注册
                		return decodeRegister(jo);
                	}
                	
                	else if(StringUtils.equalsIgnoreCase(op, HiConstants.OP_SEND)) {
                		return decodeSend(jo);
                	}
                }
                else {
                	return null;
                }
                
				return null;
			}
			
			private Object decodeVersion(JSONObject jo) {
				log.debug("decode version command");
				int opaque = jo.getInt(HiConstants.OPAQUE);
                //if (tmps.length >= 2) {
                return new VersionCommand(opaque);
                /*}
                else {
                    return new HeartBeatCommandImpl(Integer.MAX_VALUE);
                }*/
            }
			
			private Object decodePut(JSONObject jo){
				int opaque = jo.getInt(HiConstants.OPAQUE);
				PutCommand cmd = new PutCommand(null, opaque);
				cmd.fromJSON(jo);
				return cmd;
			}
			
			private Object decodeBoolean(JSONObject jo) {
				String message = jo.getString(HiConstants.MESSAGE_INFO);
				int opaque = jo.getInt(HiConstants.OPAQUE);
				String messagecode = jo.getString(HiConstants.MESSAGE_CODE);
				ResponseStatus status;
				if(StringUtils.equalsIgnoreCase(messagecode, "NO_ERROR")) {
					status = ResponseStatus.NO_ERROR;
				}
				else if(StringUtils.equalsIgnoreCase(messagecode, "ERROR")) {
					status = ResponseStatus.ERROR;
				}
				else {
					status = ResponseStatus.ERROR;
				}
				BooleanCommand booleanCommand = new BooleanCommand(opaque, status, message);
				log.debug("booleanCommand:["+booleanCommand+"] status:["+booleanCommand.getResponseStatus()+"]");
				return booleanCommand;
			}
			
			private Object decodeGet(JSONObject jo) {
				int opaque = jo.getInt(HiConstants.OPAQUE);
				ConsumerCommand consumerCommand = new ConsumerCommand(opaque);
				consumerCommand.setQueNam(jo.getString(HiConstants.QUEUE_NAME));
				return consumerCommand;
			}
			
			private Object decodeData(JSONObject jo) {
				int opaque = jo.getInt(HiConstants.OPAQUE);
				String data = jo.getString(HiConstants.DATA);
				String messagecode = jo.getString(HiConstants.MESSAGE_CODE);
				ResponseStatus status;
				if(StringUtils.equalsIgnoreCase(messagecode, "NO_ERROR")) {
					status = ResponseStatus.NO_ERROR;
				}
				else if(StringUtils.equalsIgnoreCase(messagecode, "ERROR")) {
					status = ResponseStatus.ERROR;
				}
				else {
					status = ResponseStatus.ERROR;
				}
				DataCommand dataCommand = new DataCommand(data.getBytes(), opaque);
				dataCommand.setResponseStatus(status);
				return dataCommand;
			}
			
			private Object decodeRegister(JSONObject jo) {
				int opaque = jo.getInt("opaque");
				ConsumerRegisterCommand cmd = new ConsumerRegisterCommand(opaque);
				cmd.fromJSON(jo);
				return cmd;
			}
			
			private Object decodeSend(JSONObject jo) {
				int opaque = jo.getInt(HiConstants.OPAQUE);
				MessagePushCommand push = new MessagePushCommand(opaque);
				push.fromJSON(jo);
				return push;
			}
		};
	}

}
