package com.hisun.mq.networking;

import com.taobao.gecko.core.core.Session;
import com.taobao.gecko.core.core.impl.HandlerAdapter;

public class Hi<PERSON>essageHandler extends HandlerAdapter {

	@Override
	public void onMessageSent(Session session, Object message) {
		// TODO Auto-generated method stub
		super.onMessageSent(session, message);
	}

	@Override
	public void onSessionConnected(Session session, Object... args) {
		// TODO Auto-generated method stub
		super.onSessionConnected(session, args);
	}

	@Override
	public void onSessionStarted(Session session) {
		// TODO Auto-generated method stub
		super.onSessionStarted(session);
	}

	@Override
	public void onSessionCreated(Session session) {
		// TODO Auto-generated method stub
		super.onSessionCreated(session);
	}

	@Override
	public void onSessionClosed(Session session) {
		// TODO Auto-generated method stub
		super.onSessionClosed(session);
	}

	@Override
	public void onMessageReceived(Session session, Object message) {
		// TODO Auto-generated method stub
		super.onMessageReceived(session, message);
	}

	@Override
	public void onSessionIdle(Session session) {
		// TODO Auto-generated method stub
		super.onSessionIdle(session);
	}

	@Override
	public void onSessionExpired(Session session) {
		// TODO Auto-generated method stub
		super.onSessionExpired(session);
	}

}
