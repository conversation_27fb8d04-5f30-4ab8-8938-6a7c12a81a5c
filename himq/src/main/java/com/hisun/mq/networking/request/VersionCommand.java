package com.hisun.mq.networking.request;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.networking.AbstractRequestCommand;
import com.hisun.mq.networking.MetaEncodeCommand;
import com.taobao.gecko.core.buffer.IoBuffer;
import com.taobao.gecko.core.command.kernel.HeartBeatRequestCommand;

/**
 * 心跳 -- 查询版本号
 * <AUTHOR>
 *
 */
public class VersionCommand extends AbstractRequestCommand implements HeartBeatRequestCommand {

	static final Log log = LogFactory.getLog(VersionCommand.class);
	
	public VersionCommand(final Integer opaque) {
        super(opaque);
    }


    @Override
    public IoBuffer encode() {
    	if(log.isDebugEnabled()) {
    		log.debug("VersionCommand encode");
    	}
        return IoBuffer.wrap(("{'op':"+"'"+MetaEncodeCommand.VERSION_CMD + "', 'opaque':'"+this.getOpaque()+"'}" + "\r\n").getBytes());
    }

}
