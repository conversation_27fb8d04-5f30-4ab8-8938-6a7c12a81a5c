package com.hisun.mq.networking.request;

import net.sf.json.JSONObject;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.networking.AbstractRequestCommand;
import com.hisun.mq.utils.HiConstants;
import com.taobao.gecko.core.buffer.IoBuffer;

public class PutCommand extends AbstractRequestCommand {

	static final Log log = LogFactory.getLog(PutCommand.class);
	
	private String msgId;
	
	private String queNam;
	
	private boolean isAck = false;
	
	private boolean isReTry = false;
	
	private int maxTms = 0;  //重试次数
	
	private String topic;
	
	private String data; //消息内容
	
	public PutCommand(final String topic, final Integer opaque){
		super(opaque);
	}
	
	@Override
	public IoBuffer encode() {
		JSONObject jo = new JSONObject();
		jo.put(HiConstants.OPERATOR, HiConstants.PUT);
		jo.put(HiConstants.MESSAGE_ID, this.msgId);
		jo.put(HiConstants.QUEUE_NAME, this.queNam);
		jo.put(HiConstants.IS_ACK, isAck);
		jo.put(HiConstants.IS_RETRY, this.isReTry);
		jo.put(HiConstants.MAX_TMS, this.maxTms);
		jo.put(HiConstants.TOPIC_NAME, this.topic);
		jo.put(HiConstants.DATA, this.data);
		jo.put(HiConstants.OPAQUE, this.getOpaque());
		
		byte[] data = (jo.toString()+"\r\n").getBytes();
		IoBuffer buffer = IoBuffer.allocate(data.length);
		buffer.put(data);
		buffer.flip();
		return buffer;
	}

	public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

	public String getQueNam() {
		return queNam;
	}

	public void setQueNam(String queNam) {
		this.queNam = queNam;
	}

	public boolean isAck() {
		return isAck;
	}

	public void setAck(boolean isAck) {
		this.isAck = isAck;
	}

	public boolean isReTry() {
		return isReTry;
	}

	public void setReTry(boolean isReTry) {
		this.isReTry = isReTry;
	}

	public int getMaxTms() {
		return maxTms;
	}

	public void setMaxTms(int maxTms) {
		this.maxTms = maxTms;
	}

	public String getTopic() {
		return topic;
	}

	public void setTopic(String topic) {
		this.topic = topic;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}
	
	public void fromJSON(JSONObject jo) {
		this.msgId = jo.getString(HiConstants.MESSAGE_ID);
		this.queNam = jo.getString(HiConstants.QUEUE_NAME);
		this.isAck = jo.getBoolean(HiConstants.IS_ACK);
		this.isReTry = jo.getBoolean(HiConstants.IS_RETRY);
		this.maxTms = jo.getInt(HiConstants.MAX_TMS);
		if(jo.containsKey(HiConstants.TOPIC_NAME)) {
			this.topic = jo.getString(HiConstants.TOPIC_NAME);
		}
		this.data = jo.getString(HiConstants.DATA);
	}

}
