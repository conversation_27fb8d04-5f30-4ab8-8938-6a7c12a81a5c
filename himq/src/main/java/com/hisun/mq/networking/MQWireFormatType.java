package com.hisun.mq.networking;

import com.taobao.gecko.core.command.CommandFactory;
import com.taobao.gecko.core.core.CodecFactory;
import com.taobao.gecko.service.config.WireFormatType;

public class MQWireFormatType extends WireFormatType {

	public static final String SCHEME = "tcp";
	
	@Override
	public String getScheme() {
		return SCHEME;
	}

	@Override
	public CodecFactory newCodecFactory() {
		return new MQCodecFactory();
	}

	@Override
	public CommandFactory newCommandFactory() {
		return new MQCommandFactory();
	}

	@Override
	public String name() {
		return "hisun-tmq";
	}

}
