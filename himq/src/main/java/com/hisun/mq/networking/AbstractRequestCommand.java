package com.hisun.mq.networking;

import com.taobao.gecko.core.command.CommandHeader;
import com.taobao.gecko.core.command.RequestCommand;

public abstract class AbstractRequestCommand implements RequestCommand,
		MetaEncodeCommand {

	private Integer opaque;
	//private String topic;
	static final long serialVersionUID = -1L;

	public AbstractRequestCommand(final Integer opaque) {
		super();
		//this.topic = topic;
		this.opaque = opaque;
	}

	@Override
	public Integer getOpaque() {
		return this.opaque;
	}
	
	public void setOpaque(final Integer opaque) {
        this.opaque = opaque;
    }

	@Override
	public CommandHeader getRequestHeader() {
		return this;
	}

}
