package com.hisun.mq.networking;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.networking.request.VersionCommand;
import com.hisun.mq.networking.response.BooleanCommand;
import com.taobao.gecko.core.command.CommandFactory;
import com.taobao.gecko.core.command.CommandHeader;
import com.taobao.gecko.core.command.ResponseStatus;
import com.taobao.gecko.core.command.kernel.BooleanAckCommand;
import com.taobao.gecko.core.command.kernel.HeartBeatRequestCommand;
import com.taobao.gecko.core.util.OpaqueGenerator;

public class MQCommandFactory implements CommandFactory {

	static final Log log = LogFactory.getLog(MQCommandFactory.class);
	
	@Override
	public BooleanAckCommand createBooleanAckCommand(CommandHeader request,
			ResponseStatus responseStatus, String errorMsg) {

		if(log.isDebugEnabled()) {
			log.debug("createBooleanAckCommand....");
		}
		return new BooleanCommand(request.getOpaque(), responseStatus, errorMsg);
	}

	@Override
	public HeartBeatRequestCommand createHeartBeatCommand() {
		if(log.isDebugEnabled()) {
			log.debug("createHeartBeatCommand......");
		}
		return new VersionCommand(OpaqueGenerator.getNextOpaque());
	}

}
