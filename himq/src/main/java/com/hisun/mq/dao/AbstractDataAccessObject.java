package com.hisun.mq.dao;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.logicalcobwebs.proxool.ProxoolException;
import org.logicalcobwebs.proxool.ProxoolFacade;
import org.logicalcobwebs.proxool.configuration.JAXPConfigurator;
import org.xml.sax.InputSource;

import com.hisun.mq.ProxollDBTest;
import com.hisun.mq.exception.DAOException;

public abstract class AbstractDataAccessObject implements IDataAccessObject {
	Log log = LogFactory.getLog(AbstractDataAccessObject.class);
	
	private Connection connection;
	
	public AbstractDataAccessObject() throws DAOException {
		log.debug("AbstractDataAccessObject constructor...");
		
	}
	
	public Connection getConnection() throws SQLException {
		if(connection != null && !connection.isClosed()) {
			return this.connection;
		}
		else {
			this.connection = DriverManager.getConnection("proxool.payadm");
			connection.setAutoCommit(false);
			return this.connection;
		}
	}
	
	public void close() throws SQLException {
		if(this.connection != null) {
			connection.commit();
			connection.close();
			//ProxoolFacade.shutdown(0);
		}
	}
	
}
