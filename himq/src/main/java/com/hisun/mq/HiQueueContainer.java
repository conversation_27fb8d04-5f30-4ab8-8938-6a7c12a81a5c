package com.hisun.mq;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.HashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.logicalcobwebs.proxool.ProxoolException;
import org.logicalcobwebs.proxool.configuration.JAXPConfigurator;
import org.xml.sax.InputSource;

/**
 * 单态
 * 
 * <AUTHOR>
 * 
 */
public class HiQueueContainer {
	HashMap<String, IQueue> queueMap = new HashMap<String, IQueue>();
	private static HiQueueContainer container;
	private static final byte[] LOCK = new byte[] { 0x00 };
	private final Log log = LogFactory.getLog(HiQueueContainer.class);

	public static HiQueueContainer getInstance() {
		if (container == null) {
			// -- 加锁
			synchronized (LOCK) {
				container = new HiQueueContainer();
				// return container;
			}
		}
		return container;
	}

	public HiQueueContainer() {
		if(log.isDebugEnabled()) {
			log.debug("construct container!");
		}
		container = this;
		initialize();
	}

	/**
	 * 初始化所有配置的队列
	 */
	public void initialize() {
		// -- TODO
		
		
		// -- test
		
		/*
		log.debug("初始化测试队列");
		HiP2PMemQueue queue1 = new HiP2PMemQueue(100000);
		queueMap.put("test1", queue1);

		HiSubscribeMemQueue queue2 = new HiSubscribeMemQueue(20000);
		queueMap.put("test2", queue2);
		*/
		// -- test end
	}

	public void addQueue(String queueName, IQueue queue) {
		queueMap.put(queueName, queue);
		// return false;
	}
	
	public void addQueue(IQueue queue) {
		queueMap.put(queue.getName(), queue);
	}

	public void removeQueue(String queueName) {
		queueMap.remove(queueName);
	}

	public IQueue getQueueByName(String queueName) {
		return queueMap.get(queueName);
	}

	public void start() {

	}

	public void stop() {

	}

	public void reload() {

	}
	
	private void load(){
		
	}
}
