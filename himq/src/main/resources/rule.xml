<?xml version="1.0"?>

<digester-rules>
   <pattern value="Root">
      <object-create-rule classname="com.hisun.mq.HiQueueContainer"/>
      
      
      <pattern value="Queue">
         <factory-create-rule classname="com.hisun.mq.HiQueueFactory"/>
         <set-property-rule pattern="Param" name="name" value="value"/>
         <call-method-rule  methodname="createTable" paramcount="0" />
         <set-next-rule methodname="addQueue"  />
      </pattern>
   </pattern>
</digester-rules>