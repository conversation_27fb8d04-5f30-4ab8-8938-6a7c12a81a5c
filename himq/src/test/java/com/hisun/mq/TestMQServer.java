package com.hisun.mq;

import java.net.URL;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.utils.MQServerConfig;

public class TestMQServer {

	private static final Log log = LogFactory.getLog(TestMQServer.class);
	/**
	 * @param args
	 */
	public static void main(String[] args) {
		MQServerConfig config = new MQServerConfig();
		URL url = TestMQServer.class.getClassLoader().getResource("server.conf");
		if(log.isDebugEnabled()) {
			log.debug("URL:["+url+"]");
		}
		config.load(url);
		HiMQServer server = new HiMQServer(config);
		server.start();
	}
}
