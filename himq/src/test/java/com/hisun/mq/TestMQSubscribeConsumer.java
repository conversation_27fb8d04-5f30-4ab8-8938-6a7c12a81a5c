package com.hisun.mq;

import java.util.concurrent.ThreadPoolExecutor;

import com.hisun.mq.client.ConsumerConfig;
import com.hisun.mq.client.MessageConsumer;
import com.hisun.mq.client.MessageSessionFactoryImpl;
import com.hisun.mq.client.RemotingClientWrapper;
import com.hisun.mq.networking.request.MessagePushCommand;
import com.hisun.mq.networking.response.BooleanCommand;
import com.hisun.mq.networking.response.DataCommand;
import com.hisun.mq.processor.SubscribeMessageProcessor;
import com.taobao.gecko.core.command.ResponseCommand;
import com.taobao.gecko.service.Connection;
import com.taobao.gecko.service.SingleRequestCallBackListener;

public class TestMQSubscribeConsumer {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		String serverUrl = "tcp://127.0.0.1:8889";
		MessageSessionFactoryImpl factory = new MessageSessionFactoryImpl(serverUrl);
		
		RemotingClientWrapper remotingClient = factory.getRemotingClient();
		remotingClient.connect();
        remotingClient.awaitReadyInterrupt();
        
        ConsumerConfig consumerConfig = new ConsumerConfig();
        MessageConsumer consumer = factory.createConsumer();
        consumer.createQueue("test2");
        consumer.createTopic("topic1", new SingleRequestCallBackListener() {
			@Override
			public void onResponse(ResponseCommand responseCommand,
					Connection conn) {
				System.out.println("response ..........");
				if(responseCommand instanceof DataCommand) {
					DataCommand dtCmd = (DataCommand)responseCommand;
					byte[] data = dtCmd.getData();
					System.out.println("data:["+data+"]");
				}
				else {
					BooleanCommand cmd = (BooleanCommand)responseCommand;
					System.out.println("result:"+cmd.getErrorMsg());
				}
			}

			@Override
			public void onException(Exception e) {
				
			}

			@Override
			public ThreadPoolExecutor getExecutor() {
				return null;
			}
        	
        });
        consumer.registerProcessor(MessagePushCommand.class, new SubscribeMessageProcessor());
        consumer.start(consumerConfig);
	}

}
