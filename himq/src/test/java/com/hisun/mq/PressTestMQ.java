package com.hisun.mq;

import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hisun.mq.client.MessageProducer;
import com.hisun.mq.client.MessageSessionFactoryImpl;
import com.hisun.mq.client.RemotingClientWrapper;
import com.hisun.mq.exception.NullException;
import com.taobao.gecko.service.exception.NotifyRemotingException;

public class PressTestMQ {

	private static final Log log = LogFactory.getLog(PressTestMQ.class);
	private static MessageSessionFactoryImpl factory;
	private static MessageProducer producer;
	
	private static int MAX_CNT = 10000;
	private static AtomicInteger CURRENT_INDEX = new AtomicInteger(0);
	
	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// TODO Auto-generated method stub
		log.info("start test...........");
		long st = System.currentTimeMillis();
		String serverUrl = "tcp://10.9.10.18:8889";
		factory = new MessageSessionFactoryImpl(serverUrl);
		
		RemotingClientWrapper remotingClient = factory.getRemotingClient();
		remotingClient.connect();
        remotingClient.awaitReadyInterrupt();
		
        producer = factory.createProducer("group1");
		try {
			producer.createQueue("test");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
		PressTestMQ aa = new PressTestMQ();
		aa.dotest();
		
		long et = System.currentTimeMillis();
		long t = et - st;
		double avg = (double)t / (double)MAX_CNT;
		double tps = (double)MAX_CNT * 1000 / (double)t;
		
		log.info("test end avg:["+avg+"] tps:["+tps+"]");
	}
	
	public void dotest() {
		Message message = new Message();
		message.setmsgBody("hellolworld");
		message.setMsgId("201210230000121232");
		int w = 50;
		Thread threads[] = new Thread[w];
		for(int i = 0 ; i < w; i++) {
			threads[i] = new Thread(new Worker(message));
		}
		
		for(int i = 0; i < w; i ++) {
			threads[i].start();
		}
		
		for(int i = 0; i < w; i ++) {
			try {
				threads[i].join();
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

	public class Worker implements Runnable {
		private Message message;
		
		public Worker(Message message) {
			this.message = message;
		}
		
		@Override
		public void run() {
			while(true) {
				int index = CURRENT_INDEX.getAndIncrement();
				log.info("index:["+index+"]");
				if(index < MAX_CNT) {
					try {
						producer.sendMessage(message);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						continue;
					} catch (TimeoutException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						continue;
					} catch (NotifyRemotingException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						continue;
					} catch (NullException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
						continue;
					}
				}
				else{
					break;
				}
			}
		}
	}
}
