log4j.rootLogger=INFO, toFile, INFO, toConsole

log4j.appender.toFile=org.apache.log4j.FileAppender

log4j.appender.toFile.File=remoting.txt
log4j.appender.toFile.layout=org.apache.log4j.PatternLayout
log4j.appender.toFile.layout.ConversionPattern=%5p [%t] - %m%n
log4j.appender.toFile.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}\:%L - %m%n

log4j.appender.toConsole=org.apache.log4j.ConsoleAppender
log4j.appender.toConsole.layout=org.apache.log4j.PatternLayout
log4j.appender.toConsole.layout.ConversionPattern=%5p [%t] - %m%n
log4j.appender.toConsole.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}\:%L - %m%n