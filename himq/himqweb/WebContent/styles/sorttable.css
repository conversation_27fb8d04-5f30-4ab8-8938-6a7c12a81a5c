/**
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

table.sortable {
  margin: 0px;
  padding: 0px;
  margin-bottom: 1em;
  margin: 1em auto;
  float: left;
}

th {                              
  padding: 0.5em;
  padding-left: 30px;
  padding-right: 30px;
  text-align: center;
  background-color: #ccc;
}

tfoot {
 /*  border-top: 1px solid black; */
}

td {
  padding: 0.5em;
 /*  border: 1px solid black;  */
  /** border-top: 1px solid black; */
}

tr {
  background-color: #ffffff;
}

tr.odd {
  background-color: #f3f3f3;
}

td.numeric,
  th.numeric {
  text-align: right;
}

/** forms using table layout */
td.label {
  background-color: #f3f3f3;
}
/*
table.layout {
  border-bottom: solid white;
  border-right: solid white;
}

td.layout {
  border-top: 1px solid black;
  border: solid white;
}



