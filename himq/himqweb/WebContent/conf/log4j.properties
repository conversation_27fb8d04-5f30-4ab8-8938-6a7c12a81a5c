
### ç³»ç»æ¥å¿  ###
log4j.rootLogger = info, system ,system_err

### æ­£å¸¸äº¤ææ¥å¿###
log4j.appender.system = org.apache.log4j.DailyRollingFileAppender
log4j.appender.system.file = ${webappRoot}/trc/system.start
log4j.appender.system.DatePattern='.'yyyy-MM-dd
log4j.appender.system.layout = org.apache.log4j.PatternLayout
log4j.appender.system.layout.ConversionPattern = %d{yyyy-MM-dd HH:mm:ss,SSS} [%p] - %m%n

### å¼å¸¸äº¤ææ¥å¿ ###
log4j.appender.system_err = org.apache.log4j.DailyRollingFileAppender
log4j.appender.system_err.file = ${webappRoot}/log/system_err.start
log4j.appender.system_err.DatePattern='.'yyyy-MM-dd
log4j.appender.system_err.Append = true
log4j.appender.system_err.Threshold = ERROR
log4j.appender.system_err.layout = org.apache.log4j.PatternLayout
log4j.appender.system_err.layout.ConversionPattern = %d{yyyy-MM-dd HH:mm:ss,SSS} [%p] - [%c] : %m%n

### äº¤æè¯¦æ ###
log4j.logger.transaction = info, transaction
log4j.additivity.transaction = false 
log4j.appender.transaction= org.apache.log4j.DailyRollingFileAppender
log4j.appender.transaction.file = ${webappRoot}/trc/transaction.start
log4j.appender.transaction.DatePattern='.'yyyy-MM-dd
log4j.appender.transaction.layout = org.apache.log4j.PatternLayout
log4j.appender.transaction.layout.ConversionPattern = %d{yyyy-MM-dd HH:mm:ss,SSS} [%p]: %m%n
