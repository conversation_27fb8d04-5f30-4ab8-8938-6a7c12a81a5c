package com.hisun.mq.web.servlet;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Iterator;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServlet;

import org.apache.commons.io.FileUtils;
import org.apache.log4j.PropertyConfigurator;

public class HimqLogInit extends HttpServlet {
	public void service(ServletRequest req, ServletResponse resp)
			throws ServletException, IOException {
	}

	public void init() throws ServletException {
		String realPath = getServletContext().getRealPath("/"); // 获取绝对路径

		SimpleDateFormat sdf1 = new SimpleDateFormat("dd"); // 获取当期日期
		String date1 = sdf1.format(new java.util.Date());

		SimpleDateFormat sdf2 = new SimpleDateFormat("HHmmss"); // 获取当期日期
		String date2 = sdf2.format(new java.util.Date());

		// 读取当前未归档的日志文件
		File sourcefile1 = new File(realPath + "trc\\" + date1 + "\\");
		File destfile1 = new File(realPath + "trc\\" + date1 + "\\" + date2 + "\\");
		moveLogFile(sourcefile1, destfile1);

		File sourcefile2 = new File(realPath + "log\\" + date1 + "\\");
		File destfile2 = new File(realPath + "log\\" + date1 + "\\" + date2 + "\\");
		moveLogFile(sourcefile2, destfile2);

		

		System.setProperty("webappRoot", realPath);
		PropertyConfigurator.configure(realPath
				+ getInitParameter("configfile"));

	}

	private void moveLogFile(File srcDir, File destDir) {
		try {
			String[] extensions = { "trc", "log" };
			boolean recursive = false;
			// 使用listFiles搜索
			Collection<File> files = FileUtils.listFiles(srcDir, extensions,
					recursive);
			// 迭代输出
			for (Iterator<File> iterator = files.iterator(); iterator.hasNext();) {
				File file =  iterator.next();
				FileUtils.moveToDirectory(file, destDir, true);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}