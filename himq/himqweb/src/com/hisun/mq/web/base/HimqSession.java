package com.hisun.mq.web.base;

import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;

public class HimqSession {
	/**
	 * 创建Session
	 * 
	 * @param session
	 * @param user
	 * @return
	 */
	public static void createSession(HttpSession session, String user) {
		session.setMaxInactiveInterval(600); // 设置Session超时时间为10分钟
		session.setAttribute("user", user);
	}

	public static String getSessionValue(HttpSession session) {
		return (String) session.getAttribute("user");
	}

	public static void romoveSession(HttpSession session) {
		session.removeAttribute("user");
	}

	public static boolean existSession(HttpSession session) {
		if (StringUtils.isEmpty((String)session.getAttribute("user")) || StringUtils.isBlank((String)session.getAttribute("user"))) {
			return false;
		} else {
			return true;
		}
	}
	
	public static String getSessionId(HttpSession session) {
		return session.getId();
	}

	
	
}
