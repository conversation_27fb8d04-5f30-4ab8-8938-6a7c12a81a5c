package com.hisun.mq.web.base;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

/**
 * 对ActionId进行统计
 * 
 * <AUTHOR>
 * 
 */
public class ActionManager {
	private static Map<String, String> actionMap = new HashMap<String, String>();

	public static String initAction(String actionName) {
		String curActionId = actionMap.get(actionName);
		if (StringUtils.isBlank(curActionId)) {
			curActionId = StringUtils.leftPad("1", 15, '0');
		} else {
			curActionId = StringUtils.leftPad(
					String.valueOf(Long.valueOf(curActionId) + 1), 15, '0');
		}
		actionMap.put(actionName, curActionId);
		return curActionId;
	}

	public static String closeAction(String str, HimqLogger logger) {
		logger.close();
		return str;
	}

}
