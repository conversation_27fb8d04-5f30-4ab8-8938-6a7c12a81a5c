package com.hisun.mq.web.action;

import javax.servlet.http.HttpServletRequest;

import org.apache.struts2.interceptor.ServletRequestAware;

import com.opensymphony.xwork2.ActionSupport;

public class UserReloginAction extends ActionSupport implements
ServletRequestAware {
	private HttpServletRequest request;
	public void setServletRequest(HttpServletRequest request) {
		this.request = request;
	}

	@Override
	public String execute() throws Exception {
		request.setAttribute("login_error", "您尚未登录或者登录已超时，请重新登录！");
		return "SUCCESS";
	}
}
