package com.hisun.mq.web.action;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.apache.struts2.interceptor.ServletRequestAware;

import com.hisun.mq.web.base.ActionManager;
import com.hisun.mq.web.base.HimqLogger;
import com.hisun.mq.web.base.HimqSession;
import com.opensymphony.xwork2.ActionSupport;

public class UserLoginAction extends ActionSupport implements
		ServletRequestAware {
	private HttpServletRequest request;
	private String user;
	private String password;
	private String logError;

	public void setServletRequest(HttpServletRequest request) {
		this.request = request;
	}

	public String getUser() {
		return user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getLogError() {
		return logError;
	}

	public void setLogError(String logError) {
		this.logError = logError;
	}

	@Override
	public String execute() throws Exception {
		String actionId = ActionManager.initAction(UserLoginAction.class
				.getName());
		HimqLogger logger = new HimqLogger(
				UserLoginAction.class.getSimpleName(), actionId, request);
		logger.execInfo("用户" + this.user + "登录");

		logger.execInfo("验证用户名及密码");
		boolean userInfoCheckFlag;
		if (userInfoCheckFlag = (StringUtils.isBlank(this.user) || StringUtils
				.isBlank(this.password))) {
			request.setAttribute("login_error", "用户名和密码不能为空，请重新登录！");
			logger.conditionInfo("用户名或密码为空", userInfoCheckFlag);
			return ActionManager.closeAction("LOGIN_ERROR", logger);
		}
		if (userInfoCheckFlag = this.user.equals("admin")
				&& this.password.equals("hisuncmpay")) {
			logger.conditionInfo("用户名和密码正确", userInfoCheckFlag);
		} else {
			request.setAttribute("login_error", "用户名或密码错误，请重新登录！");
			logger.conditionInfo("用户名和密码正确", userInfoCheckFlag);
			return ActionManager.closeAction("LOGIN_ERROR", logger);
		}

		logger.execInfo("创建Session");
		HttpSession session = request.getSession();
		boolean sessionCheckFlag = HimqSession.existSession(session);
		logger.conditionInfo("是否存在session信息", sessionCheckFlag);
		if (!sessionCheckFlag) {
			logger.setParamInfo("Session", "user", user);
			HimqSession.createSession(session, user);
		}

		return ActionManager.closeAction("SUCCESS", logger);
	}

}
