package com.hisun.mq.web.action;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.struts2.interceptor.ServletRequestAware;

import com.hisun.mq.web.base.HimqSession;
import com.opensymphony.xwork2.ActionSupport;

public class QueryMessageQueueAction extends ActionSupport implements
		ServletRequestAware {
	private HttpServletRequest request;

	public void setServletRequest(HttpServletRequest request) {
		this.request = request;
	}

	@Override
	public String execute() throws Exception {
		HttpSession session = request.getSession();
		if (!HimqSession.existSession(session)) {
			request.setAttribute("login_error", "您尚未登录或者登录已超时，请重新登录！");
			System.out.println("return erroror");
			return "RELOGIN";
		}
		return "SUCCESS";
	}
}
