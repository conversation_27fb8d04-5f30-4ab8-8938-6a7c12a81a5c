<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.3.dtd">
<struts>
   <constant name="struts.action.extension" value="html"></constant>
   <include file="struts-default.xml"></include>
   
   <!-- 全局公共处理 -->
   <package name="global_pkg" extends="struts-default" abstract="true">
      <global-results>
         <result name="RELOGIN" type="redirect">relogin.html</result>
         <result name="ERROR">/common/error.jsp</result>
      </global-results>
   </package>
   
   <package name="himq" extends="global_pkg">
      <action name="login" class="com.hisun.mq.web.action.UserLoginAction">
         <result name="SUCCESS" type="redirect">qry_sys_info.html</result>
         <result name="LOGIN_ERROR">/jsp/login.jsp</result>
      </action>
      
      <action name="logout" class="com.hisun.mq.web.action.UserLogoutAction">
         <result name="SUCCESS">/jsp/logout.jsp</result>
      </action>
      
      <action name="relogin" class="com.hisun.mq.web.action.UserReloginAction">
         <result name="SUCCESS">/jsp/login.jsp</result>
      </action>

      <action name="qry_sys_info" class="com.hisun.mq.web.action.QuerySysInfoAction">
         <result name="SUCCESS">/jsp/welcome.jsp</result>
      </action>

      <action name="qry_himq" class="com.hisun.mq.web.action.QueryMessageQueueAction">
         <result>/jsp/queue_mng.jsp</result>
      </action>
   </package>
</struts>