package com.hisun.activiti.atc;

import java.util.List;
import java.util.Map;

import org.activiti.engine.task.Task;

import com.hisun.activiti.abs.AbstractTaskQuery;
import com.hisun.exception.HiException;
import com.hisun.service.util.StringUtils;

public class TaskQueryForPage extends AbstractTaskQuery {
//	public int execute(HiATLParam params, HiMessageContext ctx)
//			throws HiException {
//		HiMessage msg = ctx.getCurrentMsg();
//		Logger log = HiLog.getLogger(msg);
//		HiETF etf = msg.getETFBody();
//		String grpNm = HiArgUtils.getStringNotNull(params, "GRP_NM");
//		
//		String pagNoStr = params.get("PAG_NO");
//		String pagNumStr = params.get("PAG_NUM");
//		int pagNo = 1;
//		int pagNum = 10;
//		if(!StringUtils.isBlank(pagNoStr)) {
//			pagNo = Integer.valueOf(pagNoStr);
//		}
//		if(!StringUtils.isBlank(pagNumStr)) {
//			pagNum = Integer.valueOf(pagNumStr);
//		}
//		
//		int firstNum = (pagNo - 1) * pagNum;//当前页码第一条记录位置
//		List<Task> tasks = null;
//		try {
//			TaskService ts = HiProcessServiceManager.getTaskService();
//			tasks = ts.createTaskQuery().orderByProcessInstanceId().asc().listPage(firstNum, pagNum);
//		} catch (Exception e) {
//			log.error("task query failed!" + " Exception[" + e + "]");
//			return HiATCConstants.ERR;
//		}
//
//		int taskRecNum = 0;
//		int len = tasks.size();
//		if (len <= 0) {
//			return HiATCConstants.NFND_REC;
//		}
//
//		// 删除节点，避免覆盖
//		etf.removeChildNode(grpNm);
//		etf.removeChildNode(grpNm + HiProcessConstants.REC_SUFFIX);
//		for (int i = 0; i < len; i++) {
//			taskRecNum = i + 1;
//			Task task = tasks.get(i);
//			HiETF recNode = etf.addNode(grpNm + HiProcessConstants.UNDER_LINE + taskRecNum);
//			setTaskToETF(recNode, task);
//		}
//		etf.addNode(grpNm + HiProcessConstants.REC_SUFFIX, String.valueOf(taskRecNum));
//		etf.addNode("PAG_NO", String.valueOf(pagNo));
//		etf.addNode("PAG_NUM", String.valueOf(pagNum));
//		return HiATCConstants.SUCC;
//	}
	
	@Override
	public List<Task> list(Map<String, Object> vars) throws HiException {
		String pagNoStr = (String) vars.get("PAG_NO");
		String pagNumStr = (String) vars.get("PAG_NUM");
		int pagNo = 1;
		int pagNum = 10;
		if(!StringUtils.isBlank(pagNoStr)) {
			pagNo = Integer.valueOf(pagNoStr);
		}
		if(!StringUtils.isBlank(pagNumStr)) {
			pagNum = Integer.valueOf(pagNumStr);
		}
		
		int firstNum = (pagNo - 1) * pagNum;//当前页码第一条记录位置
		List<Task> tasks = getTaskService().createTaskQuery()
				.orderByProcessInstanceId().asc().listPage(firstNum, pagNum);
		return tasks;
	}
}
