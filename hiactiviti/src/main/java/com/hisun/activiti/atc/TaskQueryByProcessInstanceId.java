package com.hisun.activiti.atc;

import java.util.List;
import java.util.Map;

import org.activiti.engine.task.Task;

import com.hisun.activiti.abs.AbstractTaskQuery;
import com.hisun.exception.HiException;

/**
 * 根据流程实例ID查询任务
 * <AUTHOR>
 * @date 2017-06-09
 *
 */
public class TaskQueryByProcessInstanceId extends AbstractTaskQuery{
	@Override
	public List<Task> list(Map<String, Object> vars) throws HiException {
		String processInstanceId = (String) vars.get("PROC_INS_ID");
		List<Task> tasks = getTaskService().createTaskQuery().orderByProcessInstanceId()
				.processInstanceId(processInstanceId).asc().list();
		return tasks;
	}
}
