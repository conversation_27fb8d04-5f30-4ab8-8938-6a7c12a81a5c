package com.hisun.activiti.atc;

import com.hisun.activiti.abs.AbstractCompleteTask;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.message.HiMessageContext;

public class CompleteTask extends AbstractCompleteTask {
	@Override
	public int execute(HiATLParam params, HiMessageContext ctx)
			throws HiException {
		//TODO 待扩展
		return super.execute(params, ctx);
	}
}
