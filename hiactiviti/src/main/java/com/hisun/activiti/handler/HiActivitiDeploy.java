package com.hisun.activiti.handler;

import java.util.List;

import com.hisun.activiti.util.HiProcessUtils;
import com.hisun.exception.HiException;
import com.hisun.hilog4j.Logger;

public class HiActivitiDeploy implements Runnable {
	private static HiActivitiDeploy instance = null;
	private List<String> fileList = null;
	private Logger log = null;
	private boolean running = true;
	private HiActivitiDeploy(){}
	
	public static HiActivitiDeploy getInstance() {
		if(null == instance) {
			synchronized (HiActivitiDeploy.class) {
				instance = new HiActivitiDeploy();
			}
		}
		return instance;
	}
	
	public void run() {
		try {
			doRun();
		} catch (HiException e) {
			//TODO 此处异常不做处理
		}
	}
	
	public void stop() {
		running = false;
	}
	
	public void setFileList(List<String> fileList) {
		this.fileList = fileList;
	}
	
	public void setLog(Logger log) {
		this.log = log;
	}
	
	private void doRun() throws HiException {
		if(Thread.currentThread().isInterrupted() || !running) {
			return;
		}
		//部署流程文件
		deployBpmnFile();
	}
	
	private void deployBpmnFile() throws HiException {
		try {
			for(String fileName : fileList) {
				long l1 = System.currentTimeMillis();
				HiProcessUtils.deploy(fileName);
				long l2 = System.currentTimeMillis();
				log.info("deploy file [" + fileName + "] cost " + (l2 - l1) + "ms");
			}
		} catch (HiException e) {
			log.error("deploy file fail!", e);
			throw new HiException(e);
		}
	}
}
