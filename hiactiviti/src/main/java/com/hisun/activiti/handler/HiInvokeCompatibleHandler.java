package com.hisun.activiti.handler;

import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;

public class HiInvokeCompatibleHandler extends HiAbstractInvokeHandler {
	private static final long serialVersionUID = 1L;
	
	@Override
	public void notify(DelegateExecution de) {
		params.put("EXEC_ID", de.getProcessInstanceId());
		super.notify(de);
	}
	
	@Override
	public void notify(DelegateTask dt) {
		params.put("EXEC_ID", dt.getProcessInstanceId());
		super.notify(dt);
	}
	
}
