package com.hisun.activiti.util;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import com.hisun.message.HiETF;

public class HiMapUtils {
	public static HiETF map2Etf(Map<?, ?> map, HiETF etf) {
		Set<?> set = map.keySet();
		Iterator<?> its = set.iterator();
		while(its.hasNext()) {
			String key = (String)its.next();
			String val = (String)map.get(key);
			etf.setGrandChildNode(key.toUpperCase(), val);
		}
		return etf;
	}
}
