package com.hisun.mod.parse;

import org.apache.commons.digester.Digester;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.io.SAXReader;
import org.xml.sax.SAXException;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 *
 * example:
 * <ServiceTab>
 *    <Service code="101999" srn="CGW_REG" svrNm="OURMPVP1" txCd="102000"/>
 * </ServiceTab>
 *
 * <AUTHOR>
 * @date: 2022-11-08 10:56
 * @since: 1.0.0
 */
public class ServiceParser {

    public static ServiceTab parse(String file) throws IOException, DocumentException, SAXException {
        File f = new File(file);
        if (!f.exists()) {
            throw new IOException("文件:[" + f.getName() + "]不存在!,file:["+file+"]");
        }
        SAXReader saxReader = new SAXReader();
        InputStream is = new FileInputStream(f);
        if (is == null) {
            throw new IOException("文件:[" + f.getName() + "]不存在!,file:["+file+"]");
        }
        Document document = saxReader.read(is);

        String strXML = document.asXML();
        ByteArrayInputStream inFile = new ByteArrayInputStream(strXML.getBytes());
        InputStreamReader in = new InputStreamReader(inFile);

        Digester digester = new Digester();
        digester.setValidating(false);
        digester.setUseContextClassLoader(true);
        digester.addObjectCreate("ServiceTab", "com.hisun.mod.parse.ServiceTab");
        digester.addSetProperties("ServiceTab");

        digester.addObjectCreate("ServiceTab/Service", "com.hisun.mod.parse.Service");
        digester.addSetProperties("ServiceTab/Service");
        digester.addSetNext("ServiceTab/Service", "addService", "com.hisun.mod.parse.Service");

        return (ServiceTab) digester.parse(in);

    }
}
