package com.hisun.mod.parse;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

import org.apache.commons.digester.Digester;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.io.SAXReader;
import org.xml.sax.SAXException;

public class FrowardParse {
	
	public static ForwardTab parse(String file) throws IOException, DocumentException, SAXException{
		File f = new File(file);
		if (!f.exists()) {
			throw new IOException("文件:[" + f.getName() + "]不存在!,file:["+file+"]");
		}
		SAXReader saxReader = new SAXReader();
		InputStream is = new FileInputStream(f);
		if (is == null) {
			throw new IOException("文件:[" + f.getName() + "]不存在!,file:["+file+"]");
		}
		Document document = saxReader.read(is);
		
		String strXML = document.asXML();
		ByteArrayInputStream inFile = new ByteArrayInputStream(strXML
				.getBytes());
		InputStreamReader in = new InputStreamReader(inFile);

		Digester digester = new Digester();
		digester.setValidating(false);
		digester.setUseContextClassLoader(true);
		digester.addObjectCreate("ForwardTab",
				"com.hisun.mod.parse.ForwardTab");
		digester.addSetProperties("ForwardTab");
		
		digester.addObjectCreate("ForwardTab/Forward",
				"com.hisun.mod.parse.Forward");
		digester.addSetProperties("ForwardTab/Forward");
		digester.addSetNext("ForwardTab/Forward", "addForward",
				"com.hisun.mod.parse.Forward");
		
		digester.addObjectCreate("ForwardTab/Forward/Item",
				"com.hisun.mod.parse.Item");
		digester.addSetProperties("ForwardTab/Forward/Item");
		digester.addSetNext("ForwardTab/Forward/Item", "addItem",
				"com.hisun.mod.parse.Item");
		return (ForwardTab) digester.parse(in);
		
	}
	
}
