package com.hisun.mod.parse;

/**
 *
 * <AUTHOR>
 * @date: 2022-11-08 10:54
 * @since: 1.0.0
 */
public class Service {
    private String code;
    private String srn;
    private String svrNm;
    private String txCd;
    private String sysCnl;
    private String busCnl;

    public String getSysCnl() {
        return sysCnl;
    }

    public void setSysCnl(String sysCnl) {
        this.sysCnl = sysCnl;
    }

    public String getBusCnl() {
        return busCnl;
    }

    public void setBusCnl(String busCnl) {
        this.busCnl = busCnl;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSrn() {
        return srn;
    }

    public void setSrn(String srn) {
        this.srn = srn;
    }

    public String getSvrNm() {
        return svrNm;
    }

    public void setSvrNm(String svrNm) {
        this.svrNm = svrNm;
    }

    public String getTxCd() {
        return txCd;
    }

    public void setTxCd(String txCd) {
        this.txCd = txCd;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("sysCnl = ").append(this.sysCnl).append(",");
        sb.append("busCnl = ").append(this.busCnl).append(",");
        sb.append("code = ").append(this.code).append(",");
        sb.append("srn = ").append(this.srn).append(",");
        sb.append("svrNm = ").append(this.svrNm).append(",");
        sb.append("txCd = ").append(this.txCd);
        return sb.toString();
    }
}
