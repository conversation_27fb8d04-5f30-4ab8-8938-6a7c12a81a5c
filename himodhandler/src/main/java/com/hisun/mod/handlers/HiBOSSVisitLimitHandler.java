package com.hisun.mod.handlers;

import com.hisun.exception.HiException;
import com.hisun.framework.HiDefaultServer;
import com.hisun.framework.event.IServerInitListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.framework.imp.HiDefaultProcess;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiContext;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.mod.parse.VisitLimit;
import com.hisun.pubinterface.IHandler;
import com.hisun.util.HiByteBuffer;
import com.hisun.util.HiICSProperty;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: aichou
 * @date: 2018/3/27
 * @remark: 省公司访问限制
 */
public class HiBOSSVisitLimitHandler implements IHandler, IServerInitListener {
    private HiDefaultServer server;
    private String file;
    private String rejectDispatcher;
    private String dispatcher;
    private final static Map<String, VisitLimit> limitMap = new HashMap<String, VisitLimit>();
    private final Logger log = (Logger) HiContext.getCurrentContext().getProperty("SVR.log");

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getDispatcher() {
        return dispatcher;
    }

    public void setDispatcher(String dispatcher) {
        this.dispatcher = dispatcher;
    }

    public String getRejectDispatcher() {
        return rejectDispatcher;
    }

    public void setRejectDispatcher(String rejectDispatcher) {
        this.rejectDispatcher = rejectDispatcher;
    }

    public void process(HiMessageContext ctx) throws HiException {
        HiMessage msg = ctx.getCurrentMsg();
        HiByteBuffer body = (HiByteBuffer) msg.getBody();
        String ip = msg.getHeadItem(HiMessage.SIP);
        HiDefaultProcess process = null;
        if(null != ip && restrict(body, ip)) {
            process = this.server.getProcessByName(this.rejectDispatcher);
            if(null == process) {
                String errmsg = HiDefaultServer.sm.getString("HiDefaultServer.process02", msg.getRequestId(), this.server.getName(), this.server.getType(), dispatcher);
                this.log.error(errmsg);
                throw new HiException("211004", errmsg);
            }
            msg.setHeadItem("SCH", "rp");
            msg.setType(process.getMsgtype());
            HiByteBuffer rspBody = visitLimitRsp(body);
            HiMessage rspMsg = msg.cloneNoBody();
            rspMsg.setBody(rspBody);
            ctx.setCurrentMsg(rspMsg);
        } else {
            process = this.server.getProcessByName(this.dispatcher);
            if(null == process) {
                String errmsg = HiDefaultServer.sm.getString("HiDefaultServer.process02", msg.getRequestId(), this.server.getName(), this.server.getType(), dispatcher);
                this.log.error(errmsg);
                throw new HiException("211004", errmsg);
            }
            msg.setHeadItem("SCH", "rq");
            msg.setType(process.getMsgtype());
        }
        process.process(ctx);
    }

    public void serverInit(ServerEvent se) throws HiException {
        this.server = (HiDefaultServer) se.getServer();
        limitMap.clear();
        parse(this.file);
    }

    private void parse(String file) throws HiException {
        String homeDir = HiICSProperty.getWorkDir();
        try {
            SAXReader reader = new SAXReader();
            Document doc = reader.read(new File(homeDir + File.separator + file));
            List<Element> regions = doc.getRootElement().elements("Tab");
            for(Element region : regions) {
                String provinceCode = region.attributeValue("provinceCode");
                String visitIp = region.attributeValue("visitIp");
                String visitLimit = region.attributeValue("visitLimit");
                limitMap.put(provinceCode, new VisitLimit(visitIp, visitLimit));
            }
        } catch (DocumentException e) {
            throw new HiException(e);
        }
    }

    /**
     * 判断访问IP是否需要做限制
     * @param buffer
     * @param ip
     * @return
     */
    private boolean restrict(HiByteBuffer buffer, String ip) {
        boolean flag = false;
        String tmp = buffer.toString();
        String provinceCode = null;
        int idx1 = tmp.toUpperCase().indexOf("<REQSYS>");
        int idx2 = tmp.toUpperCase().indexOf("</REQSYS>");
        if(idx1 >= 0 && idx2 > idx1) {
            provinceCode = tmp.substring(idx1 + 8, idx2);
        }

        if(null != provinceCode && null != limitMap.get(provinceCode)) {
            VisitLimit bean = limitMap.get(provinceCode);
            if(!ip.equals(bean.getVisitIp()) && bean.isVisitLimit()) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 响应限制报文
     * @param buffer
     * @return
     */
    private HiByteBuffer visitLimitRsp(HiByteBuffer buffer) {
        String tmp = buffer.toString();
        int idx1 = tmp.toUpperCase().indexOf("<HEAD>");
        int idx2 = tmp.toUpperCase().indexOf("</HEAD>");
        String head = tmp.substring(idx1 + 6, idx2);
        head = head.replaceAll("<SCH>rq</SCH>", "<SCH>rp</SCH>");
        head = head + "<RCODE>999999</RCODE><DESC>visit limited.</DESC>";
        head = "<MPAY><HEAD>" + head + "</HEAD><BODY/></MPAY>";
        buffer.clear();;
        buffer.append(head);
        return buffer;
    }
}
