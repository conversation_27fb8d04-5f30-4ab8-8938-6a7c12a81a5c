package com.hisun.mod.handlers;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import org.apache.commons.lang.StringUtils;
import org.dom4j.DocumentException;
import org.xml.sax.SAXException;

import com.hisun.exception.HiException;
import com.hisun.framework.event.IServerDestroyListener;
import com.hisun.framework.event.IServerInitListener;
import com.hisun.framework.event.IServerStopListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.mod.parse.Forward;
import com.hisun.mod.parse.ForwardTab;
import com.hisun.mod.parse.FrowardParse;
import com.hisun.mod.parse.Item;
import com.hisun.pubinterface.IHandler;
import com.hisun.util.HiByteBuffer;
import com.hisun.util.HiICSProperty;

/**
 * tcp 转发
 * <AUTHOR>
 *
 */
public class HiTcpForwardHandle implements IHandler ,IServerInitListener,IServerStopListener, IServerDestroyListener{
	private Logger _log = null;
	
	private String filePath;
	
	private CopyOnWriteArrayList<ForwardItem> forwardItems = new CopyOnWriteArrayList<ForwardItem>();

	public void process(HiMessageContext arg0) throws HiException {
		HiMessage msg = arg0.getCurrentMsg();
		HiByteBuffer byteBuff = (HiByteBuffer)msg.getBody();
		String xmlBodyStr = byteBuff.toString();
		int start = xmlBodyStr.indexOf("<MCODE>")+7;
		int end = xmlBodyStr.indexOf("</MCODE>");
		String txcode = xmlBodyStr.substring(start, end);
		_log.debug("code:["+txcode+"],data:["+xmlBodyStr+"]");
		ForwardItem item = findForward(null,txcode);
		if(null != item){
			_log.info("txCode:["+txcode+"],forward to host:["+item.getHost()+"],port:["+item.getPort()+"]");
			msg.setHeadItem(HiMessage.OIP,item.getHost());
			msg.setHeadItem(HiMessage.OPT,item.getPort());
		}
	}
	
	public ForwardItem findForward(String svrName,String txCode){
		if(null == txCode){
			return null;
		}
		for(ForwardItem item:forwardItems){
			if(item.getTxCode().equals(txCode)){
				return item;
			}
		}
		return null;
	}

	public void serverInit(ServerEvent arg0) throws HiException {
		_log = arg0.getLog();
		try {
			forwardItems.clear();
			ForwardTab ft = FrowardParse.parse(HiICSProperty.getWorkDir() + File.separator +this.getFilePath());
			List<Forward> forwards = ft.getForwards();
			for(Forward forward : forwards){
				String host = forward.getHost();
				String port = forward.getPort();
				if(StringUtils.isBlank(host)||StringUtils.isBlank(port)){
					throw new HiException("31001","host or port is null");
				}
				for(Item item: forward.getItems()){
					String svrName = item.getSvrName();
					String txCode = item.getTxCode();
					if(StringUtils.isBlank(svrName)||StringUtils.isBlank(txCode)){
						throw new HiException("31001","svrName or txCode is null,host:["+host+"],port:["+port+"]");
					}
					_log.info("forward init,host:["+forward.getHost()+"],port:["+forward.getPort()+"],svrName:["+svrName+"],txCode:["+txCode+"]");
					ForwardItem fi = new ForwardItem(forward.getHost(),forward.getPort(),svrName,txCode);
					forwardItems.add(fi);
				}
			}
		} catch (IOException e) {
			throw new HiException(e);
		} catch (DocumentException e) {
			throw new HiException(e);
		} catch (SAXException e) {
			throw new HiException(e);
		}
	}
	
	class ForwardItem extends Item{
		private String host;
		private String port;
		
		public ForwardItem(String host, String port,String svrName,String txCode) {
			super();
			this.host = host;
			this.port = port;
			this.setSvrName(svrName);
			this.setTxCode(txCode);
		}
		/**
		 * @return the host
		 */
		public String getHost() {
			return host;
		}
		/**
		 * @param host the host to set
		 */
		public void setHost(String host) {
			this.host = host;
		}
		/**
		 * @return the port
		 */
		public String getPort() {
			return port;
		}
		/**
		 * @param port the port to set
		 */
		public void setPort(String port) {
			this.port = port;
		}
		
	}

	/**
	 * @return the filePath
	 */
	public String getFilePath() {
		return filePath;
	}

	/**
	 * @param filePath the filePath to set
	 */
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public void serverStop(ServerEvent arg0) throws HiException {
		if(null != forwardItems){
			forwardItems.clear();
		}
	}

	public void serverDestroy(ServerEvent arg0) throws HiException {
		if(null != forwardItems){
			forwardItems.clear();
		}
	}

}

