package com.hisun.mod.handlers;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;

import com.hisun.common.util.HiByteUtil;
import com.hisun.exception.HiException;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.pubinterface.IHandler;
import com.hisun.util.HiByteBuffer;

/**
 * Gzip压缩解压
 * 
 * @param encoding  可选
 *           
 * <AUTHOR>
 *
 */
public class HiGzipHandler implements IHandler {
	
	private final static String DEFAULT_ENCODING = "UTF-8";
	private String encoding;
	private Logger _log;

    public void compress(HiMessageContext ctx) throws HiException {
    	String content = getContent(ctx);
        ByteArrayOutputStream out = null;    
        GZIPOutputStream gout = null;
        try{    
            out = new ByteArrayOutputStream();    
        	gout = new GZIPOutputStream(out);
        	gout.write(content.getBytes(encoding));
			gout.flush();
            gout.close();
            String result = HiByteUtil.byteArrayToHex(out.toByteArray());
            if(_log.isInfoEnabled()) {
    			_log.info("GIZP compress result:["+result+"]");
    		}
            ctx.getCurrentMsg().setBody(new HiByteBuffer(result.getBytes()));
        } catch (IOException e) {
        	throw new HiException(e);
        } finally {
        	try { 
        		if(gout != null) {
        			gout.close();
        		}
            } catch (IOException e) {  
            	throw new HiException(e);
            }  
        	try { 
        		if(out != null) {
        			out.close();
        		}
            } catch (IOException e) {  
            	throw new HiException(e);
            }  
        }  
    }  
      
    public void decompress(HiMessageContext ctx) throws HiException {
    	String content = getContent(ctx);
        ByteArrayOutputStream out = null;  
        ByteArrayInputStream in = null;  
        GZIPInputStream gin = null;  
        try {
        	if(_log.isInfoEnabled()) {
    			_log.info("GZIP decompress content:["+content+"]");
    		}
        	byte[] hexContent = HiByteUtil.hexToByteArray(content);
            out = new ByteArrayOutputStream();  
            in = new ByteArrayInputStream(hexContent);  
            gin = new GZIPInputStream(in);  
            IOUtils.copy(gin, out);
            String data = out.toString(encoding);
            HiByteBuffer result = new HiByteBuffer(data.getBytes());
            ctx.getCurrentMsg().setBody(result);
        } catch (IOException e) {
        	throw new HiException(e);
        } finally {
        	try {  
        		if (gin != null) {  
                    gin.close(); 
        		}
        	 } catch (IOException e) { 
             	throw new HiException(e);
             }  
        	try { 
        		if (in != null) { 
        			in.close();
        		}
	        } catch (IOException e) { 
	        	throw new HiException(e);
	        }
        	try { 
        		if (out != null) { 
        			out.close();
        		}
		    } catch (IOException e) { 
		    	throw new HiException(e);
		    }  
        }  
    }
    
    private String getContent(HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		if(msg.getBody() == null ) {
			throw new HiException("241191", "message body is null");
		}
		if(StringUtils.isBlank(encoding)) {
			encoding = DEFAULT_ENCODING;
		}
		return msg.getBody().toString();
	}
    
    public void process(HiMessageContext ctx) throws HiException {
    	throw new HiException();
	}
    
    public void setLog(Logger log) {
		this._log = log;
	}

	public String getEncoding() {
		return encoding;
	}

	public void setEncoding(String encoding) {
		this.encoding = encoding;
	}
}
