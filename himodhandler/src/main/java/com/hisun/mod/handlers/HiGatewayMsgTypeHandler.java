package com.hisun.mod.handlers;

import com.hisun.exception.HiException;
import com.hisun.framework.HiDefaultServer;
import com.hisun.framework.event.IServerDestroyListener;
import com.hisun.framework.event.IServerInitListener;
import com.hisun.framework.event.IServerStopListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.framework.imp.HiDefaultProcess;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.mod.parse.Service;
import com.hisun.mod.parse.ServiceParser;
import com.hisun.mod.parse.ServiceTab;
import com.hisun.pubinterface.IHandler;
import com.hisun.util.HiByteBuffer;
import com.hisun.util.HiICSProperty;
import org.apache.commons.lang.StringUtils;
import org.dom4j.DocumentException;
import org.xml.sax.SAXException;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date: 2022-12-06 9:10
 * @since: 1.0.0
 */
public class HiGatewayMsgTypeHandler implements IHandler, IServerInitListener, IServerStopListener, IServerDestroyListener {

    private String callServiceType;

    private String forwardType;

    private Logger logger = null;

    private String filePath;

    private HiDefaultServer server = null;

    private Map<String, Service> services = new HashMap<String, Service>();

    public void process(HiMessageContext ctx) throws HiException {
        HiMessage msg = ctx.getCurrentMsg();
        if (!(msg.getBody() instanceof HiByteBuffer)) {
            return;
        }

        HiByteBuffer bb = (HiByteBuffer) msg.getBody();
        String s = bb.toString();
        int idx1 = s.indexOf("<MCODE>");
        int idx2 = s.indexOf("</MCODE>");
        if (idx1 < 0 || idx2 < 0) {
            throw new HiException("MCODE not exist");
        }
        String mcode = s.substring(idx1 + 7, idx2);
        if (this.logger.isDebugEnabled()) {
            this.logger.debug("mcode [" + mcode + "]");
        }
        Service service = this.services.get(mcode);
        if (this.logger.isDebugEnabled()) {
            this.logger.debug("service [" + service + "]");
        }
        if (null != service) {
            msg.setType(this.callServiceType);
        } else {
            msg.setType(this.forwardType);
        }
        HiDefaultProcess process = this.server.getProcess(msg.getType());
        process.process(ctx);
    }

    public void serverDestroy(ServerEvent serverEvent) throws HiException {
        this.services = null;
    }

    public void serverInit(ServerEvent serverEvent) throws HiException {
        this.logger = serverEvent.getLog();
        this.server = (HiDefaultServer) serverEvent.getServer();
        try {
            this.services.clear();
            ServiceTab serviceTab = ServiceParser.parse(HiICSProperty.getWorkDir() + File.separator + this.getFilePath());
            List<Service> services = serviceTab.getServices();
            if (this.logger.isInfoEnabled()) {
                this.logger.info("services [" + services + "]");
            }
            for(Service service : services){
                if(StringUtils.isBlank(service.getCode()) || StringUtils.isBlank(service.getSrn())
                        || StringUtils.isBlank(service.getSvrNm()) || StringUtils.isBlank(service.getTxCd())){
                    throw new HiException("31001", "code or srn or svrNm or txCd is null");
                }
                this.services.put(service.getCode(), service);
            }
        } catch (IOException e) {
            throw new HiException(e);
        } catch (DocumentException e) {
            throw new HiException(e);
        } catch (SAXException e) {
            throw new HiException(e);
        }
    }

    public void serverStop(ServerEvent serverEvent) throws HiException {
        this.services.clear();
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getForwardType() {
        return forwardType;
    }

    public void setForwardType(String forwardType) {
        this.forwardType = forwardType;
    }

    public String getCallServiceType() {
        return callServiceType;
    }

    public void setCallServiceType(String callServiceType) {
        this.callServiceType = callServiceType;
    }
}
