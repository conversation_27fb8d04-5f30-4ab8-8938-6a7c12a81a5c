<?xml version="1.0" encoding="UTF-8" ?>
<project name="makejar" default="makejar">
	<!--
	Major ：具有相同名称但不同主版本号的程序集不可互换。例如，这适用于对产品的大量重写，这些重写使得无法实现向后兼容性。
	Minor ：如果两个程序集的名称和主版本号相同，而次版本号不同，这指示显著增强，但照顾到了向后兼容性。例如，这适用于产品的修正版或完全向后兼容的新版本。
	Build ：内部版本号的不同表示对相同源所作的重新编译。这适合于更改处理器、平台或编译器的情况。
	Revision ：名称、主版本号和次版本号都相同但修订号不同的程序集应是完全可互换的。这适用于修复以前发布的程序集中的安全漏洞。
	程序集的只有内部版本号或修订号不同的后续版本被认为是先前版本的修补程序 (Hotfix) 更新。
	-->
	<!--1~99-->
	<property name="Major_Version_Number" value="1" />
	<!--1~99-->
	<property name="Minor_Version_Number" value="0" />
	<!--1~999-->
	<property name="Revision_Number" value="0" />
	<!--日期-->
	<property name="version" value="${Major_Version_Number}.${Minor_Version_Number}.${Revision_Number}" />

	 <path id="project.class.path">
	      <fileset dir="../publibs/version_lib">
	        <include name="*.jar"/>
	      </fileset>
	 </path>

	<target name="makejar" depends="init,compile,jar,clean">
	</target>

	<target name="init">
		<tstamp>
			<format property="compile.time" pattern="yyyy-MM-dd HH:mm:ss" />
		</tstamp>
		<basename property="project_name" file="${user.dir}"/>
		<buildnumber/>
		<mkdir dir="tmp"/>
	</target>

	<target name="clean">
		<delete dir="tmp"/>
	</target>

	<target name="jar" description="Create a jar for project">
		<jar destfile="../publibs/version_lib/${project_name}.jar" basedir="tmp">
			<manifest>
				<attribute name="Built-By" value="${user.name}" />
				<attribute name="IBS-Integrator-Module" value="${project_name}" />
				<attribute name="IBS-Integrator-Module-Version" value="${version}.${build.number}" />
				<attribute name="IBS-Integrator-Module-Compile-Time" value="${compile.time}" />
			</manifest>
		</jar>
	</target>
	
	<target name="compile" description="Compile">
		<javac debug="true" srcdir="src/main" destdir="tmp" classpathref="project.class.path" source="1.5" target="1.5">
		</javac>
		<copy todir="tmp">
		    <fileset dir="src/main">
		      <exclude name="**/*.java"/>
		    </fileset>
		    <fileset dir=".">
		      <include name="Change.Log"/>
		    </fileset>
		</copy>
	</target>
	
</project>