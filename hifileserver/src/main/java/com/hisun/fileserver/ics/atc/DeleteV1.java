package com.hisun.fileserver.ics.atc;

import com.alibaba.fastjson.JSONObject;
import com.hisun.atc.HiATCConstants;
import com.hisun.atc.common.HiArgUtils;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessageContext;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.message.BasicNameValuePair;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.hisun.fileserver.ics.util.FileServerClientUtil.*;

/**
 * User: QC
 * Date: 2016-12-13
 * Time: 08:59
 */
public class DeleteV1 {
    private static final String URI = "/upload/v1/delete";

    public int execute(HiATLParam args, HiMessageContext ctx) throws HiException {
        Logger log = HiLog.getLogger(ctx.getCurrentMsg());

        String apiUri = args.get(HiFileServerAtcConstants.ARG_API_URI);
        if (apiUri == null) {
            apiUri = URI;
        }
        String key = HiArgUtils.getStringNotNull(args, HiFileServerAtcConstants.ARG_KEY);
        String bucket = HiArgUtils.getStringNotNull(args, HiFileServerAtcConstants.ARG_BUCKET);
        String url = HiArgUtils.getStringNotNull(args, HiFileServerAtcConstants.ARG_URL_PREFIX) + apiUri;
        String filename = HiArgUtils.getStringNotNull(args, HiFileServerAtcConstants.ARG_FILENAME);
        HashMap<String, Object> policyMap = new HashMap<String, Object>(3);
        policyMap.put("bucket", bucket);
        policyMap.put("filename", filename);
        policyMap.put("timestamp", timestamp());

        String rawPolicy = jsonPolicy(policyMap);
        log.debug("rawPolicy: " + rawPolicy);
        String policy = encodedPolicy(rawPolicy);
        if (policy == null) {
            throw new HiException("FileServer policy encode error: " + rawPolicy);
        }
        String signature = signature(policy, key);

        try {
            List<NameValuePair> params = new ArrayList<NameValuePair>();
            params.add(new BasicNameValuePair("policy", policy));
            params.add(new BasicNameValuePair("signature", signature));
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(params, Charset.forName("UTF-8"));
            String res = send(url, entity);
            log.debug("FileServer response: " + res);
            JSONObject jsonObject = tryParseWithJson(res);
            if (jsonObject == null) {
                throw new HiException("FileServer returns error: " + res);
            }
            String msgCd = jsonObject.getString("msg_cd");
            if (StringUtils.isBlank(msgCd)) {
                throw new HiException("FileServer returns no access_token: " + res);
            }
            ctx.getCurrentMsg().getETFBody().setChildValue("_FIL_MSG_CD", msgCd);
        } catch (Exception e) {
            throw new HiException(e);
        }
        return HiATCConstants.SUCC;
    }
}
