package com.hisun.zok.test;

import org.apache.zookeeper.KeeperException.SessionExpiredException;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;
import org.apache.zookeeper.KeeperException.ConnectionLossException;

import com.hisun.exception.HiException;
import com.hisun.zok.HiZkClient;

public class HiZkWatcherTest {

	public static void main(String[] args) {

		try {
			HiZkClient c = new HiZkClient(
					"127.0.0.1:2181,127.0.0.1:2182,127.0.0.1:2183", 5, false,
					null);
			c.register(new TestWatcher2(c));
			while (true) {
				try {
					c.exists("/tw1", new TestWatcher(c));
					c.exists("/tw2", new TestWatcher2(c));
					// System.out.println(c.create("/test1/111/t",
					// "11".getBytes(), Ids.OPEN_ACL_UNSAFE,
					// CreateMode.PERSISTENT, true));
					// System.out.println("create success");
				} catch (HiException e) {
					if (e.getNestedException() instanceof ConnectionLossException) {
						System.out.println("ConnectionLossException");
						continue;
					} else if (e.getNestedException() instanceof SessionExpiredException) {
						System.out.println("SessionExpiredException");
						c = new HiZkClient(
								"127.0.0.1:2181,127.0.0.1:2182,127.0.0.1:2183",
								5, false, null);
						c.register(new TestWatcher2(c));
						continue;
					}
					e.printStackTrace();
					break;
				}
			}
		} catch (HiException e) {
			e.printStackTrace();
			// } catch (InterruptedException e1) {
			// // TODO Auto-generated catch block
			// e1.printStackTrace();
		}

	}

}

class TestWatcher implements Watcher {

	private HiZkClient c = null;

	public TestWatcher() {

	}

	public TestWatcher(HiZkClient c) {
		this.c = c;
	}

	public void process(WatchedEvent event) {
		System.out.println("TestWatcher");
		try {
			c.exists(event.getPath(), this);
		} catch (HiException e) {
			e.printStackTrace();
		}
		System.out.println("path[" + event.getPath() + "]");
		System.out.println("state[" + event.getState() + "]");
		System.out.println("type[" + event.getType() + "]");
	}

}

class TestWatcher2 implements Watcher {

	private HiZkClient c = null;

	public TestWatcher2() {

	}

	public TestWatcher2(HiZkClient c) {
		this.c = c;
	}

	public void process(WatchedEvent event) {
		System.out.println("TestWatcher2");
		System.out.println("path[" + event.getPath() + "]");
		System.out.println("state[" + event.getState() + "]");
		System.out.println("type[" + event.getType() + "]");
		try {
			if (event.getType().equals(Event.EventType.NodeDataChanged)
					|| event.getType().equals(Event.EventType.NodeCreated)
					|| event.getType().equals(Event.EventType.NodeDeleted)) {
				c.exists(event.getPath(), this);
			}
		} catch (HiException e) {
			e.printStackTrace();
		}
	}

}