package com.hisun.zok.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import com.hisun.flow.handler.param.HiFlowCtrlLoadDB;

public class PswEnc {

	public static void main(String[] args) {
		if (args.length < 2) {
			printUsage();
			System.exit(-1);
		}
		try {
			if ("1".equals(args[0])) {
				String encPsw = encryptThreeDESECB(args[1],
						HiFlowCtrlLoadDB.DES_KEY);
				System.out.println(encPsw);
			} else if ("2".equals(args[0])) {
				if (args.length < 3) {
					printUsage();
					System.exit(-1);
				}
				String srcPsw = decryptThreeDESECB(args[2],
						HiFlowCtrlLoadDB.DES_KEY);
				System.out.println(srcPsw.equalsIgnoreCase(args[1]));
			} else {
				printUsage();
				System.exit(-1);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 3DESECB加密,key必须是长度大于等于 3*8 = 24 位
	public static String encryptThreeDESECB(String src, String key)
			throws Exception {
		DESedeKeySpec dks = new DESedeKeySpec(key.getBytes("UTF-8"));
		SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");
		SecretKey securekey = keyFactory.generateSecret(dks);

		Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
		cipher.init(Cipher.ENCRYPT_MODE, securekey);
		byte[] b = cipher.doFinal(src.getBytes());

		BASE64Encoder encoder = new BASE64Encoder();
		return encoder.encode(b).replaceAll("\r", "").replaceAll("\n", "");

	}

	// 3DESECB解密,key必须是长度大于等于 3*8 = 24 位
	public static String decryptThreeDESECB(String src, String key)
			throws Exception {
		// --通过base64,将字符串转成byte数组
		BASE64Decoder decoder = new BASE64Decoder();
		byte[] bytesrc = decoder.decodeBuffer(src);
		// --解密的key
		DESedeKeySpec dks = new DESedeKeySpec(key.getBytes("UTF-8"));
		SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");
		SecretKey securekey = keyFactory.generateSecret(dks);

		// --Chipher对象解密
		Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
		cipher.init(Cipher.DECRYPT_MODE, securekey);
		byte[] retByte = cipher.doFinal(bytesrc);

		return new String(retByte);
	}

	private static void printUsage() {
		System.out.println("Usage:");
		System.out.println("\tjava " + PswEnc.class.getCanonicalName()
				+ " {type}(1|2) {password} [encPassword]");
		System.out.println("type 1 : encrypt password");
		System.out.println("type 2 : verify encrypt password, need input two format password");
	}
}
