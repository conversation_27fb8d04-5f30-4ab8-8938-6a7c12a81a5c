package com.hisun.zok.constants;

public class HiZkConstants {

	/** zookeeper操作 */
	public final static String HEAD_ZKP_OPR = "ZOP";
	public final static String HEAD_ZKP_OPR_CRT = "create";
	public final static String HEAD_ZKP_OPR_SET = "set";
	public final static String HEAD_ZKP_OPR_GET = "get";
	public final static String HEAD_ZKP_OPR_EXT = "exists";
	public final static String HEAD_ZKP_OPR_DEL = "delete";

	/** zookeeper znode路径 */
	public final static String HEAD_ZKP_PATH = "ZPT";
	/** zookeeper znode版本 */
	public final static String HEAD_ZKP_VERSION = "ZVS";
	/** zookeeper 返回数据 */
	public final static String HEAD_ZKP_RETDATA = "ZRD";

	/** zookeeper操作返回码 */
	public final static String HEAD_ZKP_RET = "ZRT";
	public final static String HEAD_ZKP_RET_SUCCESS = "0";
	public final static String HEAD_ZKP_RET_ERROR = "1";

}
