package com.hisun.zok;

import java.io.IOException;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang.StringUtils;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.KeeperException;
import org.apache.zookeeper.KeeperException.ConnectionLossException;
import org.apache.zookeeper.KeeperException.SessionExpiredException;
import org.apache.zookeeper.Op;
import org.apache.zookeeper.OpResult;
import org.apache.zookeeper.Transaction;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;
import org.apache.zookeeper.ZooKeeper;
import org.apache.zookeeper.ZooKeeper.States;
import org.apache.zookeeper.data.ACL;
import org.apache.zookeeper.data.Stat;

import com.hisun.exception.HiException;
import com.hisun.hilog4j.Logger;
import com.hisun.zok.watcher.HiAbsZkWatcher;

public class HiZkClient implements IZkClient {

	private ZooKeeper client = null;

	// 会话超时时间，单位秒
	private int sessionTimeout = 15;
	/** ip1:port1,ip2:port2,…… */
	public static final String CONN_STR_REGEX = "(\\d+(\\.\\d+){3})\\:\\d+(,\\d+(\\.\\d+){3}\\:\\d+)*";
	private String connectString = null;
	private boolean canBeReadOnly = false;
	private Watcher watcher = null;

	private Map<String, Watcher> nodeWatchers = new ConcurrentHashMap<String, Watcher>();
	private Map<String, Watcher> childNodeWatchers = new ConcurrentHashMap<String, Watcher>();

	private long _startTime;
	private int retryTimes = 5;

	private Logger log;

	public Logger getLog() {
		return log;
	}

	public void setLog(Logger log) {
		this.log = log;
	}

	public HiZkClient(String connectString, int sessionTimeout, Logger log)
			throws HiException {
		this(connectString, sessionTimeout, false, null, log);
	}

	public HiZkClient(String connectString, int sessionTimeout,
			boolean canBeReadOnly, Logger log) throws HiException {
		this(connectString, sessionTimeout, canBeReadOnly, null, log);
	}

	public HiZkClient(String connectString, int sessionTimeout,
			boolean canBeReadOnly, Watcher watcher, Logger log)
			throws HiException {
		if (!verifyConnStr(connectString)) {
			log.error("connectString[" + connectString + "] is ERROR format!");
			throw new HiException("", "connectString[" + connectString
					+ "] is ERROR format!");
		}
		this.connectString = connectString;
		this.sessionTimeout = sessionTimeout;
		this.canBeReadOnly = canBeReadOnly;
		this.watcher = watcher;
		this.log = log;

		buildClient();

		this.log.info("connect zookeeper[" + this.connectString + "]");
	}

	private void buildClient() throws HiException {

		try {
			client = new ZooKeeper(this.connectString,
					this.sessionTimeout * 1000, this.watcher,
					this.canBeReadOnly);
			if (null == watcher) {
				HiZooKeeperStateWatcher stateWatcher = new HiZooKeeperStateWatcher(
						this, false, log);
				client.register(stateWatcher);
				this.watcher = stateWatcher;
			}
			this._startTime = Calendar.getInstance().getTimeInMillis();
		} catch (IOException e) {
			this.log.error("init client error", e);
			throw new HiException(e);
		}
	}

	private boolean verifyConnStr(String connectString) {
		if (StringUtils.isBlank(connectString)
				|| !connectString.matches(CONN_STR_REGEX)) {
			return false;
		}
		return true;
	}

	@Override
	public void close() throws HiException {
		try {
			if (null != client) {
				client.close();
			}
			client = null;
			log.info("close zookeeper[" + this.connectString + "]");
		} catch (InterruptedException e) {
			throw new HiException(e);
		}
	}

	@Override
	public String create(final String path, final byte[] data,
			final List<ACL> acl, final CreateMode createMode,
			final boolean createParents) throws HiException {
		return (String) retry(new Callable() {
			@Override
			public String call() throws Exception {
				try {
					return client.create(path, data, acl, createMode);
				} catch (KeeperException.NoNodeException e) {
					if (!createParents) {
						throw new HiException(e);
					}
					String parent = path.substring(0, path.lastIndexOf("/"));
					create(parent, null, acl, createMode, createParents);
					return create(path, data, acl, createMode, createParents);

				} catch (KeeperException.NodeExistsException e) {
					return path;
				}
			}
		});

	}

	@Override
	public void delete(final String path, final int version) throws HiException {

		retry(new Callable() {
			@Override
			public Object call() throws Exception {
				client.delete(path, version);
				return null;
			}
		});
	}

	@Override
	public Stat exists(final String path, final boolean watch)
			throws HiException {
		return (Stat) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				if (watch) {
					nodeWatchers.put(path, watcher);
				}
				return client.exists(path, watch);
			}
		});
	}

	@Override
	public Stat exists(final String path, final Watcher watcher)
			throws HiException {
		return (Stat) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				if (null != watcher) {
					nodeWatchers.put(path, watcher);
				}
				return client.exists(path, watcher);
			}
		});
	}

	@Override
	public List<String> getChildren(final String path, final boolean watch)
			throws HiException {
		return (List<String>) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				if (watch) {
					childNodeWatchers.put(path, watcher);
				}
				return client.getChildren(path, watch);
			}
		});
	}

	@Override
	public List<String> getChildren(final String path, final Watcher watcher)
			throws HiException {
		return (List<String>) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				if (null != watcher) {
					childNodeWatchers.put(path, watcher);
				}
				return client.getChildren(path, watcher);
			}
		});
	}

	@Override
	public byte[] getData(final String path, final boolean watch,
			final Stat stat) throws HiException {
		return (byte[]) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				if (watch) {
					nodeWatchers.put(path, watcher);
				}
				return client.getData(path, watch, stat);
			}
		});
	}

	@Override
	public byte[] getData(final String path, final Watcher watcher,
			final Stat stat) throws HiException {
		return (byte[]) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				if (null != watcher) {
					nodeWatchers.put(path, watcher);
				}
				return client.getData(path, watcher, stat);
			}
		});
	}

	@Override
	public List<OpResult> multi(final Iterable<Op> ops) throws HiException {
		return (List<OpResult>) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				return client.multi(ops);
			}
		});
	}

	@Override
	public void register(Watcher watcher) throws HiException {
		if (null != client) {
			client.register(watcher);
			this.watcher = watcher;
		} else {
			throw new HiException("", "The client is closed");
		}
	}

	@Override
	public Stat setACL(final String path, final List<ACL> acl, final int version)
			throws HiException {
		return (Stat) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				return client.setACL(path, acl, version);
			}
		});
	}

	@Override
	public Stat setData(final String path, final byte[] data, final int version)
			throws HiException {
		return (Stat) retry(new Callable() {
			@Override
			public Object call() throws Exception {
				return client.setData(path, data, version);
			}
		});
	}

	@Override
	public Transaction transaction() throws HiException {
		if (null != client) {
			return client.transaction();
		} else {
			throw new HiException("", "The client is closed");
		}
	}

	@Override
	public States getState() throws HiException {
		if (null != client) {
			return client.getState();
		} else {
			throw new HiException("", "The client is closed");
		}
	}

	public int getSessionTimeout() {
		return sessionTimeout;
	}

	public String getConnectString() {
		return connectString;
	}

	public boolean getCanBeReadOnly() {
		return canBeReadOnly;
	}

	@Override
	public void setRetryTimes(int times) {
		this.retryTimes = times;
	}

	private boolean watchResumed = true;

	private void reBuild() throws HiException {
		if (null != client) {

			synchronized (client) {
				if (Calendar.getInstance().getTimeInMillis() - this._startTime > this.sessionTimeout * 1000) {

					close();
					buildClient();
					this.log.info("reBuild zookeeper[" + this.connectString
							+ "]");

					watchResumed = false;
					// resumeWatch();
				}
			}
		} else {
			buildClient();
		}
	}

	private void resumeWatch() throws HiException {
		Iterator<String> it = nodeWatchers.keySet().iterator();
		String path = null;
		while (it.hasNext()) {
			path = it.next();
			Watcher watcher = nodeWatchers.get(path);
			this.exists(path, watcher);
		}
		this.log.info("resume nodeWatchers[" + nodeWatchers + "]");

		it = childNodeWatchers.keySet().iterator();
		path = null;
		while (it.hasNext()) {
			path = it.next();
			Watcher watcher = childNodeWatchers.get(path);
			this.getChildren(path, watcher);
		}
		this.log.info("resume childNodeWatchers[" + childNodeWatchers + "]");
	}

	/**
	 * 死循环处理：<br/>
	 * ConnectionLossException，需尝试重新处理；<br/>
	 * SessionExpiredException，需重建客户端后，继续执行；<br/>
	 * 其他异常中止
	 * 
	 * @param callable
	 * @return
	 * @throws HiException
	 */
	private Object retry(Callable callable) throws HiException {
		int currFail = 0;
		while (true) {
			try {
				if (null != client) {
					return callable.call();
				} else {
					throw new HiException("", "The client is closed");
				}

			} catch (ConnectionLossException e) {
				if (++currFail >= this.retryTimes) {
					log.error("Exception too many times[" + currFail + "]");
					throw new HiException(e);
				}
				log.warn("ConnectionLossException, sleep 100ms, continue");
				try {
					Thread.sleep(100);
				} catch (InterruptedException e1) {
				}
				continue;
			} catch (SessionExpiredException e) {
				if (++currFail >= this.retryTimes) {
					log.error("Exception too many times[" + currFail + "]");
					throw new HiException(e);
				}
				log.warn("SessionExpiredException, reBuild, sleep 100ms, continue");

				reBuild();
				try {
					Thread.sleep(100);
				} catch (InterruptedException e1) {
				}
				continue;
			} catch (KeeperException e) {
				throw new HiException(e);
			} catch (InterruptedException e) {
				throw new HiException(e);
			} catch (Exception e) {
				throw new HiException(e);
			}
		}
	}

	private class HiZooKeeperStateWatcher extends HiAbsZkWatcher {

		protected HiZooKeeperStateWatcher(IZkClient client, boolean watchNext,
				Logger log) {
			super(client, watchNext, log);
			// log.info("watchNext[" + this.watchNext + "]");
		}

		@Override
		public void doWatch(WatchedEvent event) {
			switch (event.getType()) {
			case None:
				switch (event.getState()) {
				case Disconnected:
				case Expired:
					try {
						reBuild();
					} catch (HiException e) {
						log.error("reBuildClient failed", e);
					}
					break;
				case SyncConnected:
					if (!watchResumed) {
						try {
							resumeWatch();
							watchResumed = true;
						} catch (HiException e) {
							log.error("resume watch failed", e);
						}
					}
					break;
				default:
					break;
				}
			default:
				break;
			}
		}

	}

	// public static void main(String[] args) {
	//
	// testcall();
	// }
	//
	// public static void testcall() {
	//
	// test(new Callable() {
	//
	// @Override
	// public Object call() throws Exception {
	// if(a++ >= 5){
	// int tmp = a;
	// return tmp;
	// }
	// System.out.println("callable call");
	// int tmp = a;
	// testcall();
	// return tmp;
	// }
	// });
	// }
	//
	// public static int a = 1;
	// public static void test(Callable callable) {
	// while (true) {
	// try {
	// callable.call();
	// break;
	// } catch (Exception e) {
	// e.printStackTrace();
	// }
	// }
	// }
}
