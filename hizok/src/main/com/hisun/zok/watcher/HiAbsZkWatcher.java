package com.hisun.zok.watcher;

import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;

import com.hisun.exception.HiException;
import com.hisun.hilog4j.Logger;
import com.hisun.zok.IZkClient;

public abstract class HiA<PERSON>ZkWatcher implements Watcher {

	protected IZkClient client = null;
	protected boolean watchNext = true;

	protected Logger log = null;

	protected HiAbsZkWatcher(IZkClient client, boolean watchNext, Logger log) {
		this.client = client;
		this.watchNext = watchNext;
		this.log = log;
	}

	public boolean isWatchNext() {
		return watchNext;
	}

	public void setWatchNext(boolean watchNext) {
		this.watchNext = watchNext;
	}

	@Override
	public void process(WatchedEvent event) {

		String path = event.getPath();
		log.info("znode[" + path + "] event[" + event.getType() + ","
				+ event.getState() + "] watchNext[" + this.watchNext + "] "
				+ this.getClass().getCanonicalName());
		if (watchNext) {
			try {
				if (null != path) {
					client.exists(path, this);
				}
			} catch (HiException e) {
				if (null != log) {
					log.error("watchNext failed", e);
				}
			}
		}

		doWatch(event);
	}

	public abstract void doWatch(WatchedEvent event);
}
