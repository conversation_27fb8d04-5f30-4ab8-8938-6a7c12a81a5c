package com.hisun.flow.watcher;

import java.util.Calendar;

import org.apache.commons.lang.StringUtils;
import org.apache.zookeeper.WatchedEvent;

import com.hisun.dispatcher.HiRouterOut;
import com.hisun.exception.HiException;
import com.hisun.flow.HiFlowSwitchs;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiContext;
import com.hisun.message.HiETF;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.message.HiXmlETF;
import com.hisun.util.HiThreadPool;
import com.hisun.zok.IZkClient;
import com.hisun.zok.watcher.HiAbsZkWatcher;

public class HiFlowMonitorWatcher extends HiAbsZkWatcher {

	private HiThreadPool threadPool = null;
	private String path;
	private String flowId = null;
	private String procServer = null;
	private String procTxnCod = null;
	private String monRegionId = null;
	private String monNodeId = null;
	private String monSysCnl = null;
	private String monBusCnl = null;
	private String monTxnCod = null;
	private String[] monFldNam = null;
	private String[] monFldVal = null;

	public HiFlowMonitorWatcher(IZkClient client, boolean watchNext,
			Logger log, HiThreadPool threadPool) {
		super(client, watchNext, log);
		this.threadPool = threadPool;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getFlowId() {
		return flowId;
	}

	public void setFlowId(String flowId) {
		this.flowId = flowId;
	}

	public String getMonRegionId() {
		return monRegionId;
	}

	public void setMonRegionId(String monRegionId) {
		this.monRegionId = monRegionId;
	}

	public String getMonNodeId() {
		return monNodeId;
	}

	public void setMonNodeId(String monNodeId) {
		this.monNodeId = monNodeId;
	}

	public String getMonSysCnl() {
		return monSysCnl;
	}

	public void setMonSysCnl(String monSysCnl) {
		this.monSysCnl = monSysCnl;
	}

	public String getMonBusCnl() {
		return monBusCnl;
	}

	public void setMonBusCnl(String monBusCnl) {
		this.monBusCnl = monBusCnl;
	}

	public String getMonTxnCod() {
		return monTxnCod;
	}

	public void setMonTxnCod(String monTxnCod) {
		this.monTxnCod = monTxnCod;
	}

	public String[] getMonFldNam() {
		return monFldNam;
	}

	public void setMonFldNam(String[] monFldNam) {
		this.monFldNam = monFldNam;
	}

	public String[] getMonFldVal() {
		return monFldVal;
	}

	public void setMonFldVal(String[] monFldVal) {
		this.monFldVal = monFldVal;
	}

	public String getProcServer() {
		return procServer;
	}

	public void setProcServer(String procServer) {
		this.procServer = procServer;
	}

	public String getProcTxnCod() {
		return procTxnCod;
	}

	public void setProcTxnCod(String procTxnCod) {
		this.procTxnCod = procTxnCod;
	}

	@Override
	public void doWatch(WatchedEvent event) {
		this.threadPool.execute(new FlowWatchTask(event));
	}

	private class FlowWatchTask implements Runnable {

		private WatchedEvent event = null;

		public FlowWatchTask(WatchedEvent event) {
			this.event = event;
		}

		@Override
		public void run() {

			switch (event.getType()) {
			case NodeDataChanged:
				try {
					String flowSwitch = new String(client.getData(
							event.getPath(), false, null));
					HiFlowSwitchs.getInstance().setSwitch(flowId, flowSwitch);
					log.info("[" + flowId + "] switch[" + flowSwitch
							+ "], procServer[" + procServer + "," + procTxnCod
							+ "]");

					if (!StringUtils.isEmpty(procServer)
							&& !StringUtils.isEmpty(procTxnCod)) {

						// 调用通知交易
						invokeService(flowSwitch);
					} else {
						log.info("Don't need invoke txn");
					}
				} catch (HiException e) {
					log.error("Watcher error", e);
				}
				break;
			default:
				break;
			}
		}
	}

	private HiMessage invokeService(String flowSwitch) throws HiException {
		// 触发控制交易
		HiMessage newMsg = new HiMessage(procServer, "DEFAULT");
		newMsg.setType("DEFAULT");
		newMsg.setHeadItem(HiMessage.TEXT_TYPE, HiMessage.TEXT_TYPE_ETF);
		newMsg.setHeadItem(HiMessage.STM, Calendar.getInstance()
				.getTimeInMillis());
		newMsg.setHeadItem(HiMessage.SDT, procServer);
		newMsg.setHeadItem(HiMessage.REQUEST_RESPONSE, HiMessage.TYPE_REQUEST);
		newMsg.setHeadItem(HiMessage.STC, procTxnCod);

		HiETF body = new HiXmlETF();
		body.setChildValue("MON_REGION_ID", monRegionId);
		body.setChildValue("MON_NODE_ID", monNodeId);
		body.setChildValue("MON_SYS_CNL", monSysCnl);
		body.setChildValue("MON_BUS_CNL", monBusCnl);
		body.setChildValue("MON_TXN_COD", monTxnCod);
		for (int i = 0; i < monFldNam.length; i++) {
			body.setGrandChildNode("MON_FLG." + monFldNam[i], monFldVal[i]);
		}
		body.setChildValue("MON_RESULT", flowSwitch);

		newMsg.setBody(body);

		HiMessageContext ctx = new HiMessageContext();
		ctx.setCurrentMsg(newMsg);
		HiContext hictx = HiContext.getCurrentContext();
		if (hictx == null) {
			HiMessageContext.setCurrentContext(ctx);
		}

		if (null != log) {
			log.info("[" + newMsg.getRequestId() + "]invoke txn[" + procServer
					+ "," + procTxnCod + "] begin");
		}
		newMsg = HiRouterOut.syncProcess(newMsg);

		if (null != log) {
			log.info("[" + newMsg.getRequestId() + "]invoke txn[" + procServer
					+ "," + procTxnCod + "], msgcd["
					+ newMsg.getETFBody().getGrandChildValue("GWA.MSG_CD")
					+ "]");
		}

		return newMsg;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append(this.path);
		sb.append(",");
		sb.append(this.flowId);
		sb.append(",");
		sb.append(this.monRegionId);
		sb.append(",");
		sb.append(this.monNodeId);
		sb.append(",");
		sb.append(this.monSysCnl);
		sb.append(",");
		sb.append(this.monBusCnl);
		sb.append(",");
		sb.append(this.monTxnCod);
		sb.append(",");
		for (int i = 0; i < monFldNam.length; i++) {
			sb.append(this.monFldNam[i]);
			sb.append("=");
			sb.append(this.monFldVal[i]);
			sb.append(",");
		}
		sb.append(this.procServer);
		sb.append(",");
		sb.append(this.procTxnCod);
		sb.append(",");
		sb.append(this.watchNext);
		return sb.toString();
	}
}
