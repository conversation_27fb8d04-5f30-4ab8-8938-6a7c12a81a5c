package com.hisun.flow.filter;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.lang.StringUtils;

import com.hisun.exception.HiException;
import com.hisun.flow.HiFlowSwitchs;
import com.hisun.flow.constant.HiFlowMonitorConstants;
import com.hisun.flow.util.HiFlowMonitorUtils;
import com.hisun.framework.event.IServerInitListener;
import com.hisun.framework.event.ServerEvent;
import com.hisun.hilog4j.Logger;
import com.hisun.ics.datafmt.json.HiJSONObject;
import com.hisun.ics.param.info.HiParamMain;
import com.hisun.mdlsvr.HiModuleConstants;
import com.hisun.mdlsvr.util.HiModuleUtils;
import com.hisun.message.HiETF;
import com.hisun.message.HiETFFactory;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.pubinterface.IHandler;
import com.hisun.pubinterface.IHandlerFilter;
import com.hisun.util.HiICSProperty;
import com.hisun.zok.util.HiZooKeeperUtils;

public class HiFlowMonitorFilter implements IHandlerFilter, IServerInitListener {

	private Logger log;
	private String regId;
	private String nodId;
	// private ArrayList msgTypeList = new ArrayList();
	// private static final String DEFAULT_MSG_TYPE = "DEFAULT";

	private String type = "OTA";
	private String zkServer = "S.ZOKSVR";

	private Map flowMonitorMap = new HashMap();
	private Map switchMap = new ConcurrentHashMap();

	public void process(HiMessageContext ctx, IHandler arg1) throws HiException {
		doProcess(ctx, arg1);
	}

	public void doProcess(HiMessageContext ctx, IHandler arg1)
			throws HiException {
		HiJSONObject flowMonitor = HiParamMain.getInstance().getParam(
				HiFlowMonitorConstants.PARAM_NAME);
		if (flowMonitor == null) {
			arg1.process(ctx);
			return;
		}

		String msgId = ctx.getCurrentMsg().getRequestId();
		// String msgTyp = ctx.getCurrentMsg().getType();
		String rqrp = ctx.getCurrentMsg().getHeadItem(
				HiMessage.REQUEST_RESPONSE);
		log.info("[" + msgId + "] rqrp=[" + rqrp + "], msgTextType["
				+ ctx.getCurrentMsg().getHeadItem(HiMessage.TEXT_TYPE) + "]");
		if (HiMessage.TYPE_RESPONSE.equalsIgnoreCase(rqrp)) {
			arg1.process(ctx);
			return;
		}

		// if (!msgTypeList.contains(msgTyp.toUpperCase())
		// && !msgTypeList.contains(DEFAULT_MSG_TYPE)) {
		// arg1.process(ctx);
		// return;
		// }

		doFlowMonitorProcess(flowMonitor, ctx, arg1, msgId);
	}

	public void doFlowMonitorProcess(HiJSONObject flowMonitor,
			HiMessageContext ctx, IHandler arg1, String msgId)
			throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		String txnCod = null;
		String sysCnl = null;
		String busCnl = null;
		if (type.equalsIgnoreCase("MDL")) {
			HiETF root = HiETFFactory.createETF(msg.getBody().toString());
			HiETF routeNode = HiModuleUtils.getModuleETFNode(root,
					HiModuleConstants.EXT_MODULE_ROUTE);
			if (null != routeNode) {
				txnCod = routeNode.getValue();
			} else {
				arg1.process(ctx);
				return;
			}

		} else {
			if (!HiMessage.TEXT_TYPE_ETF.equalsIgnoreCase(msg
					.getHeadItem(HiMessage.TEXT_TYPE))) {
				arg1.process(ctx);
				return;
			}

			HiETF root = msg.getETFBody();
			txnCod = msg.getHeadItem(HiMessage.STC);
			sysCnl = root.getGrandChildValue("GWA.SYS_CNL");
			busCnl = root.getGrandChildValue("GWA.BUS_CNL");
		}

		HiJSONObject monitorItem = getFlowMonitorItem(ctx, flowMonitor, regId,
				nodId, txnCod, sysCnl, busCnl, msgId);

		log.info("[" + msgId + "] monitorItem:" + monitorItem);
		if (monitorItem == null) {
			arg1.process(ctx);
			return;
		}

		String flowKey = monitorItem
				.getString(HiFlowMonitorConstants.COL_FLOW_ID);
		AtomicInteger currFlow = null;
		synchronized (flowMonitorMap) {
			currFlow = (AtomicInteger) flowMonitorMap.get(flowKey);
			if (null == currFlow) {
				currFlow = new AtomicInteger(0);
				flowMonitorMap.put(flowKey, currFlow);
			}
		}

		int upLimit = monitorItem
				.getInt(HiFlowMonitorConstants.COL_UPPER_LIMIT);
		int lowLimit = monitorItem
				.getInt(HiFlowMonitorConstants.COL_LOWER_LIMIT);
		String flgNam = monitorItem
				.getString(HiFlowMonitorConstants.COL_FLD_NAM);
		String flgVal = monitorItem
				.getString(HiFlowMonitorConstants.COL_FLD_VAL);
		try {
			int curr = currFlow.incrementAndGet();
			String s = (String) switchMap.get(flowKey);
			log.info("[" + msgId + "] increment[" + flowKey + "] current flow["
					+ curr + "], local switch[" + s + "]");
			if (curr == upLimit && !HiFlowSwitchs.SHUTDOWN.equals(s)) {
				// 关闭
				String path = HiFlowMonitorUtils.getZnodePath(monitorItem);
				if (!HiZooKeeperUtils.exists(zkServer, path)) {
					HiZooKeeperUtils.create(zkServer, path);
				}
				HiZooKeeperUtils.setData(zkServer, path,
						HiFlowSwitchs.SHUTDOWN.getBytes());
				switchMap.put(flowKey, HiFlowSwitchs.SHUTDOWN);
				log.warn("[" + msgId + "] switch closing! over upper limit["
						+ regId + "," + nodId + "," + sysCnl + "," + busCnl
						+ "," + txnCod + "," + flgNam + "," + flgVal + ","
						+ lowLimit + "-" + upLimit + "," + "], currFlow["
						+ curr + "], znode[" + path + "]");
			}

			arg1.process(ctx);
		} catch (Exception e) {
			throw new HiException(e);
		} finally {

			if (currFlow != null) {
				int curr = currFlow.decrementAndGet();
				String s = (String) switchMap.get(flowKey);
				log.info("[" + msgId + "] decrement[" + flowKey
						+ "] current flow[" + curr + "], local switch[" + s
						+ "]");
				if (curr == lowLimit && HiFlowSwitchs.SHUTDOWN.equals(s)) {
					// 打开
					String path = HiFlowMonitorUtils.getZnodePath(monitorItem);
					HiZooKeeperUtils.setData(zkServer, path,
							HiFlowSwitchs.OPEN.getBytes());
					switchMap.put(flowKey, HiFlowSwitchs.OPEN);
					log.warn("[" + msgId
							+ "] switch opening! down to lower limit[" + regId
							+ "," + nodId + "," + sysCnl + "," + busCnl + ","
							+ txnCod + "," + flgNam + "," + flgVal + ","
							+ lowLimit + "-" + upLimit + "," + "], currFlow["
							+ curr + "], znode[" + path + "]");
				}
			}
		}

	}

	public void serverInit(ServerEvent event) throws HiException {
		log = event.getLog();
		regId = HiICSProperty.getProperty("ics.sys.regionid");
		nodId = HiICSProperty.getProperty("ics.sys.nodeid");
		log.info("server init, [" + regId + "," + nodId + "]");
	}

	private HiJSONObject getFlowMonitorItem(HiMessageContext ctx,
			HiJSONObject flowMonitor, String regId, String nodId,
			String txnCod, String sysCnl, String busCnl, String msgId) {
		Enumeration en = flowMonitor.keys();
		HiJSONObject monitorItem = null;
		if (log.isDebugEnabled()) {
			log.debug("[" + msgId + "] getFlowMonitorItem, [" + regId + ","
					+ nodId + "," + "," + sysCnl + "," + busCnl + "," + txnCod
					+ "]");
		}
		while (en.hasMoreElements()) {
			String keyNam = (String) en.nextElement();
			HiJSONObject tmpMonitorItem = flowMonitor.getJSONObject(keyNam);

			String tmp = tmpMonitorItem
					.getString(HiFlowMonitorConstants.COL_REG_ID);
			if (!StringUtils.equalsIgnoreCase(tmp, "*")
					&& !StringUtils.equalsIgnoreCase(regId, tmp)) {
				continue;
			}
			tmp = tmpMonitorItem.getString(HiFlowMonitorConstants.COL_NOD_ID);
			if (!StringUtils.equalsIgnoreCase(tmp, "*")
					&& !StringUtils.equalsIgnoreCase(nodId, tmp)) {
				continue;
			}
			tmp = tmpMonitorItem.getString(HiFlowMonitorConstants.COL_SYS_CNL);
			if (!StringUtils.equalsIgnoreCase(tmp, "*")
					&& !StringUtils.equalsIgnoreCase(sysCnl, tmp)) {
				continue;
			}
			tmp = tmpMonitorItem.getString(HiFlowMonitorConstants.COL_BUS_CNL);
			if (!StringUtils.equalsIgnoreCase(tmp, "*")
					&& !StringUtils.equalsIgnoreCase(busCnl, tmp)) {
				continue;
			}
			if (!StringUtils
					.equalsIgnoreCase(txnCod, tmpMonitorItem
							.getString(HiFlowMonitorConstants.COL_TX_COD))) {
				continue;
			}

			String fldNam = tmpMonitorItem
					.getString(HiFlowMonitorConstants.COL_FLD_NAM);
			String fldVal = tmpMonitorItem
					.getString(HiFlowMonitorConstants.COL_FLD_VAL);

			int upLimit = tmpMonitorItem
					.getInt(HiFlowMonitorConstants.COL_UPPER_LIMIT);
			int lowLimit = tmpMonitorItem
					.getInt(HiFlowMonitorConstants.COL_LOWER_LIMIT);

			if (lowLimit >= upLimit) {
				log.warn("[" + msgId + "] [" + keyNam
						+ "] LOWER_LIMIT >= UPPER_LIMIT.");
				continue;
			}

			if (StringUtils.isBlank(fldNam) || StringUtils.isBlank(fldVal)) {

				monitorItem = tmpMonitorItem;
				break;
			} else {

				HiETF root = ctx.getCurrentMsg().getETFBody();
				String[] fldNams = StringUtils.splitByWholeSeparator(fldNam,
						"|");
				String[] fldVals = StringUtils.splitByWholeSeparator(fldVal,
						"|");
				boolean isMatch = true;
				for (int i = 0; i < fldNams.length; i++) {

					String name = fldNams[i];
					String tmpValue = null;
					if (StringUtils.equals("BOSS_PROV_CD", name)) {
						// 根据手机号获取BOSS省代码
						String mblNo = root.getChildValue("MBL_NO");
						if (!StringUtils.isBlank(mblNo)) {
							tmpValue = getBossProvCdByMblNo(ctx, mblNo);
						}
					} else {
						tmpValue = root.getGrandChildValue(name);
					}
					if (!StringUtils.equalsIgnoreCase(tmpValue, fldVals[i])) {
						isMatch = false;
						break;
					}
				}

				if (isMatch) {
					monitorItem = tmpMonitorItem;
					break;
				}
			}

		}
		return monitorItem;
	}

	private String getBossProvCdByMblNo(HiMessageContext ctx, String mblNo) {
		try {
			HashMap rec = ctx.getDataBaseUtil().readRecordBind(
					"select CITY_CD from cmmtmbst where MBL_SGM=?",
					StringUtils.substring(mblNo, 0, 8));
			if (!rec.isEmpty()) {
				return (String) rec.get("CITY_CD");
			}
			rec = ctx.getDataBaseUtil().readRecordBind(
					"select CITY_CD from cmmtmbst where MBL_SGM=?",
					StringUtils.substring(mblNo, 0, 7));

			if (!rec.isEmpty()) {
				return (String) rec.get("CITY_CD");
			}
			return mblNo;
		} catch (HiException e) {
			log.error(e);
			return mblNo;
		} finally {
			ctx.getDataBaseUtil().close();
		}
	}

	// public void setMsgType(String msgType) {
	// msgTypeList.add(msgType.toUpperCase());
	// }

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getZkServer() {
		return zkServer;
	}

	public void setZkServer(String zkServer) {
		this.zkServer = zkServer;
	}

}
