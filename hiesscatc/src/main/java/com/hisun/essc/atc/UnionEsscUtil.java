package com.hisun.essc.atc;

import org.apache.commons.lang.StringUtils;

import UnionTech.JavaEsscAPI.UnionAPI;
import UnionTech.JavaEsscAPI.UnionMobile;

import com.hisun.atc.HiATCConstants;
import com.hisun.atc.common.HiArgUtils;
import com.hisun.constants.HiMessageCode;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiETF;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;

/**
 * 访问essc 加密机平台的java组件
 * <AUTHOR>
 *
 */
public class UnionEsscUtil {
	
	private static Logger unionEssclog = HiLog.getLogger("UnionEssc.trc");
	
	//private UnionAPI unionApi = null;
		
	public UnionEsscUtil(){
	}
	
	/**
	 * 使用指定公钥验证
	 * @param argsMap
	 * keyName,节点名称
	 * lenOfData,签名数据的长度
	 * data，签名数据
	 * lenOfSign，签名长度
	 * flag，数据填充方式
     * hashID (HASH算法标志) 01，SHA-1，02，MD5，03，ISO 1011802，04，NoHash
     * sign，签名
	 * @param ctx
	 * @return
	 * @throws HiException
	 */
	public int UnionNewVerifySignature(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		Logger log = HiLog.getLogger(msg);
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String keyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String lenOfData = HiArgUtils.getStringNotNull(argsMap, "LenOfData");
		String data = HiArgUtils.getStringNotNull(argsMap, "Data");
		String lenOfSign = HiArgUtils.getStringNotNull(argsMap, "LenOfSign");
		String flag = HiArgUtils.getStringNotNull(argsMap, "Flag");
		String hashID = HiArgUtils.getStringNotNull(argsMap, "HashID");
		String sign = HiArgUtils.getStringNotNull(argsMap, "Sign");
		long startTime = System.currentTimeMillis();
		int ret = -1;
		try {
			ret = unionApi.UnionNewVerifySignature(keyName, flag, hashID, Integer.parseInt(lenOfData), data, sign, Integer.parseInt(lenOfSign));
		} catch (NumberFormatException e) {
			throw new HiException(e);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionNewVerifySignature exec time "+(System.currentTimeMillis() - startTime));
		}
		if(0 == ret){
			return HiATCConstants.SUCC;
		}else {
			log.info("UnionNewVerifySignature return ["+ret+"]");
			return HiATCConstants.ERR;
		}
	}
	
	/**
	 * 使用指定应用的私钥进行签名
	 * @param argsMap
	 * idOfApp,节点名
	 * lenOfData,签名数据的长度
	 * data，签名数据
	 * flag，数据填充方式 “0”：如果数据不是密钥长度的整倍数，后面补0x00，“1”：PKCS填充方式（一般情况下使用此方式）
	 * hashID，HASH算法标志（01，SHA-1，02，MD5，03，ISO 1011802，04，NoHash）
	 * DstFld 签名结果在ETF树上的节点名 默认SIGN
	 * @param ctx
	 * @return
	 * @throws HiException
	 */
	public int UnionNewGenerateSignature(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String idOfApp = HiArgUtils.getStringNotNull(argsMap, "IdOfApp");
		String lenOfData = HiArgUtils.getStringNotNull(argsMap, "LenOfData");
		String data = HiArgUtils.getStringNotNull(argsMap, "Data");
		String flag = HiArgUtils.getStringNotNull(argsMap, "Flag");
		String hashID = HiArgUtils.getStringNotNull(argsMap, "HashID");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "SIGN";
		}
		long startTime = System.currentTimeMillis();
		String sign= null;
		try {
			sign = unionApi.UnionNewGenerateSignature(idOfApp, flag, hashID, Integer.parseInt(lenOfData), data);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionNewGenerateSignature exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, sign);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 将一个ZPK加密的PIN密文转换为另一个ZPK加密的PIN密文 加密算法为Ansi x9.8/x9.18
	 * @param argsMap
	 * fullName1：节点名1
	 * fullName2：节点名2
	 * pinBlock1：源密钥加密的PIN密文(PIN格式为ANSI9。8),长度16字节字符串(ASCII), 以'\0'结束。
	 * accNo：源账号/卡号,长度13-19字节字符串(ASCII), 以'\0'结束;卡号为实际卡号;账号和卡号填16个ASCII'0',表示无账号运算。
	 * DstFld 签名结果在ETF树上的节点名 默认PinBlock2
	 * @param ctx
	 * @return
	 * 返回值：pinBlock2,pinBlock2：目的成员行机构号加密的PIN密文(PIN格式为ANSI9.8), 长度16字节字符串(ASCII), 以'\0'结束
	 * @throws HiException
	 */
	public int UnionTranslatePin(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullKeyName1 = HiArgUtils.getStringNotNull(argsMap, "FullName1");
		String fullKeyName2 = HiArgUtils.getStringNotNull(argsMap, "FullName2");
		String pinBlock1 = HiArgUtils.getStringNotNull(argsMap, "PinBlock1");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinBlock2";
		}
		long startTime = System.currentTimeMillis();
		String pinBlock2= null;
		try {
			pinBlock2 = unionApi.UnionTranslatePin(fullKeyName1, fullKeyName2, pinBlock1, accNo);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionTranslatePin exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinBlock2);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 310将一个ZPK加密的PIN密文转换为另一个ZPK加密的PIN密文,带2个账号 加密算法为Ansi x9.8/x9.18
	 * @param argsMap
	 * fullName1：节点名1
	 * fullName2：节点名2
	 * pinBlock1：源密钥加密的PIN密文(PIN格式为ANSI9。8),长度16字节字符串(ASCII), 以'\0'结束。
	 * accNo：源账号/卡号,长度13-19字节字符串(ASCII), 以'\0'结束;卡号为实际卡号;账号和卡号填16个ASCII'0',表示无账号运算。
	 * accNo：目标账号/卡号,长度13-19字节字符串(ASCII), 以'\0'结束;卡号为实际卡号;账号和卡号填16个ASCII'0',表示无账号运算。
	 * DstFld 签名结果在ETF树上的节点名 默认PinBlock2
	 * @param ctx
	 * @return
	 * 返回值：pinBlock2,pinBlock2：目的成员行机构号加密的PIN密文(PIN格式为ANSI9.8), 长度16字节字符串(ASCII), 以'\0'结束
	 * @throws HiException
	 */
	public int UnionTranslatePinWith2AccNo(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullKeyName1 = HiArgUtils.getStringNotNull(argsMap, "FullName1");
		String fullKeyName2 = HiArgUtils.getStringNotNull(argsMap, "FullName2");
		String pinBlock1 = HiArgUtils.getStringNotNull(argsMap, "PinBlock1");
		String accNo1 = HiArgUtils.getStringNotNull(argsMap, "AccNo1");
		String accNo2 = HiArgUtils.getStringNotNull(argsMap, "AccNo2");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinBlock2";
		}
		long startTime = System.currentTimeMillis();
		String pinBlock2= null;
		try {
			pinBlock2 = unionApi.UnionTranslatePinWith2AccNo(fullKeyName1, fullKeyName2, pinBlock1, accNo1, accNo2);
		} catch (Exception e) {
			throw new HiException(e);
		}
		
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionTranslatePinWith2AccNo exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinBlock2);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 将一个lmk加密的PIN转换为由zpk加密的PIN 
	 * @param argsMap
	 * accNo：账号/卡号
	 * zpkName：ZPK密钥名称
	 * pinByLmk：LMK0203对加密的PIN
	 * DstFld 签名结果在ETF树上的节点名 默认PinByZpk
	 * @param ctx
	 * @return
	 * 返回值 pin密文：ZPK加密的PIN
	 * @throws HiException
	 */
	public int UnionDerivePinBlockFromPinByLmk(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String zpkName = HiArgUtils.getStringNotNull(argsMap, "ZpkName");
		String pinByLmk = HiArgUtils.getStringNotNull(argsMap, "PinByLmk");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinByZpk";
		}
		long startTime = System.currentTimeMillis();
		String pinByZpk= null;
		try {
			pinByZpk = unionApi.UnionDerivePinBlockFromPinByLmk(accNo, pinByLmk, zpkName);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionDerivePinBlockFromPinByLmk exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinByZpk);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 使用指定ZMK加密的密钥生成MAC 337
	 * @param argsMap
	 * fullName：节点名
	 * keyByZmk: 是ZMK加密的密钥
	 * lenOfMacData：MAC数据的长度,以十进制表示, <=500
	 * macData：MAC数据,长度变长, 字符串, 以'\0'结束
	 * DstFld 签名结果在ETF树上的节点名 默认Mac
	 * @param ctx
	 * @return
	 * 返回值 MAC值,长度16字节字符串(ASCII), 以'\0'结束
	 * @throws HiException
	 */
	public int UnionGenerateChinaPayMacUsingKeyByZmk(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullName = HiArgUtils.getStringNotNull(argsMap, "FullName");
		String keyByZmk = HiArgUtils.getStringNotNull(argsMap, "KeyByZmk");
		String lenOfMacData = HiArgUtils.getStringNotNull(argsMap, "LenOfMacData");
		String macData = HiArgUtils.getStringNotNull(argsMap, "MacData");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Mac";
		}
		long startTime = System.currentTimeMillis();
		String mca= null;
		try {
			mca = unionApi.UnionGenerateChinaPayMacUsingKeyByZmk(fullName, keyByZmk, Integer.parseInt(lenOfMacData), macData);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGenerateChinaPayMacUsingKeyByZmk exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, mca);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 将ZPK1（公钥加密）加密的PIN转换为ZPK2加密的PIN（支持密文） 
	 * @param argsMap
	 * zpk1Ciphertext:公钥加密下的zpk1密文值 
	 * zpk2KeyName:Zpk2密钥名
	 * lengthFlagOfzpk1:密钥长度标志
	 * privateKeyIndex:私钥索引号
	 * pinblock:Pinblock
	 * acco:账号/手机号
	 * checkValue:校验值
	 * srcPinBlockFormat:源PINBLOCK格式
	 * objPinBlockFormat:目的PINBLOCK格式
	 * 
	 * DstFld 签名结果在ETF树上的节点名 默认PinByZpk
	 * @param ctx
	 * @return
	 * 返回值 pinBlock:Zpk2下加密的pin
	 * @throws HiException
	 */
	public int UnionZpk1PinToZpk2PinSupportCiphertext(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String zpk1Ciphertext = HiArgUtils.getStringNotNull(argsMap, "Zpk1Ciphertext");
		String zpk2KeyName = HiArgUtils.getStringNotNull(argsMap, "Zpk2KeyName");
		String lengthFlagOfzpk1 = HiArgUtils.getStringNotNull(argsMap, "LengthFlagOfzpk1");
		String privateKeyIndex = HiArgUtils.getStringNotNull(argsMap, "PrivateKeyIndex");
		String pinBlock = HiArgUtils.getStringNotNull(argsMap, "Pinblock");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String checkValue = HiArgUtils.getStringNotNull(argsMap, "CheckValue");
		String srcPinBlockFormat = HiArgUtils.getStringNotNull(argsMap, "SrcPinBlockFormat");
		String objPinBlockFormat = HiArgUtils.getStringNotNull(argsMap, "ObjPinBlockFormat");
		
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Zpk2Pin";
		}
		long startTime = System.currentTimeMillis();
		String Zpk2Pin= null;
		try {
			Zpk2Pin = unionApi.UnionConvertsZpk1EncryptedPinToZpk2EncryptedPinSupportCiphertext(zpk1Ciphertext, zpk2KeyName, lengthFlagOfzpk1, privateKeyIndex, pinBlock, accNo, checkValue, srcPinBlockFormat, objPinBlockFormat);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionZpk1PinToZpk2PinSupportCiphertext exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, Zpk2Pin);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 将ZPK1（公钥加密）加密的PIN转换为ZPK2加密的PIN（支持密文,不传校验值） 
	 * @param argsMap
	 * zpk1Ciphertext:公钥加密下的zpk1密文值 
	 * zpk2KeyName:Zpk2密钥名
	 * lengthFlagOfzpk1:密钥长度标志
	 * privateKeyIndex:私钥索引号
	 * pinblock:Pinblock
	 * acco:账号/手机号
	 * checkValue:校验值
	 * srcPinBlockFormat:源PINBLOCK格式
	 * objPinBlockFormat:目的PINBLOCK格式
	 * 
	 * DstFld 签名结果在ETF树上的节点名 默认PinByZpk
	 * @param ctx
	 * @return
	 * 返回值 pinBlock:Zpk2下加密的pin
	 * @throws HiException
	 */
	public int UnionZpk1PinToZpk2PinSupportCiphertextNoCheckValue(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String zpk1Ciphertext = HiArgUtils.getStringNotNull(argsMap, "Zpk1Ciphertext");
		String zpk2KeyName = HiArgUtils.getStringNotNull(argsMap, "Zpk2KeyName");
		String lengthFlagOfzpk1 = HiArgUtils.getStringNotNull(argsMap, "LengthFlagOfzpk1");
		String privateKeyIndex = HiArgUtils.getStringNotNull(argsMap, "PrivateKeyIndex");
		String pinBlock = HiArgUtils.getStringNotNull(argsMap, "Pinblock");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String srcPinBlockFormat = HiArgUtils.getStringNotNull(argsMap, "SrcPinBlockFormat");
		String objPinBlockFormat = HiArgUtils.getStringNotNull(argsMap, "ObjPinBlockFormat");
		
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Zpk2Pin";
		}
		long startTime = System.currentTimeMillis();
		String Zpk2Pin= null;
		try {
			Zpk2Pin = unionApi.UnionConvertsZpk1EncryptedPinToZpk2EncryptedPinSupportCiphertextWithNoCheckValue(zpk1Ciphertext, zpk2KeyName, lengthFlagOfzpk1, privateKeyIndex, pinBlock, accNo, srcPinBlockFormat, objPinBlockFormat);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionZpk1PinToZpk2PinSupportCiphertextNoCheckValue exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, Zpk2Pin);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 把公钥下加密的PIN转到ZPK下加密(支持密文)
	 * @param argsMap
	 * privateKeyIndex:私钥索引
	 * zpkKeyName:ZPK密钥名称
	 * acco:账号或卡号或手机号
	 * pinCiphertext:经公钥加密的PIN数据密文
	 * 
	 * DstFld 签名结果在ETF树上的节点名 默认PinCiphertextByZpk
	 * @param ctx
	 * @return
	 * 返回值 pinCiphertextByZpk:ZPK加密下的PIN密文 
	 * @throws HiException
	 */
	public int UnionChangePkEncryptPinToZpkEncryptSupportCiphertext(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String privateKeyIndex = HiArgUtils.getStringNotNull(argsMap, "PrivateKeyIndex");
		String zpkKeyName = HiArgUtils.getStringNotNull(argsMap, "ZpkKeyName");
		String acco = HiArgUtils.getStringNotNull(argsMap, "Acco");
		String pinCiphertext = HiArgUtils.getStringNotNull(argsMap, "PinCiphertext");
		
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinCiphertextByZpk";
		}
		long startTime = System.currentTimeMillis();
		String pinOffset= null;
		try {
			pinOffset = unionApi.UnionChangePkEncryptPinToZpkEncryptSupportCiphertext(privateKeyIndex, zpkKeyName, acco, pinCiphertext);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionChangePkEncryptPinToZpkEncryptSupportCiphertext exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinOffset);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 将ZEK（公钥加密）加密/待加密的数据进行解密/加密 （支持密文）
	 * @param argsMap
	 * zekCiphertext:公钥加密下的zek密文值 
	 * lengthFlagOfZek:密钥长度标志
	 * privateKeyIndex:私钥索引号
	 * checkValue:校验值
	 * modeTag:方案模式
	 * encryptMode:加密模式
	 * data:待加密/解密的数据
	 * infoType：输入输出的消息类型
	 * iv:初始向量
	 * 
	 * DstFld 签名结果在ETF树上的节点名 默认Result
	 * @param ctx
	 * @return
	 * 返回值 Result:加密解密后的数据
	 * @throws HiException
	 */
	public int UnionEncryptZekEncDataSupportCiphertext(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String zekCiphertext = HiArgUtils.getStringNotNull(argsMap, "ZekCiphertext");
		String lengthFlagOfZek = HiArgUtils.getStringNotNull(argsMap, "LengthFlagOfZek");
		String privateKeyIndex = HiArgUtils.getStringNotNull(argsMap, "PrivateKeyIndex");
		String modeTag = HiArgUtils.getStringNotNull(argsMap, "ModeTag");
		String encryptMode = HiArgUtils.getStringNotNull(argsMap, "EncryptMode");
		String data = HiArgUtils.getStringNotNull(argsMap, "Data");
		String infoType = HiArgUtils.getStringNotNull(argsMap, "InfoType");
		String iv = argsMap.get("Iv");
		
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Result";
		}
		long startTime = System.currentTimeMillis();
		String pinOffset= null;
		try {
			pinOffset = unionApi.UnionEncryptZekEncDataSupportCiphertext(zekCiphertext, lengthFlagOfZek, privateKeyIndex, modeTag, encryptMode, data, infoType, iv);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionEncryptZekEncDataSupportCiphertext exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinOffset);
		return HiATCConstants.SUCC;
	}
	
	
	/**
	 * 生成指定的密钥替换库中的密钥
	 * @param argsMap
	 * KeyName，节点名 
	 * 
	 * DstFldKeyVal 签名结果在ETF树上的节点名 默认KeyValue
	 * DstFldChkVal 签名结果在ETF树上的节点名 默认CheckValue
	 * @param ctx
	 * @return
	 * 返回值 KeyValue, 密钥密文（ZMK加密）
	 * 返回值 CheckValue, 校验值（加密全0）
	 * @throws HiException
	 */
	public int UnionGenerateKey(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullKeyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		
		String destFld = argsMap.get("DstFldKeyVal");
		if(StringUtils.isBlank(destFld)){
			destFld = "KeyValue";
		}
		String destFld2 = argsMap.get("DstFldChkVal");
		if(StringUtils.isBlank(destFld2)){
			destFld2 = "CheckValue";
		}
		long startTime = System.currentTimeMillis();
		String[] results;
		try {
			results = unionApi.UnionGenerateKey(fullKeyName);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGenerateKey exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, results[0]);
		etfRoot.setChildValue(destFld2, results[1]);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 为省平台生成指定的密钥替换库中的密钥
	 * @param argsMap
	 * provCd，省代码
	 * 
	 * DstFldKeyVal 签名结果在ETF树上的节点名 默认KeyValue
	 * DstFldChkVal 签名结果在ETF树上的节点名 默认CheckValue
	 * @param ctx
	 * @return
	 * 返回值 KeyValue, 密钥密文（ZMK加密）
	 * 返回值 CheckValue, 校验值（加密全0）
	 * @throws HiException
	 */
	public int UnionGenerateKeyForC0(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String provCd = HiArgUtils.getStringNotNull(argsMap, "ProvCd");
		String fullKeyName = "Posp."+provCd+".zpk";
		
		String destFld = argsMap.get("DstFldKeyVal");
		if(StringUtils.isBlank(destFld)){
			destFld = "KeyValue";
		}
		String destFld2 = argsMap.get("DstFldChkVal");
		if(StringUtils.isBlank(destFld2)){
			destFld2 = "CheckValue";
		}
		long startTime = System.currentTimeMillis();
		String[] results;
		try {
			results = unionApi.UnionGenerateKey(fullKeyName);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGenerateKeyForC0 exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, results[0]);
		etfRoot.setChildValue(destFld2, results[1]);
		return HiATCConstants.SUCC;
	}
	
	
	/**
	 * 使用指定密钥生成MAC,算法是中国银联标准
	 * @param argsMap
	 * KeyName，节点名 
	 * lenOfMacData：MAC数据的长度,以十进制表示, <=500
	 * macData：MAC数据,长度变长, 字符串, 以'\0'结束
	 * 
	 * DstFld 签名结果在ETF树上的节点名 默认Mac
	 * @param ctx
	 * @return
	 * 返回值 mac：MAC值,长度16字节字符串(ASCII), 以'\0'结束
	 * @throws HiException
	 */
	public int UnionGenerateChinaPayMac(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullKeyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String lenOfMacData = HiArgUtils.getStringNotNull(argsMap, "LenOfMacData");
		String macData = HiArgUtils.getStringNotNull(argsMap, "MacData");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Mac";
		}
		long startTime = System.currentTimeMillis();
		String mac;
		try {
			mac = unionApi.UnionGenerateChinaPayMac(fullKeyName, Integer.parseInt(lenOfMacData), macData);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGenerateChinaPayMac exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, mac);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 使用指定密钥验证MAC,算法是中国银联标准
	 * @param argsMap
	 * KeyName，节点名 
	 * lenOfMacData：MAC数据的长度,以十进制表示, <=500
	 * macData：MAC数据,长度变长, 字符串, 以'\0'结束
	 * mac：MAC值,(ASCII), 以'\0'结束
	 * 
	 * @param ctx
	 * @return
	 * 返回值 
	 * @throws HiException
	 */
	public int UnionVerifyChinaPayMac(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		Logger log = HiLog.getLogger(msg);
		long startTime = System.currentTimeMillis();
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullKeyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String lenOfMacData = HiArgUtils.getStringNotNull(argsMap, "LenOfMacData");
		String macData = HiArgUtils.getStringNotNull(argsMap, "MacData");
		String mac = HiArgUtils.getStringNotNull(argsMap, "Mac");
		
		int result=-1;
		try {
			result = unionApi.UnionVerifyChinaPayMac(fullKeyName, Integer.parseInt(lenOfMacData), macData, mac);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionVerifyChinaPayMac exec time "+(System.currentTimeMillis() - startTime));
		}
		if(result <0){
			log.error("UnionVerifyChinaPayMac return ["+result+"]");
			return HiATCConstants.ERR;
		}
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 计算及校验MAC/TAC
	 * @param argsMap
	 * modeTag:加密模式标识 
	 * schemeID:方案ID
	 * keyName:根密钥名
	 * disperseTime:离散次数
	 * disperseData:离散数据
	 * dataFillFlag:MAC数据填充标志
	 * iv:IV-MAC
	 * macData:MAC计算数据
	 * macLengthFlag:MAC长度标识
	 * waitVerifyMacData:待校验的MAC
	 * 
	 * DstFld 签名结果在ETF树上的节点名 默认Mac
	 * @param ctx
	 * @return
	 * 返回值 当modeTag为1时，返回mac:MAC ;当modeTag为2时，返回错误码，小于0表示失败，否则成功
	 * @throws HiException
	 */
	public int UnionCalculateAndVerifyMac(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		Logger log = HiLog.getLogger(msg);
		long startTime = System.currentTimeMillis();
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String modeTag = HiArgUtils.getStringNotNull(argsMap, "ModeTag");
		String schemeID = HiArgUtils.getStringNotNull(argsMap, "SchemeID");
		String keyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String disperseTime = HiArgUtils.getStringNotNull(argsMap, "DisperseTime");
		String disperseData = HiArgUtils.getStringNotNull(argsMap, "DisperseData");
		String dataFillFlag = HiArgUtils.getStringNotNull(argsMap, "DataFillFlag");
		String iv = HiArgUtils.getStringNotNull(argsMap, "Iv");
		String macData = HiArgUtils.getStringNotNull(argsMap, "MacData");
		String macLengthFlag = HiArgUtils.getStringNotNull(argsMap, "MacLengthFlag");
		String waitVerifyMacData = HiArgUtils.getStringNotNull(argsMap, "WaitVerifyMacData");
		
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Mac";
		}
		
		String result=null;
		try {
			result = unionApi.UnionCalculateAndVerifyMac(modeTag, schemeID, keyName, Integer.parseInt(disperseTime), disperseData, dataFillFlag, iv, macData, Integer.parseInt(macLengthFlag), waitVerifyMacData);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionCalculateAndVerifyMac exec time "+(System.currentTimeMillis() - startTime));
		}
		log.debug("modeTag=["+modeTag+"],UnionCalculateAndVerifyMac return ["+result+"]");
		if(StringUtils.equals(modeTag, "1")){
			etfRoot.setChildValue(destFld, result);
		}else if(StringUtils.equals(modeTag, "2")){
			if(Integer.parseInt(result)<0){
				log.error("UnionCalculateAndVerifyMac return ["+result+"]");
				return HiATCConstants.ERR;
			}
		}else{
			log.error("UnionCalculateAndVerifyMac modeTag value is error,modeTag ["+modeTag+"]");
			return HiATCConstants.ERR;
		}
		return HiATCConstants.SUCC;
	}
	
	
	/**
	 * 数据加解密
	 * @param argsMap
	 * modeTag:加密模式标识 
	 * schemeID:方案ID
	 * keyType:根密钥类型
	 * keyName:根密钥名
	 * disperseTime:离散次数
	 * disperseData:离散数据
	 * processData:过程数据
	 * data:数据
	 * AutoFill 是否自动填充（ModeTag 为0 或1 的时候使用）
	 * DstFld 签名结果在ETF树上的节点名 默认Result
	 * @param ctx
	 * @return
	 * 返回值 数据明文/密文
	 * @throws HiException
	 */
	public int UnionEncryptOrdecode(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		Logger log = HiLog.getLogger(msg);
		long startTime = System.currentTimeMillis();
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String modeTag = HiArgUtils.getStringNotNull(argsMap, "ModeTag");
		String schemeID = HiArgUtils.getStringNotNull(argsMap, "SchemeID");
		String keyType = HiArgUtils.getStringNotNull(argsMap, "KeyType");
		String keyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String disperseTime = HiArgUtils.getStringNotNull(argsMap, "DisperseTime");
		String disperseData = HiArgUtils.getStringNotNull(argsMap, "DisperseData");
		String processData = null;
		if(StringUtils.equals(modeTag, "6")||StringUtils.equals(modeTag, "7")){
			disperseData = HiArgUtils.getStringNotNull(argsMap, "ProcessData");
		}else{
			processData = argsMap.get("ProcessData");
		}
		
		String data = HiArgUtils.getStringNotNull(argsMap, "Data");
		String autoFill = argsMap.get("AutoFill");
		if(StringUtils.isBlank(autoFill)){
			autoFill = "N";
		}
		
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Result";
		}
		//自动填充
		if(StringUtils.equalsIgnoreCase("Y", autoFill) && StringUtils.equals(modeTag, "0")){
			//加密数据组成：1字节明文数据的长度+明文数据+填充字符，加密数据应是8字节整数倍，不足部分使用0x80填充
			int inDataLen = data.length()/2;
			String inDataLenHex = Integer.toHexString(inDataLen).toUpperCase();
			String encDat = (inDataLen < 16 ? "0" : "") + inDataLenHex + data.toUpperCase();
			if (encDat.length() % 16 != 0){
				encDat = encDat + "8000000000000000".substring(0, 16 - (encDat.length() % 16));
			}
			data = encDat;
		}
		
		String result=null;
		try {
			result = unionApi.UnionEncryptOrdecode(modeTag, schemeID, keyType, keyName, Integer.parseInt(disperseTime), disperseData, processData, data);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionEncryptOrdecode exec time "+(System.currentTimeMillis() - startTime));
		}
		//自动填充
		if(StringUtils.equalsIgnoreCase("Y", autoFill) && StringUtils.equals(modeTag, "1")){
			String lengthStr = result.substring(0,2);//前2个字符是长度
			int length = Integer.parseInt(lengthStr, 16)*2;//16进制转10进制
			result = result.substring(2, length+2);
		}
		log.debug("modeTag=["+modeTag+"],UnionEncryptOrdecode return ["+result+"]");
		etfRoot.setChildValue(destFld, result);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * (816)解密PIN数据密文转输出pinoffset
	 * @param argsMap
	 * modeTag:加密模式标识
	 * schemeID:方案ID (加密算法模式：01=ECB 02=CBC 03=CFB 04=OFB)
	 * keyType:根密钥类型
	 * keyName:根密钥名
	 * disperseTime:离散次数
	 * disperseData:离散数据
	 * processData:过程数据
	 * data:数据
	 * pvkKeyName:Pvk密钥名
	 * acco:账号/手机号
	 * pinOffset:pin偏移量 用于运算中获取pin明文值
	 * 
	 * DstFld pinOffset在ETF树上的节点名 默认Result
	 * @param ctx
	 * @return
	 * 返回值 pinOffset
	 * @throws HiException
	 */
	public int UnionDecryptPinDataAndOutputPinOffset(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		Logger log = HiLog.getLogger(msg);
		long startTime = System.currentTimeMillis();
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String modeTag = HiArgUtils.getStringNotNull(argsMap, "ModeTag");
		String schemeID = HiArgUtils.getStringNotNull(argsMap, "SchemeID");
		String keyType = HiArgUtils.getStringNotNull(argsMap, "KeyType");
		String keyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String disperseTime = HiArgUtils.getStringNotNull(argsMap, "DisperseTime");
		String disperseData = HiArgUtils.getStringNotNull(argsMap, "DisperseData");
		String processData = HiArgUtils.getStringNotNull(argsMap, "ProcessData");
		String data = HiArgUtils.getStringNotNull(argsMap, "Data");
		String pvkKeyName = HiArgUtils.getStringNotNull(argsMap, "PvkKeyName");
		String acco = HiArgUtils.getStringNotNull(argsMap, "Acco");
		String pinOffset = HiArgUtils.getStringNotNull(argsMap, "PinOffset");
		
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Result";
		}
		
		String result=null;
		try {
			result = unionApi.UnionDecryptPinDataAndOutputPinOffset(modeTag, schemeID, keyType, keyName, Integer.parseInt(disperseTime), disperseData, processData, data, pvkKeyName, acco, pinOffset);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionDecryptPinDataAndOutputPinOffset exec time "+(System.currentTimeMillis() - startTime));
		}
		log.debug("modeTag=["+modeTag+"],UnionDecryptPinDataAndOutputPinOffset return ["+result+"]");
		etfRoot.setChildValue(destFld, result);
		return HiATCConstants.SUCC;
	}
	
	/**
	 * 将ZMK加密的密钥存储到库中 283
	 * @param argsMap
	 * fullName, 节点名
	 * 	keyValue, 密钥密文（ZMK加密）
	 * 	checkValue,校验值（加密全0）
	 * @param ctx
	 * @return
	 * 返回值：	<0，函数执行失败，抛出异常，	0，成功
	 * @throws HiException
	 */
	public int UnionStoreKey(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		Logger log = HiLog.getLogger(msg);
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullName = HiArgUtils.getStringNotNull(argsMap, "FullName");
		String keyValue = HiArgUtils.getStringNotNull(argsMap, "KeyValue");
		String checkValue = argsMap.get("CheckValue");
		long startTime = System.currentTimeMillis();
		int ret=-1;
		try {
			ret = unionApi.UnionStoreKey(fullName, keyValue, checkValue);
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionStoreKey exec time "+(System.currentTimeMillis() - startTime));
		}
		if(0 == ret){
			return HiATCConstants.SUCC;
		}else {
			log.info("UnionStoreKey return ["+ret+"]");
			return HiATCConstants.ERR;
		}
	}
	
	/**
	 * (541)产生摘要3C指令 
	 * @param argsMap
	 * arithmeticMode:算法标识
	 * data:数据
	 * DstFld 签名结果在ETF树上的节点名 默认DigesteInfo
	 * @param ctx
	 * @return
	 * @throws HiException
	 */
	public int UnionGenerateDigest(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String arithmeticMode = HiArgUtils.getStringNotNull(argsMap, "ArithmeticMode");
		String data = HiArgUtils.getStringNotNull(argsMap, "Data");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "DigesteInfo";
		}
		long startTime = System.currentTimeMillis();
		String digesteInfo= null;
		try {
			digesteInfo = unionApi.UnionGenerateDigest(arithmeticMode, data);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGenerateDigest exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, digesteInfo);
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * (542)生成随机数指令R1 
	 * @param argsMap
	 * randomDataLength:生成随机数的长度
	 * DstFld 签名结果在ETF树上的节点名 默认Random
	 * @param ctx
	 * @return
	 * @throws HiException
	 */
	public int UnionGenerateRandom(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String randomDataLength = HiArgUtils.getStringNotNull(argsMap, "RandomDataLength");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Random";
		}
		long startTime = System.currentTimeMillis();
		String random= null;
		try {
			random = unionApi.UnionGenerateRandom(Integer.parseInt(randomDataLength));
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGenerateRandom exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, random);
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * 使用指定keyID的交易主密钥校验ARQC，生成ARPC（526）
	 * @param argsMap
	 * fullName：交易主密钥的全名。
	 * 	keyVersion: 密钥版本
	 * 	pan：卡号，使用交易主密钥对卡号进行分散，得到IC卡的交易密钥
	 * 	processGene：过程因子，参与生成过程密钥，如果是应用交易计数器ATC，则为4字节。
	 * 	data：计算ARQC使用的数据。
	 * 	ARQC：授权请求密文，参与生成ARPC，16字节。
	 * 	ARC：授权响应码，参与生成ARPC，4字节。
	 * 	iccType：IC卡类型。取值如下：0，PBOC2.0规范IC卡。1，VISA cvn17（cvn10）规范IC卡。
	 * DstFld 签名结果在ETF树上的节点名 默认ARPC
	 * @param ctx
	 * @return 生成的授权响应密文(ARPC)，16字节。
	 * @throws HiException
	 */
	public int UnionGenerateARPCUsingDerivedKey(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullName = HiArgUtils.getStringNotNull(argsMap, "FullName");
		String keyVersion = HiArgUtils.getStringNotNull(argsMap, "KeyVersion");
		String pan = HiArgUtils.getStringNotNull(argsMap, "Pan");
		String processGene = HiArgUtils.getStringNotNull(argsMap, "ProcessGene");
		String data = HiArgUtils.getStringNotNull(argsMap, "Data");
		String ARQC = HiArgUtils.getStringNotNull(argsMap, "ARQC");
		String ARC = HiArgUtils.getStringNotNull(argsMap, "ARC");
		String iccType = HiArgUtils.getStringNotNull(argsMap, "IccType");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "ARPC";
		}
		long startTime = System.currentTimeMillis();
		String ARPC= null;
		try {
			ARPC = unionApi.UnionGenerateARPCUsingDerivedKey(fullName, Integer.parseInt(keyVersion), pan, processGene, data, ARQC, ARC, Integer.parseInt(iccType));
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGenerateARPCUsingDerivedKey exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, ARPC);
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * 随机产生一个PIN 401 
	 * @param argsMap
	 * pinLen,PIN长度
	 * accNo：账号/卡号,长度13-19字节字符串(ASCII), 以'\0'结束;卡号为实际卡号;账号和卡号填16个ASCII'0',表示无账号运算。
	 * DstFld 签名结果在ETF树上的节点名 默认PinByLmk
	 * @param ctx
	 * @return pinByLmk,LMK0203加密的密文。
	 * @throws HiException
	 */
	public int UnionGeneratePinRandomly(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String pinLen = HiArgUtils.getStringNotNull(argsMap, "PinLen");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinByLmk";
		}
		long startTime = System.currentTimeMillis();
		String pinByLmk= null;
		try {
			pinByLmk = unionApi.UnionGeneratePinRandomly(Integer.parseInt(pinLen), accNo);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGeneratePinRandomly exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinByLmk);
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * 由ZPK加密的PIN导出一个PIN的Offset 404 
	 * 从一个ZPK加密的PIN，导出一个由PVK加密生成的PinOffset。ZPK加密PIN采用的加密标准为ANSIX9.8。用IBM方式产生一个PIN的PIN Offset。
	 * @param argsMap
	 * zpkFullName,ZPK密钥名称
	 * pvkFullName,PVK密钥名称
	 * pinBlock，ZPK加密的PIN
	 * accNo,账号
	 * DstFld 签名结果在ETF树上的节点名 默认PinOffset
	 * @param ctx
	 * @return pinOffset
	 * @throws HiException
	 */
	public int UnionDerivePinOffsetFromPinByZpk(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String zpkFullName = HiArgUtils.getStringNotNull(argsMap, "ZpkFullName");
		String pvkFullName = HiArgUtils.getStringNotNull(argsMap, "PvkFullName");
		String pinBlock = HiArgUtils.getStringNotNull(argsMap, "PinBlock");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinOffset";
		}
		long startTime = System.currentTimeMillis();
		String pinOffset= null;
		try {
			pinOffset = unionApi.UnionDerivePinOffsetFromPinByZpk(zpkFullName, pvkFullName, pinBlock, accNo);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionDerivePinOffsetFromPinByZpk exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinOffset);
		return HiATCConstants.SUCC;
	}	
	
	
	/**
	 * (812)将由ZPK１加密的PIN转换成ZPK２/PVK加密 (支持密文) 
	 * @param argsMap
	 * srcZpkKeyName:源ZPK密钥名称
	 * srcAcco:原账号或卡号或手机号
	 * objZpkName:目标ZPK/PVK密钥名称
	 * objAcco:目标账号或卡号或手机号
	 * pinCiphertext:经源ZPK加密的PIN数据密文
	 * DstFld 签名结果在ETF树上的节点名 默认PinCiphertextByZpk
	 * @param ctx
	 * @return pinCiphertextByZpk:ZPK/PVK加密下的PIN密文
	 * @throws HiException
	 */
	public int UnionChangezpk1EncryptPinToZpk2OrPvkEncryptSupportCiphertext(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String srcZpkKeyName = HiArgUtils.getStringNotNull(argsMap, "SrcZpkKeyName");
		String srcAcco = HiArgUtils.getStringNotNull(argsMap, "SrcAcco");
		String objZpkName = HiArgUtils.getStringNotNull(argsMap, "ObjZpkName");
		String objAcco = HiArgUtils.getStringNotNull(argsMap, "ObjAcco");
		String pinCiphertext = HiArgUtils.getStringNotNull(argsMap, "PinCiphertext");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinCiphertextByZpk";
		}
		long startTime = System.currentTimeMillis();
		String pinCiphertextByZpk= null;
		try {
			pinCiphertextByZpk = unionApi.UnionChangezpk1EncryptPinToZpk2OrPvkEncryptSupportCiphertext(srcZpkKeyName, srcAcco, objZpkName, objAcco, pinCiphertext);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionChangezpk1EncryptPinToZpk2OrPvkEncryptSupportCiphertext exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinCiphertextByZpk);
		return HiATCConstants.SUCC;
	}	
	
	
	/**
	 * 使用指定的密钥加密的PIN明文 433 UnionEncryptPin 
	 * 使用指定的密钥加密的PIN明文，算法为ansi x9.8/x9.18，这个函数,将用户的PIN明文加密成PIN密文。
	 * @param argsMap
	 * fullName: 节点名称
	 * clearPin：PIN明文,长度6字节字符串(ASCII), 以'\0'结束。
	 * accNo：账号/卡号,长度13-19字节字符串(ASCII), 以'\0'结束;卡号为实际卡号;账号和卡号填16个ASCII'0',表示无账号运算。
	 * 返回值：pinBlock
	 * pinBlock：上送中心加密的PIN密文(PIN格式为ANSI9.8), 长度16字节字符串(ASCII), 以'\0'结束
	 * DstFld 签名结果在ETF树上的节点名 默认PinBlock
	 * @param ctx
	 * @return PinBlock:上送中心加密的PIN密文(PIN格式为ANSI9.8), 长度16字节字符串(ASCII), 以'\0'结束
	 * @throws HiException
	 */
	public int UnionEncryptPin(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullName = HiArgUtils.getStringNotNull(argsMap, "FullName");
		String clearPin = HiArgUtils.getStringNotNull(argsMap, "ClearPin");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinBlock";
		}
		long startTime = System.currentTimeMillis();
		String pinBlock= null;
		try {
			pinBlock = unionApi.UnionEncryptPin(fullName, clearPin, accNo);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionEncryptPin exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinBlock);
		return HiATCConstants.SUCC;
	}	
	
	
	/**
	 * 用指定的一把edk加密数据(703)
	 * @param argsMap
	 * fullName： 加密密钥全名 
	 * clearData： 明文数据 
	 * clearDataLen: 数据长度
	 * arithmeticFlag： 算法标识：“0”，ECB，“1”，CBC；若不需要，填“NULL” 
	 * initIV： 初始化向量，若不需要，填“NULL”
	 * DstFld 签名结果在ETF树上的节点名 默认EncryptDate
	 * @param ctx
	 * @return EncryptDate:加密后的数据（扩展后的）。
	 * @throws HiException
	 */
	public int UnionEncryptData(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullName = HiArgUtils.getStringNotNull(argsMap, "FullName");
		String clearData = HiArgUtils.getStringNotNull(argsMap, "ClearData");
		String clearDataLen = HiArgUtils.getStringNotNull(argsMap, "ClearDataLen");
		String arithmeticFlag = HiArgUtils.getStringNotNull(argsMap, "ArithmeticFlag");
		String initIV = HiArgUtils.getStringNotNull(argsMap, "InitIV");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "EncryptDate";
		}
		long startTime = System.currentTimeMillis();
		String encryptDate= null;
		try {
			encryptDate = unionApi.UnionEncryptData(fullName, Integer.parseInt(clearDataLen), clearData, initIV, arithmeticFlag);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionEncryptData exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, encryptDate);
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * 用指定的一把edk解密数据(704)
	 * @param argsMap
	 * fullName： 加密密钥全名 
	 * cryptographLen：密文数据长度
	 * cryptograph： 密文数据 
	 * arithmeticFlag： 算法标识：“0”，ECB，“1”，CBC；若不需要，填“NULL” 
	 * initIV： 初始化向量，若不需要，填“NULL”
	 * DstFld 签名结果在ETF树上的节点名 默认EncryptDate
	 * @param ctx
	 * @return DecryptData:加密后的数据（扩展后的）。
	 * @throws HiException
	 */
	public int UnionDecryptData(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		//Logger log = HiLog.getLogger(msg);
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullName = HiArgUtils.getStringNotNull(argsMap, "FullName");
		String cryptograph = HiArgUtils.getStringNotNull(argsMap, "Cryptograph");
		String cryptographLen = HiArgUtils.getStringNotNull(argsMap, "CryptographLen");
		String arithmeticFlag = HiArgUtils.getStringNotNull(argsMap, "ArithmeticFlag");
		String initIV = HiArgUtils.getStringNotNull(argsMap, "InitIV");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "DecryptData";
		}
		long startTime = System.currentTimeMillis();
		String decryptData= null;
		try {
			decryptData = unionApi.UnionDecryptData(fullName, Integer.parseInt(cryptographLen), cryptograph, initIV, arithmeticFlag);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionDecryptData exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, decryptData);
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * (819)指令UB：计算及校验MAC/TAC
	 * @param argsMap
	 * modeTag:模式标识
	 * schemeID:方案ID
	 * keyType:根密钥类型
	 * keyName:根密钥名
	 * disperseTime:离散次数
	 * disperseData:离散数据
	 * dataFillFlag:MAC数据填充标识
	 * Iv:IV-MAC
	 * processData:过程数据  (当方案为1,3时有该域)
	 * macData:MAC计算数据
	 * macLengthFlag:MAC长度标识
	 * waitVerifyMacData:待校验的mac  (当模式为2时有该域)
	 * DstFld 签名结果在ETF树上的节点名 默认MAC
	 * @param ctx
	 * @return 成功返回Mac (当模式为2时没有返回域) 失败返回错误码,小于0
	 * @throws HiException
	 */
	public int UnionGeneratOrVerifyMac(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String modeTag = HiArgUtils.getStringNotNull(argsMap, "ModeTag");
		String schemeID = HiArgUtils.getStringNotNull(argsMap, "SchemeID");
		String keyType = HiArgUtils.getStringNotNull(argsMap, "KeyType");
		String keyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String disperseTime = HiArgUtils.getStringNotNull(argsMap, "DisperseTime");
		String disperseData = HiArgUtils.getStringNotNull(argsMap, "DisperseData");
		String dataFillFlag = HiArgUtils.getStringNotNull(argsMap, "DataFillFlag");
		String iv = HiArgUtils.getStringNotNull(argsMap, "Iv");
		String processData = null;
		if(StringUtils.equals(schemeID, "1")||StringUtils.equals(schemeID, "3")){
			processData = HiArgUtils.getStringNotNull(argsMap, "ProcessData");
		}
		String macData = HiArgUtils.getStringNotNull(argsMap, "MacData");
		String macLengthFlag = HiArgUtils.getStringNotNull(argsMap, "MacLengthFlag");
		String waitVerifyMacData = null;
		if(StringUtils.equals(modeTag, "2")){
			waitVerifyMacData = HiArgUtils.getStringNotNull(argsMap, "WaitVerifyMacData");
		}
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "MAC";
		}
		long startTime = System.currentTimeMillis();
		String mac= null;
		try {
			mac = unionApi.UnionGeneratOrVerifyMac(modeTag, schemeID, keyType, keyName, Integer.parseInt(disperseTime), disperseData, Integer.parseInt(dataFillFlag), iv, processData, macData, Integer.parseInt(macLengthFlag), waitVerifyMacData);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGeneratOrVerifyMac exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, mac);
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * (820)离散子密钥并用此密钥转加密PIN 98+CC
	 * @param argsMap
	 * bankId:银行ID  
	 * zmkKeyName:ZMK密钥名  
	 * zpkKeyName:ZPK密钥名  		
	 * accNo：账号 						
	 * encryptedKey：源密码密文  
	 * DstFld 签名结果在ETF树上的节点名 默认Pinblock 
	 * @param ctx
	 * @return 成功返回新pinblock 失败返回错误码,小于0
	 * @throws HiException
	 */
	public int UnionDiscreteSubKeyToPinKey(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String bankId = HiArgUtils.getStringNotNull(argsMap, "BankId");
		String zmkKeyName = HiArgUtils.getStringNotNull(argsMap, "ZmkKeyName");
		String zpkKeyName = HiArgUtils.getStringNotNull(argsMap, "ZpkKeyName");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String encryptedKey = HiArgUtils.getStringNotNull(argsMap, "EncryptedKey");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Pinblock";
		}
		long startTime = System.currentTimeMillis();
		String pinblock = null;
		try {
			pinblock  = unionApi.UnionDiscreteSubKeyToPinKey(bankId, zmkKeyName, zpkKeyName, accNo, encryptedKey);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionDiscreteSubKeyToPinKey exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinblock );
		return HiATCConstants.SUCC;
	}	
	/**
	 * 下载公钥值
	 * @param argsMap
	 * 函数名称	UnionGetPKValue
	 * 功能	解密数据
	 * 输入参数	fullName：PK的密钥名称

	 * DstFld 签名结果在ETF树上的节点名 默认Pinblock 
	 * @param ctx
	 * @return 成功返回新pinblock 失败返回错误码,小于0
	 * @throws HiException
	 */
	public int UnionGetPKValue(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String fullName = HiArgUtils.getStringNotNull(argsMap, "FullName");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PKValue";
		}
		long startTime = System.currentTimeMillis();
		String pkValue = null;
		try {
			pkValue  = unionApi.UnionGetPKValue(fullName);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGetPKValue exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pkValue );
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * 将一个ZPK加密的PIN转换为由LMK加密 306
	 * @param argsMap
	 * 功能：将一个ZPK加密的PIN转换为由LMK加密
		从一个ZPK加密的PIN，导出一个由PVK加密生成的PVV。
		ZPK加密PIN采用的加密标准为ANSIX9.8。
		输入参数：
			zpkFullName,ZPK节点名称
			pinBlock，ZPK加密的PIN
			accNo,账号
		返回值：
			pinByLmk,LMK0203加密的PIN

	 * DstFld 签名结果在ETF树上的节点名 默认PinByLmk 
	 * @param ctx
	 * @return 成功返回新PinByLmk 失败返回错误码,小于0
	 * @throws HiException
	 */
	public int UnionDerivePinByLmkFromPinByZpk(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String zpkFullName = HiArgUtils.getStringNotNull(argsMap, "ZpkFullName");
		String pinBlock = HiArgUtils.getStringNotNull(argsMap, "PinBlock");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "PinByLmk";
		}
		long startTime = System.currentTimeMillis();
		String pinByLmk = null;
		try {
			pinByLmk  = unionApi.UnionDerivePinByLmkFromPinByZpk(zpkFullName, pinBlock, accNo);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionDerivePinByLmkFromPinByZpk exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinByLmk );
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * (821)由lmk加密的PIN导出一个pinoffset
	 * @param argsMap
	 * 功能	由lmk加密的PIN导出一个pinoffset
		输入参数	keyName:PVK密钥名  
		pinBlock:lmk加密的PIN    		
		accNo：账号 		
		输出参数	无	
		返回值	成功返回pinoffset
		失败返回错误码,小于0
		服务代码	821
	 * DstFld 签名结果在ETF树上的节点名 默认Pinoffset 
	 * @param ctx
	 * @return 成功返回新Pinoffset 失败返回错误码,小于0
	 * @throws HiException
	 */
	public int UnionGeneratePin(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionAPI unionApi = getUnionAPI(argsMap,ctx);
		String keyName = HiArgUtils.getStringNotNull(argsMap, "KeyName");
		String pinBlock = HiArgUtils.getStringNotNull(argsMap, "PinBlock");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String destFld = argsMap.get("DstFld");
		if(StringUtils.isBlank(destFld)){
			destFld = "Pinoffset";
		}
		long startTime = System.currentTimeMillis();
		String pinoffset = null;
		try {
			pinoffset  = unionApi.UnionGeneratePin(keyName, pinBlock, accNo);
		} catch (NumberFormatException e) {
			throw new HiException(e); 
		} catch (Exception e) {
			throw new HiException(e);
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionGeneratePin exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, pinoffset );
		return HiATCConstants.SUCC;
	}	
	
	/**
	 * UnionPIN 整合登录及支付密码 验证修改初始化6个交易
	 * @param argsMap
	 * @param ctx
	 * @param pinType        处理类型  L0：登录密码验证; L1:登录密码修改; L2:登录密码初始化; 
	 *                               P0: 支付密码验证; P1:支付密码修改; P2:支付密码初始化 ;
	 *                               T0:柜员密码验证;T1:柜员密码修改;T2:柜员密码初始化  ;
	 *                               C0:验证POS机支付密码 ;                                            
	 * @param PINLength      默认为6
	 * @param pinBlockByZPK  PIN密文 
	 * @param accNo          离散因子16位 
	 * @param zpk            渠道号/省平台编号
	 * @param pvk            密钥索引 默认Host.pinoffset.pvk
	 * @param pinOffset      消息鉴别码
	 * @return 0 - 成功， 其他为操作失败
	 * @return pinOffset  当处理类型是1,2时,返回pinOffset
	 * @throws HiException
	 */
	public int UnionPin(HiATLParam argsMap, HiMessageContext ctx)
			throws HiException {
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		String pinType = HiArgUtils.getStringNotNull(argsMap, "PinType");	
		
		String pinBlockByZPK = argsMap.get("PinBlockByZPK");
		String pinOffset = argsMap.get("PinOffset");
		
		int pinLength=0;
		String sPinLength = argsMap.get("PinLength");
		if (StringUtils.isBlank(sPinLength)) {
			pinLength = 6 ;
		}else{
			pinLength = Integer.parseInt(sPinLength);
		}
		
		String dstfld =argsMap.get("DstFld");
		String ecp_dstfld =argsMap.get("EcpDstFld");
		
		String zpkDefSelf = argsMap.get("ZpkDefSelf");
		
		String zpk = argsMap.get("Zpk");
//		STK渠道上送的交易直接进行密码比较和密码修改不调用加密机
		if (zpk.equals("STK")){
			if (pinType.equals("P0")){
				if (pinBlockByZPK.equals(pinOffset)){
				   return HiATCConstants.SUCC;
			    }
				else{
					log.info("password is not correct");
					return HiATCConstants.ERR;	
				}
			}else if(pinType.equals("P1")){
				ctx.getCurrentMsg().getETFBody().setGrandChildNode(dstfld,pinBlockByZPK);
				return HiATCConstants.SUCC;
			}else{
				log.info("continue");
			}
		}
		
		
		if (pinType.equals("L2")||pinType.equals("P2")||pinType.equals("T2")) {
			//zpk ="262";
			zpk ="Host.exchange.zpk";
			log.info("zpk :"+zpk);
		}else if(pinType.equals("C0")){
			if(!"Y".equals(zpkDefSelf)){//非自定义
				zpk = "Posp."+zpk+".zpk";
			}
			log.info("zpk :"+zpk);		
		}else {
			//设置渠道号与ZPK对应关系
			//String [] zpkCmp ={"260","259","261","258","258","262","273","261","260","260","273","273"};
			String [] zpkCmp ={"HX.ywwebsite.zpk","HX.iPOS.zpk","Host.IVRQZ.zpk","HX.UI.zpk","HX.UI.zpk","Host.exchange.zpk","HX.mobilepay.zpk","Host.IVRQZ.zpk","HX.ywwebsite.zpk","HX.ywwebsite.zpk","HX.mobilepay.zpk","HX.mobilepay.zpk","HX.mobilepay.zpk"};
			String [] strCmp ={"CMPAY","IPOS","CALL","UI","TLR","SMS","MCLIT","ATM","PK200","WWW","WAP","WIPOS","CAS"};
					
			for (int i = 0; i < strCmp.length; ++i) {
				if (zpk.equals(strCmp[i])) {
					zpk = zpkCmp[i];
					log.info("zpk:[" + zpk + "]  and strcmp[" + i + "]:["
							+ strCmp[i] + "]");
					break;
				} else if (i == strCmp.length - 1) {
					log.info("channel:[" + zpk + "]is not exist");
					return HiATCConstants.ERR;
				}
			}
		   
		}
		
		String pvk = argsMap.get("Pvk");
		if (StringUtils.isBlank(pvk)) {
			//pvk ="264";
			 pvk ="Host.pinoffset.pvk";
		}
		
		String objAcco = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		if(pinType.equals("T0")||pinType.equals("T1")||pinType.equals("T2")){
			
			objAcco = accNoConvert(objAcco);
			
		}
		long startTime = System.currentTimeMillis();
		try{
			UnionAPI unionApi = getUnionAPI(argsMap,ctx);
			if (pinType.equals("L0")||pinType.equals("T0")){
				
		    	String srcAcco = "0000000000000000";
			    String pinCiphertextByZpk = unionApi.UnionChangezpk1EncryptPinToZpk2OrPvkEncryptSupportCiphertext(zpk, srcAcco, pvk, objAcco, pinBlockByZPK);
			    if(!StringUtils.equals(pinCiphertextByZpk, pinOffset)){
			    	 throw new HiException(HiMessageCode.DEFAULT_ERR_CODE, HiATCConstants.MSG_CD); 
			    }
			    
		    }else if(pinType.equals("C0")){
		    	
		    	String srcAcco = "0000000000000000";
		    	String objZpkName = "HX.iPOS.zpk";
		    	String pinBlock2 = unionApi.UnionTranslatePin(zpk, objZpkName, pinBlockByZPK, srcAcco);
		    	String pinByLmk = unionApi.UnionDerivePinByLmkFromPinByZpk(objZpkName, pinBlock2, srcAcco);
		    	String pinOffset2 = unionApi.UnionGeneratePin(pvk, pinByLmk, objAcco);
		    	if(!StringUtils.equals(pinOffset2, pinOffset)){
			    	 throw new HiException(HiMessageCode.DEFAULT_ERR_CODE, HiATCConstants.MSG_CD); 
			    }
		    	
		    }else if(pinType.equals("L1")||pinType.equals("T1")){
		    	
				String srcAcco = "0000000000000000";
				String pinByPvk = unionApi.UnionChangezpk1EncryptPinToZpk2OrPvkEncryptSupportCiphertext(zpk, srcAcco, pvk, objAcco, pinBlockByZPK);
				ctx.getCurrentMsg().getETFBody().setGrandChildNode(dstfld, pinByPvk);
				
			}else if(pinType.equals("L2")||pinType.equals("T2")){
	
				String[] cipher = unionApi.UnionGeneratePinOffsetAndPinBlockSupportCiphertext(Integer.valueOf(pinLength).toString(), pvk, zpk, objAcco);
				String strPinByPVK = cipher[0];
				log.info("login password is :["+strPinByPVK+"]");
				String strpinBlock = unionApi.UnionChangezpk1EncryptPinToZpk2OrPvkEncryptSupportCiphertext(zpk, objAcco, zpk, "0000000000000000", cipher[1]);
				log.info("login password for ecap is :["+strpinBlock+"]");
				ctx.getCurrentMsg().getETFBody().setGrandChildNode(dstfld, strPinByPVK);
				ctx.getCurrentMsg().getETFBody().setGrandChildNode(ecp_dstfld, strpinBlock);
				
			}else if(pinType.equals("P0")){
	
				String pinByLmk = unionApi.UnionDerivePinByLmkFromPinByZpk(zpk, pinBlockByZPK, "0000000000000000");
				String pinOffset1 = unionApi.UnionGeneratePin(pvk, pinByLmk, objAcco);
				if(!StringUtils.equals(pinOffset1, pinOffset)){
			    	 throw new HiException(HiMessageCode.DEFAULT_ERR_CODE, HiATCConstants.MSG_CD); 
			    }
				
			}else if(pinType.equals("P1")){

				String pinByLmk = unionApi.UnionDerivePinByLmkFromPinByZpk(zpk, pinBlockByZPK, "0000000000000000");
				String pinOffset1 = unionApi.UnionGeneratePin(pvk, pinByLmk, objAcco);
				log.info("payment password has been modified to :["+pinOffset1+"]");
				ctx.getCurrentMsg().getETFBody().setGrandChildNode(dstfld,pinOffset1);
				
			}else if(pinType.equals("P2")){

				String srcAcco = "0000000000000000";
				String pinByLmk = unionApi.UnionGeneratePinRandomly(pinLength, objAcco);
				String pinBlock = unionApi.UnionDerivePinBlockFromPinByLmk(srcAcco, pinByLmk, zpk);
				String pinOffset1 = unionApi.UnionGeneratePin(pvk, pinByLmk, objAcco);
				
				log.info("payment password is :["+pinOffset1+"]");
				log.info("payment password for ecap is :["+pinBlock+"]");
				ctx.getCurrentMsg().getETFBody().setGrandChildNode(dstfld, pinOffset1);
				ctx.getCurrentMsg().getETFBody().setGrandChildNode(ecp_dstfld, pinBlock);
	
			}else{
				log.error("pinType:["+pinType+"] is not exist");
				throw new HiException(HiMessageCode.DEFAULT_ERR_CODE,"pinType:["+pinType+"] is not exist");
			}
			if(unionEssclog.isDebugEnabled()){
				unionEssclog.debug("UnionPin exec time "+(System.currentTimeMillis() - startTime));
			}
			return HiATCConstants.SUCC;
			
		}catch(Exception e){
			throw new HiException(e);
		}
		
	}
	
	/**
	 * UnionTransPinOffset 转加密生成pinoffset
	 * @param argsMap
	 * @param ctx
	 * @param pinBlockByZPK  PIN密文 
	 * @param SrcAccNo          离散因子16位 
	 * @param ObjAccNo          离散因子16位 
	 * @param zpk            渠道号/省平台编号
	 * @param pvk            密钥索引 默认Host.pinoffset.pvk
	 * @return 0 - 成功， 其他为操作失败
	 * @return pinOffset  当处理类型是1,2时,返回pinOffset
	 * @throws HiException
	 */
	public int UnionTransPinOffset(HiATLParam argsMap, HiMessageContext ctx)
			throws HiException {
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());

		String pinBlockByZPK = argsMap.get("PinBlockByZPK");

		String dstfld = HiArgUtils.getStringNotNull(argsMap, "DstFld");

		String zpk = HiArgUtils.getStringNotNull(argsMap, "Zpk");

		String pvk = argsMap.get("Pvk");
		if (StringUtils.isBlank(pvk)) {
			pvk = "Host.pinoffset.pvk";
		}
		String srcAcco = HiArgUtils.getStringNotNull(argsMap, "SrcAccNo");
		String objAcco = HiArgUtils.getStringNotNull(argsMap, "ObjAccNo");
		long startTime = System.currentTimeMillis();
		try {
			UnionAPI unionApi = getUnionAPI(argsMap, ctx);

			String pinByLmk = unionApi.UnionDerivePinByLmkFromPinByZpk(zpk,
					pinBlockByZPK, srcAcco);
			String pinOffset1 = unionApi.UnionGeneratePin(pvk, pinByLmk,
					objAcco);
			log.info("payment password has been modified to :[" + pinOffset1+ "]");
			ctx.getCurrentMsg().getETFBody()
					.setGrandChildNode(dstfld, pinOffset1);
			if(unionEssclog.isDebugEnabled()){
				unionEssclog.debug("UnionTransPinOffset exec time "+(System.currentTimeMillis() - startTime));
			}
			return HiATCConstants.SUCC;

		} catch (Exception e) {
			throw new HiException(e);
		}

	}
	
	/**
	 * 功能：申请随机Pin
		输入参数：
		appID				应用ID，关联私钥位置
		desEncByPK			被公钥加密的随机desKey
		accNo				用户帐号
		pinoffset				若已存在Pin则传pin，若为新申请，则为null
		pvkName			pvk密钥名称
		返回值：
		返回null则为错码返回
		若已存在Pin则返回数组中只包含一个PinBlock，若为新申请则返回数组中包含PinBlock及Pinoffset
	 * @param argsMap
	 * @param ctx
	 * @return
	 * @throws HiException
	 */
	public int UnionGetDynamicPin(HiATLParam argsMap, HiMessageContext ctx)
			throws HiException {
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		
		String appID = HiArgUtils.getStringNotNull(argsMap, "AppID");
		String desEncByPK = HiArgUtils.getStringNotNull(argsMap, "DesEncByPK");
		String accNo = HiArgUtils.getStringNotNull(argsMap, "AccNo");
		String zpkName = HiArgUtils.getStringNotNull(argsMap, "ZpkName");
		String pinBlock = argsMap.get("PinBlock");
		
		String dstfldPinBlock = argsMap.get("DstFldPinBlock");
		if(StringUtils.isEmpty(dstfldPinBlock)){
			dstfldPinBlock = "DstPinBlock";
		}
		//String dstfldPinoffset = argsMap.get("DstFldPinoffset");
		
		long startTime = System.currentTimeMillis();
		try {
			UnionAPI unionApi = getUnionAPI(argsMap, ctx);
			
			String[] dynamicPin = unionApi.UnionGetDynamicPin(appID, desEncByPK, accNo, pinBlock, zpkName);
			if(dynamicPin == null || dynamicPin.length ==0){
				if(log.isErrorEnabled()){
					log.error("UnionGetDynamicPin return null");
				}
				throw new Exception("UnionGetDynamicPin return null");
			}
			HiETF etf = ctx.getCurrentMsg().getETFBody();
			//if(pinBlock == null){
				etf.setGrandChildNode(dstfldPinBlock, dynamicPin[0]);
				if(log.isInfoEnabled()){
					log.info("PinBlock ["+dynamicPin[0]+"]");
				}
			/*}else{
				etf.setGrandChildNode(dstfldPinBlock, dynamicPin[0]);
				etf.setGrandChildNode(dstfldPinoffset, dynamicPin[1]);
				if(log.isInfoEnabled()){
					log.info("PinBlock ["+dynamicPin[0]+"],Pinoffset["+dynamicPin[1]+"]");
				}
			}*/
				if(unionEssclog.isDebugEnabled()){
					unionEssclog.debug("UnionGetDynamicPin exec time "+(System.currentTimeMillis() - startTime));
				}
			return HiATCConstants.SUCC;
			
		} catch (Exception e) {
			throw new HiException(e);
		}
		
	}
	
	/**
	 * 离散因子格式化
	 * @param  柜员号
	 * @return 转化为16进制的ASC码值，如果其中某个字符出现数字，那么就用这个字母的 
	 */
	public static String accNoConvert(String accNo)
	{   
		String  accNoConvert = "";
		byte [] bAccNo = accNo.toUpperCase().getBytes();
		
		for (int i = 0; i < accNo.length(); i++) { 
			String hex = Integer.toHexString(bAccNo[i]&0xFF); 
			if (hex.length() == 1) { 
			hex = '0' + hex;
			}
            if(hex.equals("4a")){
            	hex = "10";
            }
            else if(hex.equals("4b")){
            	hex ="11";
            }
            else if(hex.equals("4c")){
            	hex ="12";
            }
            else if(hex.equals("4d")){
            	hex ="13";
            }
            else if(hex.equals("4e")){
            	hex ="14";
            }
            else if(hex.equals("4f")){
            	hex ="15";
            }
            else if(hex.equals("5a")){
            	hex ="26";
            }
			
			accNoConvert = accNoConvert + hex;
		}
		return accNoConvert;
	}
	
	
	public UnionAPI getUnionAPI(String ip,String port,String timeout,String app) throws HiException{
		if(StringUtils.isBlank(ip)||StringUtils.isBlank(port)||StringUtils.isBlank(timeout)||StringUtils.isBlank(app)){
			throw new HiException("parameter ip or port or timeout or app is null");
		}
		return new UnionAPI(ip,Integer.parseInt(port),Integer.parseInt(timeout),app);
	}
	
	public UnionAPI getUnionAPI(HiMessageContext context,String app) throws HiException{
		String ip = (String) context.getPara("_ESSC_IP");
		String port = (String) context.getPara("_ESSC_PORT");
		String timeout = (String) context.getPara("_ESSC_TIMEOUT");
		return getUnionAPI(ip,port,timeout,app);
	}
	
	public UnionAPI getUnionAPI(HiMessageContext context) throws HiException{
		String ip = (String) context.getPara("_ESSC_IP");
		String port = (String) context.getPara("_ESSC_PORT");
		String timeout = (String) context.getPara("_ESSC_TIMEOUT");
		String app = (String) context.getPara("_ESSC_APP");
		return getUnionAPI(ip,port,timeout,app);
	}
	
	public UnionAPI getUnionAPI(HiATLParam argsMap,HiMessageContext context) throws HiException{
		String app = argsMap.get("App");
		if(StringUtils.isBlank(app)){
			return getUnionAPI(context);
		}else{
			return getUnionAPI(context,app);
		}
	}
	
	public UnionMobile getUnionMobile(String ip,String port,String timeout,String app) throws HiException{
		if(StringUtils.isBlank(ip)||StringUtils.isBlank(port)||StringUtils.isBlank(timeout)||StringUtils.isBlank(app)){
			throw new HiException("parameter ip or port or timeout or app is null");
		}
		return new UnionMobile(ip,Integer.parseInt(port),Integer.parseInt(timeout),app);
	}
	
	public UnionMobile getUnionMobile(HiMessageContext context,String app) throws HiException{
		String ip = (String) context.getPara("_ESSC_IP");
		String port = (String) context.getPara("_ESSC_PORT");
		String timeout = (String) context.getPara("_ESSC_TIMEOUT");
		return getUnionMobile(ip,port,timeout,app);
	}
	
	public UnionMobile getUnionMobile(HiMessageContext context) throws HiException{
		String ip = (String) context.getPara("_ESSC_IP");
		String port = (String) context.getPara("_ESSC_PORT");
		String timeout = (String) context.getPara("_ESSC_TIMEOUT");
		String app = (String) context.getPara("_ESSC_APP");
		return getUnionMobile(ip,port,timeout,app);
	}
	
	public UnionMobile getUnionMobile(HiATLParam argsMap,HiMessageContext context) throws HiException{
		String app = argsMap.get("App");
		if(StringUtils.isBlank(app)){
			return getUnionMobile(context);
		}else{
			return getUnionMobile(context,app);
		}
	}
	
	
	public int UnionPhoneChangePayPinPK(HiATLParam argsMap, HiMessageContext ctx) throws HiException {
		HiMessage msg = ctx.getCurrentMsg();
		HiETF etfRoot = msg.getETFBody();
		
		UnionMobile unionMobile = getUnionMobile(argsMap,ctx);
		String cipherPayPinBuf = HiArgUtils.getStringNotNull(argsMap, "CipherPayPinBuf"); 		// 公钥加密的支付密码
		String phoneNum = HiArgUtils.getStringNotNull(argsMap, "PhoneNum"); 		// 手机号
		String vkIndex = HiArgUtils.getStringNotNull(argsMap, "VkIndex"); 			// 私钥索引
		String destFld = HiArgUtils.getStringNotNull(argsMap, "DstFld"); 			// 获取用于存放结果的ETF节点名
		if(StringUtils.isBlank(destFld)){
			destFld = "Value";
		}
		long startTime = System.currentTimeMillis();
		String value = null;
		try {
			value = unionMobile.UnionPhoneChangePayPinPK(cipherPayPinBuf, phoneNum, vkIndex);
		} catch (Exception e1) {
			throw new HiException(e1); 
		}
		if(unionEssclog.isDebugEnabled()){
			unionEssclog.debug("UnionNewVerifySignature exec time "+(System.currentTimeMillis() - startTime));
		}
		etfRoot.setChildValue(destFld, value );
		
		return HiATCConstants.SUCC;
	}	
}
