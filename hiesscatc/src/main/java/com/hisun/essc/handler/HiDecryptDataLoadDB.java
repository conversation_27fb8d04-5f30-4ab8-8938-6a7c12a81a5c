package com.hisun.essc.handler;

import UnionTech.JavaEsscAPI.UnionAPI;
import com.hisun.constants.HiMessageCode;
import com.hisun.exception.HiException;
import com.hisun.ics.datafmt.json.HiJSONObject;
import com.hisun.ics.param.info.HiAbstractHandler;
import com.hisun.ics.param.info.HiDBUtil;
import com.hisun.message.HiETF;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;

/**
 * 功能 装载数据库表记录,对decrypts字段解密
 *
 * <AUTHOR>
 */
public class HiDecryptDataLoadDB extends HiAbstractHandler {

    /**
     * SQL语句
     */
    protected String sqlCmd;
    /**
     * 数据源名称（为JNDI名称）
     */
    protected String dsNam;
    /**
     * 主键名称
     */
    protected String keyNam;
    /**
     * fields
     */
    protected String[] params;

    /**
     * 需要解密的fields
     */
    protected String decrypts;

    //essc 平台配置
    protected String ip;
    protected String port;
    protected String timeout;
    protected String app;
    /***
     * fullName： 加密密钥全名
     * arithmeticFlag： 算法标识：“0”，ECB，“1”，CBC；若不需要，填“NULL”
     * initIV： 初始化向量，若不需要，填“NULL”
     ***/
    protected String fullName;
    protected String arithmeticFlag;
    protected String initIV;

    private HiDBUtil dbUtil = new HiDBUtil();

    private UnionAPI unionApi = null;

    /*
     * 是否自动加载：YES-按interval间隔自动加载、NO-仅首次加载一次
     */
    private String autoLoad = "NO";

    private boolean isAutoLoad() {
        return "YES".equalsIgnoreCase(autoLoad);
    }

    public void setAutoLoad(String autoLoad) {
        this.autoLoad = autoLoad;
    }

    /**
     * @return the sql
     */
    public String getSqlCmd() {
        return sqlCmd;
    }

    /**
     * @param sql the sql to set
     */
    public void setSqlCmd(String sqlCmd) {
        this.sqlCmd = sqlCmd;
    }

    public void load(HiETF param) throws HiException {
        log.debug("HiDecryptLoadDB init,ip[" + ip + "],port[" + port + "],timeout[" + timeout + "],app[" + app + "],decrypts[" + decrypts + "]");
        unionApi = new UnionAPI(ip, Integer.parseInt(port), Integer.parseInt(timeout), app);
        try {
            if (StringUtils.isBlank(arithmeticFlag)) {
                arithmeticFlag = "0";
            }
            if (StringUtils.isBlank(initIV)) {
                initIV = "NULL";
            }
            dbUtil.setDsName(dsNam);
            List list = dbUtil.execQuery(sqlCmd, params);
            for (int i = 0; i < list.size(); i++) {
                HashMap rec = (HashMap) list.get(i);
                String keyVal;
                if (!rec.containsKey(keyNam)) {
                    throw new HiException(HiMessageCode.ERR_NFND, "keyName:[" + keyNam + "]");
                }
                keyVal = (String) rec.get(keyNam);
                String[] decrptss = StringUtils.splitByWholeSeparator(decrypts, "|");
                if (decrptss != null && decrptss.length > 0) {
                    boolean isError = false;
                    for (String decrypt : decrptss) {
                        if (!rec.containsKey(decrypt)) {
                            throw new HiException(HiMessageCode.ERR_NFND, "decrypt:[" + decrypt + "]");
                        }
                        String cryptograph = String.valueOf(rec.get(decrypt));
                        if (StringUtils.isNotEmpty(cryptograph)) {
                            int cryptographLen = cryptograph.getBytes().length;
                            String result = "";
                            try {
                                result = unionApi.UnionDecryptData(fullName, cryptographLen, cryptograph, initIV, arithmeticFlag);
                                rec.put(decrypt, result);
                            } catch (NumberFormatException e) {
                                //throw new HiException(e);
                                log.error(e);
                                isError = true;
                            } catch (Exception e) {
                                //throw new HiException(e);
                                log.error(e);
                                isError = true;
                            }
                        }
                    }

                    if (isError) {
                        continue;//出现异常时，该条记录不加载到缓存
                    }

                }
                valueObj.put(keyVal, HiJSONObject.fromObject(rec));
                //log.info("key["+keyVal+"],value["+HiJSONObject.fromObject(rec)+"]");
            }
        } finally {
            // 关闭数据库连接，防止应用服务器检查连接泄漏时关闭
            dbUtil.close();
        }
    }

    /**
     * @return the dsName
     */
    public String getDsNam() {
        return dsNam;
    }

    /**
     * @param dsName the dsName to set
     */
    public void setDsNam(String dsNam) {
        this.dsNam = dsNam;
    }

    /**
     * @return the keyNam
     */
    public String getKeyNam() {
        return keyNam;
    }

    /**
     * @return the keyNam
     */
    public void setKeyNam(String keyNam) {
        this.keyNam = keyNam;
    }

    public String[] getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = StringUtils.splitByWholeSeparator(params, "|");
    }

    /**
     * @return the decrypts
     */
    public String getDecrypts() {
        return decrypts;
    }

    /**
     * @param decrypts the decrypts to set
     */
    public void setDecrypts(String decrypts) {
        this.decrypts = decrypts;//StringUtils.splitByWholeSeparator(decrypts, "|");
    }

    /**
     * @return the ip
     */
    public String getIp() {
        return ip;
    }

    /**
     * @param ip the ip to set
     */
    public void setIp(String ip) {
        this.ip = ip;
    }

    /**
     * @return the port
     */
    public String getPort() {
        return port;
    }

    /**
     * @param port the port to set
     */
    public void setPort(String port) {
        this.port = port;
    }

    /**
     * @return the timeout
     */
    public String getTimeout() {
        return timeout;
    }

    /**
     * @param timeout the timeout to set
     */
    public void setTimeout(String timeout) {
        this.timeout = timeout;
    }

    /**
     * @return the app
     */
    public String getApp() {
        return app;
    }

    /**
     * @param app the app to set
     */
    public void setApp(String app) {
        this.app = app;
    }

    /**
     * @return the fullName
     */
    public String getFullName() {
        return fullName;
    }

    /**
     * @param fullName the fullName to set
     */
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    /**
     * @return the arithmeticFlag
     */
    public String getArithmeticFlag() {
        return arithmeticFlag;
    }

    /**
     * @param arithmeticFlag the arithmeticFlag to set
     */
    public void setArithmeticFlag(String arithmeticFlag) {
        this.arithmeticFlag = arithmeticFlag;
    }

    /**
     * @return the initIV
     */
    public String getInitIV() {
        return initIV;
    }

    /**
     * @param initIV the initIV to set
     */
    public void setInitIV(String initIV) {
        this.initIV = initIV;
    }

    @Override
    public boolean isNeedLoad(int count) {
        if (isAutoLoad()) {
            return super.isNeedLoad(count);
        } else {
            return !isSuccess();
        }
    }
}
