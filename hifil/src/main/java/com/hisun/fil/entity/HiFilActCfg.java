package com.hisun.fil.entity;

public class HiFilActCfg {
	private String actCfgNo;
	private String sysCnl;
	private String busCnl;
	private String actNm;
	private String actTyp;
	private String srnNm;
	private String oIp;
	private String oPort;
	private String svrCode;
	private String txnCode;
	private String tmSmp;

	public HiFilActCfg() {
		super();
		// TODO Auto-generated constructor stub
	}

	public String getActCfgNo() {
		return actCfgNo;
	}

	public void setActCfgNo(String actCfgNo) {
		this.actCfgNo = actCfgNo==null?"":actCfgNo;
	}

	public String getSysCnl() {
		return sysCnl;
	}

	public void setSysCnl(String sysCnl) {
		this.sysCnl = sysCnl==null?"":sysCnl;
	}

	public String getBusCnl() {
		return busCnl;
	}

	public void setBusCnl(String busCnl) {
		this.busCnl = busCnl==null?"":busCnl;
	}

	public String getActNm() {
		return actNm;
	}

	public void setActNm(String actNm) {
		this.actNm = actNm==null?"":actNm;
	}

	public String getActTyp() {
		return actTyp;
	}

	public void setActTyp(String actTyp) {
		this.actTyp = actTyp==null?"":actTyp;
	}

	public String getSrnNm() {
		return srnNm;
	}

	public void setSrnNm(String srnNm) {
		this.srnNm = srnNm==null?"":srnNm;
	}

	public String getoIp() {
		return oIp;
	}

	public void setoIp(String oIp) {
		this.oIp = oIp==null?"":oIp;
	}

	public String getoPort() {
		return oPort;
	}

	public void setoPort(String oPort) {
		this.oPort = oPort==null?"":oPort;
	}

	public String getSvrCode() {
		return svrCode;
	}

	public void setSvrCode(String svrCode) {
		this.svrCode = svrCode==null?"":svrCode;
	}

	public String getTxnCode() {
		return txnCode;
	}

	public void setTxnCode(String txnCode) {
		this.txnCode = txnCode==null?"":txnCode;
	}

	public String getTmSmp() {
		return tmSmp;
	}

	public void setTmSmp(String updTm) {
		this.tmSmp = updTm==null?"":updTm;
	}
  
	public String getCnlKey () {
		String actKey = "[SYS_CNL="+ sysCnl +"]" +
						"[BUS_CNL="+ busCnl +"]" ;
		return actKey;
	}
	
	@Override
	public String toString() {
		return "HiFilActCfg [actCfgNo=" + actCfgNo + ", actNm=" + actNm
				+ ", acttyp=" + actTyp + ", busCnl=" + busCnl + ", oIp=" + oIp
				+ ", oPort=" + oPort + ", srnNm=" + srnNm + ", svrCode="
				+ svrCode + ", sysCnl=" + sysCnl + ", txnCode=" + txnCode
				+ ", updTm=" + tmSmp + "]";
	}

	
}
