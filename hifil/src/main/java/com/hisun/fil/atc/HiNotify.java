package com.hisun.fil.atc;

import java.util.Date;

import org.apache.commons.lang.StringUtils;

import com.hisun.atc.HiCallOut;
import com.hisun.exception.HiException;
import com.hisun.fil.HiFILConstants;
import com.hisun.fil.HiFILMessageCode;
import com.hisun.fil.entity.HiFilActCfg;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.ics.param.handler.HiJMSParamHelper;
import com.hisun.message.HiETF;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.util.HiJMSHelper;

public class HiNotify {

	public static HiJMSHelper jmsHelper = null;
	
	/**
	 * @comment     发送通知给服务器(应用/文件)
	 * @param args  ETF
	 * @param ctx   平台消息
	 * @return FIL_RESULT_SUCC: success 
	 * @throws HiException
	 * <AUTHOR>
	 * @time 2010-9-6  16:49 
	 */
	public static int notifyToServer(HiMessageContext ctx, HiETF etfData, HiFilActCfg actCfg) 
	{
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		int nRet = HiFILConstants.FIL_RESULT_FAIL;
		try {
			HiMessage message = ctx.getCurrentMsg();	
			message.setBody(etfData);	
			if(log.isDebugEnabled()) 
				log.debug("notifyToServer : "+ etfData.toString());		
				
			HiATLParam argsMap = new HiATLParam();
			boolean isOutMsg = false;
			if (!StringUtils.isEmpty(actCfg.getoIp()) && !StringUtils.isEmpty(actCfg.getoPort()))  {
				message.setHeadItem("OIP", actCfg.getoIp());   //目标 IP
				message.setHeadItem("OPT", actCfg.getoPort()); //目标 PORT
				
				String sRn = actCfg.getSrnNm();
				argsMap.put("Srn", sRn);                //目标域名
				isOutMsg = true;
			}
			argsMap.put("ObjSvr", actCfg.getSvrCode()); //目标服务器	
			log.info("notifyToServer:  "+ message.toString());	
			
			if(isOutMsg) {
				argsMap.put("HTxnCd", actCfg.getTxnCode()); //交易码      for CallThirdService
				nRet= HiCallOut.CallThirdService(argsMap, ctx);  // CallThirdOther
			}
			else {
				argsMap.put("TxnCod", actCfg.getTxnCode()); //交易码   for CallLocal 
				nRet= HiCallOut.CallLocal(argsMap, ctx);
			}
			log.info("notifyToServer: ret = "+ nRet + "  map: " +  argsMap.toMap().toString());			
		} catch (HiException e) {
			if(log.isInfoEnabled()) 
				log.info("notifyToServer err : "+  e);					
		}
		return nRet;
	}	
	
	/**
	 * @comment     发送通知, 执行代理文件下载,上传
	 * @param args  ETF
	 * @param ctx   平台消息
	 * @return FIL_RESULT_SUCC: success 
	 * @throws HiException
	 * <AUTHOR>
	 * @time 2010-9-8  16:49 
	 */	
	public static int sendJmsMessage(HiMessageContext ctx, HiETF etfData, String objSvr,String strTxncod, int timeOut,String strTrace){
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		HiMessageContext ctx1 = new HiMessageContext();
		HiMessage message = new HiMessage(objSvr,"PLTIN0");
		ctx1.setCurrentMsg(message);
		int nRet = HiFILConstants.FIL_RESULT_FAIL;
		try {
			message.setBody(etfData);	
			
			String strTxnDataType = "text/etf";
			message.setHeadItem("SCH", "rq");
			message.setHeadItem("ECT", strTxnDataType);  // 报文类型，text/xml，text/plain
			message.setHeadItem("STC", strTxncod);  // 交易码
			message.setHeadItem("STF", strTrace);    // 调试标志:  0 - 不记  1 - 记调试日志
			//mess.setHeadItem("SYN", "N"); //异步处理
			long l = new Date().getTime();
			message.setHeadItem("STM", new Long(l));     // 交易接入时间
			// comment by wangjing 20130627 start
//			message.setHeadItem("ETM", new Long(l + (long)timeOut));  //交易结束时间
			// commmet by wangjing 20130627 end
			message.setHeadItem("NTF", "1");  // 设置通知标志; 
			message.setType("PLTIN0");          // 消息类型;  -- 定位 process -> Transaction  process. 
			
			if(StringUtils.isNotEmpty(objSvr))
				message.setHeadItem("SDT", objSvr);     // 目标服务器
		
			if(log.isInfoEnabled()){
				log.info("sendJmsMessage: EFTDATA ("+message.toString() + ")" );				
			}			
			if (jmsHelper==null)   {
				if(log.isInfoEnabled())
					log.info("sendJmsMessage : create jms handler !" );	
				String factoryName =(String) ctx.getPara("_QUEUE_FACTORY"); // HiFILConstants.ACT_JMS_FACTORY; //message.getHeadItem("JMS_FACTORY");	
				String queueName = (String) ctx.getPara("_QUEUE"); //HiFILConstants.ACT_JMS_QUEUE;   //message.getHeadItem("JMS_QUEUENM");
//				message.delHeadItem("JMS_FACTORY");
//				message.delHeadItem("JMS_QUEUENM");
				if (StringUtils.isNotEmpty(factoryName) && StringUtils.isNotEmpty(queueName)) {
					jmsHelper = new HiJMSHelper();
					jmsHelper.initialize(factoryName, queueName);	
				}
			}
			if (jmsHelper!=null)  
				jmsHelper.sendMessage(ctx1);
			else {
				if(log.isInfoEnabled()) 
					log.info("sendJmsMessage : Jms Handler is null !" );					
			}
			nRet = HiFILConstants.FIL_RESULT_SUCC;
		} catch (HiException e) {
			if(log.isInfoEnabled()) 
				log.info("sendJmsMessage Error :"+ e.getLocalizedMessage() );				
			    log.info("stacktrace:" + e);
			    e.printStackTrace();
			if (e.getCode().equals(HiFILMessageCode.FIL_JMS_SENDERR) ) {   // JMS ERROR : HiDispatcherCode.ERR_SEND_FAILURE   "212006"
				jmsHelper = null;
			}
			// e.printStackTrace();
		}		
		return nRet ; 
	}


	/**
	 * @comment           发送消息到本地交易
	 * @param ctx         平台消息
	 * @param data
	 * @return
	 * @throws HiException
	 * <AUTHOR>
	 * @time 2010-6-27 上午03:03:27
	 */
	public static int notifySvr(HiMessageContext ctx, HiETF data) throws HiException
	{		
		Logger log = HiLog.getLogger(ctx.getCurrentMsg());
		HiMessage msg = ctx.getCurrentMsg();		
		msg.setBody(data);

		HiATLParam argsMap = new HiATLParam();
		String sNtcCode = (String) ctx.getPara("NTC_OUT");//从配置文件获取通知交易码	
		String sNtcSvr = (String) ctx.getPara("NTC_SVR");
		argsMap.put("TxnCod", sNtcCode);
		argsMap.put("ObjSvr", sNtcSvr);
		
		if(log.isDebugEnabled()){
			log.debug("notifySvr: " + ctx.toString());				
		}				

		return HiCallOut.CallLocal(argsMap, ctx);
	}

}
