package com.hisun.fil.atc;

import com.hisun.atc.common.HiArgUtils;
import com.hisun.exception.HiException;
import com.hisun.fil.HiFILConstants;
import com.hisun.fil.HiFILMessageCode;
import com.hisun.fil.entity.HiFilMonCfg;
import com.hisun.fil.utils.HiDbCfgUtils;
import com.hisun.fil.utils.HiVfsUtils;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessageContext;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map.Entry;

public class HiFileMonitor {

    /**
     * @param HiATLParam args, HiMessageContext ctx
     * @return int
     * @throws HiException
     * @comment 监控&文件控制触发;
     * <AUTHOR>
     * @time 2010-9-5 上午10:50:27
     */
    @SuppressWarnings("unchecked")
    public static int MonitorFil(HiATLParam args, HiMessageContext ctx) throws HiException {
        int nRet = HiFILConstants.FIL_RESULT_SUCC;
        HashMap<String, String> monCfg = new HashMap<String, String>();
        Logger log = HiLog.getLogger(ctx.getCurrentMsg());
        String strOprMth = HiArgUtils.getStringNotNull(args, "OPR_MTH");// 控制动作
        String strMigration = args.get("MIGRATION");
        if (StringUtils.equals(strMigration, "1")) {
            monCfg.put(HiFILConstants.FILDB_MON_MIGRATION, "1");
        }
        String strMonDesc = args.get("MON_DESC");// 操作描述
        if (strMonDesc == null)
            strMonDesc = "";
        monCfg.put(HiFILConstants.FILDB_OPR_MTH, strOprMth);
        monCfg.put(HiFILConstants.FILDB_MON_DESC, strMonDesc);

        String strSysTyp = args.get("SYS_TYP"); // 系统类型 - 未使用;
        if (StringUtils.isEmpty(strSysTyp))
            strSysTyp = HiFILConstants.SERVER_TYPE_FIL;
        String strSysCnl = HiArgUtils.getStringNotNull(args, "SYS_CNL"); // 系统渠道
        String strBusCnl = HiArgUtils.getStringNotNull(args, "BUS_CNL"); // 业务渠道
        String strFilTyp = args.get("FIL_TYP"); // 文件类型: 默认数据文件
        if (StringUtils.isEmpty(strFilTyp))
            strFilTyp = HiFILConstants.TYPE_DATA_FILE;

        //20171012 modify by zeng_yi
        boolean checkSvrUrl = false;
        HashMap<String, String> fileCfg = HiDbCfgUtils.getUrlCfgByCNL(ctx, strSysTyp, strSysCnl, strBusCnl, strFilTyp);
        if (null == fileCfg || fileCfg.isEmpty()) {
            checkSvrUrl = true;
        }

        monCfg.put(HiFILConstants.FILDB_SYS_TYP, strSysTyp);
        monCfg.put(HiFILConstants.FILDB_SYS_CNL, strSysCnl);
        monCfg.put(HiFILConstants.FILDB_BUS_CNL, strBusCnl);
        monCfg.put(HiFILConstants.FILDB_FIL_TYP, strFilTyp);

        String strLclDir = args.get("LCL_DIR"); // 本地目录
        if (strLclDir == null)
            strLclDir = "";
        String strLclNam = args.get("LCL_NM"); // 本地文件名
        strLclNam = strLclNam == null ? "" : strLclNam.trim();
        String strFilMatch = args.get("LCL_MATCH"); // 文件通配名称
        strFilMatch = strFilMatch == null ? "" : strFilMatch.trim();
        if (StringUtils.isEmpty(strLclNam) && StringUtils.isEmpty(strFilMatch))
            throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, "缺少文件名称信息 !");
        if (StringUtils.isNotEmpty(strFilMatch)) { // 如为文件通配名称,则将文件名称设置为与通配数据;
            strLclNam = strFilMatch;
            if (strFilMatch.indexOf("|") >= 0)
                throw new HiException(HiFILMessageCode.FIL_NOT_SUPPORT, "不支持的功能操作 !");
        }

        String strTdFlag = args.get("FIL_TDFLAG"); // 通配扫描是否检索当日文件
        if (StringUtils.isEmpty(strTdFlag))
            strTdFlag = HiFILConstants.TYPE_CUR_NODATE;
        String strIsBak = args.get("IS_BAK"); // 是否备份文件 0: 不备份 1:备份;
        if (StringUtils.isEmpty(strIsBak))
            strIsBak = HiFILConstants.TYPE_BAK_NO;
        monCfg.put(HiFILConstants.FILDB_LCL_DIR, strLclDir);
        monCfg.put(HiFILConstants.FILDB_LCL_NM, strLclNam);
        monCfg.put(HiFILConstants.FILDB_LCL_MATCH, strFilMatch);
        monCfg.put(HiFILConstants.FILDB_LCL_TDFLAG, strTdFlag);
        monCfg.put(HiFILConstants.FILDB_FIL_BAK, strIsBak);

        String strFilMerge = args.get("FIL_MERGE"); // 是否合并文件
        if (StringUtils.isEmpty(strFilMerge))
            strFilMerge = "0";
        String strFilMergeNm = args.get("FIL_MERGENM"); // 合并的文件名称
        if (strFilMergeNm == null)
            strFilMergeNm = "";
        if (strFilMerge == "1" && StringUtils.isEmpty(strFilMergeNm))
            throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, "缺少合并文件名称信息 !");
        monCfg.put(HiFILConstants.FILDB_FIL_MERGE, strFilMerge);
        monCfg.put(HiFILConstants.FILDB_FIL_MERGENM, strFilMergeNm);

        String strActTimeOut = args.get("ACT_TIMOUT"); // 动作超时时间, 默认3小时 ;
        if (StringUtils.isEmpty(strActTimeOut) || Integer.parseInt(strActTimeOut) < 1)
            strActTimeOut = "" + HiFILConstants.ACT_RETRY_TIMEOUT;
        else
            strActTimeOut = String.valueOf(Integer.parseInt(strActTimeOut) * 60 * 1000);
        String strActTry = args.get("ACT_TRY"); // 重做次数, 默认 5 次
        if (StringUtils.isEmpty(strActTry) || Integer.parseInt(strActTry) < 1)
            strActTry = String.valueOf(HiFILConstants.ACT_RETRY_DEFNUM);
        int strEncrypt = args.getInt("ENCRYPT"); // 是否加密
        monCfg.put(HiFILConstants.FILDB_CTL_INTERVAL, strActTimeOut);
        monCfg.put(HiFILConstants.FILDB_FIL_TMOUT, strActTimeOut); // 记录在控制表
        monCfg.put(HiFILConstants.FILDB_CTL_MAX, strActTry);
        monCfg.put(HiFILConstants.FILDB_ENCRYPT, String.valueOf(strEncrypt));

        String strSvrUrl = args.get("SVR_URL"); // 远程服务地址
        if (strSvrUrl == null)
            strSvrUrl = "";
        if (Integer.valueOf(strOprMth) == HiFILConstants.MON_OPR_HTTPGET && StringUtils.isEmpty(strSvrUrl)) // for HTTPGET
            throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, strSysTyp, strSysCnl, strBusCnl, strFilTyp);
        String strUsrName = args.get("USR_NM"); // 登录账号
        if (strUsrName == null)
            strUsrName = "";
        String strUsrPwd = args.get("USR_PWD"); // 登录密码
        if (strUsrPwd == null)
            strUsrPwd = "";
        String strObjDir = args.get("OBJ_DIR"); // 远程地址的相对路径
        if (strObjDir == null)
            strObjDir = "";
        String strObjExtDir = args.get("OBJ_EXTDIR"); // 扩展相对路径;
        if (strObjExtDir == null)
            strObjExtDir = "";
        monCfg.put(HiFILConstants.FILDB_SVR_URL, strSvrUrl);
        monCfg.put(HiFILConstants.FILDB_USR_NM, strUsrName);
        monCfg.put(HiFILConstants.FILDB_USR_PWD, strUsrPwd);
        monCfg.put(HiFILConstants.FILDB_OBJ_DIR, strObjDir);
        monCfg.put(HiFILConstants.FILDB_OBJ_EXTDIR, strObjExtDir);

        //20171012 modify by zeng_yi
        if (checkSvrUrl) {
            //非http获取文件时
            if (!strOprMth.equals(HiFILConstants.MON_OPR_HTTPGET)) {
                if (StringUtils.isBlank(strSvrUrl) || StringUtils.isBlank(strUsrName) || StringUtils.isBlank(strUsrPwd) || StringUtils.isBlank(strObjDir)) {
                    throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, strSvrUrl, strUsrName, strUsrPwd, strObjDir);
                }
            } else {
                if (StringUtils.isBlank(strSvrUrl)) {
                    throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, strSvrUrl);
                }
            }
        }

        if (log.isInfoEnabled()) {
            log.info("HiOprFile: [OPR_MTH=" + strOprMth + "] [SYS_TYPE=" + strSysTyp + "] [SYS_CNL=" + strSysCnl + "] [BUS_CNL=" + strBusCnl + "] [FIL_TYP="
                    + strFilTyp + "] " + " [LCL_NM=" + strLclNam + "] [LCL_MATCH=" + strFilMatch + "] [IS_BAK=" + strIsBak + "] [FIL_MERGE=" + strFilMerge
                    + "] [FIL_TDFLAG=" + strTdFlag + "] [ACT_TIMOUT=" + strActTimeOut + "] [ACT_TRY=" + strActTry + "] [OBJ_EXTDIR=" + strObjExtDir
                    + "] [SVR_URL=" + strSvrUrl + "]");
        }

        // 监控控制表 : 监控类型(MON_TYP) , 优先级别 (OPR_PRIORITY: 1-> 5) 处理
        // F_ACT_NO , 固定写入 : ftpGet, ftpPut, HttpGet ......
        monCfg.put(HiFILConstants.FILDB_OPR_PRIORITY, "1");
        int oprMth = Integer.valueOf(strOprMth);
        if (oprMth == HiFILConstants.MON_OPR_FTPGET) {
            monCfg.put(HiFILConstants.FILDB_MON_TYP, HiFILConstants.MON_TYP_GET);// MON_TYP
            monCfg.put(HiFILConstants.FILDB_OPR_PRIORITY, "2");
        } else if (oprMth == HiFILConstants.MON_OPR_FTPPUT) {
            monCfg.put(HiFILConstants.FILDB_MON_TYP, HiFILConstants.MON_TYP_PUT);// MON_TYP
        } else if (oprMth == HiFILConstants.MON_OPR_HTTPGET) {
            monCfg.put(HiFILConstants.FILDB_MON_TYP, HiFILConstants.MON_TYP_GET);// MON_TYP
            monCfg.put(HiFILConstants.FILDB_OPR_PRIORITY, "3");
            if (StringUtils.isEmpty(strSvrUrl) || StringUtils.isEmpty(strLclNam))
                throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, "缺少配置参数信息 !");
        } else if (oprMth == HiFILConstants.MON_OPR_HTTPPUT) {
            monCfg.put(HiFILConstants.FILDB_MON_TYP, HiFILConstants.MON_TYP_PUT);// MON_TYP
            monCfg.put(HiFILConstants.FILDB_OPR_PRIORITY, "2");
        } else if (oprMth == HiFILConstants.MON_OPR_LOCALFIND) {
            monCfg.put(HiFILConstants.FILDB_MON_TYP, HiFILConstants.MON_TYP_FIL);// MON_TYP
        } else
            throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, "非法的操作类型 !");
        if (StringUtils.isNotEmpty(strFilMatch) && monCfg.get("MON_TYP").equalsIgnoreCase(HiFILConstants.MON_TYP_PUT))
            throw new HiException(HiFILMessageCode.FIL_NO_CONFIG, "不支持匹配式上传模式 !");

        // 监控控制表 : 更新时间
        // TimeZone tz = TimeZone.getTimeZone("GMT+08:00");
        // TimeZone.setDefault(tz);
        Date curDate = new Date();
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMddHHmm"); // 精确到分钟;
        String strDate = fmt.format(curDate);
        monCfg.put(HiFILConstants.FILDB_MON_REGTM, strDate);
        monCfg.put(HiFILConstants.FILDB_OPR_FLAG, String.valueOf(HiFILConstants.STS_FIL_INITIAL)); // 操作状态初始化;

        // 监控控制表 : 文件数量, 通配标识;
        if (StringUtils.isEmpty(strFilMatch)) { // 具体明确文件处理 , 分割 "|" ;
            String[] files = StringUtils.split(strLclNam, HiFILConstants.FIL_SPLIT_CHAR);
            monCfg.put(HiFILConstants.FILDB_FIL_ALL_CNT, String.valueOf(files.length));
            monCfg.put(HiFILConstants.FILDB_FIL_NOW_CNT, "0");
            monCfg.put(HiFILConstants.FILDB_FIL_MATCH, "0");
        } else {
            monCfg.put(HiFILConstants.FILDB_FIL_ALL_CNT, "0"); // 多文件通配符, 初始不确定,暂设置为 0 ;
            monCfg.put(HiFILConstants.FILDB_FIL_NOW_CNT, "0");
            monCfg.put(HiFILConstants.FILDB_FIL_MATCH, "1");
        }

        // 监控控制表 : 通知标志( NOTIFY_FLAG), 状态(MON_STS), 应用主键(MON_KEY);
        String strMonKey = getMonExtKey(monCfg);
        monCfg.put(HiFILConstants.FILDB_NOTIFY_FLAG, "0"); // 通知初始化;
        monCfg.put(HiFILConstants.FILDB_MON_STS, String.valueOf(HiFILConstants.STS_MON_INITIAL)); // 状态初始化;
        monCfg.put(HiFILConstants.FILDB_MON_KEY, strMonKey);

        // 文件控制表: REG_TM , LCL_FILLIST, LCL_TMPLIST, CTL_PROC,CTL_FAILS
        fmt = new SimpleDateFormat("yyyyMMddHHmmss");
        strDate = fmt.format(curDate);
        monCfg.put(HiFILConstants.FILDB_REG_TM, strDate);
        fmt = new SimpleDateFormat("yyyyMMdd"); //
        strDate = fmt.format(curDate);
        monCfg.put(HiFILConstants.FILDB_SYS_DT, strDate);
        monCfg.put(HiFILConstants.FILDB_LCL_FILLIST, "");
        monCfg.put(HiFILConstants.FILDB_LCL_TMPLIST, "");
        monCfg.put(HiFILConstants.FILDB_CTL_ACTTM, "0");
        monCfg.put(HiFILConstants.FILDB_CTL_PROC, String.valueOf(HiFILConstants.STS_FIL_INITIAL));
        monCfg.put(HiFILConstants.FILDB_CTL_FAILS, "0");

        if (log.isInfoEnabled()) {
            String showInfo = "MonitorFil: ", key = "", value = "";
            Iterator iter = monCfg.entrySet().iterator();
            while (iter.hasNext()) {
                Entry<String, String> entry = (Entry<String, String>) iter.next();
                key = entry.getKey();
                value = entry.getValue();
                showInfo += "[" + key + "=" + value + "] ";
            }
            if (log.isInfoEnabled())
                log.info(showInfo);
        }

        HiFilMonCfg inMonCfg = null;
        // 检索是否重复控制请求;
        String strMonCfgNo = HiDbCfgUtils.checkIsSameRequest(ctx, monCfg);
        if (!StringUtils.isEmpty(strMonCfgNo)) {
            monCfg.put(HiFILConstants.FILDB_MON_NO, strMonCfgNo); //
            monCfg.put(HiFILConstants.FILDB_MON_FNO, strMonCfgNo);
            inMonCfg = (HiFilMonCfg) HiVfsUtils.convertToMonCfgByMap(ctx, monCfg);
            nRet = HiDbCfgUtils.updateMonFileCtrlParam(ctx, inMonCfg); // 更新请求控制信息; 初始化文件控制表的相关控制参数
        } else {
            // 1.获取控制编号;
            strMonCfgNo = HiDbCfgUtils.getUniqueNo(ctx);
            monCfg.put(HiFILConstants.FILDB_MON_NO, strMonCfgNo);
            monCfg.put(HiFILConstants.FILDB_MON_FNO, strMonCfgNo);

            // 2.构造FilMonCfg, FilCtlPra对象
            inMonCfg = (HiFilMonCfg) HiVfsUtils.convertToMonCfgByMap(ctx, monCfg);
            if (inMonCfg == null)
                return HiFILConstants.FIL_RESULT_FAIL;
            if (inMonCfg.getFilCtlPraList().size() <= 0)
                throw new HiException(HiFILMessageCode.FIL_CONVERT_ERR, "Converting map to object error !");

            // 3.写入监控配置, 文件控制表信息
            nRet = HiDbCfgUtils.insertMonCtrlParam(ctx, inMonCfg);
            if (nRet == HiFILConstants.FIL_RESULT_SUCC)
                nRet = HiDbCfgUtils.insertFileCtrlParam(ctx, inMonCfg);
            if (nRet != HiFILConstants.FIL_RESULT_SUCC) {
                ctx.getDataBaseUtil().rollback();
                throw new HiException(HiFILMessageCode.FIL_DB_EXPRECTION, "Database operation exception !");
            }
        }
        return nRet;
    }

    private static String getMonExtKey(HashMap<String, String> monCfg) {
        String strMonKey = "SYS_CNL=[" + monCfg.get("SYS_CNL") + "] " + "BUS_CNL=[" + monCfg.get("BUS_CNL") + "] " + "FIL_TYP=[" + monCfg.get("FIL_TYP") + "] "
                + "OPR_MTH=[" + monCfg.get("OPR_MTH") + "] " + "OPR_FLAG=[" + monCfg.get("OPR_FLAG") + "] " + "REG_TM =[" + monCfg.get("MON_REGTM") + "] "
                + "LCL_NM=[" + monCfg.get("LCL_NM") + "] " + "LCL_MATCH=[" + monCfg.get("LCL_MATCH") + "] ";
        return strMonKey;
    }
}