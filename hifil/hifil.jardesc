<?xml version="1.0" encoding="UTF-8"?>
<jardesc>
<jar path="motion2/app/applib/hifil.jar"/>
<options buildIfNeeded="true" compress="true" descriptionLocation="/motion2/src/hifil/hifil.jardesc" exportErrors="true" exportWarnings="true" includeDirectoryEntries="false" overwrite="false" saveDescription="true" storeRefactorings="false" useSourceFolders="false"/>
<storedRefactorings deprecationInfo="true" structuralOnly="false"/>
<selectedProjects/>
<manifest generateManifest="true" manifestLocation="" manifestVersion="1.0" reuseManifest="false" saveManifest="false" usesManifest="true">
<sealing sealJar="false">
<packagesToSeal/>
<packagesToUnSeal/>
</sealing>
</manifest>
<selectedElements exportClassFiles="true" exportJavaFiles="false" exportOutputFolder="false">
<folder path="/motion2/src/hifil"/>
</selectedElements>
</jardesc>
