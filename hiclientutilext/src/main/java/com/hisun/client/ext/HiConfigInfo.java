/*
 * @(#)HiConfigInfo.java 1.0 Dec 2, 2008
 *
 * Copyright 2000 - 2008 HISUN. All Rights Reserved.
 */
package com.hisun.client.ext;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

public class HiConfigInfo {
	private String msgType;
	private String serverName;
	private String logFile;
	private String mode;
	private ArrayList<IpPortPair> ipPortPairs = new ArrayList<IpPortPair>();
	private int idx = 0;
	public String getLogFile() {
		return logFile;
	}
	
	public void SetLogFile(String logFile) {
		this.logFile = logFile; 
	}
	
	public void load(String file) throws IllegalAccessException, DocumentException, IOException {
		ClassLoader loader = Thread.currentThread().getContextClassLoader();
		InputStream is = loader.getResourceAsStream(file);
		if( is == null ) {
			throw new RuntimeException(file + " not exists");
		}
		try {
			load(is);
		} finally {
			is.close();
		}
	}

	public void load(InputStream is) throws IllegalAccessException, DocumentException {
		SAXReader reader = new SAXReader();
		Document doc = reader.read(is);
		Element root = doc.getRootElement();
		msgType = root.attributeValue("msgType");
		if( StringUtils.isBlank(msgType) ) {
			msgType = "PLTIN0";
		}
		serverName = root.attributeValue("serverName");
		if( StringUtils.isBlank(serverName) ) {
			serverName = "";
		}
		logFile = root.attributeValue("logFile");
		if( StringUtils.isBlank(logFile)) {
			logFile = "invokeservice.trc";
		}
		
		mode = root.attributeValue("mode");
		if( StringUtils.isBlank(mode)) {
			mode = "";
		}
		
		
		Iterator iter  = root.elementIterator("Item");
		while (iter.hasNext()) {
			String tmp;
			Element element = (Element) iter.next();
			IpPortPair ipPortPair = new IpPortPair();
			if( (tmp = element.attributeValue("ip")) != null ) {
				ipPortPair.setIp(tmp);
			}
			
			if( StringUtils.isBlank(ipPortPair.getIp()) ) {
				throw new RuntimeException("ip is empty");
			}
			
			if( (tmp = element.attributeValue("port")) != null ) {
				ipPortPair.setPort(NumberUtils.toInt(tmp));
			}
			
			if( ipPortPair.getPort() <= 0 ) {
				throw new IllegalAccessException("port is not number");
			}
			
			if( (tmp = element.attributeValue("isSRNConn")) != null ) {
				ipPortPair.setSRNConn(BooleanUtils.toBoolean(tmp));
			}
			
			if( (tmp = element.attributeValue("tmOut")) != null ) { 
				ipPortPair.setTmOut(NumberUtils.toInt(tmp));
			}
			
			if( (tmp = element.attributeValue("logSwitch")) != null ) { 
				ipPortPair.setLogSwitch(BooleanUtils.toBoolean(tmp));
			}
			//-- SSL ����
			int sslMode = NumberUtils.toInt(element.attributeValue("sslMode"));
			if(sslMode == 1){
				ipPortPair.setIdentityFile(element.attributeValue("identityFile"));
				ipPortPair.setTrustFile(element.attributeValue("trustFile"));
				ipPortPair.setKeyPsw(element.attributeValue("keyPsw"));
				ipPortPair.setTrustPsw(element.attributeValue("trustPsw"));
//				ipPortPairs.add(new IpPortPair(ip, port, sslMode, identityFile,trustFile,keyPsw,trustPsw, isSRNConn));
			}
			ipPortPairs.add(ipPortPair);
		}
	}

	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public IpPortPair getIpPortPair() {
		if( idx >= ipPortPairs.size() )
			idx = 0;
		IpPortPair ipPortPair = ipPortPairs.get(idx);
		idx++;
		return ipPortPair;
	}

	public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	/**
	 * @return the mode
	 */
	public String getMode() {
		return mode;
	}

	/**
	 * @param mode the mode to set
	 */
	public void setMode(String mode) {
		this.mode = mode;
	}
}

class IpPortPair{
	int port;
	String ip;
	int tmOut = 30;
	int sslMode = 0;  // 0: ��ʹ��SSL  1��ʹ��SSL
	String identityFile = "";
	String trustFile = "";
	String keyPsw = "";
	String trustPsw = "";
	boolean logSwitch = false;
	/**
	 * �Ƿ�Ϊ��l��
	 */
	boolean isSRNConn = false;
	public IpPortPair() {
		
	}
	public IpPortPair(String ip, int port) {
		this.ip = ip;
		this.port = port;
	}
	public IpPortPair(String ip, int port, boolean isSRNConn) {
		this.ip = ip;
		this.port = port;
		this.isSRNConn = isSRNConn;
	}
	
	public IpPortPair(String ip, int port, int sslMode, String identityFile, String trustFile, String keyPsw, String trustPsw){
		this.ip = ip;
		this.port = port;
		this.sslMode = sslMode;
		this.identityFile = identityFile;
		this.trustFile = trustFile;
		this.keyPsw = keyPsw;
		this.trustPsw = trustPsw;
	}

	public IpPortPair(String ip, int port, int sslMode, String identityFile, String trustFile, String keyPsw, String trustPsw, boolean isSRNConn){
		this.ip = ip;
		this.port = port;
		this.sslMode = sslMode;
		this.identityFile = identityFile;
		this.trustFile = trustFile;
		this.keyPsw = keyPsw;
		this.trustPsw = trustPsw;
		this.isSRNConn = isSRNConn;
	}

	public boolean isSRNConn() {
		return isSRNConn;
	}
	
	public void setSRNConn(boolean isSRNConn) {
		this.isSRNConn = isSRNConn;
	}
	/**
	 * @return the tmOut
	 */
	public int getTmOut() {
		return tmOut;
	}
	/**
	 * @param tmOut the tmOut to set
	 */
	public void setTmOut(int tmOut) {
		this.tmOut = tmOut;
	}
	/**
	 * @return the port
	 */
	public int getPort() {
		return port;
	}
	/**
	 * @param port the port to set
	 */
	public void setPort(int port) {
		this.port = port;
	}
	/**
	 * @return the ip
	 */
	public String getIp() {
		return ip;
	}
	/**
	 * @param ip the ip to set
	 */
	public void setIp(String ip) {
		this.ip = ip;
	}
	/**
	 * @return the sslMode
	 */
	public int getSslMode() {
		return sslMode;
	}
	/**
	 * @param sslMode the sslMode to set
	 */
	public void setSslMode(int sslMode) {
		this.sslMode = sslMode;
	}
	/**
	 * @return the identityFile
	 */
	public String getIdentityFile() {
		return identityFile;
	}
	/**
	 * @param identityFile the identityFile to set
	 */
	public void setIdentityFile(String identityFile) {
		this.identityFile = identityFile;
	}
	/**
	 * @return the trustFile
	 */
	public String getTrustFile() {
		return trustFile;
	}
	/**
	 * @param trustFile the trustFile to set
	 */
	public void setTrustFile(String trustFile) {
		this.trustFile = trustFile;
	}
	/**
	 * @return the keyPsw
	 */
	public String getKeyPsw() {
		return keyPsw;
	}
	/**
	 * @param keyPsw the keyPsw to set
	 */
	public void setKeyPsw(String keyPsw) {
		this.keyPsw = keyPsw;
	}
	/**
	 * @return the trustPsw
	 */
	public String getTrustPsw() {
		return trustPsw;
	}
	/**
	 * @param trustPsw the trustPsw to set
	 */
	public void setTrustPsw(String trustPsw) {
		this.trustPsw = trustPsw;
	}
	/**
	 * @return the logSwitch
	 */
	public boolean isLogSwitch() {
		return logSwitch;
	}
	/**
	 * @param logSwitch the logSwitch to set
	 */
	public void setLogSwitch(boolean logSwitch) {
		this.logSwitch = logSwitch;
	}
}
