/*
 * @(#)HiETF2HashMapList.java 1.0 Sep 10, 2008
 *
 * Copyright 2000 - 2008 HISUN. All Rights Reserved.
 */
package com.hisun.client.ext;

import java.util.ArrayList;
import java.util.List;

import com.hisun.message.HiETF;

public class HiETF2HashMapList {
	private HiIgnoreHashMap _map = new HiIgnoreHashMap();
	public String toString() {
		return _map.toString();
	}
	public HiIgnoreHashMap map() {
		return _map;
	}
	public HiETF2HashMapList(HiETF etf) {
		toHashMapList(etf.getChildNodes(), _map, _map);
	}
	
	private void toHashMapList(List list, HiIgnoreHashMap map, HiIgnoreHashMap parentMap) {
		for( int i = 0; i < list.size(); i++ ) {
			HiETF node = (HiETF)list.get(i);
			List list1 = node.getChildNodes();
			if( list1.size() != 0 ) {
				parentMap = map; 
				String tmp = node.getName();
				int idx = tmp.lastIndexOf('_');
				if( idx == -1 ) {
					idx = tmp.length();
				}
				tmp = tmp.substring(0, idx);
				ArrayList tmpList;
				if( (tmpList = (ArrayList)parentMap.get(tmp)) == null ) {
					tmpList = new ArrayList();
					parentMap.put(tmp, tmpList);
				}
				HiIgnoreHashMap tmpMap = new HiIgnoreHashMap();
				tmpList.add(tmpMap);
				toHashMapList(list1, tmpMap, parentMap);
			} else {
				String value = node.getValue();
				if( "null".equals(value))
					map.put(node.getName(), "");
				else
					map.put(node.getName(), value);
					
			}
		}
	}
}
