package com.cmpay.hacp.tenant.service.impl;

import com.cmpay.hacp.tenant.adapter.TenantWorkspaceUserAdapter;
import com.cmpay.hacp.tenant.client.TenantWorkspaceUserServiceClient;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;

import javax.annotation.Resource;

public class TenantWorkspaceUserServiceClientImpl extends TenantWorkspaceUserAdapter {

    private TenantWorkspaceUserServiceClient tenantWorkspaceUserServiceClient;


    @Override
    public void existWorkspaceUser(String workspaceId, String userId) {
        DefaultRspDTO defaultRspDto = tenantWorkspaceUserServiceClient.existWorkspaceUser(workspaceId, userId);
        if (JudgeUtils.isNotSuccess(defaultRspDto.getMsgCd())) {
            BusinessException.throwBusinessException(defaultRspDto.getMsgCd());
        }
    }

}
