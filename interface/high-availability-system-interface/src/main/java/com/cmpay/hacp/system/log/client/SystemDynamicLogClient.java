package com.cmpay.hacp.system.log.client;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemDynamicLogApi;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.dto.DynamicLogReqDTO;
import com.cmpay.lemon.framework.data.NoBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 日志管理
 *
 * <AUTHOR>
 */
@FeignClient(name = "${hacp.management.name:high-availability-control-platform-app}", url = "${hacp.management.url:}", contextId = "systemDynamicLogClient")
public interface SystemDynamicLogClient {


    /**
     * 新增日志
     *
     * @param dynamicLogReqDTO
     * @return
     */
    @LogNoneRecord
    @PostMapping(SystemDynamicLogApi.ADD)
    GenericRspDTO<NoBody> addDynamicLog(@RequestBody DynamicLogReqDTO dynamicLogReqDTO);

    /**
     * 修改日志
     *
     * @param dynamicLogReqDTO
     * @return
     */
    @LogNoneRecord
    @PostMapping(SystemDynamicLogApi.UPDATE)
    GenericRspDTO<NoBody> setDynamicRspDataSize(@RequestBody DynamicLogReqDTO dynamicLogReqDTO);
}
