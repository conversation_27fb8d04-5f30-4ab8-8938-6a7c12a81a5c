package com.cmpay.hacp.system.service.impl;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dto.system.CipherDataInfoReqDTO;
import com.cmpay.hacp.system.client.SystemCipherClient;
import com.cmpay.hacp.system.service.adapter.SystemCipherServiceAdapter;
import com.cmpay.hacp.utils.security.TokenUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SystemCipherClientServiceImpl extends SystemCipherServiceAdapter {

    private final SystemCipherClient systemCipherClient;

    @Override
    public String decryptData(String key, String data) {
        CipherDataInfoReqDTO reqDTO =  new CipherDataInfoReqDTO();
        reqDTO.setKey(key);
        reqDTO.setEncryptData(data);
        GenericRspDTO<String> rspDTO = systemCipherClient.decryptData(reqDTO, TokenUtil.getToken());
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(rspDTO.getMsgCd(),rspDTO.getMsgInfo());
        }
        return rspDTO.getBody();
    }

}
