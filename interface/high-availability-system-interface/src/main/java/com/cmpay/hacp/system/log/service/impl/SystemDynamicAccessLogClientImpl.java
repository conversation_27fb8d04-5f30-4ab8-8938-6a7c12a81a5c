package com.cmpay.hacp.system.log.service.impl;

import com.cmpay.hacp.system.log.bo.SysDynamicLogBO;
import com.cmpay.hacp.system.log.client.SystemDynamicLogClient;
import com.cmpay.hacp.system.log.dto.DynamicLogReqDTO;
import com.cmpay.hacp.system.log.service.SystemDynamicLogService;
import com.cmpay.lemon.common.utils.BeanUtils;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public class SystemDynamicAccessLogClientImpl implements SystemDynamicLogService {

    private final SystemDynamicLogClient systemDynamicLogClient;

    public SystemDynamicAccessLogClientImpl(SystemDynamicLogClient systemDynamicLogClient) {
        this.systemDynamicLogClient = systemDynamicLogClient;
    }

    @Override
    @Async
    public void setDynamicRspDataSize(Long dataSize, String dataSizeType, String requestId, String api) {
        DynamicLogReqDTO dynamicLogReqDTO = new DynamicLogReqDTO();
        dynamicLogReqDTO.setDataSize(dataSize);
        dynamicLogReqDTO.setDataSizeType(dataSizeType);
        dynamicLogReqDTO.setLogId(requestId);
        dynamicLogReqDTO.setInterfaceRecord(api);
        systemDynamicLogClient.setDynamicRspDataSize(dynamicLogReqDTO);
    }

    @Override
    @Async
    public void addDynamicLog(SysDynamicLogBO sysDynamicLogBO) {
        DynamicLogReqDTO dynamicLogReqDTO = new DynamicLogReqDTO();
        BeanUtils.copyProperties(dynamicLogReqDTO, sysDynamicLogBO);
        systemDynamicLogClient.addDynamicLog(dynamicLogReqDTO);
    }

    @Override
    public List<SysDynamicLogBO> getDynamicListByRequestId(String requestId) {
        return null;
    }
}
