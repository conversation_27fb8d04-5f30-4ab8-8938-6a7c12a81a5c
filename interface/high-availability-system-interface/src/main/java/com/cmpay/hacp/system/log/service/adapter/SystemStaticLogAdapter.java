package com.cmpay.hacp.system.log.service.adapter;

import com.cmpay.hacp.system.log.bo.StaticDataSizeBO;
import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import com.cmpay.hacp.system.log.service.SystemStaticLogService;
import com.cmpay.lemon.common.context.LemonContext;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

public abstract class SystemStaticLogAdapter implements SystemStaticLogService {
    @Override
    public void add(SysStaticLogBO logBO) {

    }

    @Override
    public PageInfo<SysStaticLogBO> list(int pageNum, int pageSize, SysStaticLogBO logBO) {
        return null;
    }

    @Override
    public SysStaticLogBO info(String id) {
        return null;
    }

    @Override
    public void delete(List<String> ids) {

    }

    @Override
    public void setStaticRspDataSize(long rspDataSize, String rspDataSizeType) {
        StaticDataSizeBO staticDataSizeBO = new StaticDataSizeBO();
        staticDataSizeBO.setRspDataSizeType(rspDataSizeType);
        staticDataSizeBO.setRspDataSize(rspDataSize);
        LemonContext.getCurrentContext().put(STATIC_RSP_DATA_SIZE, staticDataSizeBO);
    }

    @Override
    public StaticDataSizeBO getStaticRspDataSize() {
        StaticDataSizeBO staticDataSizeBO = LemonContext.getCurrentContext().containsKey(STATIC_RSP_DATA_SIZE) ? (StaticDataSizeBO) LemonContext.getCurrentContext().get(STATIC_RSP_DATA_SIZE) : null;
        LemonContext.getCurrentContext().remove(STATIC_RSP_DATA_SIZE);
        return staticDataSizeBO;
    }
}
