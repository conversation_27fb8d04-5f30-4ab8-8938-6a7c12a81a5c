package com.cmpay.hacp.system.service.impl;

import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.system.client.SystemPermissionClient;
import com.cmpay.hacp.system.service.adapter.SystemPermissionServiceAdapter;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@RequiredArgsConstructor
public class SystemPermissionClientImpl extends SystemPermissionServiceAdapter {

    private final SystemPermissionClient systemPermissionClient;

    @Override
    public PermMenuTreeMetaBO queryUserPermissions(String userId, String applicationName) {
        GenericRspDTO<PermMenuTreeMetaBO> genericRspDTO = systemPermissionClient.queryUserPermissions(new GenericDTO<>());
        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(genericRspDTO.getMsgCd());
        }
        return genericRspDTO.getBody();
    }
}
