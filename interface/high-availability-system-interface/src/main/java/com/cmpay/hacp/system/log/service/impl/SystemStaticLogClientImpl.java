package com.cmpay.hacp.system.log.service.impl;

import com.cmpay.hacp.dto.system.SystemLogDTO;
import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import com.cmpay.hacp.system.log.client.SystemStaticLogClient;
import com.cmpay.hacp.system.log.service.adapter.SystemStaticLogAdapter;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.utils.WebUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import static com.cmpay.hacp.constant.TenantConstant.WORKSPACE_ID;

@Slf4j
@AllArgsConstructor
public class SystemStaticLogClientImpl extends SystemStaticLogAdapter {

    private final SystemStaticLogClient riseSystemLogClient;

    @Override
    public void add(SysStaticLogBO sysStaticLogBO) {
        try {
            SystemLogDTO riseSystemLogDto = new SystemLogDTO();
            BeanUtils.copyProperties(riseSystemLogDto, sysStaticLogBO);

            HttpServletRequest request = WebUtils.getHttpServletRequest();
            if (JudgeUtils.isNotNull(request) && JudgeUtils.isNotBlank(request.getHeader(WORKSPACE_ID))) {
                riseSystemLogDto.setWorkspaceId(request.getHeader(WORKSPACE_ID));
            }


            if (JudgeUtils.isNotBlank(riseSystemLogDto.getWorkspaceId())) {
                DefaultRspDTO defaultRspDTO = riseSystemLogClient.addSystemLogInfo(riseSystemLogDto);
                log.info("addSystemLogInfo msgCd {}", defaultRspDTO.getMsgCd());
            }
        } catch (Exception e) {
            log.warn("RiseSystemLogClientImpl add Exception {}", e.getMessage());
        }


    }
}
