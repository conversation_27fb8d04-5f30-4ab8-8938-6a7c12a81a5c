package com.cmpay.hacp.system.client;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.BaseApi;
import com.cmpay.hacp.api.SystemCipherApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.dto.system.CipherDataInfoReqDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform-app}", url = "${hacp.management.discovery.url:}", contextId = "systemCipherClient")
public interface SystemCipherClient {

    /**
     * 解密数据，不包括加密，由各服务自己再次加密存储
     * @param data
     * @param token
     * @return
     */
    @PostMapping(VersionApi.VERSION_V1 + SystemCipherApi.DECRYPT_DATA)
    GenericRspDTO<String> decryptData(@RequestBody CipherDataInfoReqDTO data,@RequestHeader("token")String token);
}
