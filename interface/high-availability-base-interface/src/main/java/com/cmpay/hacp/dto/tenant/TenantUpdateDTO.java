/*
 * @ClassName TenantDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "修改租户")
public class TenantUpdateDTO {

    /**
     * @Fields tenantId 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
    @NotBlank(message = "HAC00004")
    private String tenantId;

    /**
     * @Fields tenantName 租户名称
     */
    @ApiModelProperty(value = "租户名称", required = true, example = "租户名称")
    @NotBlank(message = "HAC00002")
    private String tenantName;

    /**
     * @Fields userId 租户管理员ID
     */
    @ApiModelProperty(value = "租户管理员ID", required = true, example = "MON000001")
    @NotBlank(message = "HAC00003")
    private String userId;

    /**
     * @Fields deptId 部门编号
     */
    @ApiModelProperty(value = "部门编号", required = false, example = "888888")
    private String deptId;

    /**
     * @Fields remarks 租户备注
     */
    @ApiModelProperty(value = "租户备注", required = false, example = "租户备注")
    private String remarks;

}
