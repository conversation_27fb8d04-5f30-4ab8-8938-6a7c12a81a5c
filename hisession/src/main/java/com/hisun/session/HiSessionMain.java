package com.hisun.session;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;

import com.hisun.session.HiSession;
import com.hisun.session.manager.inf.HiMemcachedSessionManager;

public class HiSessionMain {
	private static HiSessionMain instance = null;

	public static synchronized HiSessionMain getInstance() {
		if (instance == null) {
			instance = new HiSessionMain();
		}
		return instance;
	}

	public Object getSession(HttpServletRequest request, String name) {
		// 获取需要待取值节点名
		String[] names = StringUtils.split(name, ".");

		String sessionId = (String) request.getAttribute("_XMEM_ID");
		HiMemcachedSessionManager memSesMan = new HiMemcachedSessionManager();
		// log.info("start GetSession");
		// memSesMan.setExpTime(120);
		HiSession session = null;
		JSONObject sess = null;
		if (!StringUtils.isBlank(sessionId)) {
			session = memSesMan.GetSession(sessionId);
			sess = session.getSession();
		}

		Object obj = null;
		if (sess != null) {
			for (int i = 0; i < names.length; i++) {
				obj = sess.get(names[i]);
				if (obj instanceof JSONObject) {
					sess = (JSONObject) obj;
				}
			}
		}
		return obj;
	}

}
