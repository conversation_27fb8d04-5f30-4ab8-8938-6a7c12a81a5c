-- CREATE TABLE `表名` (
--     -- 主键
--                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
--
--     -- 多租户支持（按需）：如需支持多租户，在表中添加 `workspace_id` 字段，并为其建立索引。
--     -- 若不需要多租户支持，则不添加该字段。
--                         `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',

--
--     -- 业务字段（根据实际需求添加）
--                         `name` VARCHAR(100) NOT NULL COMMENT '名称',
--                         `description` varchar(500) DEFAULT NULL COMMENT '描述',
--
--     -- 审计字段
--                         `created_by` varchar(64) NOT NULL COMMENT '创建人ID',
--                         `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
--                         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--                         `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人ID',
--                         `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
--                         `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--
--     -- 索引
--                         PRIMARY KEY (`id`),
--                         INDEX `idx_workspace_id` (`workspace_id`),
--     -- 根据业务需求添加其他索引
--
--     -- 表备注
-- ) ENGINE=InnoDB CHARSET=utf8 COMMENT='表描述';

CREATE TABLE `inspection_plugin`
(
    `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
    `workspace_id`    varchar(64)  NOT NULL COMMENT 'workspace ID',
    `plugin_id`       varchar(64)  NOT NULL COMMENT '插件ID',
    `name`            varchar(128) NOT NULL COMMENT '插件名称',
    `type`            tinyint(1) DEFAULT '0' COMMENT '插件类型(0-SHELL、1-PYTHON)',
    `status`          tinyint(1) DEFAULT '0' COMMENT '插件状态(0禁用，1启用)',
    `description`     varchar(500)          DEFAULT NULL COMMENT '插件描述',
    `created_by`      varchar(64)           DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
    `created_by_name` varchar(64)  NOT NULL COMMENT '创建人',
    `created_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_by`      varchar(64)           DEFAULT NULL COMMENT '修改人',
    `updated_by_name` varchar(64)           DEFAULT NULL COMMENT '更新人',
    `updated_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workspace_plugin` (`workspace_id`, `plugin_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='巡检插件基本信息表';

CREATE TABLE `inspection_plugin_script`
(
    `id`                 bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
    `workspace_id`       varchar(64) NOT NULL COMMENT 'workspace ID',
    `plugin_id`          varchar(64) NOT NULL COMMENT '插件ID',
    `script_content`     text        NOT NULL COMMENT '脚本内容',
    `script_result_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '输出类型: 0=结构化数据, 1=文本数据',
    `created_by`         varchar(64)          DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
    `created_by_name`    varchar(64) NOT NULL COMMENT '创建人',
    `created_time`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_by`         varchar(64)          DEFAULT NULL COMMENT '修改人',
    `updated_by_name`    varchar(64)          DEFAULT NULL COMMENT '更新人',
    `updated_time`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`),
    KEY                  `idx_workspace_plugin` (`workspace_id`, `plugin_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='插件脚本内容表';

CREATE TABLE `inspection_plugin_script_parameter`
(
    `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
    `workspace_id`    varchar(64)  NOT NULL COMMENT 'workspace ID',
    `plugin_id`       varchar(64)  NOT NULL COMMENT '插件ID',
    `param_name`      varchar(128) NOT NULL COMMENT '参数名称',
    `param_type`      tinyint(1) DEFAULT '0' COMMENT '参数类型(文本、数字、邮箱、URL、自定义正则等)',
    `regex_pattern`   varchar(256)          DEFAULT NULL COMMENT '参数限制(正则表达式)',
    `param_value`     varchar(256) NOT NULL COMMENT '参数值',
    `param_desc`      varchar(512) NOT NULL COMMENT '参数描述',
    `is_encrypted`    tinyint(1) DEFAULT '0' COMMENT '是否加密(0不加密，1加密)',
    `created_by`      varchar(64)           DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
    `created_by_name` varchar(64)  NOT NULL COMMENT '创建人',
    `created_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_by`      varchar(64)           DEFAULT NULL COMMENT '修改人',
    `updated_by_name` varchar(64)           DEFAULT NULL COMMENT '更新人',
    `updated_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workspace_plugin_param` (`workspace_id`, `plugin_id`, `param_name`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='插件参数定义表';

CREATE TABLE `inspection_plugin_script_output_filed`
(
    `id`              bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `workspace_id`    varchar(64)    NOT NULL COMMENT 'workspace ID',
    `plugin_id`       varchar(64)    NOT NULL COMMENT '插件ID',
    `field_name`      varchar(100)   NOT NULL COMMENT '字段名称(如cpu.usage)',
    `example_value`   varchar(100) NOT NULL COMMENT '示例值',
    `field_unit`      varchar(20)             DEFAULT NULL COMMENT '单位(如%)',
    `field_type`      tinyint(1) NOT NULL COMMENT '字段类型(0:数值型, 1:字符串型, 2:布尔型)',
    `description`     varchar(500)            DEFAULT NULL COMMENT '描述',
    `created_by`      varchar(64)    NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64)    NOT NULL COMMENT '创建人',
    `created_time`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`      varchar(64)             DEFAULT NULL COMMENT '更新人',
    `updated_by_name` varchar(64)             DEFAULT NULL COMMENT '更新人',
    `updated_time`    datetime                DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workspace_plugin_field` (`workspace_id`, `plugin_id`, `field_name`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='结构化数据字段定义';

CREATE TABLE `inspection_tag`
(
    `id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `workspace_id`    varchar(64) NOT NULL COMMENT 'workspace ID',
    `name`            varchar(64) NOT NULL COMMENT '标签名称',
    `description`     varchar(255)         DEFAULT NULL COMMENT '标签描述',
    `created_by`      varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`      varchar(64)          DEFAULT NULL COMMENT '更新人',
    `updated_by_name` varchar(64)          DEFAULT NULL COMMENT '更新人',
    `updated_time`    datetime             DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workspace_tag_name` (`workspace_id`, `name`),
    KEY               `idx_workspace_id` (`workspace_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='标签定义表';

CREATE TABLE `inspection_plugin_tags`
(
    `id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `workspace_id`    varchar(64) NOT NULL COMMENT 'workspace ID',
    `plugin_id`       varchar(64) NOT NULL COMMENT '插件ID',
    `tag_id`          bigint      NOT NULL COMMENT '标签ID',
    `created_by`      varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workspace_plugin_tag` (`workspace_id`, `plugin_id`, `tag_id`),
    KEY               `idx_workspace_id` (`workspace_id`),
    KEY               `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='插件标签关联表';

CREATE TABLE `inspection_rule_tags`
(
    `id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `workspace_id`    varchar(64) NOT NULL COMMENT 'workspace ID',
    `rule_id`         bigint      NOT NULL COMMENT '规则ID',
    `tag_id`          bigint      NOT NULL COMMENT '标签ID',
    `created_by`      varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workspace_rule_tag` (`workspace_id`, `rule_id`, `tag_id`),
    KEY               `idx_workspace_id` (`workspace_id`),
    KEY               `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='规则标签关联表';

-- 指标定义表
CREATE TABLE `prometheus_indicator_definition`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `indicator_id`   varchar(20)  NOT NULL COMMENT '指标ID',
    `indicator_name` varchar(100) NOT NULL COMMENT '指标名',
    `indicator_type` varchar(20)  NOT NULL COMMENT '指标类型：mid-中间件，host-主机，container-容器',
    `description`    varchar(500) COMMENT '指标描述',
    `status`         tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_indicator_id` (`indicator_id`),
    UNIQUE KEY `uk_indicator_name` (`indicator_name`),
    KEY              `idx_indicator_type` (`indicator_type`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='Prometheus指标定义表';

-- 指标参数配置表
CREATE TABLE `prometheus_indicator_param`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `indicator_id` varchar(20) NOT NULL COMMENT '指标ID',
    `param_order`  tinyint(2) NOT NULL COMMENT '参数顺序：1-10',
    `param_name`   varchar(50) NOT NULL COMMENT '参数名称',
    `param_code`   varchar(50) NOT NULL COMMENT '参数代码',
    `is_required`  tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否必填：0-否，1-是',
    `description`  varchar(200) COMMENT '参数描述',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_indicator_param` (`indicator_id`, `param_order`),
    KEY            `idx_indicator_id` (`indicator_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='Prometheus指标参数配置表';

-- 机房配置表
CREATE TABLE `prometheus_zone_config`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `zone_code`    varchar(50)  NOT NULL COMMENT '机房代码',
    `zone_name`    varchar(100) NOT NULL COMMENT '机房名称',
    `description`  varchar(200) COMMENT '机房描述',
    `status`       tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_zone_code` (`zone_code`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='Prometheus机房配置表';

