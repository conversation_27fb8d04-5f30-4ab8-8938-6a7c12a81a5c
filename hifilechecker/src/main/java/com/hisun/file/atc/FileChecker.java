package com.hisun.file.atc;

import com.hisun.atc.HiATCConstants;
import com.hisun.atc.common.HiArgUtils;
import com.hisun.exception.HiException;
import com.hisun.file.utils.FileCheckUtils;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessageContext;
import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @date: 2023-03-10 9:39
 * @since: 1.0.0
 */
public class FileChecker {

    private static final Logger logger = Logger.getLogger("FileChecker.trc");

    public int execute(HiATLParam param, HiMessageContext ctx) throws HiException {
        String requestId = ctx.getCurrentMsg().getRequestId();
        String content = HiArgUtils.getStringNotNull(param, "fileContent");
        String filename = HiArgUtils.getStringNotNull(param, "fileName");
        String fileTypes = param.get("fileTypes");

        // 获取文件名
        byte[] bytes = null;
        try {
            bytes = Base64.decodeBase64(content.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            if (logger.isInfoEnabled()) {
                logger.info("msgId[" + requestId +"], unsupported encoding UTF-8");
            }
            return HiATCConstants.ERR;
        }

        String fileType = FileCheckUtils.getFileType(bytes);
        if (null == fileType) {
            if (logger.isInfoEnabled()) {
                logger.info("msgId[" + requestId +"], get file type from content is null");
            }
            return HiATCConstants.ERR;
        }

        String filenameSuffix = getFileNameSuffix(filename.toLowerCase());
        if (!fileType.contains(filenameSuffix)) {
            return HiATCConstants.ERR;
        }

        // 检查文件名是否允许
        if (null != fileTypes && fileTypes.trim() != "") {
            return isAllowedFileType(fileTypes, filenameSuffix, requestId);
        }

        return HiATCConstants.SUCC;
    }

    private String getFileNameSuffix(String filename) {
        int index = filename.lastIndexOf(".");
        return filename.substring(index + 1);
    }

    private int isAllowedFileType(String fileTypes, String realFileType, String requestId) {
        boolean allowed = false;
        String[] allowedFileTypes = fileTypes.split(",");
        for (int i = 0, len = allowedFileTypes.length; i < len; i++) {
            if (allowedFileTypes[i].equalsIgnoreCase(realFileType)) {
                allowed = true;
                break;
            }
        }

        if (!allowed) {
            if (logger.isInfoEnabled()) {
                logger.info("msgId[" + requestId +"], file real type: " + realFileType + ", allowed types: " + fileTypes);
            }
            return HiATCConstants.ERR;
        }

        return HiATCConstants.SUCC;
    }
}
