package com.hisun.msession;

import com.hisun.atc.common.HiArgUtils;
import com.hisun.exception.HiException;
import com.hisun.hilib.HiATLParam;
import com.hisun.hilog4j.HiLog;
import com.hisun.hilog4j.Logger;
import com.hisun.message.HiMessage;
import com.hisun.message.HiMessageContext;
import com.hisun.msession.util.HiMemcachedUtils;
import com.hisun.springsession.ics.RedisSessionRepositoryUtil;
import com.hisun.util.HiICSProperty;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class HiMemcachedATC {
    private static final boolean ADAPT_TO_SPRING = HiICSProperty.getBoolean("ics.msession.adapt_to_spring", true);
    private static Logger log = HiLog.getLogger("HIMEMCACHED.trc");

    private void deleteSpringSession(String sessionId) {
        if (!ADAPT_TO_SPRING) {
            return;
        }
        try {
            UUID.fromString(sessionId);
            new RedisSessionRepositoryUtil().delete(sessionId);
        } catch (Exception ignored) {
        }
    }

    public int MemSet(HiATLParam args, HiMessageContext ctx) throws HiException {
        String key = HiArgUtils.getStringNotNull(args, "key");
        String val = HiArgUtils.getStringNotNull(args, "value");
        String svr = args.get("SVR_NM");
        if (StringUtils.isEmpty(svr)) {
            svr = HiSessAtcConstants.SESSION_SVRNM;
        }

        HiMemcachedUtils.setToMem(key, val, svr);
        return 0;
    }

    public int MemGet(HiATLParam args, HiMessageContext ctx) throws HiException {
        HiMessage msg = ctx.getCurrentMsg();

        String key = HiArgUtils.getStringNotNull(args, "key");
        String svr = args.get("SVR_NM");
        if (StringUtils.isEmpty(svr)) {
            svr = HiSessAtcConstants.SESSION_SVRNM;
        }

        Object res = HiMemcachedUtils.getFromMem(key, svr);
        msg.getETFBody().setChildValue("SESSION", String.valueOf(res));
        log.info("RES=[" + res + "]");

        return 0;
    }

    public int MemDelete(HiATLParam args, HiMessageContext ctx)
            throws HiException {
        String key = HiArgUtils.getStringNotNull(args, "key");
        String svr = args.get("SVR_NM");
        if (StringUtils.isEmpty(svr)) {
            svr = HiSessAtcConstants.SESSION_SVRNM;
        }

        HiMemcachedUtils.deleteMem(key, svr);
        deleteSpringSession(key);
        return 0;
    }

    public int MemReplace(HiATLParam args, HiMessageContext ctx)
            throws HiException {
        String key = HiArgUtils.getStringNotNull(args, "key");
        String val = HiArgUtils.getStringNotNull(args, "value");
        String svr = args.get("SVR_NM");
        if (StringUtils.isEmpty(svr)) {
            svr = HiSessAtcConstants.SESSION_SVRNM;
        }

        HiMemcachedUtils.replaceMem(key, val, svr);
        return 0;
    }

    public int MemIncr(HiATLParam args, HiMessageContext ctx)
            throws HiException {
        String key = HiArgUtils.getStringNotNull(args, "key");
        String val = HiArgUtils.getStringNotNull(args, "increment");
        long increment = Long.parseLong(val);
        String init = args.get("initValue");
        long initValue = 0;
        if (!StringUtils.isEmpty(init)) {
            initValue = Long.parseLong(init);
        }

        String dstFld = args.get("dstFld");
        if (StringUtils.isEmpty(dstFld)) {
            dstFld = "RESULT";
        }
        String svr = args.get("SVR_NM");
        if (StringUtils.isEmpty(svr)) {
            svr = HiSessAtcConstants.SESSION_SVRNM;
        }

        long ret = HiMemcachedUtils.incrToMem(key, increment, initValue, svr);
        ctx.getCurrentMsg().getETFBody()
                .setChildValue(dstFld, String.valueOf(ret));
        log.info("incr result=" + ret);
        return 0;
    }

    public int MemDecr(HiATLParam args, HiMessageContext ctx)
            throws HiException {
        String key = HiArgUtils.getStringNotNull(args, "key");
        String val = HiArgUtils.getStringNotNull(args, "decrement");
        long decrement = Long.parseLong(val);
        String init = args.get("initValue");
        long initValue = 0;
        if (!StringUtils.isEmpty(init)) {
            initValue = Long.parseLong(init);
        }

        String dstFld = args.get("dstFld");
        if (StringUtils.isEmpty(dstFld)) {
            dstFld = "RESULT";
        }
        String svr = args.get("SVR_NM");
        if (StringUtils.isEmpty(svr)) {
            svr = HiSessAtcConstants.SESSION_SVRNM;
        }

        long ret = HiMemcachedUtils.decrToMem(key, decrement, initValue, svr);
        ctx.getCurrentMsg().getETFBody()
                .setChildValue(dstFld, String.valueOf(ret));
        log.info("decr result=" + ret);
        return 0;
    }

    /**
     * @param args
     * @param ctx
     * @return
     * @throws HiException
     * @拨测组件
     */

    public int MemStats(HiATLParam args, HiMessageContext ctx)
            throws HiException {
        HiMessage msg = ctx.getCurrentMsg();

        String svr = args.get("SVR_NM");
        if (StringUtils.isEmpty(svr)) {
            svr = HiSessAtcConstants.SESSION_SVRNM;
        }

        Object res = HiMemcachedUtils.statsMem(svr);
        msg.getETFBody().setChildValue("SESSION", String.valueOf(res));
        log.info("RES=[" + res + "]");

        return 0;
    }

    public int ManageMemServer(HiATLParam args, HiMessageContext ctx)
            throws HiException {
        String objSvr = HiArgUtils.getStringNotNull(args, "OBJ_SVR");
        String memSvr = HiArgUtils.getStringNotNull(args, "MEM_SVR");
        String msgTyp = HiArgUtils.getStringNotNull(args, "MSG_TYP");

        HiMemcachedUtils.mngServer(memSvr, objSvr, msgTyp);
        return 0;
    }

    public int PressTestMem(HiATLParam args, HiMessageContext ctx)
            throws HiException {
        int len = NumberUtils.toInt(HiArgUtils.getStringNotNull(args, "LEN"));
        totCnt = NumberUtils
                .toInt(HiArgUtils.getStringNotNull(args, "TOT_CNT"));
        log.info("PressTestMem Test Beginning...");
        log.info("totcnt=[" + totCnt + "] len=[" + len + "]");

        long st = System.currentTimeMillis();
        String thdNm = Thread.currentThread().getName();
        log.info("Current Thread [" + Thread.currentThread().getClass()
                + "] name =[" + thdNm + "]");
        curIndex = new AtomicInteger(0);
        failureNum = new AtomicInteger(0);

        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(len, len, 10,
                TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(),
                new ThreadPoolExecutor.DiscardOldestPolicy());

        while (true) {
            int i = curIndex.addAndGet(1);
            // log.info("curIndex=[" + i + "] totCnt=[" + totCnt + "]");
            if (threadPool.getActiveCount() > len) {
                log.info("activeCount :" + threadPool.getActiveCount());
            }

            if (curIndex.get() < totCnt) {
                threadPool.execute(new MemTest(i));
            } else {
                break;
            }
        }

        threadPool.shutdown();
        try {
            while (true) {
                if (!threadPool.awaitTermination(300, TimeUnit.SECONDS)) {

                } else {
                    break;
                }
            }
        } catch (Exception e) {

        }

        long et = System.currentTimeMillis();
        long tot = et - st;

        log.debug("Total TM=[" + tot / 1000 + "." + tot % 1000 + "] SECONDS");
        log.debug("Avg TM=[" + totCnt * 1000 / tot + "] TPS  Failure=["
                + failureNum.get() + "]");
        log.info("Memcache Test over");
        return 0;
    }

    public class MemTest implements Runnable {
        private String key = "KEY";

        int index = 0;

        public MemTest(int index) {
            this.index = index;
        }

        public void run() {
            char[] arr = new char[4096];
            for (int j = 0; j < arr.length; j++) {
                arr[j] = 'A';
            }

            try {
                // client.set(name, exptime, value)
                key = key + "_" + index;
                String value = arr.toString();
                Map map = new HashMap();
                map.put(key, value);
                HiMemcachedUtils.setToMem(key, map, "S.MEMSVR");
                Object obj = HiMemcachedUtils.getFromMem(key, "S.MEMSVR");
                if (obj == null) {
                    failureNum.addAndGet(1);
                }

                if (Thread.currentThread().interrupted()) {
                    log.info(Thread.currentThread() + " end interrupted!");
                } else {
                    log.info(Thread.currentThread() + "end is not interrupted");
                }
            } catch (HiException e) {
                failureNum.addAndGet(1);
                log.error("", e);
                // break;
            }

            log.info("THREAD Run Over");
        }

    }

    // --
    private static int totCnt = 0;
    private static AtomicInteger curIndex = new AtomicInteger(0);
    private static AtomicInteger failureNum = new AtomicInteger(0);
    private static final int MAX_FAILURE_NUM = 5;
}
